select
    id ,
    combinations_no ,
    warehouse_id ,
    warehouse_name ,
    owner_id ,
    owner_name ,
    sku_id ,
    sku_code ,
    sku_name ,
    package_id  ,
    package_name ,
    package_unit_id ,
    package_unit_name ,
    count ,
    count_ea ,
    combinations_status ,
    combinations_type ,
    remark ,
    creator ,
    create_time

from  cs_combinations
where deleted = 0
<<and combinations_no like concat('%',:combinationsNo,'%')>>
<<and remark like concat('%',:remark,'%')>>
<<and sku_name like concat('%',:skuName,'%')>>
<<and combinations_status  = :combinationsStatus>>
<<and warehouse_id  = :warehouseId>>
<<and owner_id  = :ownerId>>
<<and sku_id  = :skuId>>
<<and sku_code  = :skuCode>>
order by create_time desc