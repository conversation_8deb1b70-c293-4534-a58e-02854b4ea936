/*列表查询设备列表*/
SELECT cps.id,
       cps.staff_no,
       cps.staff_name,
       cps.status,
       cps.warehouse_id,
       cw.warehouse_name,
       cps.position,
       cps.phone,
       cps.address,
       cps.emergency_contact,
       cps.emergency_phone,
       cps.creator,
       cps.create_time
FROM cs_process_staff cps
LEFT JOIN cs_warehouse cw ON cps.warehouse_id = cw.id
WHERE 1 = 1
AND cps.deleted = 0
    << AND cps.staff_name like CONCAT('%',:staffName,'%') >>
    << AND cps.status = :status >>
    << AND cps.warehouse_id = :warehouseId >>
    << AND cps.position = :position >>
    << AND cps.phone like CONCAT('%',:phone,'%') >>
ORDER BY cps.create_time DESC