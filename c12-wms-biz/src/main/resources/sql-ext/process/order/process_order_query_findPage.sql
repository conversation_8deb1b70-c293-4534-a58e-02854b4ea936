select
    cpo.id ,
    cpo.process_order_no ,
    cpo.warehouse_id ,
    cpo.warehouse_name ,
    cpo.owner_id ,
    cpo.owner_name ,
    cpo.loc_id ,
    cpo.loc_name ,
    cpo.process_staff_id ,
    cpo.process_staff_name ,
    cpo.order_type ,
    cpo.process_type ,
    cpo.process_order_status ,
    cpo.allocation_status ,
    cpo.picking_status ,
    cpo.expected_processing_time ,
    cpo.actual_processing_time ,
    cpo.creator ,
    cpo.create_time
from cs_process_order cpo left join cs_process_task cpt on cpo.process_order_no = cpt.process_order_no and cpt.deleted = 0
where cpo.deleted = 0 and cpo.order_type = :orderType
<<and cpo.process_order_no like concat('%',:processOrderNo,'%')>>
<<and cpo.warehouse_id = :warehouseId>>
<<and cpo.owner_id = :ownerId>>
<<and cpt.combinations_id = :combinationsId>>
<<and cpt.combinations_sku_name like concat('%', :combinationsName, '%')>>
order by cpo.modify_time desc

