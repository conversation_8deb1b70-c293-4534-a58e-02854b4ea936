select cpt.id as 'process_task_details_id',
    cpt.process_order_no,
       cs.id as 'sku_id',
    cs.sku_name,
       cs.sku_code,
       cpt.`process_quantity` as 'qty_so',
    cpt.process_quantity_ea as 'qty_so_ea',
    cpt.combinations_package_id,
       cpt.combinations_package_name,
       cpt.combinations_package_unit_id,
       cpt.combinations_package_unit_name,
       (select pu.`name` from cs_package_unit as pu where pu.package_id = cpt.combinations_package_id and pu.code = 'EA') as 'package_unit_name_ea',
    (cs.net_weight * cpt.process_quantity_ea) as 'total_weight',
    (cpt.process_quantity_ea - cpt.qty_allocation_ea) as 'un_alloc_qty',
    'create' as 'soOrderStatus',
    'allocationType' as '2'
from cs_process_task as cpt
inner join cs_sku as cs on cs.id = cpt.combinations_sku_id
where 1 = 1 and cpt.deleted = 0
<< and cs.sku_name like concat('%', :keyword, '%')>>