select cptd.id as 'process_task_details_id',
    cptd.process_order_no,
       cs.id as 'sku_id',
    cs.sku_name,
       cs.sku_code,
       cptd.`details_process_quantity` as 'qty_so',
    cptd.details_process_quantity_ea as 'qty_so_ea',
    cptd.package_id,
       cptd.package_name,
       cptd.package_unit_id,
       cptd.package_unit_name,
       (select pu.`name` from cs_package_unit as pu where pu.package_id = cptd.package_id and pu.code = 'EA') as 'package_unit_name_ea',
    (cs.net_weight * cptd.details_process_quantity_ea) as 'total_weight',
    (cptd.details_process_quantity_ea - cptd.qty_allocation_ea) as 'un_alloc_qty',
    'create' as 'soOrderStatus',
    'allocationType' as '3'
from cs_process_task_details as cptd
         inner join cs_sku as cs on cs.id = cptd.sku_id
where 1 = 1 and cptd.deleted = 0
    << and cs.sku_name like concat('%', :keyword, '%')>>