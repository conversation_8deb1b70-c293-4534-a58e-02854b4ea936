select t.id as recheck_detail_id,
       q1.so_no,
       q1.recheck_no,
       t.so_detail_no,
       a.pa_no,
       t.sku_id,
       q.sku_code,
       q.sku_name,
       ifnull(t.qty_picking_ea, 0)                             as qty_ea,
       ifnull(t.qty_check_ea, 0)                       as qty_check_ea,
       ifnull(t.qty_picking_ea, 0) - ifnull(t.qty_check_ea, 0) as un_qty_check_ea
from cs_so_recheck_detail t
         left join cs_sku q on t.sku_id = q.id
         left join cs_so_recheck_header q1 on q1.recheck_no = t.recheck_no
         left join cs_pa_task_association a on a.recheck_no = t.recheck_no
where t.deleted = 0
    <<
  and q1.recheck_no = :recheckNo >>
    <<
  and t.so_detail_no = :soDetailNo >>
    <<
  and t.id = :recheckDetailId >>
<#if checkStatus == 'un_recheck'>
  and (t.check_status in ('un_recheck', 'partial_recheck')
    or t.check_status is null
      )
</#if>
<#if checkStatus == 'complete_recheck'>
  and t.check_status in ('complete_recheck', 'partial_recheck')
</#if>
    <<
  and q.sku_code = :skuCode >>