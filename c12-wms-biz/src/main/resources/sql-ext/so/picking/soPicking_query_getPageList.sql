select
	a.id
	,a.picking_no
	,a.status
	,a.rotation_rule_id
	,a.rotation_rule_name
	,a.alloc_rule_id
	,a.alloc_rule_name
	,a.so_id
	,a.so_no
	,a.wv_id
	,a.wv_no
	,a.process_order_no
	,a.process_order_type
	,a.remark
	,a.create_time
    ,COUNT(distinct q.picking_task_no) as picking_task_num
    ,(CASE
          WHEN COUNT(CASE WHEN q3.picking_op_id IS NULL THEN 1 END) = COUNT(q.picking_task_no)
              THEN 'unallocated'
          WHEN COUNT(CASE WHEN q3.picking_op_id IS NOT NULL THEN 1 END) = COUNT(q.picking_task_no)
              THEN 'full_allocation'
          ELSE 'partial_allocation'
    END)                                AS assign_pickers_status
    ,group_concat(distinct q3.picking_op_name)  as picking_op_name
    ,group_concat(a1.pa_no) as pa_no
    ,(CASE
        WHEN COUNT(CASE WHEN q.task_status = 'completed' THEN 1 END) = COUNT(q.picking_task_no)
            THEN 'completed'
        WHEN COUNT(CASE WHEN q.task_status = 'not_started' THEN 1 END) = COUNT(q.picking_task_no)
            THEN 'not_started'
        ELSE 'in_progress'
    END) as task_status
    , group_concat(q.tag_no)  as tag_no
from cs_so_picking a
         left join cs_so_allocation q on q.picking_no = a.picking_no
         left join cs_so_allocation_picking_op q3 on q3.allocation_id = q.id and q3.current_picking_op = '1'
                                                         and q3.deleted = 0
         left join cs_wv_detail q1 on q1.so_detail_id = q.so_detail_id and q1.wv_no = a.wv_no
         left join cs_so_header q2 on q2.so_no = a.so_no
         left join cs_pa_task_association a1 on a1.picking_task_no = q.picking_task_no
where a.deleted = 0
    and q.deleted = 0
    and a.status != 'not_generate'
    and q.picking_status != 'cancelled'
	<<and a.picking_no like concat('%', :pickingNo, '%')>>
	<<and a.picking_no in (:pickingNoList)>>
	<<and a.status = :status>>
	<<and (q1.so_id = :soId or q2.id = :soId)>>
	<<and (q1.so_no like concat('%', :soNo, '%') or q2.so_no like concat('%', :soNo, '%'))>>
	<<and q1.wv_id = :wvId>>
	<<and q1.wv_no like concat('%', :wvNo, '%')>>
	<<and q.picking_task_no like concat('%', :pickingTaskNo, '%')>>
	<<and q3.picking_op_name like concat('%', :pickingOpName, '%')>>
	<<and a1.pa_no like concat('%', :paNo, '%')>>
	<<and exists(select 1 from cs_so_allocation t where t.picking_no = a.picking_no and t.tag_no like concat('%', :tagNo, '%'))>>
group by a.picking_no, a.create_time
<#if assignPickersStatus == 'unallocated'>
having COUNT(CASE WHEN q3.picking_op_id IS NULL THEN 1 END) = COUNT(q.picking_task_no)
</#if>
<#if is_not_empty(assignPickersStatus) && assignPickersStatus == 'full_allocation'>
having COUNT(CASE WHEN q3.picking_op_id IS NOT NULL THEN 1 END) = COUNT(q.picking_task_no)
</#if>
<#if is_not_empty(assignPickersStatus) && assignPickersStatus == 'partial_allocation'>
having 0 < COUNT(CASE WHEN q3.picking_op_id IS NOT NULL THEN 1 END) and COUNT(CASE WHEN q3.picking_op_id IS NOT NULL THEN 1 END) < COUNT(q.picking_task_no)
</#if>
	order by a.create_time desc