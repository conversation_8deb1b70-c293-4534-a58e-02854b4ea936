select t.*
from cs_so_header t
         left join cs_so_detail csd on  csd.so_no = t.so_no
         left join cs_sku ck on ck.id = csd.sku_id
         left join cs_pa_task_association pat on csd.so_detail_no = pat.so_detail_no
         left join cs_pa_task pt on pt.pa_no = pat.pa_no
where 1 = 1
  << and t.so_no like concat('%', :soNo, '%') >>
  << and t.so_type = :soType >>
  << and ck.sku_name = :skuName >>
  << and ck.sku_code = :skuCode >>
  << and t.logistic_no like concat('%', :logisticNo, '%') >>
  << and t.owner_id = :ownerId >>
  << and t.carrier_id = :carrierId >>
  << and t.warehouse_id = :warehouseId >>
  << and t.logistic_no = :lohisticNo >>
  << and t.order_source = :orderSource >>
  << and t.create_time between :createStartTime and :createEndTime>>
  << and t.alloc_status = :allocStatus >>
  << and t.client_id = :clientId >>
  << and t.priority = :priority >>
  << and t.status = :status>>
  << and pat.pa_no like concat('%', :paNo, '%') >>
  <#if is_not_empty(isCreatePaTask) && isCreatePaTask == '1'>
    and pat.pa_no is not null
  </#if>
  <#if is_not_empty(isCreatePaTask) && isCreatePaTask == '0'>
    and pat.pa_no is null
  </#if>
  << and pt.status = :paStatus >>
group by t.so_no,t.create_time
order by t.create_time desc
