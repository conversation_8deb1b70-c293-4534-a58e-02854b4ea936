/*二次分拣列表查询*/
select q.wv_no,
       q.wv_id,
       q.picking_no,
       group_concat(distinct a.pa_no) AS pa_no,
       q2.sorting_task_status as task_status,
       sum(ifnull(q2.picking_ea, 0))       as picking_ea,
       sum(ifnull(q2.sorting_ea, 0))       as sorting_ea,
       sum(ifnull(q2.picking_ea, 0))
           - sum(ifnull(q2.sorting_ea, 0)) as un_sorting_ea,
       group_concat(distinct q2.sorting_op_name) as sorting_op_name,
       min(q2.sorting_time) as sorting_time,
       (CASE
            WHEN COUNT(CASE
                           WHEN q2.secondary_sorting_status IS NULL or q2.secondary_sorting_status = 'un_sorting'
                               THEN 1 END) = COUNT(q2.picking_task_no)
                THEN 'un_sorting'
            WHEN COUNT(CASE WHEN q2.secondary_sorting_status = 'complete_sorting' THEN 1 END) =
                 COUNT(q2.picking_task_no)
                THEN 'complete_sorting'
            ELSE 'partial_sorting'
           END)             AS secondary_sorting_status,
       group_concat(distinct q2.so_detail_id) as so_detail_id_list
from cs_so_picking q
         left join cs_so_allocation q2 on q2.picking_no = q.picking_no
         left join cs_pa_task_association a on a.picking_task_no = q2.picking_task_no
where q.deleted = 0
  and q2.deleted = 0
  and q.wv_no is not null
  and q.status = 'complete_picking'
  and q2.picking_status != 'cancelled'
<<  and q.wv_no like concat('%', :wvNo, '%')>>
<<  and q.picking_no like concat('%', :pickingNo, '%')>>
<<  and q.picking_no in (:pickingNos)>>
<<  and q2.sorting_op_name like concat('%', :sortingOpName, '%')>>
<<  and date(q2.sorting_time) >= date(:startTime)>>
<<  and date(q2.sorting_time) <= date(:endTime)>>
<<  and q2.sorting_op_id = :sortingOpId>>
<<  and q2.secondary_sorting_status = :secondarySortingStatus>>
<<  and a.pa_no like concat('%', :paNo, '%')>>
group by q.wv_no, q.picking_no, q.create_time
order by q.create_time desc




