-- 通过出库单Id获取出库明细
select t.id,
       t.so_no,
       t.so_detail_no,
       t.sku_id,
       cs.sku_code,
       cs.sku_name,
       cs.lot_id,
       t.package_id,
       cp.name as package_name,
       t.package_unit_id,
       t.package_unit_name,
       t.sum_weight,
       t.sum_gross_weight,
       t.qty_so,
       t.qty_so_ea,
       t.qty_allocation_ea,
       t.qty_picked_ea,
       t.qty_shipped_ea,
       t.pallet_num,
       t.lot_att01,
       t.lot_att02,
       t.lot_att03,
       t.lot_att04,
       t.lot_att05,
       t.lot_att06,
       t.lot_att07,
       t.lot_att08,
       t.lot_att09,
       t.lot_att10,
       t.lot_att11,
       t.lot_att12,
       t.order_sku_id,
       t.status,
       (select name from cs_package_unit where package_id = t.package_id and code = 'EA') as package_unit_ea_name,
       t.qty_check_ea
    from cs_so_detail t
         left join cs_sku cs on cs.id = t.sku_id
         left join cs_package cp ON cp.id = t.package_id
where t.so_no in (:soNos)