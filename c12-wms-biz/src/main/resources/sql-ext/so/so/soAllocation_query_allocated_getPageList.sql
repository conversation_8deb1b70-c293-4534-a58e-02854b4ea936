select t.id
     , t.picking_task_no
     , t.picking_no
     , t.picking_status
     , t.pallet_id
     , q1.picking_op_id
     , q1.picking_op_name
     , t.loc_id
     , l.loc_code
     , t.lot_num
     , t.to_loc_id
     , c.loc_code as to_loc_code
     , t.sku_id
     , s.sku_name
     , s.sku_code
     , s.package_id
     , t.qty_ea
     , t.package_unit_name
     , sum(ifnull(t.picking_ea, 0)) as picking_ea
     , (t.qty_ea - ifnull(t.picking_ea, 0)) as un_qty_ea
     , p.wv_no
     , a.pa_no
     , t.task_status
     , t.process_order_no
     , t.process_task_no
     , t.process_task_details_id
     , t.process_order_type
  from cs_so_allocation t
  left join cs_so_picking p on p.picking_no = t.picking_no
  left join cs_so_allocation_picking_op q1 on q1.allocation_id = t.id and q1.deleted = 0 and q1.current_picking_op = '1'
  left join cs_sku s on s.id = t.sku_id
  left join cs_warehouse_loc l on l.id = t.loc_id
  left join cs_warehouse_loc c on c.id = t.to_loc_id
  left join cs_pa_task_association a on a.picking_task_no = t.picking_task_no
 where t.deleted = 0
   and p.deleted = 0
   and t.picking_status != 'cancelled'
<< and t.id = :id >>
<< and t.picking_no = :pickingNo >>
<< and t.picking_no in (:pickingNos) >>
<< and p.so_id = :soId >>
<< and p.so_no = :soNo >>
<< and p.wv_id = :wvId >>
<< and p.wv_no = :wvNo >>
<< and t.owner_id = :ownerId >>
<< and t.sku_id  = :skuId >>
<< and p.status = :status >>
<< and t.warehouse_id = :warehouseId >>
<< and t.create_time >= :beginTime >>
<< and t.create_time <= :endTime >>
<< and p.status in (:statuses) >>
<< and t.picking_status = :pickingStatus >>
<#if assignPickers == 'unallocated'>
   and q1.picking_op_id is null
</#if>
<#if is_not_empty(assignPickers) && assignPickers == 'allocated'>
   and q1.picking_op_id is not null
</#if>
group by q1.id
order by t.create_time desc