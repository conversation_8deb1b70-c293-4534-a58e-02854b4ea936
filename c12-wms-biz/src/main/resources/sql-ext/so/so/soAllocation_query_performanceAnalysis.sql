select q.picking_op_id                   as user_id,
       q.picking_op_name                 as real_name,
       e.warehouse_name,
       min(w.picking_op_allocation_time) as task_start_time,
       max(q.picking_time)               as task_end_time,
       count(distinct q.id)              as picking_task_count,
       count(distinct q.sku_id)          as picking_sku_count
from cs_so_allocation q
         left join cs_so_allocation_picking_op w on w.allocation_id = q.id
         left join cs_warehouse e on e.id = q.warehouse_id
where q.deleted = 0
  and w.deleted = 0
  and q.picking_status <> 'cancelled'
  and q.picking_time is not null
  <<and q.warehouse_id = :warehouseId>>
  <<and q.picking_op_name LIKE concat('%', :operator, '%')>>
  <<and w.picking_op_allocation_time >= :startTime>>
  <<and w.picking_op_allocation_time <= :endTime>>
  <<and q.picking_time >= :startTime>>
  <<and q.picking_time <= :endTime>>
group by q.picking_no, q.picking_op_id