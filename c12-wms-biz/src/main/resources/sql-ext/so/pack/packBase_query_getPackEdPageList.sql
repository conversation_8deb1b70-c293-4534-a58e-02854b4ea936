select cph.id,
       cph.pack_no,
       cph.customer_id,
       cc.customer_name,
       cph.carrier_name,
       cph.create_time,
       cph.packager_id,
       cph.packager_name,
       cph.pack_status,
       concat(ifnull(cc.province_name,''),ifnull(cc.city_name,''),ifnull(cc.county_name,''), ifnull(cc.address,'')) as address,
       cph.shipping_status,
       COUNT(DISTINCT cpbd.so_no) as so_num,
       JSON_ARRAYAGG(
            JSON_OBJECT(
            'soId', cso.id,
            'soNo', cpbd.so_no
        )
       )                              AS so_no_list,
       (SELECT GROUP_CONCAT(DISTINCT pa_no SEPARATOR ',')
        FROM cs_pa_task_association
        WHERE pack_no = cph.pack_no
          <<AND pa_no LIKE CONCAT('%', :paNo, '%')>>) AS pa_no
from cs_pack_header cph
         left join cs_customer cc on cc.id = cph.customer_id
         left join cs_pack_detail cpd on cpd.pack_no = cph.pack_no
         left join cs_pack_box_detail cpbd on cpbd.box_no = cpd.box_no
         left JOIN cs_so_header cso ON cso.so_no = cpbd.so_no
where cph.deleted = 0
    <<and cph.customer_id = :customerId>>
    <<and cph.carrier_name = :carrierName>>
    <<and cph.carrier_id = :carrierId>>
    <<and cph.pack_no = :packNo>>
    <<and cph.pack_status = :packStatus>>
    <<and cph.packager_id = :packagerId>>
    <<and cpbd.so_no like concat('%', :soNo, '%') >>
    <<AND EXISTS (
    SELECT 1
    FROM cs_pa_task_association a
    WHERE a.pack_no = cph.pack_no
      AND a.pa_no LIKE CONCAT('%', :paNo, '%')
) >>
group by cph.pack_no
order by cph.create_time desc
