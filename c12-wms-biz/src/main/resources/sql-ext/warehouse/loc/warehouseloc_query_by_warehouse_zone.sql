select t.*,
       cill.sku_id,
       cill.lot_num as lot_code,
       COALESCE(pallet.unique_count, 0) AS pallet_num,
       COALESCE(lot.unique_count, 0) AS lot_num,
       COALESCE(sku.unique_count, 0) AS sku_num,
       CASE WHEN EXISTS (
           SELECT 1
           FROM cs_inv_lot_loc cill
           WHERE cill.loc_id = t.id
             AND cill.qty > 0
       ) THEN 0 ELSE 1 END AS is_empty_loc
from cs_warehouse_loc t
         LEFT JOIN cs_inv_lot_loc cill ON cill.loc_id = t.id
         LEFT JOIN (SELECT loc_id, COUNT(DISTINCT pallet_num) AS unique_count
                    FROM cs_inv_lot_loc where pallet_num != '*'
                    GROUP BY loc_id) pallet ON t.id = pallet.loc_id
         LEFT JOIN (SELECT loc_id, COUNT(DISTINCT lot_num) AS unique_count
                    FROM cs_inv_lot_loc
                    GROUP BY loc_id) lot ON t.id = lot.loc_id
         LEFT JOIN (SELECT loc_id, COUNT(DISTINCT sku_id) AS unique_count
                    FROM cs_inv_lot_loc
                    GROUP BY loc_id) sku ON t.id = sku.loc_id
where 1 = 1
  and t.deleted = 0
  and is_enable = '1'
<< and t.loc_code not in (:hideLocList) >>
<< and t.zone_id = :zoneId >>
<< and t.id = :id >>
<< and t.warehouse_id in (:warehouseIds) >>
<< and (t.loc_use_type in (:locUseTypeList) and t.loc_use_type is not null) >>
<< and (t.loc_use_type not in (:hideLocUseTypeList) or t.loc_use_type is null) >>
<< and (t.category in (:categoryList) and t.category is not null) >>
<< and (t.category not in (:hideCategoryList) or t.category is null) >>
<< and (t.abc in (:abcList) and t.abc is not null) >>
<< and (t.abc not in (:hideAbcList) or t.abc is null) >>
<< and t.abc = :abc >>
<< and cill.sku_id = :skuId >>
<< and cill.lot_num = :lotCode >>
<< and cill.owner_id = :ownerId >>
