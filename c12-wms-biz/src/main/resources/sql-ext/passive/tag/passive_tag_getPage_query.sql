/*分页查询标签管理*/
select *
from cs_passive_tag t
where 1 = 1 and t.deleted = 0
  << and t.tag_no like concat('%', :tagNo, '%') >>
  << and t.tag_type = :tagType >>
  << and t.activate_status = :activateStatus >>
  << and t.status = :status >>
  << and t.bind_subject = :bindSubject >>
  << and t.warehouse_id = :warehouseId >>
  << and t.sku_id = :skuId >>
  << and t.whether_serial_controller = :whetherSerialController >>
  << and t.box_code like concat('%', :boxCode, '%') >>
  << and t.sn_no like concat('%', :snNo, '%') >>
  << and t.related_tags_no like concat('%', :relatedTagsNo, '%') >>
  << and t.asn_no like concat('%', :asnNo, '%') >>
  << and true = :unpacking
     and t.bind_subject = 1
     and t.status = 3
     and ( t.activate_status IS NULL OR t.activate_status = '1' )
     and t.id != :id >> -- 拆包过滤使用
order by t.create_time desc