SELECT
    -- 应盘数量（账面库存总EA数）
    SUM(quantity_inventory) AS quantity_inventory,
    -- 实盘数量（已盘记录的实盘EA数）
    SUM(IF(status = 1, passive_inventory, 0)) AS actual_inventory,
    -- 差异数量（应盘 - 实盘，取绝对值）
    ABS(SUM(quantity_inventory) - SUM(IF(status = 1, passive_inventory, 0))) AS not_inventory,
    -- 盘存率（保留两位小数）
    ROUND(AVG(COALESCE(inventory_stock, 0)) * 100, 2) AS inventory_rate,
    -- 盘点进度百分比（已盘标签数/总标签数）
    ROUND(
                (COUNT(CASE WHEN status = 1 THEN 1 END) / NULLIF(COUNT(*), 0)) * 100,
                2
        ) AS progress_percentage
FROM cs_passive_auto_inventory
WHERE deleted = 0
  << AND event_no = :eventNo >>
  << AND warehouse_id = :warehouseId >>
  << AND warehouse_zone_id = :warehouseZoneId >>
  << AND bind_subject = :bindSubject >>
