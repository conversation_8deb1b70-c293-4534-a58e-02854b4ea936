/*统计*/
select (sum(if(status = 1, 1, 0)) / count(1)) as inventory_stock,
       count(1)                               as quantity_inventory,
       sum(if(status = 1, 1, 0))              as actual_inventory,
       sum(if(status = 0, 1, 0))              as not_inventory
from cs_passive_auto_inventory t
where t.deleted = 0
<<  and t.event_no = :eventNo>>
<<  and t.warehouse_id = :warehouseId>>
<<  and t.warehouse_zone_id = :warehouseZoneId>>
<<  and t.bind_subject = :bindSubject>>
