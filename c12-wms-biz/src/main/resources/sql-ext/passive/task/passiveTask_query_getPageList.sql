select
	t.id
	,t.location_name
	,t.task_no
	,t.status
	,t.type
	,t.begin_time
	,t.end_time
    ,t.warehouse_zone_id
    ,t.task_id
	,t.tenancy
	,t.creator
	,t.create_time
	,t.modifier
	,t.modify_time
	,t.rec_ver
	,t.company_id
from cs_passive_task t
where 1 = 1
	<<and t.location_name like concat('%', :locationName ,'%')>>
	<<and t.task_no like concat('%', :taskNo ,'%') >>
	<<and t.status = :status>>
	<<and t.type = :type>>
	<<and t.begin_time >= :beginStartTime>>
	<<and t.begin_time <= :beginEndTime>>
	<<and t.end_time >= :endStartTime>>
	<<and t.end_time <= :endEndTime>>
	<<and t.rec_ver = :recVer>>
	order by t.id desc
