SELECT t.id,
       t.device_id,
       t.parent_id,
       t.parent_device_name,
       t.device_type,
       t.port_count,
       t.device_sn,
       t.device_name,
       t.warehouse_id,
       t.warehouse_name,
       t.warehouse_zone_id,
       t.warehouse_zone_name,
       t.location,
       t.port_number,
       t.serial_number,
       t.warehousing_date,
       t.device_business_type
FROM cs_passive_device t
WHERE 1 = 1
    << AND t.device_id LIKE CONCAT('%', :deviceId, '%') >>
    << AND t.device_sn LIKE CONCAT('%', :deviceSn, '%') >>
    << AND t.device_name LIKE CONCAT('%', :deviceName, '%') >>
    << AND t.device_type = :deviceType >>
    << AND t.device_business_type = :deviceBusinessType >>
ORDER BY serial_number