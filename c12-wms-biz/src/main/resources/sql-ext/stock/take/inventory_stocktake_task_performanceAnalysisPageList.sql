select
    temp.id,
    temp.personnel_id,
    temp.personnel_name as real_name,
    temp.task_start_time,
    GROUP_CONCAT(DISTINCT ciso.warehouse_name  SEPARATOR ',') AS warehouse_name,
    count(temp.id) as task_count,
    ifnull(sum(temp.actual_count),0) as sku_count
from (select ifnull(sum(cistd.actual_count),0) as actual_count,cist.*
      from cs_inventory_stocktake_task cist
               left join cs_inventory_stocktake_task_details cistd on cist.task_no = cistd.task_no and cistd.deleted = 0
      where cist.deleted = 0
      and cist.task_start_time is not null
      <<and cist.warehouse_id = :warehouseId>>
      <<and cist.cist.personnel_name like concat('%',:operator,'%')>>
      <<and cist.cist.task_start_time >= :startTime>>
      <<and cist.cist.task_end_time <= :endTime>>
      group by cist.id ) as temp
         left join cs_inventory_stocktake_order ciso on temp.stocktake_no = ciso.stocktake_no and ciso.deleted = 0
group by temp.personnel_id,temp.task_start_time
