select
  id ,
  losses_no ,
  warehouse_id ,
  warehouse_name ,
  owner_id ,
  owner_name ,
  losses_type ,
  losses_status ,
  losses_time ,
  losses_reason ,
  creator_name ,
  create_time
from cs_inv_reporting_losses where deleted = 0
<<and losses_no like concat('%',:losses_no,'%')>>
<<and warehouse_id = :warehouseId>>
<<and owner_id = :ownerId>>
<<and losses_status = :lossesStatus>>
<<and losses_time >= :startTime>>
<<and losses_time <= :endTime>>
order by create_time desc
