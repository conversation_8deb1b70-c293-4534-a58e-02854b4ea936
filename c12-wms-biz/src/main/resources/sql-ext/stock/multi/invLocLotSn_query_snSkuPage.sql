select
       cills.id,
       cills.warehouse_id,
       cills.warehouse_name,
       cills.owner_id,
       cills.owner_name,
       cills.sku_id,
       cills.sku_code,
       cills.sku_name,
       sum(cills.qty) as qty
from cs_inv_lot_loc_sn cills
where cills.deleted = 0 and cills.qty > 0 and cills.is_losses = 0 and loc_code not in('STAGE','SORTATION')
<<and cills.sku_id = :skuId>>
<<and cills.sku_code like concat('%',:skuCode,'%')>>
<<and cills.sku_name like concat('%',:skuName,'%')>>
<<and cills.owner_id = :ownerId>>
<<and cills.owner_name like concat('%',:ownerName,'%')>>
<<and cills.warehouse_id = :warehouseId>>
<<and cills.warehouse_name like concat('%',:warehouseName,'%')>>
group by cills.warehouse_id, cills.owner_id, cills.sku_id
order by cills.sku_code