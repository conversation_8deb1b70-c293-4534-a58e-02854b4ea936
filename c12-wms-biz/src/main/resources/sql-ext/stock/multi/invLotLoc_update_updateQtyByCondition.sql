update cs_inv_lot_loc
set rec_ver = ifnull(rec_ver,0)+1
  ,modify_time = now()
<<,qty = ifnull(qty,0) + :updateQty>>
<<,qty_hold = ifnull(qty_hold,0) + :updateQtyHold>>
<<,qty_alloc = ifnull(qty_alloc,0) + :updateQtyAlloc>>
<<,qty_pk = ifnull(qty_pk,0) + :updateQtyPk>>
<<,qty_qc = ifnull(qty_qc,0) + :updateQtyQc>>
<<,qty_wait_move = ifnull(qty_wait_move,0) + :updateQtyWaitMove>>
<<,qty_wait_damage = ifnull(qty_wait_damage,0) + :qtyWaitDamage>>
<<,qty_pa_in = ifnull(qty_pa_in,0) + :updateQtyPaIn>>
<<,qty_pa_out = ifnull(qty_pa_out,0) + :updateQtyPaOut>>
<<,qty_ad_in = ifnull(qty_ad_in,0) + :updateQtyAdIn>>
<<,qty_ad_out = ifnull(qty_ad_out,0) + :updateQtyAdOut>>
<<,qty_mv_in = ifnull(qty_mv_in,0) + :updateQtyMvIn>>
<<,qty_mv_out = ifnull(qty_mv_out,0) + :updateQtyMvOut>>
<<,qty_tf_in = ifnull(qty_tf_in,0) + :updateQtyTfIn>>
<<,qty_tf_out = ifnull(qty_tf_out,0) + :updateQtyTfOut>>
<<,qty_rp_in = ifnull(qty_rp_in,0) + :updateQtyRpIn>>
<<,qty_rp_out = ifnull(qty_rp_out,0) + :updateQtyRpOut>>
where 1=1
<<and id = :id>>
<<and ifnull(rec_ver,0) = :recVer>>
<<and pallet_num = :palletNum>>
<<and owner_id = :ownerId>>
<<and sku_id = :skuId>>
<<and loc_id = :locId>>
<<and lot_num = :lotNum>>
<<and warehouse_id = :warehouseId>>