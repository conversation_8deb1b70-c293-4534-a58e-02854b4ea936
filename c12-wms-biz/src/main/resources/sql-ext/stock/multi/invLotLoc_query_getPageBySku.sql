/*根据商品查询库存*/
SELECT
    t.owner_id,
    o.owner_name,
    sum(ifnull(t.qty, 0))        as qty,
    sum(ifnull(t.qty_hold, 0))   as qty_hold,
    sum(ifnull(t.qty_alloc, 0))  as qty_alloc,
    sum(ifnull(t.qty_pk, 0))     as qty_pk,
    sum(ifnull(t.qty_qc, 0))     as qty_qc,
    sum(ifnull(t.qty_wait_move, 0)) as qty_wait_move,
    sum(ifnull(t.qty_wait_damage, 0)) as qty_wait_damage,
    o.owner_code,
    s.sku_code,
    s.sku_name,
    s.id as sku_id,
    t.warehouse_name

FROM
    cs_inv_lot_loc t
    left join cs_owner o on t.owner_id = o.id
    left join cs_sku s on t.sku_id = s.id

WHERE
    t.qty > 0
    << and t.owner_id = :ownerId>>
    << and t.warehouse_id = :warehouseId>>
    << and t.sku_id = :skuId>>
    group by t.owner_id,t.sku_id,t.warehouse_id
    order by t.owner_id desc


