SELECT
    t.owner_id,
    o.owner_name,
    sum(ifnull(t.qty, 0))        as qty,
    sum(ifnull(t.qty_hold, 0))   as qty_hold,
    sum(ifnull(t.qty_alloc, 0))  as qty_alloc,
    sum(ifnull(t.qty_pk, 0))     as qty_pk,
    sum(ifnull(t.qty_qc, 0))     as qty_qc,
    sum(ifnull(t.qty_wait_move, 0)) as qty_wait_move,
    sum(ifnull(t.qty_wait_damage, 0)) as qty_wait_damage,
    o.owner_code,
    s.sku_code,
    s.sku_name,
    s.id as sku_id,
    s.lot_id,
    t.lot_num,
    t.loc_code,
    t.warehouse_name,
    t.pallet_num,
    t8.lot_att01,
    t8.lot_att02,
    t8.lot_att03,
    t8.lot_att04,
    t8.lot_att05,
    t8.lot_att06,
    t8.lot_att07,
    t8.lot_att08,
    t8.lot_att09,
    t8.lot_att10,
    t8.lot_att11,
    t8.lot_att12,
    t7.supplier_name,
    t7.id as 'supplier_id'
FROM
    cs_inv_lot_loc t
        left join cs_owner o on t.owner_id = o.id
        left join cs_sku s on t.sku_id = s.id
        left join cs_warehouse_loc l on t.loc_id = l.id
        left join cs_inv_lot_att as t8 on t8.lot_num = t.lot_num
        left join cs_supplier as t7 on t7.id = t8.lot_att04

WHERE
    t.qty > 0
    and t.deleted = 0
    and t.pallet_num is not null and t.pallet_num != '' and t.pallet_num != '*'
    << and t.owner_id = :ownerId>>
    << and t.warehouse_id = :warehouseId>>
    << and t.sku_id = :skuId>>
    << and t.lot_num LIKE concat('%',:lotNum,'%')>>
    << and t.pallet_num = :palletNum>>
group by t.warehouse_id,t.sku_id,t.loc_code,t.lot_num,t.pallet_num
ORDER BY t.owner_id,t.sku_id,t.lot_num,t.loc_code,t.pallet_num

