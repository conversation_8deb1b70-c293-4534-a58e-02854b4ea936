SELECT
    t.owner_id,
    sum(ifnull(t.qty, 0))        as qty,
    sum(ifnull(t.qty_hold, 0))   as qty_hold,
    sum(ifnull(t.qty_alloc, 0))  as qty_alloc,
    sum(ifnull(t.qty_pk, 0))     as qty_pk,
    sum(ifnull(t.qty_qc, 0))     as qty_qc,
    sum(ifnull(t.qty_wait_move, 0)) as qty_wait_move,
    sum(ifnull(t.qty_wait_damage, 0)) as qty_wait_damage,
    t.loc_id,
    t.loc_code,
    l.loc_name,
    t.warehouse_name

FROM
    cs_inv_lot_loc t

        left join cs_warehouse_loc l on t.loc_id = l.id

WHERE
    t.qty>0
    << and t.loc_id = :locId>>
    << and t.warehouse_id = :warehouseId>>
group by t.warehouse_id,t.loc_code
ORDER BY t.warehouse_id




