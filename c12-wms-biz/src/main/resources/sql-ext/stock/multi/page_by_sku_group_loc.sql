select loc.warehouse_name,
       ow.owner_code,
       ow.owner_name,
       sku.sku_code,
       sku.sku_name,
       sku.sku_group_name,
       sku.sku_group_code,
       sum(loc.qty)                                                          as qty,
       sum(loc.qty - loc.qty_hold - loc.qty_alloc - loc.qty_pk - loc.qty_qc - loc.qty_wait_move - loc.qty_wait_damage) as qty_ky,
       sum(loc.qty_hold)                                                     as qty_hold,
       sum(loc.qty_alloc)                                                    as qty_alloc,
       sum(loc.qty_pk)                                                       as qty_pk,
       sum(loc.qty_qc)                                                       as qty_qc,
       sum(loc.qty_wait_move)                                                as qty_wait_move,
       sum(loc.qty_wait_damage)                                              as qty_wait_damage
from cs_inv_lot_loc as loc
left join cs_owner as ow on loc.owner_id = ow.id
left join cs_sku as sku on loc.sku_id = sku.id
where sku.sku_group_name is not null and sku.sku_group_name != '' and loc.deleted = 0
<< and loc.warehouse_id = :warehouseId >>
<< and loc.owner_id = :ownerId >>
<< and loc.sku_id = :skuId >>
<< and sku.sku_group_code = :skuGroupCode >>
group by loc.warehouse_id, loc.owner_id, loc.sku_id, sku.sku_group_code
order by sku.sku_group_code, loc.create_time desc