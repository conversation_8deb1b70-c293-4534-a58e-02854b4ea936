select pta.pa_no,
       if(sap.to_pallet_num is null,'*',sap.to_pallet_num) as `pallet_num`,
       pta.picking_task_no,
       if(sum(sap.picking_ea) is null,'0',sum(sap.picking_ea))as ea
from cs_pa_task_association as pta
         left join cs_so_allocation_pallet as sap on sap.picking_task_no = pta.picking_task_no
where 1=1 and pta.deleted = 0
<< and pta.pa_no in (:paNoList) >>
group by pta.pa_no, pallet_num, pta.picking_task_no;