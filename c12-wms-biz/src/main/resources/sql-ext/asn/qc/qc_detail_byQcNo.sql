-- 根据qcId和lineNo获取质检单明细信息
select
    t.id,
    t.header_id,
    t.owner_id,
    t.owner_name,
    t.sku_id,
    t.sku_name,
    ck.sku_code,
    t.warehouse_id,
    t.warehouse_name,
    t.pallet_no,
    t.planned_quality_inspection,
    t.actual_quality_inspection,
    t.sn_str,
    t.qc_sn,
    t.un_qc_sn,
    t.return_warehouse_num,
    t.qualified_num,
    t.unqualified_num,
    t.inspection_expected_time_start,
    t.inspection_expected_time_end,
    t.loc_id,
    t.loc_name,
    t.qty_available_number,
    t.shelf_name,
    t.lot_att,
    t.zone_id,
    t.zone_name,
    t.lot_num,
    t.remark
from cs_qc_detail t
         left join cs_qc_header qch on qch.id = t.header_id
         left join cs_sku ck on ck.id = t.sku_id
where 1 = 1
  << and qch.quality_inspection_order_no = :qcNo >>
    << and t.header_id  in (:qcIdList) >>
order by t.modify_time desc