select
    t.id,
    t.asn_no,
    t.sku_id,
    sku.sku_name,
    sku.sku_code,
    sku.lot_id,
    sku.whether_serial_controller,
    t.package_id,
    cp.name as package_name,
    t.package_unit_id,
    cpu.name as package_unit_name,
    t.lot_att01,
    t.lot_att02,
    t.lot_att03,
    t.lot_att04,
    t.lot_att05,
    t.lot_att06,
    t.lot_att07,
    t.lot_att08,
    t.lot_att09,
    t.lot_att10,
    t.lot_att11,
    t.lot_att12
FROM cs_asn_detail t
LEFT join cs_sku sku on sku.id = t.sku_id
LEFT join cs_package cp on cp.id = t.package_id
LEFT join cs_package_unit cpu on cpu.id = t.package_unit_id
where t.deleted = 0
    << and t.asn_no = :asnNo >>
    << and sku.sku_name like concat('%', :skuName, '%') >>
    << and sku.sku_code like concat('%', :skuCode, '%') >>