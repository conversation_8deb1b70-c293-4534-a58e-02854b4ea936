select ciws.id,
       ciw.warehouse_name,
       ciw.owner_name,
       ciw.sku_name,
       ciw.sku_code,
       ciws.min_inv_ea,
       ciws.qty,
       ciws.warning_code,
       ciws.warning_time
from cs_inv_warning_message ciws
         right join cs_inv_warning ciw on ciw.id = ciws.inv_warning_id
where 1 = 1
  <<and ciw.owner_id = :ownerId>>
  <<and ciw.sku_id = :skuId>>
  <<and ciw.warehouse_id = :warehouseId>>
  <<and ciws.warning_code = :warningCode>>
  <<and ciws.warning_time between :warningTimeFrom and :warningTimeTo>>
order by ciws.create_time desc