SELECT
    @pageTag(){
    t.*,
    (ROW_NUMBER() OVER (ORDER BY t.assessor_fraction desc)) AS row_num
    @}
FROM
    cs_kpi_header t
where t.deleted = 0
<< and t.user_name like concat('%', :userName, '%') >>
<< and t.position_id = :positionId >>
<< and t.assessor_id = :assessorId >>
<< and t.assessor like concat('%', :assessor, '%') >>
<< and t.assessor_time >= :examineStartTime >>
<< and t.assessor_time <= :examineEndTime >>
order by t.assessor_fraction desc