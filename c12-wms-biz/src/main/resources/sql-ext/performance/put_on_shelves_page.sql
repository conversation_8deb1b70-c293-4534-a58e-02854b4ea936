select pa_person_name as 'user_name',
        pa_person_id as 'user_id',
        warehouse_id as 'warehouse_id',
        warehouse_name as 'warehouse_name',
        CONCAT(DATE_FORMAT(MIN(create_time), '%Y-%m-%d %H:%i:%s'), ' ~ ', DATE_FORMAT(MAX(pa_time), '%Y-%m-%d %H:%i:%s')) AS 'operation_time',
        count(id) as 'item_count',
        TIMESTAMPDIFF(MINUTE, MIN(create_time), MAX(pa_time)) AS 'work_duration',
        TIMESTAMPDIFF(MINUTE, MIN(create_time), MAX(pa_time)) / count(id) as 'aht',
        GROUP_CONCAT(DISTINCT pa_no SEPARATOR ',') AS 'pa_no_join'
from cs_pa_task_detail
where deleted = 0
<< warehouse_id = :warehouseId>>
<< pa_person_name like concat('%',:userName,'%')>>
<< create_time >= :startDate>>
<< create_time <= :endDate>>
group by warehouse_id,pa_person_id;