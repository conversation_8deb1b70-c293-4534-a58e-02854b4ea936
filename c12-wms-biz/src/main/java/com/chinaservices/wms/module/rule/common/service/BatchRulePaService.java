package com.chinaservices.wms.module.rule.common.service;

import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.module.asn.receive.model.AsnReceive;
import com.chinaservices.wms.module.rule.common.RulePaCommonService;
import com.chinaservices.wms.module.rule.common.domain.BatchRulePaResult;
import com.chinaservices.wms.module.rule.common.domain.RulePaProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 批量上架规则处理服务
 * 
 * 这个服务类展示了如何使用批量上架规则处理功能，
 * 包括事务管理、异常处理和性能优化的最佳实践。
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class BatchRulePaService {
    
    @Autowired
    private RulePaCommonService rulePaCommonService;
    
    /**
     * 批量处理收货明细的上架规则
     * 
     * 特点：
     * 1. 支持大批量数据处理
     * 2. 自动分批处理，避免单个事务过大
     * 3. 并行处理提升性能
     * 4. 详细的异常处理和错误收集
     * 5. 完整的处理结果统计
     * 
     * @param asnReceiveList 收货明细列表
     * @return 批量处理结果
     */
    public BatchRulePaResult processBatchRulePa(List<AsnReceive> asnReceiveList) {
        log.info("开始批量处理收货明细上架规则，数据量: {}", 
                EmptyUtil.isEmpty(asnReceiveList) ? 0 : asnReceiveList.size());
        
        // 参数验证
        if (EmptyUtil.isEmpty(asnReceiveList)) {
            throw new ServiceException("收货明细列表不能为空");
        }
        
        try {
            // 调用批量处理方法
            BatchRulePaResult result = rulePaCommonService.batchCheckRulePaTaskAndGetLoc(asnReceiveList);
            
            // 记录处理结果
            logProcessingResult(result);
            
            // 如果有失败的记录，记录详细信息
            if (!result.isAllSuccess()) {
                logFailureDetails(result);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("批量处理收货明细上架规则失败", e);
            throw new ServiceException("批量处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量处理并更新收货明细的库位信息
     * 
     * 这个方法不仅处理上架规则，还会将结果更新到收货明细中
     * 
     * @param asnReceiveList 收货明细列表
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public BatchRulePaResult processBatchRulePaAndUpdate(List<AsnReceive> asnReceiveList) {
        log.info("开始批量处理并更新收货明细上架规则，数据量: {}", 
                EmptyUtil.isEmpty(asnReceiveList) ? 0 : asnReceiveList.size());
        
        // 1. 批量处理上架规则
        BatchRulePaResult result = processBatchRulePa(asnReceiveList);
        
        // 2. 更新成功处理的记录
        updateAsnReceiveWithResults(asnReceiveList, result.getSuccessResults());
        
        log.info("批量处理并更新完成，成功更新 {} 条记录", result.getSuccessCount());
        
        return result;
    }
    
    /**
     * 记录处理结果
     */
    private void logProcessingResult(BatchRulePaResult result) {
        log.info("批量处理完成统计: 总数={}, 成功={}, 失败={}, 耗时={}ms, 成功率={:.2f}%",
                result.getTotalCount(),
                result.getSuccessCount(),
                result.getFailureCount(),
                result.getProcessingTime(),
                result.getTotalCount() > 0 ? (double) result.getSuccessCount() / result.getTotalCount() * 100 : 0);
    }
    
    /**
     * 记录失败详情
     */
    private void logFailureDetails(BatchRulePaResult result) {
        log.warn("批量处理存在失败记录:");
        log.warn(result.getFailureSummary());
        
        // 按错误类型分组统计
        result.getFailureResults().stream()
                .collect(java.util.stream.Collectors.groupingBy(
                        RulePaProcessResult::getErrorMessage,
                        java.util.stream.Collectors.counting()))
                .forEach((errorMsg, count) -> 
                        log.warn("错误类型: {}, 数量: {}", errorMsg, count));
    }
    
    /**
     * 更新收货明细的库位信息
     */
    private void updateAsnReceiveWithResults(List<AsnReceive> asnReceiveList, 
                                           List<RulePaProcessResult> successResults) {
        // 创建ID到库位的映射
        java.util.Map<Long, Long> idToLocMap = successResults.stream()
                .filter(result -> result.getLocId() != null)
                .collect(java.util.stream.Collectors.toMap(
                        RulePaProcessResult::getAsnReceiveId,
                        RulePaProcessResult::getLocId,
                        (existing, replacement) -> replacement));
        
        // 更新收货明细
        int updateCount = 0;
        for (AsnReceive asnReceive : asnReceiveList) {
            Long locId = idToLocMap.get(asnReceive.getId());
            if (locId != null) {
                // 这里可以添加实际的更新逻辑
                // 例如：asnReceive.setTargetLocId(locId);
                // asnReceiveService.update(asnReceive);
                updateCount++;
                log.debug("更新收货明细 {} 的目标库位为 {}", asnReceive.getId(), locId);
            }
        }
        
        log.info("实际更新了 {} 条收货明细的库位信息", updateCount);
    }
    
    /**
     * 获取处理统计信息
     */
    public String getProcessingSummary(BatchRulePaResult result) {
        if (result == null) {
            return "无处理结果";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("=== 批量上架规则处理报告 ===\n");
        summary.append(String.format("处理总数: %d\n", result.getTotalCount()));
        summary.append(String.format("成功数量: %d\n", result.getSuccessCount()));
        summary.append(String.format("失败数量: %d\n", result.getFailureCount()));
        summary.append(String.format("处理耗时: %d ms\n", result.getProcessingTime()));
        summary.append(String.format("成功率: %.2f%%\n", 
                result.getTotalCount() > 0 ? (double) result.getSuccessCount() / result.getTotalCount() * 100 : 0));
        
        if (!result.isAllSuccess()) {
            summary.append("\n=== 失败详情 ===\n");
            summary.append(result.getFailureSummary());
        }
        
        return summary.toString();
    }
}
