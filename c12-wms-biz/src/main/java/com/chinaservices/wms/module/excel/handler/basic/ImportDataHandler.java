package com.chinaservices.wms.module.excel.handler.basic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chinaservices.auth.module.administrativeregion.domain.AdministrativeRegionItem;
import com.chinaservices.auth.module.administrativeregion.feign.RemoteAdministrativeRegionService;
import com.chinaservices.auth.module.position.domain.WarehouseDataItem;
import com.chinaservices.auth.module.position.domain.WarehousePermissionItem;
import com.chinaservices.auth.module.position.feign.RemotePositionWarehouseService;
import com.chinaservices.auth.module.user.domain.UserForOuterCondition;
import com.chinaservices.auth.module.user.domain.UserQuery;
import com.chinaservices.auth.module.user.feign.RemoteUserService;
import com.chinaservices.core.app.AppConfig;
import com.chinaservices.core.constant.AppConstant;
import com.chinaservices.core.enums.StatusEnum;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.core.file.util.CustomFile;
import com.chinaservices.core.third.constant.ThirdResponse;
import com.chinaservices.core.third.passive.domain.PassiveBatchTagAddRequest;
import com.chinaservices.core.third.passive.domain.PassiveBatchTagResponse;
import com.chinaservices.core.third.passive.service.ThirdPassiveService;
import com.chinaservices.excel.core.ExcelResult;
import com.chinaservices.excel.util.ExcelUtil;
import com.chinaservices.file.FileObject;
import com.chinaservices.jasypt.util.SimpleSecureAesUtil;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.enums.DocumentTypeEnum;
import com.chinaservices.wms.common.enums.ReceiveStatusEnum;
import com.chinaservices.wms.common.enums.asn.QcInspectedEnum;
import com.chinaservices.wms.common.enums.basic.LotQualityEnum;
import com.chinaservices.wms.common.enums.basic.PalletTypeEnum;
import com.chinaservices.wms.common.enums.common.AuditStatusEnum;
import com.chinaservices.wms.common.enums.common.FileTypeEnum;
import com.chinaservices.wms.common.enums.common.WmsDataSourceTypeEnum;
import com.chinaservices.wms.common.enums.order.OrderSourceEnum;
import com.chinaservices.wms.common.enums.order.OrderStatusEnum;
import com.chinaservices.wms.common.enums.order.OrderTypeEnum;
import com.chinaservices.wms.common.enums.passive.*;
import com.chinaservices.wms.common.exception.ImportExceptionEnum;
import com.chinaservices.wms.common.exception.PassiveExceptionEnum;
import com.chinaservices.wms.common.properties.PassivePlatformProperties;
import com.chinaservices.wms.common.util.MapObjectUtil;
import com.chinaservices.wms.module.archive.archives.service.LogisticsFileAutoGenerateService;
import com.chinaservices.wms.module.archive.excel.item.passive.ArchivePassiveTagExcelItem;
import com.chinaservices.wms.module.archive.tag.service.ArchivePassiveTagService;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailDao;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailSnDao;
import com.chinaservices.wms.module.asn.asn.dao.AsnHeaderDao;
import com.chinaservices.wms.module.asn.asn.domain.AsnDetailInfo;
import com.chinaservices.wms.module.asn.asn.domain.AsnDetailSnItem;
import com.chinaservices.wms.module.asn.asn.domain.QcSnItem;
import com.chinaservices.wms.module.asn.asn.model.AsnDetail;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.qc.dao.CsQcHeaderDao;
import com.chinaservices.wms.module.asn.qc.domain.CsQcDetailQuery;
import com.chinaservices.wms.module.asn.qc.domain.SnDetailItem;
import com.chinaservices.wms.module.basic.carrier.CarrierItem;
import com.chinaservices.wms.module.basic.carrier.model.Carrier;
import com.chinaservices.wms.module.basic.carrier.service.CarrierService;
import com.chinaservices.wms.module.basic.carrier.service.FreightConfigService;
import com.chinaservices.wms.module.basic.cspackage.dao.PackageUnitDao;
import com.chinaservices.wms.module.basic.cspackage.domain.PackageItem;
import com.chinaservices.wms.module.basic.cspackage.domain.PackageUnitQuery;
import com.chinaservices.wms.module.basic.cspackage.model.Package;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageService;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.basic.customer.domain.CustomerItem;
import com.chinaservices.wms.module.basic.customer.model.Customer;
import com.chinaservices.wms.module.basic.customer.service.CustomerService;
import com.chinaservices.wms.module.basic.owner.domain.OwnerItem;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.basic.pallet.dao.PalletFileDao;
import com.chinaservices.wms.module.basic.pallet.domain.PalletItem;
import com.chinaservices.wms.module.basic.pallet.model.Pallet;
import com.chinaservices.wms.module.basic.pallet.model.PalletFile;
import com.chinaservices.wms.module.basic.pallet.service.PalletService;
import com.chinaservices.wms.module.basic.sku.domain.SkuInfo;
import com.chinaservices.wms.module.basic.sku.domain.SkuItem;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.basic.supplier.domain.SupplierItem;
import com.chinaservices.wms.module.basic.supplier.model.Supplier;
import com.chinaservices.wms.module.basic.supplier.service.SupplierService;
import com.chinaservices.wms.module.excel.enums.DownLoadTemplateEnum;
import com.chinaservices.wms.module.excel.enums.ImportExcelTypeEnum;
import com.chinaservices.wms.module.excel.item.AsnDetailExcelItem;
import com.chinaservices.wms.module.excel.item.QcSnExcelItem;
import com.chinaservices.wms.module.excel.item.basic.*;
import com.chinaservices.wms.module.excel.item.order.PurchaseOrderExcelItem;
import com.chinaservices.wms.module.excel.item.order.SaleOrderExcelItem;
import com.chinaservices.wms.module.excel.item.passive.PassiveTagBindExcelItem;
import com.chinaservices.wms.module.excel.item.passive.PassiveTagExcelItem;
import com.chinaservices.wms.module.excel.item.rule.RuleCarrierDistributionExcelItem;
import com.chinaservices.wms.module.folder.logistics.domain.LogisticsFolderDetailLinkCondition;
import com.chinaservices.wms.module.order.model.Order;
import com.chinaservices.wms.module.order.model.OrderSku;
import com.chinaservices.wms.module.order.service.OrderService;
import com.chinaservices.wms.module.order.service.OrderSkuService;
import com.chinaservices.wms.module.passive.tag.dao.PassiveTagDao;
import com.chinaservices.wms.module.passive.tag.dao.PassiveTagLogDao;
import com.chinaservices.wms.module.passive.tag.domain.PassiveTagLogItem;
import com.chinaservices.wms.module.passive.tag.model.PassiveTag;
import com.chinaservices.wms.module.passive.tag.model.PassiveTagLog;
import com.chinaservices.wms.module.passive.tag.service.PassiveTagLogService;
import com.chinaservices.wms.module.passive.tag.service.PassiveTagService;
import com.chinaservices.wms.module.rule.carrier.domain.CsRuleCarrierDistributionItem;
import com.chinaservices.wms.module.rule.carrier.model.CsRuleCarrierDistribution;
import com.chinaservices.wms.module.rule.carrier.service.CsRuleCarrierDistributionService;
import com.chinaservices.wms.module.warehouse.area.domain.WarehouseAreaItem;
import com.chinaservices.wms.module.warehouse.area.model.WarehouseArea;
import com.chinaservices.wms.module.warehouse.area.service.WarehouseAreaService;
import com.chinaservices.wms.module.warehouse.loc.dao.WarehouseLocFileDao;
import com.chinaservices.wms.module.warehouse.loc.domain.WarehouseLocItem;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLocFile;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import com.chinaservices.wms.module.warehouse.shelf.domain.WarehouseShelfCondition;
import com.chinaservices.wms.module.warehouse.shelf.domain.WarehouseShelfItem;
import com.chinaservices.wms.module.warehouse.shelf.domain.WarehouseShelfQuery;
import com.chinaservices.wms.module.warehouse.shelf.model.WarehouseShelf;
import com.chinaservices.wms.module.warehouse.shelf.service.WarehouseShelfService;
import com.chinaservices.wms.module.warehouse.warehouse.domain.WarehouseItem;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import com.chinaservices.wms.module.warehouse.zone.domain.WarehouseZoneItem;
import com.chinaservices.wms.module.warehouse.zone.model.WarehouseZone;
import com.chinaservices.wms.module.warehouse.zone.service.WarehouseZoneService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/01/12
 **/
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ImportDataHandler {

    private final WarehouseService warehouseService;
    private final NumberGenerator numberGenerator;
    private final WarehouseAreaService warehouseAreaService;
    private final WarehouseZoneService warehouseZoneService;
    private final WarehouseShelfService warehouseShelfService;
    private final WarehouseLocService warehouseLocService;
    private final CustomFile customFile;
    private final WarehouseLocFileDao warehouseLocFileDao;
    private final PackageService packageService;
    private final OwnerService ownerService;
    private final CustomerService customerService;
    private final SupplierService supplierService;
    private final CarrierService carrierService;
    private final FreightConfigService freightConfigService;
    private final PalletService palletService;
    private final PalletFileDao palletFileDao;
    private final PackageUnitService packageUnitService;
    private final SkuService skuService;
    private final RemotePositionWarehouseService positionWarehouseService;
    private final PackageUnitDao packageUnitDao;
    private final ImportCommonService importCommonService;
    private final CsRuleCarrierDistributionService ruleCarrierDistributionService;
    private final RemoteAdministrativeRegionService remoteAdministrativeRegionService;
    private final PassiveTagService passiveTagService;
    private final PassiveTagDao passiveTagDao;
    private final PassiveTagLogService passiveTagLogService;
    private final PassiveTagLogDao passiveTagLogDao;
    private final RemoteUserService remoteUserService;
    private final ThirdPassiveService thirdPassiveService;
    private final OrderService orderService;
    private final OrderSkuService orderSkuService;
    private final CsQcHeaderDao csQcHeaderDao;
    private final ArchivePassiveTagService archivePassiveTagService;
    private final LogisticsFileAutoGenerateService logisticsFileAutoGenerateService;
    private final PassivePlatformProperties passivePlatformProperties;
    private final AsnHeaderDao asnHeaderDao;
    private final AsnDetailDao asnDetailDao;
    private final AsnDetailSnDao asnDetailSnDao;

    /**
     * 模板下载
     *
     * @param type
     * @return
     */
    public FileObject downloadTemplate(String type) {
        DownLoadTemplateEnum downLoadTemplate = EnumUtil.getBy(DownLoadTemplateEnum::getType, type);
        if (ObjUtil.isNull(downLoadTemplate)) {
            throw new RuntimeException(StrUtil.format("模板下载type不存在：{}", type));
        }
        return switch (downLoadTemplate) {
            case DownLoadTemplateEnum.WAREHOUSE_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), WarehouseExcelItem.class);
            case DownLoadTemplateEnum.WAREHOUSE_AREA_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), WarehouseAreaExcelItem.class);
            case DownLoadTemplateEnum.WAREHOUSE_ZONE_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), WarehouseZoneExcelItem.class);
            case DownLoadTemplateEnum.WAREHOUSE_SHELF_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), WarehouseShelfExcelItem.class);
            case DownLoadTemplateEnum.WAREHOUSE_LOC_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), WarehouseLocExcelItem.class);
            case DownLoadTemplateEnum.OWNER_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), OwnerExcelItem.class);
            case DownLoadTemplateEnum.CUSTOMER_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), CustomerExcelItem.class);
            case DownLoadTemplateEnum.SUPPLIER_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), SupplierExcelItem.class);
            case DownLoadTemplateEnum.CARRIER_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), CarrierExcelItem.class);
            case DownLoadTemplateEnum.PACKAGE_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), PackageExcelItem.class);
            case DownLoadTemplateEnum.SKU_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), SkuExcelItem.class);
            case DownLoadTemplateEnum.PALLET_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), PalletExcelItem.class);
            case DownLoadTemplateEnum.SN_NO_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), AsnDetailExcelItem.class);
            case DownLoadTemplateEnum.RULE_CARRIER_DISTRIBUTION_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), RuleCarrierDistributionExcelItem.class);
            case DownLoadTemplateEnum.PASSIVE_TAG ->
                    this.downloadTemplate(downLoadTemplate.getValue(), PassiveTagExcelItem.class);
            case DownLoadTemplateEnum.PURCHASE_ORDER_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), PurchaseOrderExcelItem.class);
            case DownLoadTemplateEnum.SALE_ORDER_TEMPLATE ->
                    this.downloadTemplate(downLoadTemplate.getValue(), SaleOrderExcelItem.class);
            case DownLoadTemplateEnum.QC_SN_NO ->
                    this.downloadTemplate(downLoadTemplate.getValue(), QcSnExcelItem.class);
            case DownLoadTemplateEnum.QC_SN ->
                    this.downloadTemplate(downLoadTemplate.getValue(), AsnDetailExcelItem.class);
            case DownLoadTemplateEnum.PASSIVE_TAG_BIND ->
                    this.downloadTemplate(downLoadTemplate.getValue(), PassiveTagBindExcelItem.class);
        };
    }

    /**
     * 模板下载
     *
     * @param fileName
     * @param clazz
     * @return
     */
    public FileObject downloadTemplate(String fileName, Class<?> clazz) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(CollectionUtil.newArrayList(), fileName, clazz, outputStream);
        InputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        return customFile.upload(fileName, inputStream);
    }

    /**
     * 导入文件
     *
     * @param file
     * @param type
     * @param params
     * @return
     */
    public ResponseData importDataHandler(MultipartFile file, String type, String params) {
        ImportExcelTypeEnum importExcelType = EnumUtil.getBy(ImportExcelTypeEnum::getType, type);
        if (ObjUtil.isNull(importExcelType)) {
            throw new RuntimeException(StrUtil.format("导入type不存在：{}", type));
        }
        JSONObject paramsMap = null;
        if (StrUtil.isNotEmpty(params)) {
            paramsMap = JSONUtil.parseObj(params);
        }
        try (InputStream inputStream = file.getInputStream()) {
            return switch (importExcelType) {
                case ImportExcelTypeEnum.WAREHOUSE -> {
                    List<WarehouseExcelItem> list = this.importDataHandler(inputStream, WarehouseExcelItem.class);
                    yield this.warehouseImportDataHandler(list);
                }
                case ImportExcelTypeEnum.WAREHOUSE_AREA -> {
                    List<WarehouseAreaExcelItem> list = this.importDataHandler(inputStream, WarehouseAreaExcelItem.class);
                    yield this.warehouseAreaImportDataHandler(list);
                }
                case ImportExcelTypeEnum.WAREHOUSE_ZONE -> {
                    List<WarehouseZoneExcelItem> list = this.importDataHandler(inputStream, WarehouseZoneExcelItem.class);
                    yield this.warehouseZoneImportDataHandler(list);
                }
                case ImportExcelTypeEnum.WAREHOUSE_SHELF -> {
                    List<WarehouseShelfExcelItem> list = this.importDataHandler(inputStream, WarehouseShelfExcelItem.class);
                    yield this.warehouseShelfImportDataHandler(list);
                }
                case ImportExcelTypeEnum.WAREHOUSE_LOC -> {
                    List<WarehouseLocExcelItem> list = this.importDataHandler(inputStream, WarehouseLocExcelItem.class);
                    yield this.warehouseLocImportDataHandler(list);
                }
                case ImportExcelTypeEnum.OWNER -> {
                    List<OwnerExcelItem> list = this.importDataHandler(inputStream, OwnerExcelItem.class);
                    yield this.ownerImportDataHandler(list);
                }
                case ImportExcelTypeEnum.CUSTOMER -> {
                    List<CustomerExcelItem> list = this.importDataHandler(inputStream, CustomerExcelItem.class);
                    yield this.customerImportDataHandler(list);
                }
                case ImportExcelTypeEnum.SUPPLIER -> {
                    List<SupplierExcelItem> list = this.importDataHandler(inputStream, SupplierExcelItem.class);
                    yield this.supplierImportDataHandler(list);
                }
                case ImportExcelTypeEnum.CARRIER -> {
                    List<CarrierExcelItem> list = this.importDataHandler(inputStream, CarrierExcelItem.class);
                    yield this.carrierImportDataHandler(list);
                }
                case ImportExcelTypeEnum.PACKAGE -> {
                    List<PackageExcelItem> list = this.importDataHandler(inputStream, PackageExcelItem.class);
                    yield this.packageImportDataHandler(list);
                }
                case ImportExcelTypeEnum.SKU -> {
                    List<SkuExcelItem> list = this.importDataHandler(inputStream, SkuExcelItem.class);
                    yield this.skuImportDataHandler(list);
                }
                case ImportExcelTypeEnum.PALLET -> {
                    List<PalletExcelItem> list = this.importDataHandler(inputStream, PalletExcelItem.class);
                    yield this.palletImportDataHandler(list);
                }
                case ImportExcelTypeEnum.SN_NO -> {
                    List<AsnDetailExcelItem> list = this.importDataHandler(inputStream, AsnDetailExcelItem.class);
                    yield this.asnDetailImportDataHandler(list, paramsMap);
                }
                case ImportExcelTypeEnum.RULE_CARRIER_DISTRIBUTION -> {
                    List<RuleCarrierDistributionExcelItem> list = this.importDataHandler(inputStream, RuleCarrierDistributionExcelItem.class);
                    yield this.ruleCarrierDistributionImportDataHandler(list);
                }
                case ImportExcelTypeEnum.PASSIVE_TAG -> {
                    List<PassiveTagExcelItem> list = this.importDataHandler(inputStream, PassiveTagExcelItem.class);
                    yield this.passiveTagImportDataHandler(list, file);
                }
                case ImportExcelTypeEnum.PURCHASE_ORDER -> {
                    List<PurchaseOrderExcelItem> list = this.importDataHandler(inputStream, PurchaseOrderExcelItem.class);
                    yield this.PurchaseOrderImportDataHandler(list);
                }
                case ImportExcelTypeEnum.SALE_ORDER -> {
                    List<SaleOrderExcelItem> list = this.importDataHandler(inputStream, SaleOrderExcelItem.class);
                    yield this.SaleOrderImportDataHandler(list);
                }
                case ImportExcelTypeEnum.QC_SN_NO -> {
                    List<QcSnExcelItem> list = this.importDataHandler(inputStream, QcSnExcelItem.class);
                    yield this.qcSnQualityInsImportDataHandler(list, paramsMap);
                }
                case ImportExcelTypeEnum.QC_SN -> {
                    List<AsnDetailExcelItem> list = this.importDataHandler(inputStream, AsnDetailExcelItem.class);
                    yield this.qcSnImportDataHandler(list, paramsMap);
                }
                case ImportExcelTypeEnum.ARCHIVE_PASSIVE_TAG -> {
                    List<ArchivePassiveTagExcelItem> list = this.importDataHandler(inputStream, ArchivePassiveTagExcelItem.class);
                    yield archivePassiveTagService.importHandler( list);
                }
                case ImportExcelTypeEnum.PASSIVE_TAG_BIND -> {
                    List<PassiveTagBindExcelItem> list = this.importDataHandler(inputStream, PassiveTagBindExcelItem.class);
                    yield this.passiveTagImportAndBindDataHandler(list, file);
                }
            };
        } catch (IOException e) {
            throw new RuntimeException("文件读取失败", e);
        }
    }

    /**
     *@Description 采购订单导入
     *@Param list
     *@Return * {@link ResponseData }
     *@Date 2025/4/24 14:23
     *<AUTHOR>
     **/
    private ResponseData PurchaseOrderImportDataHandler(List<PurchaseOrderExcelItem> list) {
        String orderType = OrderTypeEnum.PURCHASE_ORDER.getCode();
        String orderNoCode = IdRuleConstant.P_ORDER_CODE;
        //查询供应商信息
        List<String> suplierCodeList = list.stream().map(PurchaseOrderExcelItem::getSupplierCode).distinct().collect(Collectors.toList());
        List<Supplier> supplierList = supplierService.findBySupplierCodeList(suplierCodeList);
        if(CollectionUtil.isEmpty(supplierList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SUPLIER_ERROR,suplierCodeList);
        }
        Map<String, Supplier> supplierMap = supplierList.stream().collect(Collectors.toMap(Supplier::getSupplierCode, Function.identity(), (oldValue, newValue) -> oldValue));
        //查询商品信息
        List<String> skuCodeList = list.stream().map(PurchaseOrderExcelItem::getSkuCode).distinct().collect(Collectors.toList());
        List<Sku> skuList = skuService.listBySkuCode(skuCodeList);
        if(CollectionUtil.isEmpty(skuList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SKU_ERROR,skuCodeList);
        }
        Map<String, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getSkuCode, Function.identity(), (oldValue, newValue) -> oldValue));
        //查询仓库信息
        List<String> warehouseNameList = list.stream().map(PurchaseOrderExcelItem::getWarehouseName).distinct().collect(Collectors.toList());
        List<Warehouse> warehouseList = warehouseService.listByWarehouseNameList(warehouseNameList);
        if (CollectionUtil.isEmpty(warehouseList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_WAREHOUSE_ERROR,warehouseNameList);
        }
        Map<String, Warehouse> warehouseMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getWarehouseName, Function.identity(), (oldValue, newValue) -> oldValue));
        //根据货主代码查询货主
        List<String> ownerCodeList = list.stream().map(PurchaseOrderExcelItem::getOwnerCode).distinct().collect(Collectors.toList());
        List<Owner> ownerList = ownerService.listBySkuCode(ownerCodeList);
        if (CollectionUtil.isEmpty(ownerList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_OWNER_ERROR,ownerCodeList);
        }
        Map<String, Owner> ownerMap = ownerList.stream().collect(Collectors.toMap(Owner::getOwnerCode, Function.identity(), (oldValue, newValue) -> oldValue));
        //抽取包装代码
        List<String> packageCodeList = list.stream().map(PurchaseOrderExcelItem::getPackageCode).distinct().collect(Collectors.toList());
        List<Package> packageList = packageService.listByCodeList(packageCodeList);
        if(CollectionUtil.isEmpty(packageList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_PACKAGE_ERROR,packageCodeList);
        }
        Map<String, Package> packageMap = packageList.stream().collect(Collectors.toMap(Package::getCode, Function.identity(), (oldValue, newValue) -> oldValue));
        //抽取包装ID
        List<Long> packageIdList = packageList.stream().map(Package::getId).distinct().collect(Collectors.toList());
        //抽取包装单位代码
        List<String> packageUnitCodeList = list.stream().map(PurchaseOrderExcelItem::getPackageUnitCode).distinct().collect(Collectors.toList());
        //查询包装单位
        List<PackageUnit> packageUnitList = packageUnitService.listByPackageIdListAndCodeList(packageIdList, packageUnitCodeList);
        if(CollectionUtil.isEmpty(packageUnitList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_PACKAGE_UNIT_ERROR,packageUnitCodeList,packageCodeList);
        }
        Map<Long, List<PackageUnit>> groupByPackageIdMap = packageUnitList.stream().collect(Collectors.groupingBy(PackageUnit::getPackageId));
        //根据供应商编码、供应商名称、供应商地址、交货期、采购负责人、优先级、仓库、货主、联系人、联系方式分组
        Map<String, List<PurchaseOrderExcelItem>> groupByMap = list.stream()
                .collect(Collectors
                        .groupingBy(item ->
                                item.getSupplierCode() +"_"+ item.getPartnerName() +"_"+"_"+item.getPartnerAddress()+ item.getDeliveryDate() + "_"+item.getManagerName() + "_"
                                        + item.getPriorityLevel() +"_"+item.getWarehouseName()+"_"+item.getOwnerCode()+"_"+item.getOwnerName() +"_"+item.getContactName()+"_"+item.getContactPhone()));
        List<Order> orderList = new ArrayList<>();
        List<OrderSku> orderSkuList = new ArrayList<>();
        groupByMap.forEach((key, value) -> {
            PurchaseOrderExcelItem excelItem = value.get(0);
            Supplier supplier = supplierMap.get(value.get(0).getSupplierCode());
            if(ObjectUtil.isNull(supplier)){
                throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SUPLIER_ERROR, excelItem.getSupplierCode());
            }
            //验证供应商名称是否和系统匹配
            if(!supplier.getSupplierName().equals(excelItem.getPartnerName())){
                throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SUPLIER_NAME_ERROR, excelItem.getSupplierCode(), excelItem.getPartnerName());
            }
            Owner owner = ownerMap.get(excelItem.getOwnerCode());
            if(ObjectUtil.isNull(owner)){
                throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_OWNER_ERROR, excelItem.getOwnerCode());
            }
            //验证货主名称是否和系统匹配
            if(!owner.getOwnerName().equals(excelItem.getOwnerName())){
                throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_OWNER_NAME_ERROR, excelItem.getOwnerCode(), excelItem.getOwnerName());
            }
            Warehouse warehouse = warehouseMap.get(excelItem.getWarehouseName());
            if(ObjectUtil.isNull( warehouse)){
                throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_WAREHOUSE_ERROR, excelItem.getWarehouseName());
            }
            Order order = new Order();
            BeanUtil.copyProperties(excelItem, order);
            order.setPartnerId(supplier.getId());
            order.setOrderSource(OrderSourceEnum.SELF_CREATE.getCode());
            order.setOrderType(orderType);
            order.setOrderStatus(OrderStatusEnum.CREATE.getCode());
            order.setOrderNo(numberGenerator.nextValue(orderNoCode));
            order.setWarehouseId(warehouse.getId());
            order.setWarehouseName(warehouse.getWarehouseName());
            order.setOwnerId(owner.getId());
            order.setOwnerName(owner.getOwnerName());
            order.setPartnerContact(excelItem.getContactName());
            order.setPartnerContactInformation(excelItem.getContactPhone());
            order.setAuditStatus(AuditStatusEnum.UNREVIEWED.getCode());
            orderService.preSave(order);
            orderList.add(order);
            value.forEach(item -> {
                Sku sku = skuMap.get(item.getSkuCode());
                if (ObjectUtil.isNull(sku)){
                    throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SKU_ERROR, item.getSkuCode());
                }
                //验证当前商品是否属于当前货主
                if(!sku.getOwnerId().equals(owner.getId())){
                    throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SKU_OWNER_ERROR, item.getSkuCode(), item.getOwnerCode());
                }
                Package currentPackage = packageMap.get(item.getPackageCode());
                if(ObjectUtil.isNull(currentPackage)){
                    throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_PACKAGE_ERROR, item.getPackageCode());
                }
                List<PackageUnit> currentPackageUnitList = groupByPackageIdMap.get(currentPackage.getId());
                if(CollectionUtil.isEmpty(currentPackageUnitList) || currentPackageUnitList.stream().noneMatch(x -> x.getCode().equals(item.getPackageUnitCode()))){
                    throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_PACKAGE_UNIT_ERROR, item.getPackageUnitCode(),item.getPackageCode());
                }
                PackageUnit currentPackageUnit = currentPackageUnitList.stream().filter(packageUnit -> packageUnit.getCode().equals(item.getPackageUnitCode())).findFirst().get();
                OrderSku orderSku = new OrderSku();
                BeanUtil.copyProperties(item, orderSku);
                orderSku.setSkuId(sku.getId());
                orderSku.setOrderNo(order.getOrderNo());
                orderSku.setPackageUnitId(currentPackageUnit.getPackageId());
                orderSku.setPackageUnitName(currentPackageUnit.getName());
                orderSku.setPackageId(currentPackage.getId());
                orderSku.setPackageName(currentPackage.getName());
                orderSku.setTotalAmount(orderSku.getTotalCount().multiply(orderSku.getUnitPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                orderSku.setRemainCount(orderSku.getTotalCount());
                BigDecimal remainCountEa = orderSku.getTotalCount().multiply(new BigDecimal(currentPackageUnit.getMinQuantity())).setScale(2, RoundingMode.HALF_UP);
                orderSku.setRemainCountEa(remainCountEa);
                orderSkuService.preSave(orderSku);
                orderSkuList.add(orderSku);
            });
        });
        if(CollectionUtil.isNotEmpty(orderSkuList)){
            orderSkuService.batchInsert(orderSkuList);
        }
        if(CollectionUtil.isNotEmpty(orderList)){
            orderService.batchInsert(orderList);
            //生成物流文件数据
            generateLogisticsFile(orderList);
        }
        return ResponseData.success();
    }

    /**
     *@Description 采销订单生成物流文件
     *@Param orderList
     *@Return Void
     *@Date 2025/7/1 16:49
     *<AUTHOR>
     **/
    private void generateLogisticsFile(List<Order> orderList) {
        List<String> orderNoList = orderList.stream().map(Order::getOrderNo).collect(Collectors.toList());
        DocumentTypeEnum code;
        if(OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderList.get(0).getOrderType())){
            code = DocumentTypeEnum.PURCHASE_ORDER;
        }else{
            code = DocumentTypeEnum.SALES_ORDER;
        }
        SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();
        LogisticsFolderDetailLinkCondition condition = new  LogisticsFolderDetailLinkCondition();
        condition.setDocumentNos(orderNoList);
        logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(code,Arrays.asList(condition),sessionUserInfo.getCompanyId(),sessionUserInfo.getTenancy(), null);
    }

    /**
     *@Description 销售订单导入
     *@Param list
     *@Return * {@link ResponseData }
     *@Date 2025/4/25 15:52
     *<AUTHOR>
     **/
    private ResponseData SaleOrderImportDataHandler(List<SaleOrderExcelItem> list) {
        String orderType = OrderTypeEnum.SALES_ORDER.getCode();
        String orderNoCode = IdRuleConstant.S_ORDER_CODE;
        List<String> customerCodeList = list.stream().map(SaleOrderExcelItem::getCustomerCode).distinct().collect(Collectors.toList());
        List<Customer> customerList = customerService.findByCodList(customerCodeList);
        if(CollectionUtil.isEmpty(customerList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SUPLIER_ERROR,customerList);
        }
        Map<String, Customer> customerMap = customerList.stream().collect(Collectors.toMap(Customer::getCustomerCode, Function.identity()));
        //查询商品信息
        List<String> skuCodeList = list.stream().map(SaleOrderExcelItem::getSkuCode).distinct().collect(Collectors.toList());
        List<Sku> skuList = skuService.listBySkuCode(skuCodeList);
        if(CollectionUtil.isEmpty(skuList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SKU_ERROR,skuCodeList);
        }
        Map<String, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getSkuCode, Function.identity(), (oldValue, newValue) -> oldValue));
        //查询仓库信息
        List<String> warehouseNameList = list.stream().map(SaleOrderExcelItem::getWarehouseName).distinct().collect(Collectors.toList());
        List<Warehouse> warehouseList = warehouseService.listByWarehouseNameList(warehouseNameList);
        if (CollectionUtil.isEmpty(warehouseList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_WAREHOUSE_ERROR,warehouseNameList);
        }
        Map<String, Warehouse> warehouseMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getWarehouseName, Function.identity(), (oldValue, newValue) -> oldValue));
        //根据货主代码查询货主
        List<String> ownerCodeList = list.stream().map(SaleOrderExcelItem::getOwnerCode).distinct().collect(Collectors.toList());
        List<Owner> ownerList = ownerService.listBySkuCode(ownerCodeList);
        if (CollectionUtil.isEmpty(ownerList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_OWNER_ERROR,ownerCodeList);
        }
        Map<String, Owner> ownerMap = ownerList.stream().collect(Collectors.toMap(Owner::getOwnerCode, Function.identity(), (oldValue, newValue) -> oldValue));
        //抽取包装代码
        List<String> packageCodeList = list.stream().map(SaleOrderExcelItem::getPackageCode).distinct().collect(Collectors.toList());
        List<Package> packageList = packageService.listByCodeList(packageCodeList);
        if(CollectionUtil.isEmpty(packageList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_PACKAGE_ERROR,packageCodeList);
        }
        Map<String, Package> packageMap = packageList.stream().collect(Collectors.toMap(Package::getCode, Function.identity(), (oldValue, newValue) -> oldValue));
        //抽取包装ID
        List<Long> packageIdList = packageList.stream().map(Package::getId).distinct().collect(Collectors.toList());
        //抽取包装单位代码
        List<String> packageUnitCodeList = list.stream().map(SaleOrderExcelItem::getPackageUnitCode).distinct().collect(Collectors.toList());
        //查询包装单位
        List<PackageUnit> packageUnitList = packageUnitService.listByPackageIdListAndCodeList(packageIdList, packageUnitCodeList);
        if(CollectionUtil.isEmpty(packageUnitList)){
            throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_PACKAGE_UNIT_ERROR,packageUnitCodeList,packageCodeList);
        }
        Map<Long, List<PackageUnit>> groupByPackageIdMap = packageUnitList.stream().collect(Collectors.groupingBy(PackageUnit::getPackageId));
        //根据供应商编码、供应商名称、供应商地址、交货期、采购负责人、优先级、仓库、货主、联系人、联系方式分组
        Map<String, List<SaleOrderExcelItem>> groupByMap = list.stream()
                .collect(Collectors
                        .groupingBy(item ->
                                item.getCustomerCode() +"_"+ item.getPartnerName() +"_"+"_"+item.getPartnerAddress()+ "_"+item.getManagerName() + "_"
                                        + item.getPriorityLevel() +"_"+item.getWarehouseName()+"_"+item.getOwnerCode()+"_"+item.getOwnerName() +"_"+item.getContactName()+"_"+item.getContactPhone()));
        List<Order> orderList = new ArrayList<>();
        List<OrderSku> orderSkuList = new ArrayList<>();
        groupByMap.forEach((key, value) -> {

            SaleOrderExcelItem excelItem = value.get(0);
            Customer customer = customerMap.get(excelItem.getCustomerCode());
            if(ObjectUtil.isNull(customer)){
                throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SUPLIER_ERROR, value.get(0).getCustomerCode());
            }
            //验证供应商名称是否和系统匹配
            if(!customer.getCustomerName().equals(excelItem.getPartnerName())){
                throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SUPLIER_NAME_ERROR, excelItem.getContactName(), excelItem.getPartnerName());
            }
            Owner owner = ownerMap.get(excelItem.getOwnerCode());
            if(ObjectUtil.isNull(owner)){
                throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_OWNER_ERROR, excelItem.getOwnerCode());
            }
            //验证货主名称是否和系统匹配
            if(!owner.getOwnerName().equals(excelItem.getOwnerName())){
                throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_OWNER_NAME_ERROR, excelItem.getOwnerCode(), excelItem.getOwnerName());
            }
            Warehouse warehouse = warehouseMap.get(excelItem.getWarehouseName());
            if(ObjectUtil.isNull( warehouse)){
                throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_WAREHOUSE_ERROR, excelItem.getWarehouseName());
            }
            Order order = new Order();
            BeanUtil.copyProperties(excelItem, order);
            order.setPartnerId(customer.getId());
            order.setOrderSource(OrderSourceEnum.SELF_CREATE.getCode());
            order.setOrderType(orderType);
            order.setOrderStatus(OrderStatusEnum.CREATE.getCode());
            order.setOrderNo(numberGenerator.nextValue(orderNoCode));
            order.setWarehouseId(warehouse.getId());
            order.setWarehouseName(warehouse.getWarehouseName());
            order.setOwnerId(owner.getId());
            order.setOwnerName(owner.getOwnerName());
            order.setPartnerContact(excelItem.getContactName());
            order.setPartnerContactInformation(excelItem.getContactPhone());
            order.setAuditStatus(AuditStatusEnum.UNREVIEWED.getCode());
            orderService.preSave(order);
            orderList.add(order);
            value.forEach(item -> {
                Sku sku = skuMap.get(item.getSkuCode());
                if (ObjectUtil.isNull(sku)){
                    throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SKU_ERROR, item.getSkuCode());
                }
                //验证当前商品是否属于当前货主
                if(!sku.getOwnerId().equals(owner.getId())){
                    throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_SKU_OWNER_ERROR, item.getSkuCode(), item.getOwnerCode());
                }
                Package currentPackage = packageMap.get(item.getPackageCode());
                if(ObjectUtil.isNull(currentPackage)){
                    throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_PACKAGE_ERROR, item.getPackageCode());
                }
                List<PackageUnit> currentPackageUnitList = groupByPackageIdMap.get(currentPackage.getId());
                if(CollectionUtil.isEmpty(currentPackageUnitList) || currentPackageUnitList.stream().noneMatch(x -> x.getCode().equals(item.getPackageUnitCode()))){
                    throw new ServiceException(ImportExceptionEnum.ORDER_IMPORT_PACKAGE_UNIT_ERROR, item.getPackageUnitCode(),item.getPackageCode());
                }
                PackageUnit currentPackageUnit = currentPackageUnitList.stream().filter(packageUnit -> packageUnit.getCode().equals(item.getPackageUnitCode())).findFirst().get();
                OrderSku orderSku = new OrderSku();
                BeanUtil.copyProperties(item, orderSku);
                orderSku.setSkuId(sku.getId());
                orderSku.setOrderNo(order.getOrderNo());
                orderSku.setPackageUnitId(currentPackageUnit.getPackageId());
                orderSku.setPackageUnitName(currentPackageUnit.getName());
                orderSku.setPackageId(currentPackage.getId());
                orderSku.setPackageName(currentPackage.getName());
                orderSku.setTotalAmount(orderSku.getTotalCount().multiply(orderSku.getUnitPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                orderSku.setRemainCount(orderSku.getTotalCount());
                BigDecimal remainCountEa = orderSku.getTotalCount().multiply(new BigDecimal(currentPackageUnit.getMinQuantity())).setScale(2, RoundingMode.HALF_UP);
                orderSku.setRemainCountEa(remainCountEa);
                orderSkuService.preSave(orderSku);
                orderSkuList.add(orderSku);
            });
        });
        if(CollectionUtil.isNotEmpty(orderSkuList)){
            orderSkuService.batchInsert(orderSkuList);
        }
        if(CollectionUtil.isNotEmpty(orderList)){
            orderService.batchInsert(orderList);
            generateLogisticsFile(orderList);
        }
        return ResponseData.success();
    }

    /**
     * 导入文件
     *
     * @param inputStream
     * @param clazz
     * @param <T>
     * @return
     */
    private <T> List<T> importDataHandler(InputStream inputStream, Class<T> clazz) {
        ExcelResult<T> excelResult = null;
        try {
            excelResult = ExcelUtil.importExcel(inputStream, clazz, Boolean.TRUE);
        } catch (Exception e) {
            throw new ServiceException(ImportExceptionEnum.IMPORT_ANALYSIS_ERROR, e.getMessage());
        }
        if (CollectionUtil.isNotEmpty(excelResult.getErrorList())) {
            throw new ServiceException(JSONUtil.toJsonStr(excelResult.getErrorList()));
        }
        if (CollectionUtil.isEmpty(excelResult.getList())) {
            throw new ServiceException(ImportExceptionEnum.IMPORT_DATA_EMPTY);
        }
        return excelResult.getList();
    }

    /**
     * 仓库
     *
     * @param warehouseExcelItems
     * @return ResponseData
     */
    public ResponseData warehouseImportDataHandler(List<WarehouseExcelItem> warehouseExcelItems) {
        // 一：校验仓库名称 -- 重复
        Map<String, Long> warehouseNameMap = warehouseExcelItems.stream().collect(Collectors.groupingBy(WarehouseExcelItem::getWarehouseName, Collectors.counting()));
        importCommonService.checkDuplicateName(warehouseNameMap, ImportExceptionEnum.WAREHOUSE_NAME_DUPLICATE);
        // 二：仓库名称 -- 存在
        List<String> warehouseNameList = warehouseExcelItems.stream().map(WarehouseExcelItem::getWarehouseName).distinct().toList();
        List<Warehouse> exsitWarehouseNameList = warehouseService.listByWarehouseNameList(warehouseNameList);
        if (CollectionUtil.isNotEmpty(exsitWarehouseNameList)) {
            throw new ServiceException(ImportExceptionEnum.WAREHOUSE_NAME_EXIST, StrUtil.join(StrUtil.COMMA, exsitWarehouseNameList.stream().map(Warehouse::getWarehouseName).toList()));
        }
        // 三：省市区代码 -- 存在
        Map<String, String> pccMap = MapUtil.newHashMap();
        if (BooleanUtil.isTrue(warehouseExcelItems.stream().anyMatch(warehouseExcelItem -> StrUtil.isNotEmpty(warehouseExcelItem.getProvinceName()) || StrUtil.isNotEmpty(warehouseExcelItem.getCityName()) || StrUtil.isNotEmpty(warehouseExcelItem.getCountyName())))) {
            List<WarehouseExcelItem> tempWarehouseExcelItems = warehouseExcelItems.stream().filter(item -> StrUtil.isAllNotEmpty(item.getProvinceName(), item.getCityName(), item.getCountyName())).toList();
            if (CollectionUtil.isEmpty(tempWarehouseExcelItems)) {
                throw new ServiceException(ImportExceptionEnum.IMPORT_DATA_PCC_NO_COMPLETE);
            }
            List<List<String>> pccNameList = tempWarehouseExcelItems.stream().map(warehouseExcelItem -> CollectionUtil.newArrayList(warehouseExcelItem.getProvinceName(), warehouseExcelItem.getCityName(), warehouseExcelItem.getCountyName())).distinct().collect(Collectors.toList());
            pccMap = importCommonService.checkPcc(pccNameList);
        }

        // 四：赋值
        List<Warehouse> warehouseList = CollectionUtil.newArrayList();
        for (WarehouseExcelItem warehouseExcelItem : warehouseExcelItems) {
            WarehouseItem warehouseItem = new WarehouseItem();
            warehouseItem.setWarehouseCode(numberGenerator.nextValue(IdRuleConstant.WAREHOUSE_CODE));
            warehouseItem.setWarehouseName(warehouseExcelItem.getWarehouseName());
            warehouseItem.setTel(warehouseExcelItem.getTel());
            warehouseItem.setMobile(warehouseExcelItem.getMobile());
            warehouseItem.setFax(warehouseExcelItem.getFax());
            warehouseItem.setZipCode(warehouseExcelItem.getZipCode());
            warehouseItem.setContactPerson(warehouseExcelItem.getContactPerson());
            warehouseItem.setProvinceCode(MapUtil.getStr(pccMap, warehouseExcelItem.getProvinceName()));
            warehouseItem.setProvinceName(warehouseExcelItem.getProvinceName());
            warehouseItem.setCityCode(MapUtil.getStr(pccMap, warehouseExcelItem.getCityName()));
            warehouseItem.setCityName(warehouseExcelItem.getCityName());
            warehouseItem.setCountyCode(MapUtil.getStr(pccMap, warehouseExcelItem.getCountyName()));
            warehouseItem.setCountyName(warehouseExcelItem.getCountyName());
            warehouseItem.setArea(Convert.toBigDecimal(warehouseExcelItem.getArea()));
            warehouseItem.setVolume(Convert.toBigDecimal(warehouseExcelItem.getVolume()));
            warehouseItem.setAddress(warehouseExcelItem.getAddress());
            warehouseItem.setWarehouseType(warehouseExcelItem.getWarehouseType());
            Warehouse warehouse = new Warehouse();
            BeanUtil.copyProperties(warehouseItem, warehouse);
            warehouse.setStatus(StatusEnum.YES.getCode());
            warehouse.setSource(WmsDataSourceTypeEnum.CREATE.getCode());
            warehouseService.preSave(warehouse);
            warehouseList.add(warehouse);
        }
        ResponseData responseData = new ResponseData<>();
        if (CollectionUtil.isEmpty(warehouseList)) {
            return responseData;
        }
        // 五：新增时初始化区域、库区、库位
        int count = warehouseService.batchInsert(warehouseList);
        WarehousePermissionItem warehousePermissionItem = new WarehousePermissionItem();
        List<WarehouseDataItem> warehouseDataItems = CollectionUtil.newArrayList();
        warehouseList.forEach(e -> {
            warehouseService.updateInitByWarehouse(e);
            //同步给管理员仓库权限
            WarehouseDataItem dataItem = new WarehouseDataItem();
            dataItem.setWarehouseName(e.getWarehouseName());
            dataItem.setWarehouseId(e.getId());
            dataItem.setWarehouseCode(e.getWarehouseCode());
            warehouseDataItems.add(dataItem);
        });
        warehousePermissionItem.setWarehouseDataItems(warehouseDataItems);
        positionWarehouseService.saveAdmin(warehousePermissionItem);
        responseData.setMsg(String.format(ImportExceptionEnum.IMPORT_DATA_SUCCESS.getMsg(), Convert.toStr(count)));
        responseData.setSuccess(true);
        responseData.setCode(200);
        return responseData;
    }

    /**
     * 区域
     *
     * @param warehouseAreaExcelItems
     * @return ResponseData
     */
    public ResponseData warehouseAreaImportDataHandler(List<WarehouseAreaExcelItem> warehouseAreaExcelItems) {
        // 一：区域名称是否重复
        // 1)根据仓库名称获取数据分组Map
        Map<String, List<String>> warehouseNameMap = MapObjectUtil.getMap(warehouseAreaExcelItems, WarehouseAreaExcelItem::getWarehouseName, WarehouseAreaExcelItem::getAreaName);
        // 2)校验是否存在重复数据
        importCommonService.checkDuplicate(warehouseNameMap, ImportExceptionEnum.WAREHOUSE_AREA_DUPLICATE);
        // 二：是否存在
        // 2.1：区域名称 -- 存在
        List<String> areaNameList = warehouseAreaExcelItems.stream().map(WarehouseAreaExcelItem::getAreaName).distinct().toList();
        List<WarehouseArea> exsitAreaNameList = warehouseAreaService.listByAreaNameList(areaNameList);
        importCommonService.checkWarehouse(warehouseNameMap,exsitAreaNameList, WarehouseArea::getWarehouseName, WarehouseArea::getAreaName,ImportExceptionEnum.WAREHOUSE_AREA_EXIST);
        // 2.2：所属仓库 -- 获取<仓库名称，仓库id>
        List<String> warehouseNameList = warehouseAreaExcelItems.stream().map(WarehouseAreaExcelItem::getWarehouseName).distinct().toList();
        Map<String, Long> warehouseMap = importCommonService.getEntity(ImportExcelTypeEnum.WAREHOUSE,warehouseNameList, ImportExceptionEnum.WAREHOUSE_AREA_WAREHOUSE_EMPTY,Long.class);
        // 三：赋值
        List<WarehouseArea> warehouseAreaList = CollectionUtil.newArrayList();
        for (WarehouseAreaExcelItem warehouseAreaExcelItem : warehouseAreaExcelItems) {
            WarehouseAreaItem warehouseAreaItem = new WarehouseAreaItem();
            warehouseAreaItem.setAreaCode(numberGenerator.nextValue(IdRuleConstant.WAREHOUSE_AREA_CODE));
            warehouseAreaItem.setAreaName(warehouseAreaExcelItem.getAreaName());
            warehouseAreaItem.setWarehouseId(MapUtil.getLong(warehouseMap, warehouseAreaExcelItem.getWarehouseName()));
            warehouseAreaItem.setWarehouseName(warehouseAreaExcelItem.getWarehouseName());
            warehouseAreaItem.setRemark(warehouseAreaExcelItem.getRemark());
            warehouseAreaItem.setTemperatureControlType(warehouseAreaExcelItem.getTemperatureControlType());
            warehouseAreaItem.setIsDefault(YesNoEnum.NO.getCode());
            warehouseAreaItem.setSource(WmsDataSourceTypeEnum.CREATE.getCode());
            WarehouseArea warehouseArea = new WarehouseArea();
            BeanUtil.copyProperties(warehouseAreaItem, warehouseArea);
            warehouseAreaService.preSave(warehouseArea);
            warehouseAreaList.add(warehouseArea);
        }
        return getResponseData(CollectionUtil.isNotEmpty(warehouseAreaList), warehouseAreaService.batchInsert(warehouseAreaList));
    }

    @NotNull
    private ResponseData getResponseData(boolean notEmpty, int i) {
        ResponseData responseData = new ResponseData<>();
        if (notEmpty) {
            int count = i;
            responseData.setMsg(String.format(ImportExceptionEnum.IMPORT_DATA_SUCCESS.getMsg(), Convert.toStr(count)));
            responseData.setSuccess(true);
            responseData.setCode(200);
        }
        return responseData;
    }

    /**
     * 库区
     *
     * @param warehouseZoneExcelItems
     * @return ResponseData
     */
    public ResponseData warehouseZoneImportDataHandler(List<WarehouseZoneExcelItem> warehouseZoneExcelItems) {
        // 一: 校验库区名称是否重复
        Map<String, List<String>> warehouseNameMap = MapObjectUtil.getMap(warehouseZoneExcelItems, WarehouseZoneExcelItem::getWarehouseName, WarehouseZoneExcelItem::getZoneName);
        importCommonService.checkDuplicate(warehouseNameMap, ImportExceptionEnum.WAREHOUSE_ZONE_DUPLICATE);

        // 二: 校验是否存在
        // 2.1: 库区名称 -- 存在
        List<String> zoneNameList = warehouseZoneExcelItems.stream().map(WarehouseZoneExcelItem::getZoneName).distinct().toList();
        List<WarehouseZone> exsitZoneNameList = warehouseZoneService.listByZoneNameList(zoneNameList);
        importCommonService.checkWarehouse(warehouseNameMap,exsitZoneNameList, WarehouseZone::getWarehouseName, WarehouseZone::getZoneName,ImportExceptionEnum.WAREHOUSE_ZONE_EXIST);
        // 2.2: 所属仓库 -- 获取<仓库名称，仓库id>
        List<String> warehouseNameList = warehouseZoneExcelItems.stream().map(WarehouseZoneExcelItem::getWarehouseName).filter(StrUtil::isNotEmpty).distinct().toList();
        Map<String, Long> warehouseMap = importCommonService.getEntity(ImportExcelTypeEnum.WAREHOUSE,warehouseNameList, ImportExceptionEnum.WAREHOUSE_ZONE_WAREHOUSE_EMPTY,Long.class);
        // 2.3: 所属区域 -- 获取<区域名称，区域信息>
        List<String> areaNameList = warehouseZoneExcelItems.stream().map(WarehouseZoneExcelItem::getAreaName).filter(StrUtil::isNotEmpty).distinct().toList();
        Map<String, WarehouseArea> warehouseAreaMap = importCommonService.getEntity(ImportExcelTypeEnum.WAREHOUSE_AREA,areaNameList, ImportExceptionEnum.WAREHOUSE_ZONE_AREA_EMPTY,WarehouseArea.class);
        // 三: 校验归属性
        // 3.1: 区域 --是否--> 归属的仓库
        Map<String, String> bindAreaWarehouseMap = MapUtil.newHashMap();
        for (WarehouseZoneExcelItem item : warehouseZoneExcelItems) {
            bindAreaWarehouseMap = importCommonService.checkAreaWarehouseBind(item.getAreaName(), item.getWarehouseName(), warehouseAreaMap, warehouseMap);
        }
        importCommonService.checkBindError(bindAreaWarehouseMap, ImportExceptionEnum.WAREHOUSE_ZONE_AREA_BIND_WAREHOUSE_EMPTY);
        List<WarehouseZone> warehouseZoneList = CollectionUtil.newArrayList();
        for (WarehouseZoneExcelItem warehouseZoneExcelItem : warehouseZoneExcelItems) {
            WarehouseZoneItem warehouseZoneItem = new WarehouseZoneItem();
            warehouseZoneItem.setZoneCode(numberGenerator.nextValue(IdRuleConstant.WAREHOUSE_ZONE_CODE));
            warehouseZoneItem.setZoneName(warehouseZoneExcelItem.getZoneName());
            WarehouseArea area = warehouseAreaMap.get(warehouseZoneExcelItem.getWarehouseName() + "+" +warehouseZoneExcelItem.getAreaName());
            if (Objects.nonNull(area)){
                warehouseZoneItem.setAreaId(area.getId());
            }
            warehouseZoneItem.setAreaName(warehouseZoneExcelItem.getAreaName());
            warehouseZoneItem.setWarehouseId(MapUtil.getLong(warehouseMap, warehouseZoneExcelItem.getWarehouseName()));
            warehouseZoneItem.setWarehouseName(warehouseZoneExcelItem.getWarehouseName());
            warehouseZoneItem.setType(warehouseZoneExcelItem.getType());
            warehouseZoneItem.setRemark(warehouseZoneExcelItem.getRemark());
            warehouseZoneItem.setIsDefault(YesNoEnum.NO.getCode());
            warehouseZoneItem.setSource(WmsDataSourceTypeEnum.CREATE.getCode());
            WarehouseZone warehouseZone = new WarehouseZone();
            BeanUtil.copyProperties(warehouseZoneItem, warehouseZone);
            warehouseZoneService.preSave(warehouseZone);
            warehouseZoneList.add(warehouseZone);
        }
        return getResponseData(CollectionUtil.isNotEmpty(warehouseZoneList), warehouseZoneService.batchInsert(warehouseZoneList));
    }

    /**
     * 货架
     *
     * @param warehouseShelfExcelItems
     * @return ResponseData
     */
    public ResponseData warehouseShelfImportDataHandler(List<WarehouseShelfExcelItem> warehouseShelfExcelItems) {
        // 查询库区
        List<String> zoneCodeList = warehouseShelfExcelItems.stream().map(WarehouseShelfExcelItem::getZoneCode).distinct().toList();
        List<WarehouseZone> warehouseZoneList = warehouseZoneService.listByZoneCodeList(zoneCodeList);
        Map<String, WarehouseZone> zoneCodeMap = warehouseZoneList.stream().collect(Collectors.toMap(WarehouseZone::getZoneCode, e -> e));
        ArrayList<String> zoneCode = new ArrayList<>();
        Map<String, List<String>> nameCodeMap = new HashMap<>();
        for (WarehouseShelfExcelItem warehouseShelfExcelItem : warehouseShelfExcelItems) {
            WarehouseZone zone = MapUtil.get(zoneCodeMap, warehouseShelfExcelItem.getZoneCode(), WarehouseZone.class);
            if (zone == null){
                zoneCode.add(warehouseShelfExcelItem.getZoneCode());
                break;
            }
            if (!zone.getZoneName().equals(warehouseShelfExcelItem.getZoneName())){
                nameCodeMap.put(warehouseShelfExcelItem.getShelfName(), Collections.singletonList(warehouseShelfExcelItem.getZoneName()));
                break;
            }
            warehouseShelfExcelItem.setWarehouseName(zone.getWarehouseName());
            warehouseShelfExcelItem.setZoneId(zone.getId());
        }
        if (CollectionUtil.isNotEmpty(zoneCode)){
            throw new ServiceException(ImportExceptionEnum.WAREHOUSE_SHELF_CODE_ZONE_EMPTY,zoneCode);
        }
        if (CollectionUtil.isNotEmpty(nameCodeMap)){
            importCommonService.importExistError(nameCodeMap,ImportExceptionEnum.WAREHOUSE_SHELF_ZONE_EMPTY);
        }

        // 一：校验货架名称是否重复
        Map<String, List<String>> warehouseNameMap = MapObjectUtil.getMap(warehouseShelfExcelItems, WarehouseShelfExcelItem::getWarehouseName, WarehouseShelfExcelItem::getShelfName);
        importCommonService.checkDuplicate(warehouseNameMap, ImportExceptionEnum.WAREHOUSE_SHELF_DUPLICATE);
        // 二：是否存在
        // 2.1：货架名称 -- 存在
        List<String> shelfNameList = warehouseShelfExcelItems.stream().map(WarehouseShelfExcelItem::getShelfName).distinct().toList();
        Long[] zoneIds = warehouseZoneList.stream().map(WarehouseZone::getId).toArray(Long[]::new);
        WarehouseShelfCondition shelfCondition = new WarehouseShelfCondition();
        shelfCondition.setZoneIds(zoneIds);
        shelfCondition.setShelfNameList(shelfNameList);
        List<WarehouseShelfQuery> exsitNameList = warehouseShelfService.listGetByShelfName(shelfCondition);
        importCommonService.checkWarehouse(warehouseNameMap,exsitNameList, WarehouseShelfQuery::getWarehouseName, WarehouseShelfQuery::getShelfName,ImportExceptionEnum.WAREHOUSE_SHELF_EXIST);

        List<WarehouseShelf> warehouseShelfList = CollectionUtil.newArrayList();
        for (WarehouseShelfExcelItem warehouseShelfExcelItem : warehouseShelfExcelItems) {
            WarehouseShelfItem warehouseShelfItem = new WarehouseShelfItem();
            warehouseShelfItem.setShelfName(warehouseShelfExcelItem.getShelfName());
            WarehouseZone zone = MapUtil.get(zoneCodeMap, warehouseShelfExcelItem.getZoneCode(), WarehouseZone.class);
            warehouseShelfItem.setZoneId(zone.getId());
            warehouseShelfItem.setZoneName(warehouseShelfExcelItem.getZoneName());
            warehouseShelfItem.setType(warehouseShelfExcelItem.getType());
            warehouseShelfItem.setVolume(Convert.toBigDecimal(warehouseShelfExcelItem.getVolume()));
            warehouseShelfItem.setShelfCode(numberGenerator.nextValue(IdRuleConstant.WAREHOUSE_SHELF_CODE));
            warehouseShelfItem.setWarehouseId(zone.getWarehouseId());
            warehouseShelfItem.setWarehouseName(zone.getWarehouseName());
            WarehouseShelf warehouseShelf = new WarehouseShelf();
            BeanUtil.copyProperties(warehouseShelfItem, warehouseShelf);
            warehouseShelfService.preSave(warehouseShelf);
            warehouseShelfList.add(warehouseShelf);
        }
        return getResponseData(CollectionUtil.isNotEmpty(warehouseShelfList), warehouseShelfService.batchInsert(warehouseShelfList));
    }

    /**
     * 库位
     *
     * @param warehouseLocExcelItems
     * @return ResponseData
     */
    public ResponseData warehouseLocImportDataHandler(List<WarehouseLocExcelItem> warehouseLocExcelItems) {
        // 一: 校验是否重复
        // 1.1：库位代码是否重复
        Map<String, List<String>> warehouseNameMap = MapObjectUtil.getMap(warehouseLocExcelItems, WarehouseLocExcelItem::getWarehouseName, WarehouseLocExcelItem::getLocName);
        importCommonService.checkDuplicate(warehouseNameMap, ImportExceptionEnum.WAREHOUSE_LOC_NAME_DUPLICATE);
        // 1.2：库位名称是否重复
        Map<String, List<String>> warehouseCodeMap = MapObjectUtil.getMap(warehouseLocExcelItems, WarehouseLocExcelItem::getWarehouseName, WarehouseLocExcelItem::getLocCode);
        importCommonService.checkDuplicate(warehouseCodeMap, ImportExceptionEnum.WAREHOUSE_LOC_CODE_DUPLICATE);
        // 二: 校验是否存在
        // 2.1: 库位代码 -- 存在
        List<String> locCodeList = warehouseLocExcelItems.stream().map(WarehouseLocExcelItem::getLocCode).distinct().toList();
        List<WarehouseLoc> exsitCodeList = warehouseLocService.listByLocCodeList(locCodeList);
        importCommonService.checkWarehouse(warehouseNameMap,exsitCodeList, WarehouseLoc::getWarehouseName, WarehouseLoc::getLocCode,ImportExceptionEnum.WAREHOUSE_LOC_CODE_EXIST);
        // 1.3 code是否合法
        importCommonService.isValidCode(locCodeList, ImportExceptionEnum.IMPORT_DATA_FAIL_CODE_VALID);
        // 2.2: 库位名称 -- 存在
        List<String> locNameList = warehouseLocExcelItems.stream().map(WarehouseLocExcelItem::getLocName).distinct().toList();
        List<WarehouseLoc> exsitNameList = warehouseLocService.listByLocNameList(locNameList);
        importCommonService.checkWarehouse(warehouseNameMap,exsitNameList, WarehouseLoc::getWarehouseName, WarehouseLoc::getLocName,ImportExceptionEnum.WAREHOUSE_LOC_NAME_EXIST);

        // 2.3: 所属仓库 -- 获取<仓库名称，仓库id>
        List<String> warehouseNameList = warehouseLocExcelItems.stream().map(WarehouseLocExcelItem::getWarehouseName).filter(StrUtil::isNotEmpty).distinct().toList();
        Map<String, Long> warehouseMap = importCommonService.getEntity(ImportExcelTypeEnum.WAREHOUSE,warehouseNameList, ImportExceptionEnum.WAREHOUSE_LOC_WAREHOUSE_EMPTY,Long.class);
        // 2.4: 所属库区 -- 获取<仓库名称+库区名称，库区信息>
        List<String> zoneNameList = warehouseLocExcelItems.stream().map(WarehouseLocExcelItem::getZoneName).distinct().filter(StrUtil::isNotEmpty).distinct().toList();
        Map<String, WarehouseZone> warehouseZoneMap = importCommonService.getEntity(ImportExcelTypeEnum.WAREHOUSE_ZONE,zoneNameList, ImportExceptionEnum.WAREHOUSE_LOC_ZONE_EMPTY,WarehouseZone.class);
        // 2.5: 所属区域 -- 获取<区域名称，区域信息>
        List<String> areaNameList = warehouseLocExcelItems.stream().map(WarehouseLocExcelItem::getAreaName).filter(StrUtil::isNotEmpty).distinct().toList();
        Map<String, WarehouseArea> warehouseAreaMap = importCommonService.getEntity(ImportExcelTypeEnum.WAREHOUSE_AREA,areaNameList, ImportExceptionEnum.WAREHOUSE_LOC_AREA_EMPTY,WarehouseArea.class);
        // 2.6: 所属货架 -- 获取<库区id+货架名称，货架信息>
        List<String> shelfNameList = warehouseLocExcelItems.stream().map(WarehouseLocExcelItem::getShelfName).filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, WarehouseShelf> warehouseShelfMap = importCommonService.getEntity(ImportExcelTypeEnum.WAREHOUSE_SHELF,shelfNameList, ImportExceptionEnum.WAREHOUSE_LOC_SHELF_EMPTY,WarehouseShelf.class);

        // 三: 校验归属性 todo
        // 3.1: 库区 --> 仓库
        Map<String, String> bindWarehouseZoneWarehouseMap = MapUtil.newHashMap();
        for (WarehouseLocExcelItem item : warehouseLocExcelItems) {
            WarehouseZone zone = MapUtil.get(warehouseZoneMap, item.getWarehouseName()+"+"+item.getZoneName(), WarehouseZone.class);
            if (ObjUtil.isNull(zone) || ObjUtil.notEqual(item.getWarehouseName(), zone.getWarehouseName())) {
                bindWarehouseZoneWarehouseMap.put(item.getZoneName(), item.getWarehouseName());
            }
        }
        importCommonService.checkBindError(bindWarehouseZoneWarehouseMap, ImportExceptionEnum.WAREHOUSE_LOC_ZONE_BIND_WAREHOUSE_EMPTY);
        // 3.2: 库区 --> 区域
        Map<String, String> bindAreaWarehouseMap = MapUtil.newHashMap();
        for (WarehouseLocExcelItem item : warehouseLocExcelItems) {
            bindAreaWarehouseMap = importCommonService.checkZoneAreaBind(item.getWarehouseName(), item.getAreaName(), item.getZoneName(), warehouseAreaMap, warehouseZoneMap);
        }
        importCommonService.checkBindError(bindAreaWarehouseMap, ImportExceptionEnum.WAREHOUSE_LOC_AREA_BIND_WAREHOUSE_EMPTY);
        // 3.3: 货架 --> 库区
        Map<String, String> bindWarehouseShelfZoneMap = MapUtil.newHashMap();
        for (WarehouseLocExcelItem item : warehouseLocExcelItems) {
            if (EmptyUtil.isEmpty(item.getShelfName())) {
                continue;
            }
            WarehouseZone zone = MapUtil.get(warehouseZoneMap, item.getWarehouseName() + "+" + item.getZoneName(), WarehouseZone.class);
            WarehouseShelf shelf = MapUtil.get(warehouseShelfMap, zone.getId() + "+" + item.getShelfName(), WarehouseShelf.class);
            if (ObjUtil.isNull(zone) || ObjUtil.notEqual(zone.getId(), shelf.getZoneId())) {
                bindWarehouseShelfZoneMap.put(item.getShelfName(), item.getZoneName());
            }
        }
        importCommonService.checkBindError(bindWarehouseShelfZoneMap, ImportExceptionEnum.WAREHOUSE_LOC_SHELF_BIND_ZONE_EMPTY);
        // 四: 赋值
        List<WarehouseLoc> warehouseLocList = CollectionUtil.newArrayList();
        for (WarehouseLocExcelItem warehouseLocExcelItem : warehouseLocExcelItems) {
            WarehouseLocItem warehouseLocItem = new WarehouseLocItem();
            warehouseLocItem.setLocCode(warehouseLocExcelItem.getLocCode());
            warehouseLocItem.setLocName(warehouseLocExcelItem.getLocName());
            warehouseLocItem.setLocUseType(warehouseLocExcelItem.getLocUseType());
            warehouseLocItem.setIsEnable(warehouseLocExcelItem.getIsEnable());
            warehouseLocItem.setPaSeq(warehouseLocExcelItem.getPaSeq());
            warehouseLocItem.setPkSeq(warehouseLocExcelItem.getPkSeq());
            warehouseLocItem.setAbc(warehouseLocExcelItem.getAbc());
            warehouseLocItem.setLength(Convert.toBigDecimal(warehouseLocExcelItem.getLength()));
            warehouseLocItem.setWidth(Convert.toBigDecimal(warehouseLocExcelItem.getWidth()));
            warehouseLocItem.setHeight(Convert.toBigDecimal(warehouseLocExcelItem.getHeight()));
            warehouseLocItem.setCategory(warehouseLocExcelItem.getCategory());
            warehouseLocItem.setRemark(warehouseLocExcelItem.getRemark());
            //库区上有区域，仓库，库区的相关信息
            WarehouseZone warehouseZone = warehouseZoneMap.get(warehouseLocExcelItem.getWarehouseName() +"+"+warehouseLocExcelItem.getZoneName());
            warehouseLocItem.setWarehouseId(warehouseZone.getWarehouseId());
            warehouseLocItem.setWarehouseName(warehouseLocExcelItem.getWarehouseName());
            warehouseLocItem.setAreaId(warehouseZone.getAreaId());
            warehouseLocItem.setAreaName(warehouseLocExcelItem.getAreaName());
            warehouseLocItem.setZoneId(warehouseZone.getId());
            warehouseLocItem.setZoneName(warehouseLocExcelItem.getZoneName());
            WarehouseShelf shelf = warehouseShelfMap.get(warehouseZone.getId() + "+" + warehouseLocExcelItem.getShelfName());
            if (Objects.nonNull(shelf)){
                warehouseLocItem.setShelfId(shelf.getId());
            }
            warehouseLocItem.setShelfName(warehouseLocExcelItem.getShelfName());
            warehouseLocItem.setIsMixSku(EmptyUtil.isNotEmpty(warehouseLocExcelItem.getMaxMixSku()) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
            warehouseLocItem.setIsMixLot(EmptyUtil.isNotEmpty(warehouseLocExcelItem.getMaxMixLot()) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
            warehouseLocItem.setMaxMixSku(EmptyUtil.isNotEmpty(warehouseLocExcelItem.getMaxMixSku()) ? Long.valueOf(warehouseLocExcelItem.getMaxMixSku()) : null);
            warehouseLocItem.setMaxMixLot(EmptyUtil.isNotEmpty(warehouseLocExcelItem.getMaxMixLot()) ? Long.valueOf(warehouseLocExcelItem.getMaxMixLot()) : null);
            warehouseLocItem.setSource(WmsDataSourceTypeEnum.CREATE.getCode());
            WarehouseLoc warehouseLoc = new WarehouseLoc();
            BeanUtil.copyProperties(warehouseLocItem, warehouseLoc);
            warehouseLoc.setIsLoseId(StatusEnum.YES.getCode());
            warehouseLoc.setIsDefault(YesNoEnum.NO.getCode());
            warehouseLocService.preSave(warehouseLoc);
            warehouseLocList.add(warehouseLoc);
        }
        ResponseData responseData = new ResponseData<>();
        if (CollectionUtil.isNotEmpty(warehouseLocList)) {
            int count = warehouseLocService.batchInsert(warehouseLocList);
            List<WarehouseLocFile> warehouseLocFileList = CollectionUtil.newArrayList();
            for (WarehouseLoc warehouseLoc : warehouseLocList) {
                FileObject fileObject = null;
                try {
                    fileObject = customFile.barcodeGenerate(warehouseLoc.getLocCode());
                } catch (Exception e) {
                    throw new ServiceException(GlobalExceptionEnum.BARCODE_GENERATION_FAILED.getMsg());
                }
                WarehouseLocFile warehouseLocFile = new WarehouseLocFile();
                warehouseLocFile.setBusinessId(Convert.toStr(warehouseLoc.getId()));
                warehouseLocFile.setFileId(fileObject.getUuid());
                warehouseLocFile.setFileName(fileObject.getOrigFileName());
                warehouseLocFile.setFileType(FileTypeEnum.png.getCode());
                warehouseLocFile.setFilePath(fileObject.getUrl());
                warehouseLocFile.setOriginalName(fileObject.getOrigFileName());
                warehouseLocFile.setFileSize(Convert.toStr(fileObject.getFileSize()));
                warehouseLocFileList.add(warehouseLocFile);
            }
            warehouseLocFileDao.batchInsert(warehouseLocFileList);
            responseData.setMsg(String.format(ImportExceptionEnum.IMPORT_DATA_SUCCESS.getMsg(), Convert.toStr(count)));
            responseData.setSuccess(true);
            responseData.setCode(200);
        }
        return responseData;
    }

    /**
     * 货主
     *
     * @param ownerExcelItems
     * @return ResponseData
     */
    public ResponseData ownerImportDataHandler(List<OwnerExcelItem> ownerExcelItems) {
        // 校验省市区代码是否存在
        Map<String, String> pccMap = MapUtil.newHashMap();
        if (BooleanUtil.isTrue(ownerExcelItems.stream().anyMatch(item -> StrUtil.isNotEmpty(item.getProvinceName()) || StrUtil.isNotEmpty(item.getCityName()) || StrUtil.isNotEmpty(item.getCountyName())))) {
            List<OwnerExcelItem> tempOwnerExcelItems = ownerExcelItems.stream().filter(ownerExcelItem -> StrUtil.isAllNotEmpty(ownerExcelItem.getProvinceName(), ownerExcelItem.getCityName(), ownerExcelItem.getCountyName())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(tempOwnerExcelItems)) {
                throw new ServiceException(ImportExceptionEnum.IMPORT_DATA_PCC_NO_COMPLETE);
            }
            List<List<String>> pccNameList = tempOwnerExcelItems.stream().map(ownerExcelItem -> CollectionUtil.newArrayList(ownerExcelItem.getProvinceName(), ownerExcelItem.getCityName(), ownerExcelItem.getCountyName())).distinct().collect(Collectors.toList());
            pccMap = importCommonService.checkPcc(pccNameList);

        }
        List<Owner> ownerList = CollectionUtil.newArrayList();
        for (OwnerExcelItem ownerExcelItem : ownerExcelItems) {
            OwnerItem ownerItem = new OwnerItem();
            ownerItem.setOwnerCode(numberGenerator.nextValue(IdRuleConstant.OWNER_CODE));
            ownerItem.setOwnerName(ownerExcelItem.getOwnerName());
            ownerItem.setOwnerType(ownerExcelItem.getOwnerType());
            ownerItem.setOwnerAllName(ownerExcelItem.getOwnerAllName());
            ownerItem.setTel(ownerExcelItem.getTel());
            ownerItem.setProvinceCode(MapUtil.getStr(pccMap, ownerExcelItem.getProvinceName()));
            ownerItem.setProvinceName(ownerExcelItem.getProvinceName());
            ownerItem.setCityCode(MapUtil.getStr(pccMap, ownerExcelItem.getCityName()));
            ownerItem.setCityName(ownerExcelItem.getCityName());
            ownerItem.setCountyCode(MapUtil.getStr(pccMap, ownerExcelItem.getCountyName()));
            ownerItem.setCountyName(ownerExcelItem.getCountyName());
            ownerItem.setAddress(ownerExcelItem.getAddress());
            ownerItem.setBusinessRegistraTionNumber(ownerExcelItem.getBusinessRegistraTionNumber());
            ownerItem.setIndustryCategory(ownerExcelItem.getIndustryCategory());
            ownerItem.setMainBusiness(ownerExcelItem.getMainBusiness());
            ownerItem.setInformationSources(WmsDataSourceTypeEnum.CREATE.getCode());
            Owner owner = new Owner();
            BeanUtil.copyProperties(ownerItem, owner);
            owner.setStatus(StatusEnum.YES.getCode());
            ownerService.preSave(owner);
            ownerList.add(owner);
        }
        return getResponseData(CollectionUtil.isNotEmpty(ownerList), ownerService.batchInsert(ownerList));
    }

    /**
     * 客户
     *
     * @param customerExcelItems
     * @return ResponseData
     */
    public ResponseData customerImportDataHandler(List<CustomerExcelItem> customerExcelItems) {
        // 校验省市区代码是否存在
        Map<String, String> pccMap = MapUtil.newHashMap();
        if (BooleanUtil.isTrue(customerExcelItems.stream().anyMatch(ownerExcelItem -> StrUtil.isNotEmpty(ownerExcelItem.getProvinceName()) || StrUtil.isNotEmpty(ownerExcelItem.getCityName()) || StrUtil.isNotEmpty(ownerExcelItem.getCountyName())))) {
            List<CustomerExcelItem> tempOwnerExcelItems = customerExcelItems.stream().filter(item -> StrUtil.isAllNotEmpty(item.getProvinceName(), item.getCityName(), item.getCountyName())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(tempOwnerExcelItems)) {
                throw new ServiceException(ImportExceptionEnum.IMPORT_DATA_PCC_NO_COMPLETE);
            }
            List<List<String>> pccNameList = tempOwnerExcelItems.stream().map(ownerExcelItem -> CollectionUtil.newArrayList(ownerExcelItem.getProvinceName(), ownerExcelItem.getCityName(), ownerExcelItem.getCountyName())).distinct().collect(Collectors.toList());
            pccMap = importCommonService.checkPcc(pccNameList);
        }
        List<Customer> customerList = CollectionUtil.newArrayList();
        for (CustomerExcelItem customerExcelItem : customerExcelItems) {
            CustomerItem customerItem = new CustomerItem();
            customerItem.setCustomerCode(numberGenerator.nextValue(IdRuleConstant.CUSTOMER_CODE));
            customerItem.setCustomerName(customerExcelItem.getCustomerName());
            customerItem.setCustomerType(customerExcelItem.getCustomerType());
            customerItem.setCustomerAllName(customerExcelItem.getCustomerAllName());
            customerItem.setTel(customerExcelItem.getTel());
            customerItem.setProvinceCode(MapUtil.getStr(pccMap, customerExcelItem.getProvinceName()));
            customerItem.setProvinceName(customerExcelItem.getProvinceName());
            customerItem.setCityCode(MapUtil.getStr(pccMap, customerExcelItem.getCityName()));
            customerItem.setCityName(customerExcelItem.getCityName());
            customerItem.setCountyCode(MapUtil.getStr(pccMap, customerExcelItem.getCountyName()));
            customerItem.setCountyName(customerExcelItem.getCountyName());
            customerItem.setAddress(customerExcelItem.getAddress());
            customerItem.setBusinessRegistraTionNumber(customerExcelItem.getBusinessRegistraTionNumber());
            customerItem.setIndustryCategory(customerExcelItem.getIndustryCategory());
            customerItem.setMainBusiness(customerExcelItem.getMainBusiness());
            customerItem.setInformationSources(WmsDataSourceTypeEnum.CREATE.getCode());
            Customer customer = new Customer();
            BeanUtil.copyProperties(customerItem, customer);
            customer.setStatus(StatusEnum.YES.getCode());
            customerService.preSave(customer);
            customerList.add(customer);
        }
        return getResponseData(CollectionUtil.isNotEmpty(customerList), customerService.batchInsert(customerList));
    }

    /**
     * 供应商
     *
     * @param supplierExcelItems
     * @return ResponseData
     */
    public ResponseData supplierImportDataHandler(List<SupplierExcelItem> supplierExcelItems) {
        List<Supplier> supplierList = CollectionUtil.newArrayList();
        for (SupplierExcelItem supplierExcelItem : supplierExcelItems) {
            SupplierItem supplierItem = new SupplierItem();
            supplierItem.setSupplierCode(numberGenerator.nextValue(IdRuleConstant.SUPPLIER_CODE));
            supplierItem.setSupplierName(supplierExcelItem.getSupplierName());
            supplierItem.setAddress(supplierExcelItem.getAddress());
            supplierItem.setTel(supplierExcelItem.getTel());
            Supplier supplier = new Supplier();
            BeanUtil.copyProperties(supplierItem, supplier);
            supplier.setStatus(StatusEnum.YES.getCode());
            supplierService.preSave(supplier);
            supplierList.add(supplier);
        }
        return getResponseData(CollectionUtil.isNotEmpty(supplierList), supplierService.batchInsert(supplierList));
    }

    /**
     * 承运商
     *
     * @param carrierExcelItems
     * @return ResponseData
     */
    public ResponseData carrierImportDataHandler(List<CarrierExcelItem> carrierExcelItems) {
        // 校验省市区代码是否存在
        Map<String, String> pccMap = MapUtil.newHashMap();
        if (BooleanUtil.isTrue(carrierExcelItems.stream().anyMatch(ownerExcelItem -> StrUtil.isNotEmpty(ownerExcelItem.getProvinceName()) || StrUtil.isNotEmpty(ownerExcelItem.getCityName()) || StrUtil.isNotEmpty(ownerExcelItem.getCountyName())))) {
            List<CarrierExcelItem> tempOwnerExcelItems = carrierExcelItems.stream().filter(item -> StrUtil.isAllNotEmpty(item.getProvinceName(), item.getCityName(), item.getCountyName())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(tempOwnerExcelItems)) {
                throw new ServiceException(ImportExceptionEnum.IMPORT_DATA_PCC_NO_COMPLETE);
            }
            List<List<String>> pccNameList = tempOwnerExcelItems.stream().map(ownerExcelItem -> CollectionUtil.newArrayList(ownerExcelItem.getProvinceName(), ownerExcelItem.getCityName(), ownerExcelItem.getCountyName())).distinct().collect(Collectors.toList());
            pccMap = importCommonService.checkPcc(pccNameList);
        }
        List<Carrier> carrierList = CollectionUtil.newArrayList();
        for (CarrierExcelItem carrierExcelItem : carrierExcelItems) {
            CarrierItem carrierItem = new CarrierItem();
            carrierItem.setCarrierName(carrierExcelItem.getCarrierName());
            carrierItem.setCarrierCode(numberGenerator.nextValue(IdRuleConstant.CYS_CODE));
            carrierItem.setCarrierType(carrierExcelItem.getCarrierType());
            carrierItem.setSettlementCode(carrierExcelItem.getSettlementCode());
            carrierItem.setProvinceCode(MapUtil.getStr(pccMap, carrierExcelItem.getProvinceName()));
            carrierItem.setProvinceName(carrierExcelItem.getProvinceName());
            carrierItem.setCityCode(MapUtil.getStr(pccMap, carrierExcelItem.getCityName()));
            carrierItem.setCityName(carrierExcelItem.getCityName());
            carrierItem.setCountyCode(MapUtil.getStr(pccMap, carrierExcelItem.getCountyName()));
            carrierItem.setCountyName(carrierExcelItem.getCountyName());
            carrierItem.setAddress(carrierExcelItem.getAddress());
            if (StrUtil.isNotEmpty(carrierExcelItem.getContact())) {
                carrierItem.setContact(SimpleSecureAesUtil.encryptName(carrierExcelItem.getContact()));
            }
            carrierItem.setTel(carrierExcelItem.getTel());
            Carrier carrier = new Carrier();
            BeanUtil.copyProperties(carrierItem, carrier);
            carrier.setStatus(StatusEnum.YES.getCode());
            carrierService.preSave(carrier);
            carrierList.add(carrier);
        }
        ResponseData responseData = new ResponseData<>();
        if (CollectionUtil.isNotEmpty(carrierList)) {
            int count = carrierService.batchInsert(carrierList);
            freightConfigService.initCarrierFreightConfigList(carrierList,SessionContext.get());
            responseData.setMsg(String.format(ImportExceptionEnum.IMPORT_DATA_SUCCESS.getMsg(), Convert.toStr(count)));
            responseData.setSuccess(true);
            responseData.setCode(200);
        }
        return responseData;
    }

    @Autowired
    private TaskExecutor taskExecutor;
    /**
     * 包装
     *
     * @param packageExcelItems
     * @return ResponseData
     */
    public ResponseData packageImportDataHandler(List<PackageExcelItem> packageExcelItems) {
        // 一：校验 包装名称是否重复
        Map<String, Long> locCodeMap = packageExcelItems.stream().collect(Collectors.groupingBy(PackageExcelItem::getName, Collectors.counting()));
        importCommonService.checkDuplicateName(locCodeMap, ImportExceptionEnum.PACKAGE_NAME_DUPLICATE);
        // 二：校验 包装名称是否存在
        List<String> packageNameList = locCodeMap.keySet().stream().toList();
        List<Package> packageList = packageService.listByName(packageNameList);
        if (CollectionUtil.isNotEmpty(packageList)) {
            throw new ServiceException(ImportExceptionEnum.PACKAGE_NAME_EXIST, StrUtil.join(StrUtil.COMMA, packageList.stream().map(Package::getName).toList()));
        }
        // 三：赋值
        for (PackageExcelItem packageExcelItem : packageExcelItems) {
            PackageItem packageItem = new PackageItem();
            packageItem.setCode(numberGenerator.nextValue(IdRuleConstant.PACKAGE_CODE));
            packageItem.setName(packageExcelItem.getName());
            packageItem.setType(packageExcelItem.getType());
            packageItem.setRemark(packageExcelItem.getRemark());
            packageItem.setInformationSources(WmsDataSourceTypeEnum.CREATE.getCode());
            Package csPackage = new Package();
            BeanUtil.copyProperties(packageItem, csPackage);
            packageService.preSave(csPackage);
            packageList.add(csPackage);
        }
        ResponseData responseData = new ResponseData<>();
        if (CollectionUtil.isNotEmpty(packageList)) {
            int count = packageService.batchInsert(packageList);
            for (Package csPackage : packageList) {
                packageUnitService.generateDefaultPackageUnits(csPackage.getId());
            }
            responseData.setMsg(String.format(ImportExceptionEnum.IMPORT_DATA_SUCCESS.getMsg(), Convert.toStr(count)));
            responseData.setSuccess(true);
            responseData.setCode(200);
        }
        return responseData;
    }

    /**
     * 商品
     *
     * @param skuExcelItems
     * @return ResponseData
     */
    public ResponseData skuImportDataHandler(List<SkuExcelItem> skuExcelItems) {
        // 一：校验重复性
        // 1.1：商品编码是否重复
        Map<String, Long> locCodeMap = skuExcelItems.stream().collect(Collectors.groupingBy(SkuExcelItem::getSkuCode, Collectors.counting()));
        importCommonService.checkDuplicateName(locCodeMap, ImportExceptionEnum.SKU_CODE_DUPLICATE);
        // 二: 校验是否存在
        // 2.1：商品代码 -- 存在
        List<String> skuCodeList = skuExcelItems.stream().map(SkuExcelItem::getSkuCode).toList();
        importCommonService.isValidCode(skuCodeList, ImportExceptionEnum.IMPORT_DATA_FAIL_CODE_VALID);
        List<Sku> existSkuList = skuService.listBySkuCode(skuCodeList);
        if (CollectionUtil.isNotEmpty(existSkuList)) {
            throw new ServiceException(ImportExceptionEnum.SKU_CODE_EXIST, StrUtil.join(StrUtil.COMMA, existSkuList.stream().map(Sku::getSkuCode).toList()));
        }
        // 2.2：所属货主 -- 获取 -- 名字可以重复，用code
        List<String> ownerCodeList = skuExcelItems.stream().map(SkuExcelItem::getOwnerCode).distinct().toList();
        Map<String, Long> ownerMap = importCommonService.getOwnerCode(ownerCodeList, skuExcelItems);
        // 2.3：包装规格 -- 获取
        List<String> packageNameList = skuExcelItems.stream().map(SkuExcelItem::getPackageName).filter(StrUtil::isNotEmpty).distinct().toList();
        Map<String, Package> packageMap = importCommonService.getPackage(packageNameList);
        // 2.4：包装单位 -- 获取
        List<PackageUnitQuery> packageUnitList = packageUnitDao.findPackageUnitByPackageName(packageNameList);
        Map<String, Map<String, PackageUnitQuery>> packageUnitMap = packageUnitList.stream().collect(Collectors.groupingBy(PackageUnitQuery::getPackageName, Collectors.toMap(PackageUnitQuery::getName, Function.identity(), (k1, k2) -> k1)));
        // 2.5：批次属性规则
        List<String> lotRuleNameList = skuExcelItems.stream().map(SkuExcelItem::getLotRuleName).distinct().collect(Collectors.toList());
        Map<String, Long> warehouseLotMap = importCommonService.getWarehouseLot(lotRuleNameList);
        // 三：校验单位是否归属包装规格
        List<String> bindPackageUnitList = new ArrayList<>();
        for (int i = 0; i < skuExcelItems.size(); i++) {
            SkuExcelItem skuExcelItem = skuExcelItems.get(i);
            StringBuilder skuPackageUnitStr = new StringBuilder();
            Map<String, PackageUnitQuery> stringPackageUnitQueryMap = packageUnitMap.get(skuExcelItem.getPackageName());
            if (EmptyUtil.isEmpty(stringPackageUnitQueryMap)) {
                skuPackageUnitStr.append(StrUtil.LF).append(String.format(ImportExceptionEnum.SKU_DEFAULT_PACKAGE_UNIT_ERROR.getMsg(), Convert.toStr(i + 1)));
                bindPackageUnitList.add(skuPackageUnitStr.toString());
                continue;
            }
            List<String> packageUnitNameList = stringPackageUnitQueryMap.keySet().stream().toList();
            if (!new HashSet<>(packageUnitNameList).containsAll(Arrays.asList(skuExcelItem.getPrintUnitName(), skuExcelItem.getRcvUnitName(), skuExcelItem.getShipUnitName()))) {
                skuPackageUnitStr.append(StrUtil.LF).append(String.format(ImportExceptionEnum.SKU_PACKAGE_PRINT_UNIT_ERROR.getMsg(), Convert.toStr(i + 1), StrUtil.join(StrUtil.COMMA, packageUnitNameList)));
                bindPackageUnitList.add(skuPackageUnitStr.toString());
            }
        }
        if (CollectionUtil.isNotEmpty(bindPackageUnitList)) {
            throw new ServiceException(ImportExceptionEnum.IMPORT_DATA_FAIL_NO_COMPLETE, StrUtil.join(StrUtil.COMMA, bindPackageUnitList));
        }

        List<Sku> skuList = CollectionUtil.newArrayList();
        for (int i = 0; i < skuExcelItems.size(); i++) {
            SkuExcelItem skuExcelItem = skuExcelItems.get(i);
            SkuItem skuItem = new SkuItem();
            skuItem.setSkuCode(skuExcelItem.getSkuCode());
            skuItem.setSkuName(skuExcelItem.getSkuName());
            skuItem.setType(skuExcelItem.getType());
            Map<String, PackageUnitQuery> untilMap = packageUnitMap.get(skuExcelItem.getPackageName());
            skuItem.setPackageId(MapUtil.get(packageMap, skuExcelItem.getPackageName(), Package.class).getId());
            skuItem.setPackageName(skuExcelItem.getPackageName());
            skuItem.setPrintUnitId(MapUtil.get(untilMap, skuExcelItem.getPrintUnitName(), PackageUnitQuery.class).getId());
            skuItem.setPrintUnitName(skuExcelItem.getPrintUnitName());
            skuItem.setRcvUnitId(MapUtil.get(untilMap, skuExcelItem.getRcvUnitName(), PackageUnitQuery.class).getId());
            skuItem.setRcvUnitName(skuExcelItem.getRcvUnitName());
            skuItem.setShipUnitId(MapUtil.get(untilMap, skuExcelItem.getShipUnitName(), PackageUnitQuery.class).getId());
            skuItem.setShipUnitName(skuExcelItem.getShipUnitName());
            skuItem.setSource(WmsDataSourceTypeEnum.CREATE.getCode());
            skuItem.setOwnerId(MapUtil.getLong(ownerMap, skuExcelItem.getOwnerCode()));
            skuItem.setOwnerName(skuExcelItem.getOwnerName());
            skuItem.setLotId(MapUtil.getLong(warehouseLotMap, skuExcelItem.getLotRuleName()));
            skuItem.setLotName(skuExcelItem.getLotRuleName());
            skuItem.setBarCode(skuExcelItem.getBarCode());
            skuItem.setGrossWeight(Convert.toBigDecimal(skuExcelItem.getGrossWeight()));
            skuItem.setNetWeight(Convert.toBigDecimal(skuExcelItem.getGrossWeight()));
            skuItem.setWhetherSerialController(skuExcelItem.getWhetherSerialController());
            Sku sku = new Sku();
            BeanUtil.copyProperties(skuItem, sku);
            sku.setStatus(StatusEnum.YES.getCode());
            skuService.preSave(sku);
            skuList.add(sku);
        }
        return getResponseData(CollectionUtil.isNotEmpty(skuList), skuService.batchInsert(skuList));
    }

    /**
     * 容器号
     *
     * @param palletExcelItems
     * @return ResponseData
     */
    public ResponseData palletImportDataHandler(List<PalletExcelItem> palletExcelItems) {
        // 校验容器号所属仓库是否存在
        List<String> warehouseNameList = palletExcelItems.stream().map(PalletExcelItem::getWarehouseName).distinct().collect(Collectors.toList());
        Map<String, Long> warehouseMap = importCommonService.getEntity(ImportExcelTypeEnum.WAREHOUSE,warehouseNameList, ImportExceptionEnum.PALLET_WAREHOUSE_EMPTY,Long.class);

        List<Pallet> palletList = CollectionUtil.newArrayList();
        for (PalletExcelItem palletExcelItem : palletExcelItems) {
            PalletItem palletItem = new PalletItem();
            palletItem.setWarehouseId(MapUtil.getLong(warehouseMap, palletExcelItem.getWarehouseName()));
            palletItem.setWarehouseName(palletExcelItem.getWarehouseName());
            palletItem.setPalletNo(numberGenerator.nextValue(IdRuleConstant.PALLET));
            palletItem.setType(palletExcelItem.getType());
            palletItem.setStatus(palletExcelItem.getStatus());
            palletItem.setIsUsed(palletExcelItem.getIsUsed());
            Pallet pallet = new Pallet();
            BeanUtil.copyProperties(palletItem, pallet);
            pallet.setStatus(StrUtil.isEmpty(palletItem.getStatus()) ? StatusEnum.YES.getCode() : palletItem.getStatus());
            palletService.preSave(pallet);
            palletList.add(pallet);
        }
        ResponseData responseData = new ResponseData<>();
        if (CollectionUtil.isNotEmpty(palletList)) {
            int count = palletService.batchInsert(palletList);
            List<PalletFile> palletFileList = CollectionUtil.newArrayList();
            for (Pallet pallet : palletList) {
                FileObject fileObject = null;
                try {
                    fileObject = customFile.barcodeGenerate(pallet.getPalletNo());
                } catch (Exception e) {
                    throw new ServiceException(GlobalExceptionEnum.BARCODE_GENERATION_FAILED.getMsg());
                }
                PalletFile palletFile = new PalletFile();
                palletFile.setBusinessId(pallet.getId());
                palletFile.setFileId(fileObject.getUuid());
                palletFile.setFileName(fileObject.getOrigFileName());
                palletFile.setFileType(FileTypeEnum.png.getCode());
                palletFile.setFilePath(fileObject.getUrl());
                palletFile.setOriginalName(fileObject.getOrigFileName());
                palletFile.setFileSize(Convert.toStr(fileObject.getFileSize()));
                palletFileList.add(palletFile);
            }
            palletFileDao.batchInsert(palletFileList);
            responseData.setMsg(String.format(ImportExceptionEnum.IMPORT_DATA_SUCCESS.getMsg(), Convert.toStr(count)));
            responseData.setSuccess(true);
            responseData.setCode(200);
        }
        return responseData;
    }

    /**
     * 序列号
     *
     * @param asnDetailExcelItems
     * @param paramsMap
     * @return ResponseData
     */
    public ResponseData asnDetailImportDataHandler(List<AsnDetailExcelItem> asnDetailExcelItems, JSONObject paramsMap) {
        if (MapUtil.isEmpty(paramsMap)) {
            throw new RuntimeException("缺少必要参数");
        }
        Long ownerId = Convert.toLong(paramsMap.get("ownerId"));
        String asnNo = Convert.toStr(paramsMap.get("asnNo"));
        String asnType = Convert.toStr(paramsMap.get("asnType"));
        // 一：校验重复
        // 1.1：校验SN码是否重复
        Map<String, Long> snNoMap = asnDetailExcelItems.stream().collect(Collectors.groupingBy(AsnDetailExcelItem::getSnNo, Collectors.counting()));
        importCommonService.checkDuplicateName(snNoMap, ImportExceptionEnum.ASN_DETAIL_SN_NO_DUPLICATE);
        // 二：校验是否存在
        // 2.1：SN码 -- 存在
        List<String> snNoList = asnDetailExcelItems.stream().map(AsnDetailExcelItem::getSnNo).distinct().toList();
        importCommonService.getSn(snNoList,asnNo,asnType);
        // 2.2：商品 -- 存在
        Map<String, SkuInfo> skuInfoMap = importCommonService.getSku(asnDetailExcelItems,ownerId);
        Map<String, AsnDetailInfo> asnDetailMap = MapUtil.newHashMap();
        // 三：组装并返回数据给前端
        for (AsnDetailExcelItem asnDetailExcelItem : asnDetailExcelItems) {
            SkuInfo skuInfo = MapUtil.get(skuInfoMap, asnDetailExcelItem.getSkuCode(), SkuInfo.class);
            AsnDetailInfo asnDetailInfo = null;
            List<AsnDetailSnItem> asnDetailSnItemList = null;
            if (MapUtil.isNotEmpty(asnDetailMap) && asnDetailMap.containsKey(skuInfo.getSkuCode())) {
                asnDetailInfo = MapUtil.get(asnDetailMap, skuInfo.getSkuCode(), AsnDetailInfo.class);
                asnDetailSnItemList = asnDetailInfo.getAsnDetailSns();
                asnDetailInfo.setQtyPlanEa(NumberUtil.add(asnDetailInfo.getQtyPlanEa(), BigDecimal.ONE));
                asnDetailInfo.setQtyPlan(NumberUtil.add(asnDetailInfo.getQtyPlan(), BigDecimal.ONE));
            } else {
                asnDetailInfo = new AsnDetailInfo();
                asnDetailSnItemList = CollectionUtil.newArrayList();
                BeanUtil.copyProperties(skuInfo, asnDetailInfo);
                asnDetailInfo.setQtyPlan(BigDecimal.ONE);
                asnDetailInfo.setQtyPlanEa(BigDecimal.ONE);
            }
            AsnDetailSnItem asnDetailSnItem = new AsnDetailSnItem();
            asnDetailSnItem.setBoxCode(asnDetailExcelItem.getBoxCode());
            asnDetailSnItem.setSnNo(asnDetailExcelItem.getSnNo());
            asnDetailSnItemList.add(asnDetailSnItem);
            asnDetailInfo.setAsnDetailSns(asnDetailSnItemList);
            asnDetailMap.put(skuInfo.getSkuCode(), asnDetailInfo);
        }
        ResponseData responseData = new ResponseData<>();
        if (MapUtil.isNotEmpty(asnDetailMap)) {
            List<AsnDetailInfo> asnDetailInfoList = Convert.toList(AsnDetailInfo.class, asnDetailMap.values());
            int count = CollectionUtil.size(asnDetailInfoList);
            responseData.setMsg(String.format(ImportExceptionEnum.IMPORT_DATA_SUCCESS.getMsg(), Convert.toStr(count)));
            responseData.setSuccess(true);
            responseData.setCode(200);
            responseData.setData(asnDetailInfoList);
        }
        return responseData;
    }

    /**
     * 承运商分配规则
     *
     * @param ruleCarrierDistributionExcelItems
     * @return ResponseData
     */
    public ResponseData ruleCarrierDistributionImportDataHandler(List<RuleCarrierDistributionExcelItem> ruleCarrierDistributionExcelItems) {
        // 校验省份名称是否重复
        Map<String, Long> proviceNameMap = ruleCarrierDistributionExcelItems.stream().collect(Collectors.groupingBy(RuleCarrierDistributionExcelItem::getProvinceName, Collectors.counting()));
        importCommonService.checkDuplicateName(proviceNameMap, ImportExceptionEnum.RULE_CARRIER_DISTRIBUTION_PROVINCE_NAME_DUPLICATE);
        // 校验省份名称是否存在
        List<String> provinceNames = ruleCarrierDistributionExcelItems.stream().map(RuleCarrierDistributionExcelItem::getProvinceName).distinct().collect(Collectors.toList());
        List<CsRuleCarrierDistribution> exsitRuleCarrierDistributionList = ruleCarrierDistributionService.listByProvinceNameList(provinceNames);
        if (CollectionUtil.isNotEmpty(exsitRuleCarrierDistributionList)) {
            throw new ServiceException(ImportExceptionEnum.RULE_CARRIER_DISTRIBUTION_PROVINCE_NAME_EXIST, StrUtil.join(StrUtil.COMMA, exsitRuleCarrierDistributionList.stream().map(CsRuleCarrierDistribution::getProvinceName).collect(Collectors.toList())));
        }
        // 校验省份代码是否存在
        ResponseData<List<AdministrativeRegionItem>> addressCodeByNames = remoteAdministrativeRegionService.getAddressCodeByNames(provinceNames);
        if (!addressCodeByNames.getSuccess()) {
            throw new ServiceException(ImportExceptionEnum.RULE_CARRIER_DISTRIBUTION_PROVINCE_CODE_ERROR, addressCodeByNames.getMsg());
        }
        Map<String, String> provinceMap = MapUtil.newHashMap();
        List<AdministrativeRegionItem> addressCodeByNameList = addressCodeByNames.getData();
        List<String> addressNameList = addressCodeByNameList.stream().map(AdministrativeRegionItem::getName).distinct().collect(Collectors.toList());
        if (ObjUtil.notEqual(CollectionUtil.size(provinceNames), CollectionUtil.size(addressNameList))) {
            Collection<String> provinceNameSubtractList = CollectionUtil.subtract(provinceNames, addressNameList);
            throw new ServiceException(ImportExceptionEnum.IMPORT_DATA_PROVINCE_EMPTY, StrUtil.join(StrUtil.COMMA, provinceNameSubtractList));
        } else {
            provinceMap = addressCodeByNameList.stream().collect(Collectors.toMap(AdministrativeRegionItem::getName, AdministrativeRegionItem::getCode));
        }
        // 校验承运商名称是否存在
        List<String> carrierNameList = ruleCarrierDistributionExcelItems.stream().map(RuleCarrierDistributionExcelItem::getCarrierName).distinct().collect(Collectors.toList());
        List<Carrier> exsitCarrierList = carrierService.listByCarrierNameList(carrierNameList);
        if (CollectionUtil.isEmpty(exsitCarrierList)) {
            throw new ServiceException(ImportExceptionEnum.RULE_CARRIER_DISTRIBUTION_CARRIER_NAME_EMPTY, StrUtil.join(StrUtil.COMMA, carrierNameList));
        }
        if (ObjUtil.notEqual(CollectionUtil.size(carrierNameList), CollectionUtil.size(exsitCarrierList))) {
            Collection<String> carrierNameSubtractList = CollectionUtil.subtract(carrierNameList, exsitCarrierList.stream().map(Carrier::getCarrierName).collect(Collectors.toList()));
            throw new ServiceException(ImportExceptionEnum.RULE_CARRIER_DISTRIBUTION_CARRIER_NAME_EMPTY, StrUtil.join(StrUtil.COMMA, carrierNameSubtractList));
        }
        Map<String, Long> carrierMap = exsitCarrierList.stream().collect(Collectors.toMap(Carrier::getCarrierName, Carrier::getId));
        List<CsRuleCarrierDistribution> ruleCarrierDistributionList = CollectionUtil.newArrayList();
        for (RuleCarrierDistributionExcelItem ruleCarrierDistributionExcelItem : ruleCarrierDistributionExcelItems) {
            CsRuleCarrierDistributionItem ruleCarrierDistributionItem = new CsRuleCarrierDistributionItem();
            ruleCarrierDistributionItem.setProvinceCode(MapUtil.getStr(provinceMap, ruleCarrierDistributionExcelItem.getProvinceName()));
            ruleCarrierDistributionItem.setProvinceName(ruleCarrierDistributionExcelItem.getProvinceName());
            ruleCarrierDistributionItem.setKiloFrom(Convert.toBigDecimal(ruleCarrierDistributionExcelItem.getKiloFrom()));
            ruleCarrierDistributionItem.setKiloTo(Convert.toBigDecimal(ruleCarrierDistributionExcelItem.getKiloTo()));
            ruleCarrierDistributionItem.setCarrierId(MapUtil.getLong(carrierMap, ruleCarrierDistributionItem.getCarrierName()));
            ruleCarrierDistributionItem.setCarrierName(ruleCarrierDistributionExcelItem.getCarrierName());
            CsRuleCarrierDistribution ruleCarrierDistribution = new CsRuleCarrierDistribution();
            BeanUtil.copyProperties(ruleCarrierDistributionItem, ruleCarrierDistribution);
            ruleCarrierDistribution.setStatus(YesNoEnum.YES.getCode());
            ruleCarrierDistributionService.preSave(ruleCarrierDistribution);
            ruleCarrierDistributionList.add(ruleCarrierDistribution);
        }
        return getResponseData(CollectionUtil.isNotEmpty(ruleCarrierDistributionList), ruleCarrierDistributionService.batchInsert(ruleCarrierDistributionList));
    }

    /**
     * 无源标签导入
     * @param passiveTagExcelItemList 无源标签导入实体列表
     * @return ResponseData
     */
    public ResponseData passiveTagImportDataHandler(List<PassiveTagExcelItem> passiveTagExcelItemList, MultipartFile file) {
        // 校验标签号是否重复
        Map<String, Long> tagNoMap = passiveTagExcelItemList.stream().collect(Collectors.groupingBy(PassiveTagExcelItem::getTagNo, Collectors.counting()));
        importCommonService.checkDuplicateName(tagNoMap, ImportExceptionEnum.PASSIVE_TAG_NO_DUPLICATE);
        // 构建异常信息输出列表
        List<String> errorMsgList = CollectionUtil.newArrayList();
        // 构造仓库信息Map
        List<String> warehouseNameList = passiveTagExcelItemList.stream().map(PassiveTagExcelItem::getWarehouseName).filter(StrUtil::isNotBlank).toList();
        List<Warehouse> warehouseList = warehouseService.find(new ConditionRule().andIn(Warehouse::getWarehouseName, warehouseNameList));
        Map<String, Warehouse> warehouseMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getWarehouseName, Function.identity(), (k1, k2) -> k1));
        // 数据校验操作
        for (int i = 0; i < passiveTagExcelItemList.size(); i++) {
            PassiveTagExcelItem passiveTagExcelItem = CollectionUtil.get(passiveTagExcelItemList, i);
            // 校验标签号、归属仓库是否已存在
            if (passiveTagDao.isDuplicate(passiveTagExcelItem, "tagNo")) {
                errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_NO_ALREADY_EXIST.getMsg(), Convert.toStr(i + 1)));
            }
            if (!warehouseMap.containsKey(passiveTagExcelItem.getWarehouseName())) {
                errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_WAREHOUSE_NOT_EXIST.getMsg(), Convert.toStr(i + 1)));
            }
        }
        if (CollectionUtil.isNotEmpty(errorMsgList)) {
            ResponseData<List<String>> responseData = new ResponseData<>();
            responseData.setCode(ImportExceptionEnum.PASSIVE_TAG_IMPORT_ERROR.getCode());
            responseData.setData(errorMsgList);
            responseData.setSuccess(false);
            return responseData;
        }
        // 构建新增列表
        List<PassiveTag> passiveTagList = CollectionUtil.newArrayList();
        // 标签新增操作
        for (PassiveTagExcelItem passiveTagExcelItem : passiveTagExcelItemList) {
            PassiveTag passiveTag = new PassiveTag();
            passiveTag.setTagNo(passiveTagExcelItem.getTagNo());
            passiveTag.setTagType(Convert.toInt(passiveTagExcelItem.getTagType()));
            passiveTag.setWarehouseId(warehouseMap.get(passiveTagExcelItem.getWarehouseName()).getId());
            passiveTag.setWarehouseName(passiveTagExcelItem.getWarehouseName());
            Optional<String> appEnvName = AppConfig.getAppEnvName();
            if (appEnvName.isPresent()) {
                String activateStatus = null;
                if (StrUtil.equals(PassiveTagPlatformEnum.YN.getCode(), passivePlatformProperties.getPassivePlatform())){
                    activateStatus = PassiveTagActivateStatusEnum.ACTIVATED.getCode();
                }
                String appEnv = appEnvName.get();
                if (CollectionUtil.contains(CollectionUtil.newArrayList(AppConstant.PRE_CODE, AppConstant.PROD_CODE), appEnv)
                        && StrUtil.equals(PassiveTagPlatformEnum.YN.getCode(), passivePlatformProperties.getPassivePlatform())) {
                    PassiveBatchTagAddRequest request = new PassiveBatchTagAddRequest();
                    request.setLabelPhysicalType(passiveTag.getTagType().toString());
                    request.setLabelInitStatus(PassiveTagActivateStatusEnum.ACTIVATED.getCode());
                    request.setEpcList(CollectionUtil.toList(passiveTag.getTagNo()));
                    UserForOuterCondition userForOuterCondition = new UserForOuterCondition();
                    userForOuterCondition.setId(SessionContext.get().getUserId());
                    ResponseData<UserQuery> userQueryResponse = remoteUserService.getByUserId(userForOuterCondition);
                    if (!userQueryResponse.getSuccess()) {
                        throw new ServiceException(PassiveExceptionEnum.USER_EMPTY);
                    }
                    request.setProvinceId(userQueryResponse.getData().getProvinceId());
                    request.setPiotCustCode(userQueryResponse.getData().getCustCode());
                    ThirdResponse thirdResponse = thirdPassiveService.batchTagAdd(request);
                    if (thirdResponse.getIsSuccess() && thirdResponse.getData() != null) {
                        PassiveBatchTagResponse response = JSONUtil.toBean(JSONUtil.toJsonStr(thirdResponse.getData()), PassiveBatchTagResponse.class);
                        if (!Objects.equals("1", response.getSuccessCount())){
                            activateStatus = PassiveTagActivateStatusEnum.REGISTER_FAILED.getCode();
                        }
                    }else {
                        activateStatus = PassiveTagActivateStatusEnum.REGISTER_FAILED.getCode();
                    }
                }
                passiveTag.setActivateStatus(activateStatus);
            }
            passiveTagService.preSave(passiveTag);
            passiveTag.setStatus(PassiveTagStatusEnum.USED.getCode());
            passiveTagList.add(passiveTag);
        }
        ResponseData responseData = new ResponseData<>();
        if (CollectionUtil.isNotEmpty(passiveTagList)) {
            int count = passiveTagService.batchInsert(passiveTagList);
            List<PassiveTagLog> passiveTagLogList = CollectionUtil.newArrayList();
            for (PassiveTag passiveTag : passiveTagList) {
                PassiveTagLogItem passiveTagLogItem = new PassiveTagLogItem();
                passiveTagLogItem.setTagId(passiveTag.getId());
                passiveTagLogItem.setEvent(PassiveTagEventStatusEnum.IMPORT.getIntCode());
                passiveTagLogItem.setDescription(String.format(PassiveTagLogOperationEnum.IMPORT.getMsg(), file.getOriginalFilename()));
                PassiveTagLog passiveTagLog = new PassiveTagLog();
                BeanUtil.copyProperties(passiveTagLogItem, passiveTagLog);
                passiveTagLogService.preSave(passiveTagLog);
                passiveTagLogList.add(passiveTagLog);
            }
            passiveTagLogDao.batchInsert(passiveTagLogList);
            responseData.setMsg(String.format(ImportExceptionEnum.IMPORT_DATA_SUCCESS.getMsg(), Convert.toStr(count)));
            responseData.setSuccess(true);
            responseData.setCode(200);
        }
        return responseData;
    }

    /**
     * 确认质检
     * @param qcSnExcelItems
     * @param paramsMap
     * @return
     */
    public ResponseData qcSnQualityInsImportDataHandler(List<QcSnExcelItem> qcSnExcelItems, JSONObject paramsMap){
        if (MapUtil.isEmpty(paramsMap)) {
            throw new RuntimeException(ImportExceptionEnum.IMPORT_DATA_EXIST_ERROR_NOT_PARAMS.getMsg());
        }
        String qcNo = Convert.toStr(paramsMap.get("qualityInsOrderNo"));
        // 一：校验重复
        // 1.1：校验SN码是否重复
        Map<String, Long> snNoMap = qcSnExcelItems.stream().collect(Collectors.groupingBy(QcSnExcelItem::getSnNo, Collectors.counting()));
        importCommonService.checkDuplicateName(snNoMap, ImportExceptionEnum.ASN_DETAIL_SN_NO_DUPLICATE);
        CsQcDetailQuery detailByQcNo = csQcHeaderDao.getDetailByQcNo(qcNo);
        // 二：校验是否存在 -- 根据质检单获取相关数据
        // 2.1: 商品名称,商品代码 -- 一致
        importCommonService.checkQcSnSku(qcSnExcelItems,QcSnExcelItem::getSkuName,detailByQcNo.getSkuName(),ImportExceptionEnum.QC_SN_NO_SKU_NAME_NOT_EXIST);
        importCommonService.checkQcSnSku(qcSnExcelItems,QcSnExcelItem::getSkuCode,detailByQcNo.getSkuCode(),ImportExceptionEnum.QC_SN_NO_SKU_CODE_NOT_EXIST);
        // 2.2: SN码 -- 存在
        Map<String, String> cillSnMap = importCommonService.checkConfirmedQcSn(qcSnExcelItems, detailByQcNo.getSnStr());

        // 三：组装并返回数据给前端
        QcSnItem qcSnItem = qcSnExcelItems.stream()
                .peek(e -> {
                    // 1. 箱号智能填充
                    String boxCode = Optional.ofNullable(cillSnMap.get(e.getSnNo()))
                            .filter(StrUtil::isNotBlank)
                            .orElse("无箱码");
                    e.setBoxCode(boxCode);
                })
                .collect(Collectors.collectingAndThen(
                        Collectors.partitioningBy(e ->
                                LotQualityEnum.QUALIFIED.getCode().equals(e.getQualified()) // [!code ++]
                        ),
                        map -> {
                            QcSnItem item = new QcSnItem();
                            item.setQcSnMap(extractSnMap(map.get(true)));
                            item.setUnQcSnMap(extractSnMap(map.get(false)));
                            return item;
                        }
                ));

        ResponseData responseData = new ResponseData<>();
        int count = CollectionUtil.size(qcSnExcelItems);
        responseData.setMsg(String.format(ImportExceptionEnum.IMPORT_DATA_SUCCESS.getMsg(), Convert.toStr(count)));
        responseData.setSuccess(true);
        responseData.setCode(200);
        responseData.setData(qcSnItem);
        return responseData;
    }
    // 提取SN映射的辅助方法
    private static Map<String, List<String>> extractSnMap(List<QcSnExcelItem> list) {
        return list == null ? Collections.emptyMap() :
                list.stream().collect(Collectors.groupingBy(
                        QcSnExcelItem::getBoxCode,
                        Collectors.mapping(QcSnExcelItem::getSnNo, Collectors.toList())
                ));
    }

    /**
     * 新增质检--导入序列号
     *
     * @param asnDetailExcelItems
     * @param paramsMap
     * @return ResponseData
     */
    public ResponseData qcSnImportDataHandler(List<AsnDetailExcelItem> asnDetailExcelItems, JSONObject paramsMap) throws UnsupportedEncodingException {
        if (MapUtil.isEmpty(paramsMap)) {
            throw new RuntimeException(ImportExceptionEnum.IMPORT_DATA_EXIST_ERROR_NOT_PARAMS.getMsg());
        }
        String asnNo = Convert.toStr(paramsMap.get("asnNo"));
        String qcInspection = Convert.toStr(paramsMap.get("qcInspection"));
        String skuCode = Convert.toStr(paramsMap.get("skuCode"));
        String lotNum = Convert.toStr(paramsMap.get("lotNum"));
        Long locId = Convert.toLong(paramsMap.get("locId"));
        Long qcId = Convert.toLong(paramsMap.get("headerId"));
        String skuName = new String(
                Convert.toStr(paramsMap.get("skuName")).getBytes(StandardCharsets.ISO_8859_1),
                StandardCharsets.UTF_8
        );
        if (StrUtil.isBlank(qcInspection) || StrUtil.isBlank(skuCode) || StrUtil.isBlank(skuName) || (QcInspectedEnum.BEFORERECEIVING.getName().equals(qcInspection) && StrUtil.isBlank(asnNo))){
            throw new RuntimeException(ImportExceptionEnum.IMPORT_DATA_EXIST_ERROR_NOT_PARAMS.getMsg());
        }
        // 一：校验重复
        // 1.1：校验SN码是否重复
        Map<String, Long> snNoMap = asnDetailExcelItems.stream().collect(Collectors.groupingBy(AsnDetailExcelItem::getSnNo, Collectors.counting()));
        importCommonService.checkDuplicateName(snNoMap, ImportExceptionEnum.QC_SN_NO_DUPLICATE);

        // 二：校验是否存在
        // 2.1: 商品名称,商品代码 -- 一致
        importCommonService.checkQcSnSku(asnDetailExcelItems,AsnDetailExcelItem::getSkuName,skuName,ImportExceptionEnum.QC_SN_NO_SKU_NAME_NOT_EXIST);
        importCommonService.checkQcSnSku(asnDetailExcelItems,AsnDetailExcelItem::getSkuCode,skuCode,ImportExceptionEnum.QC_SN_NO_SKU_CODE_NOT_EXIST);

        // 2.2：SN码 -- 存在
        // 1. 获取库存/入库SN数据（避免重复查询）
        List<String> soNos = asnDetailExcelItems.stream().map(AsnDetailExcelItem::getSnNo).distinct().toList();
        List<SnDetailItem> asnDetailSnList;
        if (qcId != null) {
            asnDetailSnList = importCommonService.getSnReturn(qcId);
        } else {
            asnDetailSnList = StrUtil.isNotBlank(asnNo)
                    ? importCommonService.getSnFromAsn(asnNo, soNos)
                    : importCommonService.getSnFromStock(locId, lotNum, soNos);
        }

        // 2. 校验新增质检序列号数据
        importCommonService.checkAddQcSn(asnDetailExcelItems, asnDetailSnList);
        // 三：组装并返回数据给前端
        Set<String> filterSet = new HashSet<>(soNos);
        asnDetailSnList = asnDetailSnList.stream()
                .filter(e -> filterSet.contains(e.getSnNo()))
                .collect(Collectors.toList());
        Map<String, List<String>> snMap = asnDetailSnList.stream()
                .peek(e -> e.setBoxCode(
                        Optional.ofNullable(e.getBoxCode())
                                .filter(StrUtil::isNotBlank)
                                .orElse("无箱码")
                ))
                .collect(Collectors.groupingBy(SnDetailItem::getBoxCode, Collectors.mapping(SnDetailItem::getSnNo, Collectors.toList())));

        ResponseData responseData = new ResponseData<>();
        int count = CollectionUtil.size(asnDetailExcelItems);
        responseData.setMsg(String.format(ImportExceptionEnum.IMPORT_DATA_SUCCESS.getMsg(), Convert.toStr(count)));
        responseData.setSuccess(true);
        responseData.setCode(200);
        responseData.setData(snMap);
        return responseData;

    }

    /**
     * 无源标签导入&绑定
     * @param bindExcelList 无源标签导入实体列表
     * @return ResponseData
     */
    public ResponseData passiveTagImportAndBindDataHandler(List<PassiveTagBindExcelItem> bindExcelList, MultipartFile file) {
        // 校验标签号是否重复
        Map<String, Long> tagNoMap = bindExcelList.stream().collect(Collectors.groupingBy(PassiveTagBindExcelItem::getTagNo, Collectors.counting()));
        importCommonService.checkDuplicateName(tagNoMap, ImportExceptionEnum.PASSIVE_TAG_NO_DUPLICATE);
        // 构建异常信息输出列表
        List<String> errorMsgList = CollectionUtil.newArrayList();
        // 构造仓库信息Map
        List<String> warehouseNameList = bindExcelList.stream().map(PassiveTagBindExcelItem::getWarehouseName).filter(StrUtil::isNotBlank).toList();
        List<Warehouse> warehouseList = warehouseService.find(new ConditionRule().andIn(Warehouse::getWarehouseName, warehouseNameList));
        Map<String, Warehouse> warehouseMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getWarehouseName, Function.identity(), (k1, k2) -> k1));
        // 构建新增列表 更新列表
        List<PassiveTag> passiveTagAddList = CollectionUtil.newArrayList();
        List<PassiveTag> passiveTagUpdateList = CollectionUtil.newArrayList();
        // 数据校验操作
        for (int i = 0; i < bindExcelList.size(); i++) {
            PassiveTagBindExcelItem item = CollectionUtil.get(bindExcelList, i);
            PassiveTag passiveTag = passiveTagDao.findFirst(ConditionRule.getInstance().andEqual(PassiveTag::getTagNo, item.getTagNo()));
            Warehouse warehouse = null;
            // 校验【标签号】是否已存在
            if (passiveTagDao.isDuplicate(item, "tagNo")) {
                errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_NO_ALREADY_EXIST.getMsg(), Convert.toStr(i + 1)));
            }
            // 校验【归属仓库】是否已存在
            if (!warehouseMap.containsKey(item.getWarehouseName())) {
                errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_WAREHOUSE_NOT_EXIST.getMsg(), Convert.toStr(i + 1)));
            }
            else {
                warehouse = warehouseMap.get(item.getWarehouseName());
            }
            // 1.校验绑定对象 -- 【容器】
            if (ObjUtil.equals(PassiveBindSubjectEnum.PALLET.getCode(), item.getBindSubject()) && ObjUtil.isNotNull(warehouse)) {
                // 1.1 校验绑定容器时 【容器号】与【容器类型】不能为空
                if (StrUtil.isBlank(item.getSubjectType()) || StrUtil.isBlank(item.getSubjectNo())) {
                    errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_BIND_PALLET_NOT_EMPTY.getMsg(), Convert.toStr(i + 1)));
                }
                ConditionRule conditionRule = ConditionRule.getInstance()
                        .andEqual(Pallet::getWarehouseId, warehouse.getId()) // 仓库
                        .andEqual(Pallet::getType, item.getSubjectType()) // 容器类型
                        .andEqual(Pallet::getPalletNo, item.getSubjectNo()); // 容器号
                Pallet pallet = palletService.findFirst(conditionRule);
                // 1.2 校验仓库内该容器是否存在
                if (ObjUtil.isNull(pallet)) {
                    errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_PALLET_NOT_EXIST.getMsg(), Convert.toStr(i + 1), PalletTypeEnum.getNameByCode(item.getSubjectType()), item.getSubjectNo()));
                }
                // 1.3 校验容器是否被其它标签绑定
                conditionRule = ConditionRule.getInstance()
                        .andEqual(PassiveTag::getSubjectNo, item.getSubjectNo()) // 同容器号
                        .andNotEqual(PassiveTag::getStatus, PassiveTagStatusEnum.DAMAGED.getCode()); // 过滤损坏标签
                if (ObjUtil.isNotNull(passiveTag)){
                    conditionRule.andNotEqual(PassiveTag::getId, passiveTag.getId()); // 排除自身
                }
                List<PassiveTag> illegalTagList = passiveTagDao.find(conditionRule);
                if (CollectionUtil.isNotEmpty(illegalTagList)) {
                    String illegalTagNo = illegalTagList.stream().map(PassiveTag::getTagNo).filter(StrUtil::isNotBlank).collect(Collectors.joining(StrPool.COMMA));
                    errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_PALLET_ALREADY_BEAN_BOUND.getMsg(), Convert.toStr(i + 1), PalletTypeEnum.getNameByCode(item.getSubjectType()), item.getSubjectNo(), illegalTagNo));
                }
            }
            // 2.校验绑定对象 -- 【货物】
            else if (ObjUtil.equals(PassiveBindSubjectEnum.PALLET.getCode(), item.getBindSubject()) && ObjUtil.isNotNull(warehouse)) {
                // 2.1 校验绑定货物时 【商品编码】【序列商品】与【入库订单】不能为空
                if (StrUtil.isBlank(item.getSkuCode()) || StrUtil.isBlank(item.getWhetherSerialController()) || StrUtil.isBlank(item.getAsnNo())) {
                    errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_BIND_GOODS_NOT_EMPTY.getMsg(), Convert.toStr(i + 1)));
                }
                // 2.2 校验仓库内【入库订单】是否存在
                ConditionRule conditionRule = ConditionRule.getInstance()
                        .andEqual(AsnHeader::getWarehouseId, warehouse.getId())
                        .andEqual(AsnHeader::getAsnNo, item.getAsnNo());
                AsnHeader asnHeader  = asnHeaderDao.findFirst(conditionRule);
                if (ObjUtil.isNull(asnHeader)) {
                    errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_ASN_NOT_EXIST.getMsg(), Convert.toStr(i + 1), item.getAsnNo()));
                }
                // 2.3 校验入库订单是否【未收货】且【已审核】
                if (ObjUtil.isNotNull(asnHeader) && !StrUtil.equals(ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode(), asnHeader.getReceiveStatus()) || !StrUtil.equals(AuditStatusEnum.AUDITED.getCode(), asnHeader.getAuditStatus())) {
                    errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_ASN_HEADER_STATUS_ILLEGAL.getMsg(), Convert.toStr(i + 1)));
                }
                // 2.4 校验【商品编码】是否在入库订单中存在
                conditionRule = ConditionRule.getInstance()
                        .andEqual(Sku::getSkuCode, item.getSkuCode());
                Sku sku = skuService.findFirst(conditionRule);
                if (ObjUtil.isNull(sku)) {
                    errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_ASN_HEADER_NOT_EXISTS_SKU.getMsg(), Convert.toStr(i + 1), item.getAsnNo(), item.getSkuCode()));
                } else {
                    conditionRule = ConditionRule.getInstance()
                            .andEqual(AsnDetail::getAsnNo, item.getAsnNo())
                            .andEqual(AsnDetail::getSkuCode, sku.getId());
                    List<AsnDetail> asnDetailList = asnDetailDao.find(conditionRule);
                    if (CollectionUtil.isEmpty(asnDetailList)) {
                        errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_ASN_HEADER_NOT_EXISTS_SKU.getMsg(), Convert.toStr(i + 1), item.getAsnNo(), item.getSkuCode()));
                    }
                    // 2.5 校验【序列商品】
                    if (StrUtil.equals(YesNoEnum.YES.getCode(), item.getWhetherSerialController()) && CollectionUtil.isNotEmpty(asnDetailList)) {
                        // 2.5.1 【箱码】和【序列号】二选一必填
                        if (StrUtil.isBlank(item.getBoxCode()) && StrUtil.isBlank(item.getSnNo())) {
                            errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_SERIAL_BOX_SN_EMPTY.getMsg(), Convert.toStr(i + 1)));
                        }
                        // 2.5.2 校验【箱码】
                        List<Long> detailIdList = asnDetailList.stream().map(AsnDetail::getId).filter(ObjUtil::isNotNull).toList();
                        if (StrUtil.isNotBlank(item.getBoxCode()) && CollectionUtil.isNotEmpty(detailIdList)) {
                            // 2.5.2.1 箱码存在性校验
                            conditionRule = ConditionRule.getInstance()
                                    .andIn(AsnDetailSn::getAsnDetailId, detailIdList)
                                    .andEqual(AsnDetailSn::getBoxCode, item.getBoxCode());
                            List<AsnDetailSn> detailSnList = asnDetailSnDao.find(conditionRule);
                            if (CollectionUtil.isEmpty(detailSnList)) {
                                errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_SERIAL_BOX_SN_NOT_EXIST.getMsg(), Convert.toStr(i + 1)));
                            }
                            // 2.5.2.2 箱码唯一性校验
                            conditionRule = ConditionRule.getInstance()
                                    .andEqual(PassiveTag::getBoxCode, item.getBoxCode()) // 同箱码
                                    .andNotEqual(PassiveTag::getStatus, PassiveTagStatusEnum.DAMAGED.getCode()); // 不为损坏状态
                            if (ObjUtil.isNotNull(passiveTag)) {
                                conditionRule.andNotEqual(PassiveTag::getId, passiveTag.getId());  // 排除本身
                            }
                            if (CollectionUtil.isNotEmpty(passiveTagDao.find(conditionRule))) {
                                errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_SERIAL_BOX_ALREADY_BEAN_BOUND.getMsg(), Convert.toStr(i + 1)));
                            }
                        }
                        // 2.5.3 校验【序列号】
                        else if (StrUtil.isNotBlank(item.getSnNo()) && CollectionUtil.isNotEmpty(detailIdList)) {
                            // 2.5.3.1 序列号存在性校验
                            conditionRule = ConditionRule.getInstance()
                                    .andIn(AsnDetailSn::getAsnDetailId, detailIdList)
                                    .andEqual(AsnDetailSn::getSnNo, item.getSnNo());
                            List<AsnDetailSn> detailSnList = asnDetailSnDao.find(conditionRule);
                            if (CollectionUtil.isEmpty(detailSnList)) {
                                errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_SERIAL_BOX_SN_NOT_EXIST.getMsg(), Convert.toStr(i + 1)));
                            }
                            // 2.5.3.2 序列号唯一性校验
                            // 【序列号】重复绑定校验
                            conditionRule = ConditionRule.getInstance()
                                    .andEqual(PassiveTag::getSnNo, item.getSnNo()) // 同序列号
                                    .andNotEqual(PassiveTag::getStatus, PassiveTagStatusEnum.DAMAGED.getCode()); // 不为损坏状态
                            if (ObjUtil.isNotNull(passiveTag)) {
                                conditionRule.andNotEqual(PassiveTag::getId, passiveTag.getId());  // 排除本身
                            }
                            if (CollectionUtil.isNotEmpty(passiveTagDao.find(conditionRule))) {
                                errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_SERIAL_SN_ALREADY_BEAN_BOUND.getMsg(), Convert.toStr(i + 1), item.getSnNo()));
                            }
                            // 2.【箱码】中所有【序列号】校验
                            AsnDetailSn asnDetailSn = asnDetailSnDao.findFirst(ConditionRule.getInstance().andEqual(AsnDetailSn::getSnNo, item.getSnNo()));
                            if (ObjUtil.isNotNull(asnDetailSn) && StrUtil.isNotBlank(asnDetailSn.getBoxCode())) {
                                conditionRule = ConditionRule.getInstance()
                                        .andEqual(PassiveTag::getBoxCode, asnDetailSn.getBoxCode()) // 同箱码
                                        .andNotEqual(PassiveTag::getStatus, PassiveTagStatusEnum.DAMAGED.getCode()); // 不为损坏状态
                                if (ObjUtil.isNotNull(passiveTag)) {
                                    conditionRule.andNotEqual(PassiveTag::getId, passiveTag.getId());  // 排除本身
                                }
                                if (CollectionUtil.isNotEmpty(passiveTagDao.find(conditionRule))) {
                                    errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_SERIAL_SN_IN_BOX_ALREADY_BEAN_BOUND.getMsg(), Convert.toStr(i + 1), asnDetailSn.getBoxCode()));
                                }
                            }
                        }
                    }
                    // 2.6 校验【普通商品】
                    else if (StrUtil.equals(YesNoEnum.NO.getCode(), item.getWhetherSerialController())) {
                        // 2.5.1 【包装名称】和【包装单位】必填校验
                        if (StrUtil.isBlank(item.getPackageName()) || StrUtil.isBlank(item.getPackageUnitName())) {
                            errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_GENERAL_PACKAGE_UNIT_NOT_EMPTY.getMsg(), Convert.toStr(i + 1)));
                        }
                        // 2.5.2 【包装名称】存在性校验
                        conditionRule = ConditionRule.getInstance().andEqual(Package::getName, item.getPackageName());
                        Package packageFirst = packageService.findFirst(conditionRule);
                        if (ObjUtil.isNull(packageFirst)) {
                            errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_GENERAL_PACKAGE_NOT_EXIST.getMsg(), Convert.toStr(i + 1)));
                        }
                        // 2.5.3 【包装名称】是否与【商品】绑定校验
                        if (ObjUtil.isNotNull(sku) && ObjUtil.isNotNull(packageFirst)) {
                            if (ObjUtil.notEqual(sku.getPackageId(), packageFirst.getId())) {
                                errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_GENERAL_SKU_PACKAGE_NOT_THE_SAME.getMsg(), Convert.toStr(i + 1), sku.getSkuCode(), item.getPackageName()));
                            }
                        }
                        // 2.5.4 【包装单位名称】存在性校验
                        if (ObjUtil.isNotNull(packageFirst)) {
                            conditionRule = ConditionRule.getInstance()
                                    .andEqual(PackageUnit::getPackageId, packageFirst.getId())
                                    .andEqual(PackageUnit::getName, item.getPackageUnitName());
                            PackageUnit packageUnit = packageUnitDao.findFirst(conditionRule);
                            if (ObjUtil.isNull(packageUnit)) {
                                errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_GOODS_GENERAL_PACKAGE_UNIT_NOT_EXIST.getMsg(), Convert.toStr(i + 1)));
                            }
                        }
                    }
                }
            }
            // 3.更新时校验 -- 【标签】
            if (ObjUtil.isNotNull(passiveTag)) {
                // 3.1 标签【领用状态】校验
                ArrayList<Integer> illegalStatus = CollectionUtil.newArrayList(PassiveTagStatusEnum.INVALID.getCode(), PassiveTagStatusEnum.UNPACKED.getCode(), PassiveTagStatusEnum.DAMAGED.getCode());
                if (illegalStatus.contains(passiveTag.getStatus())) {
                    errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_UPDATE_STATUS_ILLEGAL.getMsg(), Convert.toStr(i + 1)));
                }
                // 3.2 标签【收货状态】校验
                if (!StrUtil.equals(ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode(), passiveTag.getReceiveStatus())) {
                    errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_UPDATE_RECEIVE_STATUS_ILLEGAL.getMsg(), Convert.toStr(i + 1)));
                }
                // 3.3 标签【仓库信息】不允许更改
                if (warehouseMap.containsKey(item.getWarehouseName())) {
                    warehouse = warehouseMap.get(item.getWarehouseName());
                    if (ObjUtil.notEqual(warehouse.getId(), passiveTag.getWarehouseId())) {
                        errorMsgList.add(String.format(ImportExceptionEnum.PASSIVE_TAG_UPDATE_WAREHOUSE_NOT_THE_SAME.getMsg(), Convert.toStr(i + 1)));
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(errorMsgList)) {
            ResponseData<List<String>> responseData = new ResponseData<>();
            responseData.setCode(ImportExceptionEnum.PASSIVE_TAG_IMPORT_ERROR.getCode());
            responseData.setData(errorMsgList);
            responseData.setSuccess(false);
            return responseData;
        }
        for (PassiveTagBindExcelItem bindExcelItem : bindExcelList) {
            // 1. 获取无源标签
            PassiveTag passiveTag = passiveTagDao.findFirst(ConditionRule.getInstance().andEqual(PassiveTag::getTagNo, bindExcelItem.getTagNo()));
            // 2. 当标签在系统中【不存在】时 -- 【新增】赋值
            if (ObjUtil.isNull(passiveTag)) {
                passiveTag.setTagNo(bindExcelItem.getTagNo());
                passiveTag.setTagType(Convert.toInt(bindExcelItem.getTagType()));
                passiveTag.setWarehouseId(warehouseMap.get(bindExcelItem.getWarehouseName()).getId());
                passiveTag.setWarehouseName(bindExcelItem.getWarehouseName());
                passiveTag.setBindSubject(bindExcelItem.getBindSubject());
            }
            // 2.1 绑定【容器】时
            if (ObjUtil.equals(PassiveBindSubjectEnum.PALLET.getCode(), bindExcelItem.getBindSubject())) {
                passiveTag.setSubjectType(bindExcelItem.getSubjectType());
                passiveTag.setSubjectNo(bindExcelItem.getSubjectNo());
                passiveTag.setReceiveStatus(null);
                passiveTag.setMinEa(null);
            }
            // 2.2 绑定【货物】时
            else if (ObjUtil.equals(PassiveBindSubjectEnum.GOODS.getCode(), bindExcelItem.getBindSubject())) {
                Sku sku = skuService.findFirst(ConditionRule.getInstance().andEqual(Sku::getSkuCode, bindExcelItem.getSkuCode()));
                passiveTag.setAsnNo(bindExcelItem.getAsnNo());
                passiveTag.setSkuId(sku.getId());
                passiveTag.setSkuName(sku.getSkuName());
                passiveTag.setSkuCode(sku.getSkuCode());
                passiveTag.setReceiveStatus(ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode());
                passiveTag.setWhetherSerialController(bindExcelItem.getWhetherSerialController());
                // 2.2.1 绑定【序列号商品】时
                if (StrUtil.equals(YesNoEnum.YES.getCode(), bindExcelItem.getWhetherSerialController())) {
                    // 2.2.1.1 绑定【箱码】时
                    if (StrUtil.isNotBlank(bindExcelItem.getBoxCode())) {
                        passiveTag.setBoxCode(bindExcelItem.getBoxCode());
                        List<AsnDetailSn> asnDetailSnList = asnDetailSnDao.find(ConditionRule.getInstance().andEqual(AsnDetailSn::getBoxCode, bindExcelItem.getBoxCode()));
                        passiveTag.setAsnDetailId(asnDetailSnList.getFirst().getAsnDetailId());
                        long minEa = asnDetailSnList.stream().map(AsnDetailSn::getSnNo).filter(StrUtil::isNotBlank).count();
                        passiveTag.setMinEa((int) minEa);
                    }
                    // 2.2.1.2 绑定【序列号】时
                    else {
                        passiveTag.setSnNo(bindExcelItem.getSnNo());
                        AsnDetailSn asnDetailSn = asnDetailSnDao.findFirst(ConditionRule.getInstance().andEqual(AsnDetailSn::getSnNo, bindExcelItem.getSnNo()));
                        passiveTag.setAsnDetailId(asnDetailSn.getAsnDetailId());
                        passiveTag.setMinEa(1);
                    }
                }
                // 2.2.2 绑定【普通商品】时
                else if (StrUtil.equals(YesNoEnum.NO.getCode(), bindExcelItem.getWhetherSerialController())) {
                    Package packageFirst = packageService.findFirst(ConditionRule.getInstance().andEqual(Package::getName, bindExcelItem.getPackageName()));
                    PackageUnit packageUnit = packageUnitDao.findFirst(ConditionRule.getInstance().andEqual(PackageUnit::getPackageId, packageFirst.getId()).andEqual(PackageUnit::getName, bindExcelItem.getPackageUnitName()));
                    ConditionRule conditionRule = ConditionRule.getInstance()
                            .andEqual(AsnDetail::getPackageId, packageFirst.getId())
                            .andEqual(AsnDetail::getPackageUnitId, packageUnit.getId())
                            .andEqual(AsnDetail::getAsnNo, bindExcelItem.getAsnNo())
                            .andEqual(AsnDetail::getSkuId, sku.getId());
                    AsnDetail asnDetail = asnDetailDao.findFirst(conditionRule);
                    passiveTag.setPackageId(packageFirst.getId());
                    passiveTag.setPackageName(packageFirst.getName());
                    passiveTag.setPackageUnitId(packageUnit.getId());
                    passiveTag.setPackageUnitName(packageUnit.getName());
                    passiveTag.setMinEa(packageUnit.getMinQuantity());
                    passiveTag.setAsnDetailId(asnDetail.getId());
                }
            }
            // 3.新增时额外注册标签
            if (ObjUtil.isNull(passiveTag)) {
                Optional<String> appEnvName = AppConfig.getAppEnvName();
                if (appEnvName.isPresent()) {
                    String activateStatus = null;
                    if (StrUtil.equals(PassiveTagPlatformEnum.YN.getCode(), passivePlatformProperties.getPassivePlatform())){
                        activateStatus = PassiveTagActivateStatusEnum.ACTIVATED.getCode();
                    }
                    String appEnv = appEnvName.get();
                    if (CollectionUtil.contains(CollectionUtil.newArrayList(AppConstant.PRE_CODE, AppConstant.PROD_CODE), appEnv)
                            && StrUtil.equals(PassiveTagPlatformEnum.YN.getCode(), passivePlatformProperties.getPassivePlatform())) {
                        PassiveBatchTagAddRequest request = new PassiveBatchTagAddRequest();
                        request.setLabelPhysicalType(passiveTag.getTagType().toString());
                        request.setLabelInitStatus(PassiveTagActivateStatusEnum.ACTIVATED.getCode());
                        request.setEpcList(CollectionUtil.toList(passiveTag.getTagNo()));
                        UserForOuterCondition userForOuterCondition = new UserForOuterCondition();
                        userForOuterCondition.setId(SessionContext.get().getUserId());
                        ResponseData<UserQuery> userQueryResponse = remoteUserService.getByUserId(userForOuterCondition);
                        if (!userQueryResponse.getSuccess()) {
                            throw new ServiceException(PassiveExceptionEnum.USER_EMPTY);
                        }
                        request.setProvinceId(userQueryResponse.getData().getProvinceId());
                        request.setPiotCustCode(userQueryResponse.getData().getCustCode());
                        ThirdResponse thirdResponse = thirdPassiveService.batchTagAdd(request);
                        if (thirdResponse.getIsSuccess() && thirdResponse.getData() != null) {
                            PassiveBatchTagResponse response = JSONUtil.toBean(JSONUtil.toJsonStr(thirdResponse.getData()), PassiveBatchTagResponse.class);
                            if (!Objects.equals("1", response.getSuccessCount())){
                                activateStatus = PassiveTagActivateStatusEnum.REGISTER_FAILED.getCode();
                            }
                        }else {
                            activateStatus = PassiveTagActivateStatusEnum.REGISTER_FAILED.getCode();
                        }
                    }
                    passiveTag.setActivateStatus(activateStatus);
                }
                passiveTagAddList.add(passiveTag);
            } else {
                passiveTagUpdateList.add(passiveTag);
            }
            passiveTagService.preSave(passiveTag);
            passiveTag.setStatus(PassiveTagStatusEnum.BIND.getCode());
        }
        ResponseData responseData = new ResponseData<>();
        if (CollectionUtil.isNotEmpty(passiveTagAddList) || CollectionUtil.isNotEmpty(passiveTagUpdateList)) {
            int count = passiveTagService.batchInsert(passiveTagAddList) + passiveTagService.batchUpdate(passiveTagUpdateList);
            List<PassiveTagLog> passiveTagLogList = CollectionUtil.newArrayList();
            for (PassiveTag passiveTag : passiveTagAddList) {
                PassiveTagLogItem passiveTagLogItem = new PassiveTagLogItem();
                passiveTagLogItem.setTagId(passiveTag.getId());
                passiveTagLogItem.setEvent(PassiveTagEventStatusEnum.IMPORT_AND_BIND.getIntCode());
                passiveTagLogItem.setDescription(String.format(PassiveTagLogOperationEnum.IMPORT_AND_BIND.getMsg(), file.getOriginalFilename()));
                PassiveTagLog passiveTagLog = new PassiveTagLog();
                BeanUtil.copyProperties(passiveTagLogItem, passiveTagLog);
                passiveTagLogService.preSave(passiveTagLog);
                passiveTagLogList.add(passiveTagLog);
            }
            passiveTagLogDao.batchInsert(passiveTagLogList);
            responseData.setMsg(String.format(ImportExceptionEnum.IMPORT_DATA_SUCCESS.getMsg(), Convert.toStr(count)));
            responseData.setSuccess(true);
            responseData.setCode(200);
        }
        return responseData;
    }

}
