package com.chinaservices.wms.module.passive.take.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.auth.module.user.domain.UserForOuterCondition;
import com.chinaservices.auth.module.user.domain.UserQuery;
import com.chinaservices.auth.module.user.feign.RemoteUserService;
import com.chinaservices.core.app.AppConfig;
import com.chinaservices.core.constant.AppConstant;
import com.chinaservices.core.enums.StatusEnum;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.core.third.asset.domain.AssetQueryRequest;
import com.chinaservices.core.third.asset.domain.AssetStartRequest;
import com.chinaservices.core.third.asset.domain.AssetStartResponse;
import com.chinaservices.core.third.asset.domain.AssetStopRequest;
import com.chinaservices.core.third.asset.service.ThirdTaskInfoService;
import com.chinaservices.core.third.constant.ThirdResponse;
import com.chinaservices.core.third.passive.domain.*;

import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.util.NumberUtil;
import com.chinaservices.wms.common.enums.passive.PassiveAutoInventoryStatusEnum;
import com.chinaservices.wms.common.enums.passive.PassiveBindSubjectEnum;
import com.chinaservices.wms.common.enums.passive.PassiveTagActivateStatusEnum;
import com.chinaservices.wms.common.enums.passive.PassiveTagSubjectTypeEnum;
import com.chinaservices.wms.common.exception.PassiveExceptionEnum;
import com.chinaservices.wms.common.exception.StockExcepitonEnum;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.passive.device.domain.WarehouseTreeQuery;
import com.chinaservices.wms.module.passive.device.domain.WarehouseZoneTreeQuery;
import com.chinaservices.wms.module.passive.tag.model.PassiveTag;
import com.chinaservices.wms.module.passive.tag.service.PassiveTagService;
import com.chinaservices.wms.module.passive.take.dao.PassiveAutoInventoryDao;
import com.chinaservices.wms.module.passive.take.dao.PassiveAutoInventoryDetailsDao;
import com.chinaservices.wms.module.passive.take.dao.PassiveAutoInventoryLogDao;
import com.chinaservices.wms.module.passive.take.domain.*;
import com.chinaservices.wms.module.passive.take.model.PassiveAutoInventory;
import com.chinaservices.wms.module.passive.take.model.PassiveAutoInventoryDetails;
import com.chinaservices.wms.module.passive.take.model.PassiveAutoInventoryLog;
import com.chinaservices.wms.module.stock.multi.domain.InvLotLocCondition;
import com.chinaservices.wms.module.stock.multi.domain.InvLotLocSnPageCondition;
import com.chinaservices.wms.module.stock.mutli.dao.InvLotLocDao;
import com.chinaservices.wms.module.stock.mutli.dao.InvLotLocSnDao;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLocSn;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocService;
import com.chinaservices.wms.module.stock.take.model.InventoryStocktakeTaskDetails;
import com.chinaservices.wms.module.stock.take.service.InventoryStocktakeOrderService;
import com.chinaservices.wms.module.stock.take.service.InventoryStocktakeTaskDetailsService;
import com.chinaservices.wms.module.stock.take.service.InventoryStocktakeTaskService;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import com.chinaservices.wms.module.warehouse.warehouse.domain.WarehousePageCondition;
import com.chinaservices.wms.module.warehouse.warehouse.domain.WarehouseQuery;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import com.chinaservices.wms.module.warehouse.zone.model.WarehouseZone;
import com.chinaservices.wms.module.warehouse.zone.service.WarehouseZoneService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PassiveAutoInventoryNewService extends ModuleBaseServiceSupport<PassiveAutoInventoryDao, PassiveAutoInventory, Long> {

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private WarehouseZoneService warehouseZoneService;

    @Autowired
    private PassiveAutoInventoryLogDao passiveAutoInventoryLogDao;

    @Autowired
    private PassiveAutoInventoryLogService passiveAutoInventoryLogService;

    @Autowired
    private PassiveTagService passiveTagService;

    @Autowired
    private InvLotLocDao invLotLocDao;

    @Autowired
    private InvLotLocSnDao invLotLocSnDao;

    @Autowired
    private ThirdTaskInfoService thirdTaskInfoService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private PassiveAutoInventoryDetailsService passiveAutoInventoryDetailsService;

    @Autowired
    private InvLotLocService invLotLocService;

    @Autowired
    private SkuService skuService;

    @Autowired
    @Lazy
    private InventoryStocktakeOrderService inventoryStocktakeOrderService;

    @Autowired
    @Lazy
    private InventoryStocktakeTaskDetailsService inventoryStocktakeTaskDetailsService;
    @Autowired
    private PassiveAutoInventoryDetailsDao passiveAutoInventoryDetailsDao;


    /**
     * 分页查询自动化盘点
     *
     * @param condition 条件
     * @return {@link PageResult }<{@link PassiveAutoInventoryQuery }>
     */
    public PageResult<PassiveAutoInventoryQuery> page(PassiveAutoInventoryPageCondition condition)
    {
        if (EmptyUtil.isEmpty(condition.getEventNo())) {
            // 自动盘点最新日志查询
            PassiveAutoInventoryLog inventoryLog = passiveAutoInventoryLogDao.newLog(condition.getEventNo());
            if (EmptyUtil.isEmpty(inventoryLog)) {
                return new PageResult<>();
            }
            // 自动盘点仓库分组查询
            condition.setEventNo(inventoryLog.getEventNo());
        }
        return dao.page(condition);
    }

    /**
     * 自动盘点仓库分组查询
     *
     * @return {@link List }<{@link PassiveAutoInventoryQuery }>
     */
    public List<WarehouseTreeQuery> warehouseIdQuery(PassiveAutoInventoryEventNoCondition condition)
    {
        if (EmptyUtil.isEmpty(condition.getEventNo())) {
            // 自动盘点最新日志查询
            PassiveAutoInventoryLog inventoryLog = passiveAutoInventoryLogDao.newLog(condition.getEventNo());
            if (EmptyUtil.isEmpty(inventoryLog)) {
                return new ArrayList<>();
            }
            // 自动盘点仓库分组查询
            condition.setEventNo(inventoryLog.getEventNo());
        }

        List<PassiveAutoInventoryWarehouseQuery> warehouseQueryList = dao.warehouseIdQuery(condition);
        Map<Long, List<PassiveAutoInventoryWarehouseQuery>> listMap = warehouseQueryList.stream().collect(Collectors.groupingBy(PassiveAutoInventoryWarehouseQuery::getWarehouseId));

        //
        List<WarehouseTreeQuery> queryList = new ArrayList<>();
        listMap.forEach((key, value) ->
        {
            // 仓库信息
            WarehouseTreeQuery warehouseTreeQuery = new WarehouseTreeQuery();
            warehouseTreeQuery.setValue(Convert.toStr(key));
            warehouseTreeQuery.setLabel(value.getFirst().getWarehouseName());
            queryList.add(warehouseTreeQuery);
        });
        return queryList;
    }

    /**
     * 计数统计自动化盘点
     *
     * @return {@link PassiveAutoInventoryCountQuery }
     */
    public PassiveAutoInventoryCountQuery authInventoryCount(PassiveAutoInventoryCondition condition) {

        if (EmptyUtil.isEmpty(condition.getEventNo())) {
            // 自动盘点最新日志查询
            PassiveAutoInventoryLog inventoryLog = passiveAutoInventoryLogDao.newLog(condition.getEventNo());
            if (EmptyUtil.isEmpty(inventoryLog)) {
                return new PassiveAutoInventoryCountQuery();
            }
            // 自动盘点仓库分组查询
            condition.setEventNo(inventoryLog.getEventNo());
        }
        return dao.authInventoryCountByNew(condition);
    }
    /**
     * 计数统计自动化进度
     *
     * @return {@link PassiveAutoInventoryCountQuery }
     */
    public PassiveAutoInventoryCountQuery authInventoryProgress(PassiveAutoInventoryCondition condition) {
        if (EmptyUtil.isEmpty(condition.getBindSubject())) {
            throw new ServiceException(PassiveExceptionEnum.PASSIVE_AUTO_INVENTORY_BIND_SUBJECT_NOT_NULL.getMsg());
        }
        if (EmptyUtil.isEmpty(condition.getEventNo())) {
            // 自动盘点最新日志查询
            PassiveAutoInventoryLog inventoryLog = passiveAutoInventoryLogDao.newLog(condition.getEventNo());
            if (EmptyUtil.isEmpty(inventoryLog)) {
                return new PassiveAutoInventoryCountQuery();
            }
            // 自动盘点仓库分组查询
            condition.setEventNo(inventoryLog.getEventNo());
        }
        return dao.authInventoryCount(condition);
    }

    /**
     * 分页查询自动化盘点日志
     *
     * @param condition 条件
     * @return {@link PageResult }<{@link PassiveAutoInventoryLogQuery }>
     */
    public PageResult<PassiveAutoInventoryLogQuery> logPage(PassiveAutoInventoryLogPageCondition condition) {
        return dao.logPage(condition);
    }

    /**
     * 获取最近盘点启动时间
     *
     * @return {@link PassiveAutoInventoryLogQuery }
     */
    @Transactional(rollbackFor = Exception.class)
    public PassiveAutoInventoryLogQuery getLatestAutoInventoryLog(PassiveAutoInventoryEventNoCondition condition)
    {
        PassiveAutoInventoryLogQuery logQuery = dao.getLatestAutoInventoryLog(condition);
        if (EmptyUtil.isEmpty(logQuery)) {
            return new PassiveAutoInventoryLogQuery();
        }
        logQuery.setEnableStatus(StatusEnum.NO.getCode());
        // 未盘点结束的任务才能更新盘点数据
        if (EmptyUtil.isEmpty(logQuery.getEndTime())) {
            logQuery.setEnableStatus(StatusEnum.YES.getCode());
            UserQuery userQuery = this.getUserQuery();
            //异步更新盘点结果
            CompletableFuture.runAsync(() -> updateThirdPartyInventoryResult(logQuery.getEventNo(), userQuery));
        }

        return logQuery;
    }

    /**
     * 更新第三方盘点结果
     *
     * @param eventNo 事件号
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateThirdPartyInventoryResult(String eventNo, UserQuery userQuery)
    {
        // 自动盘点任务查询
        PassiveAutoInventoryCondition condition = new PassiveAutoInventoryCondition();
        condition.setEventNo(eventNo);
        List<PassiveAutoInventory> inventoryTaskList = dao.getInventoryTaskNoByEventNo(condition);
        if (EmptyUtil.isEmpty(inventoryTaskList)) {
            throw new ServiceException(PassiveExceptionEnum.PASSIVE_AUTO_INVENTORY_NOW_NULL.getMsg());
        }
        List<String> inventoryTaskNoList = inventoryTaskList.stream().map(PassiveAutoInventory::getInventoryTaskNo).collect(Collectors.toList());
        // 校验当前服务环境，pre、prod环境执行第三方接口调用
        Optional<String> appEnvName = AppConfig.getAppEnvName();
        if (appEnvName.isPresent()) {
            String appEnv = appEnvName.get();
            if (CollectionUtil.contains(
                    CollectionUtil.newArrayList(AppConstant.PRE_CODE, AppConstant.PROD_CODE),
                    appEnv)) {
                // 第三方无源盘点结果查询
                this.thirdPartyInventoryResult(inventoryTaskNoList, userQuery);
                // 更新自动盘点日志
                this.updateAutoInventoryLog(eventNo, userQuery, condition, inventoryTaskNoList);
            }
        }
    }

    /**
     * 更新自动盘点日志
     *
     * @param eventNo 事件单号
     * @param userQuery 用户查询
     * @param condition 条件
     * @param inventoryTaskNoList 库存任务单号列表
     */
    private void updateAutoInventoryLog(String eventNo, UserQuery userQuery, PassiveAutoInventoryCondition condition, List<String> inventoryTaskNoList)
    {
        // 盘点日志盘存率更新
        PassiveAutoInventoryLog inventoryLog = passiveAutoInventoryLogDao.newLog(eventNo);
        // ->盘点数据统计结果查询
        PassiveAutoInventoryCountQuery authInventoryCount = dao.authInventoryCount(condition);
        // ->盘存率转换
        double inventoryStock = authInventoryCount.getInventoryStock();
        inventoryLog.setCompleteLevel(NumberUtil.div(inventoryStock, 100));
        // ->盘点率达到100%，则停止自动盘点
        if (inventoryStock == 1) {
            // 停止盘点
            this.stopThirdPartyInventory(inventoryTaskNoList, userQuery);
            // 盘点结束时间
            inventoryLog.setEndTime(new Date());
        }

        passiveAutoInventoryLogService.preSave(inventoryLog);
        // ->更新
        passiveAutoInventoryLogService.update(inventoryLog);
    }

    /**
     * 当前用户
     *
     * @return {@link UserQuery }
     */
    private UserQuery getUserQuery()
    {
        // 当前用户
        UserForOuterCondition userForOuterCondition = new UserForOuterCondition();
        userForOuterCondition.setId(SessionContext.get().getUserId());
        ResponseData<UserQuery> userQueryResponse = remoteUserService.getByUserId(userForOuterCondition);
        if (!userQueryResponse.getSuccess()) {
            throw new ServiceException(PassiveExceptionEnum.USER_EMPTY);
        }
        return userQueryResponse.getData();
    }


    /**
     * 启动盘点
     *
     * @param item 项目
     */
    @Transactional(rollbackFor = Exception.class)
    public void startInventory(PassiveAutoInventoryItem item)
    {
        // 查询标签
        List<PassiveTag> passiveTagList = passiveTagService.find(new ConditionRule()
                .andEqual(PassiveTag::getWarehouseId, item.getWarehouseId())
                .andIsNotEmpty(PassiveTag::getSubjectNo)
                .andEqual(PassiveTag::getActivateStatus, PassiveTagActivateStatusEnum.ACTIVATED.getCode()));
        passiveTagList.forEach(passiveTag ->
        {
            if (PassiveBindSubjectEnum.PALLET.getCode().equals(passiveTag.getBindSubject().toString())) {
                passiveTag.setSubjectType(PassiveTagSubjectTypeEnum.PALLET_NO.getCode());
            }
        });
        // 标签类型分组
        Map<String, List<PassiveTag>> tagListMap = passiveTagList.stream()
                .collect(Collectors.groupingBy(PassiveTag::getSubjectType));


        // 仓库-库区，查询库存
        InvLotLocCondition invLotLocCondition = new InvLotLocCondition();
        invLotLocCondition.setWarehouseIdArray(item.getWarehouseIds());
        invLotLocCondition.setZoneIdArray(item.getWarehouseZoneIds());
        List<InvLotLoc> invLotLocList = invLotLocDao.findByWarehouseIdAndZoneId(invLotLocCondition);

        // 仓库-库区，查询序列号库存
        InvLotLocSnPageCondition invLotLocSnPageCondition = new InvLotLocSnPageCondition();
        invLotLocSnPageCondition.setWarehouseIdArray(item.getWarehouseIds());
        invLotLocSnPageCondition.setZoneIdArray(item.getWarehouseZoneIds());
        List<InvLotLocSn> invLotLocSnList = invLotLocSnDao.findByWarehouseIdAndZoneId(invLotLocSnPageCondition);

        /* ↓↓↓↓↓↓↓↓↓↓↓↓ 数据处理 ↓↓↓↓↓↓↓↓↓*/

        // 组装需要保存的自动盘点的数据
        List<PassiveAutoInventory> autoInventoryList = new ArrayList<>();
        this.savePassiveAutoInventory(tagListMap, invLotLocList, invLotLocSnList, autoInventoryList);

        // 环境过滤，调用第三方接口启动无源盘点
        this.envFiltration(autoInventoryList);

        // 日志
        PassiveAutoInventoryLog passiveAutoInventoryLog = new PassiveAutoInventoryLog();
        passiveAutoInventoryLog.setEventNo(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN));
        passiveAutoInventoryLog.setStartTime(DateUtil.date());
        passiveAutoInventoryLog.setCompleteLevel(NumberUtil.div(0, 100));
        passiveAutoInventoryLogService.preSave(passiveAutoInventoryLog);
        passiveAutoInventoryLogService.saveOrUpdate(passiveAutoInventoryLog);

        // 盘点事件号
        autoInventoryList.forEach(autoInventory ->
                autoInventory.setEventNo(passiveAutoInventoryLog.getEventNo()));
        dao.batchUpdate(autoInventoryList);
    }


    /**
     * 保存被动自动库存
     *
     * @param tagListMap 标签列表地图
     * @param invLotLocList 库存列表
     * @param invLotLocSnList 库存序列号列表
     * @param passiveAutoInventoryList 自动盘点清单
     */
    private void savePassiveAutoInventory(Map<String, List<PassiveTag>> tagListMap, List<InvLotLoc> invLotLocList,
                                          List<InvLotLocSn> invLotLocSnList, List<PassiveAutoInventory> passiveAutoInventoryList)
    {
        // 标签分组
        for (String type : tagListMap.keySet()) {
            List<PassiveTag> passiveTagTypeList = tagListMap.get(type);
            PassiveTagSubjectTypeEnum fromCode = PassiveTagSubjectTypeEnum.fromCode(type.toString());
            if (fromCode == null) {
                continue;
            }
            switch (fromCode) {
                case PALLET_NO:
                    // 容器号
                    List<String> palletNumList = new ArrayList<>(invLotLocList.stream().map(InvLotLoc::getPalletNum)
                            .filter(EmptyUtil::isNotEmpty)
                            .distinct()
                            .toList());

                    List<String> palletList = invLotLocSnList.stream().map(InvLotLocSn::getPalletNum)
                            .filter(EmptyUtil::isNotEmpty)
                            .distinct()
                            .toList();
                    palletNumList.addAll(palletList);
                    // 标签
                    List<PassiveTag> palletTagList = passiveTagTypeList.stream().filter(tag ->
                            palletNumList.contains(tag.getSubjectNo())
                    ).toList();
                    // 设置盘点
                    setInventory(invLotLocList, invLotLocSnList, passiveAutoInventoryList, palletTagList);
                    break;
                case LOT_NUM:
                    // 批次号
                    List<String> lotNumList = new ArrayList<>(invLotLocList.stream().map(InvLotLoc::getLotNum)
                            .filter(EmptyUtil::isNotEmpty)
                            .distinct()
                            .toList());

                    List<String> lotList = invLotLocSnList.stream().map(InvLotLocSn::getLotNum)
                            .filter(EmptyUtil::isNotEmpty)
                            .distinct()
                            .toList();
                    lotNumList.addAll(lotList);
                    // 标签
                    List<PassiveTag> lotNumTagList = passiveTagTypeList.stream().filter(tag ->
                            lotNumList.contains(tag.getSubjectNo())
                    ).toList();
                    // 设置盘点
                    setInventory(invLotLocList, invLotLocSnList, passiveAutoInventoryList, lotNumTagList);
                    break;
                case SSCC:
                    break;
                case SERIAL_NUMBER:
                    // 容器号
                    List<String> snNoList = invLotLocSnList.stream().map(InvLotLocSn::getSkuSn)
                            .filter(EmptyUtil::isNotEmpty)
                            .distinct()
                            .toList();
                    // 标签
                    List<PassiveTag> snNoTagList = passiveTagTypeList.stream().filter(tag ->
                            snNoList.contains(tag.getSubjectNo())
                    ).toList();
                    // 设置盘点
                    setInventory(invLotLocList, invLotLocSnList, passiveAutoInventoryList, snNoTagList);
                    break;
                case BOX:
                    // 容器号
                    List<String> boxCodeList = invLotLocSnList.stream().map(InvLotLocSn::getBoxCode)
                            .filter(EmptyUtil::isNotEmpty)
                            .distinct()
                            .toList();
                    // 标签
                    List<PassiveTag> boxCodeTagList = passiveTagTypeList.stream().filter(tag ->
                            boxCodeList.contains(tag.getSubjectNo())
                    ).toList();
                    // 设置盘点
                    setInventory(invLotLocList, invLotLocSnList, passiveAutoInventoryList, boxCodeTagList);
                default:
                    break;
            }
        }
    }

    /**
     * 设置盘点
     *
     * @param invLotLocList 库存列表
     * @param invLotLocSnList 库存序列号列表
     * @param passiveAutoInventoryList 自动盘点清单
     */
    private void setInventory(List<InvLotLoc> invLotLocList, List<InvLotLocSn> invLotLocSnList,
                                     List<PassiveAutoInventory> passiveAutoInventoryList,
                                     List<PassiveTag> passiveTagList)
    {

        for (PassiveTag tag : passiveTagList) {

            PassiveAutoInventory inventory = new PassiveAutoInventory();
            PassiveTagSubjectTypeEnum fromCode = PassiveTagSubjectTypeEnum.fromCode(tag.getSubjectType().toString());
            if (fromCode == null) {
                continue;
            }
            switch (fromCode) {
                case PALLET_NO:
                    invLotLocList.stream()
                            .filter(invLotLoc -> invLotLoc.getPalletNum().equals(tag.getSubjectNo()))
                            .findAny().ifPresent(loc ->
                                    setInventoryData(BeanUtil.copyProperties(loc, PassiveAutoInventoryDataQuery.class), tag, inventory));

                    invLotLocSnList.stream()
                            .filter(invLotLocSn -> invLotLocSn.getPalletNum().equals(tag.getSubjectNo()))
                            .findAny().ifPresent(sn ->
                                    setInventoryData(BeanUtil.copyProperties(sn, PassiveAutoInventoryDataQuery.class), tag, inventory));
                    break;
                case LOT_NUM:
                    invLotLocList.stream()
                            .filter(invLotLoc -> invLotLoc.getLotNum().equals(tag.getSubjectNo()))
                            .findAny().ifPresent(loc ->
                                    setInventoryData(BeanUtil.copyProperties(loc, PassiveAutoInventoryDataQuery.class), tag, inventory));

                    invLotLocSnList.stream()
                            .filter(invLotLocSn -> invLotLocSn.getLotNum().equals(tag.getSubjectNo()))
                            .findAny().ifPresent(sn ->
                                    setInventoryData(BeanUtil.copyProperties(sn, PassiveAutoInventoryDataQuery.class), tag, inventory));
                    break;
                case SSCC:
                    // TODO： SSCC未设定
                    break;
                case SERIAL_NUMBER:
                    invLotLocSnList.stream()
                            .filter(invLotLocSn -> invLotLocSn.getSkuSn().equals(tag.getSubjectNo()))
                            .findAny().ifPresent(sn ->
                                    setInventoryData(BeanUtil.copyProperties(sn, PassiveAutoInventoryDataQuery.class), tag, inventory));
                    break;
                case BOX:
                    invLotLocSnList.stream()
                            .filter(invLotLocSn -> invLotLocSn.getBoxCode().equals(tag.getSubjectNo()))
                            .findAny().ifPresent(sn ->
                                    setInventoryData(BeanUtil.copyProperties(sn, PassiveAutoInventoryDataQuery.class), tag, inventory));
                default:
                    break;
            }

            passiveAutoInventoryList.add(inventory);
        }
    }

    /**
     * 设置盘点清单数据
     *
     * @param dataQuery 数据查询
     * @param tag 标记
     * @param inventory 库存
     */
    private void setInventoryData(PassiveAutoInventoryDataQuery dataQuery, PassiveTag tag, PassiveAutoInventory inventory)
    {
        inventory.setWarehouseId(dataQuery.getWarehouseId());
        inventory.setWarehouseName(dataQuery.getWarehouseName());
        inventory.setWarehouseZoneId(dataQuery.getZoneId());
        inventory.setWarehouseZoneName(dataQuery.getZoneName());
        inventory.setWarehouseLocId(dataQuery.getLocId());
        inventory.setWarehouseLocName(dataQuery.getLocCode());
        inventory.setBindSubject(tag.getBindSubject());
        inventory.setTagNo(tag.getTagNo());
        inventory.setPalletNo(dataQuery.getPalletNum());
        inventory.setBatchNumber(dataQuery.getLotNum());
        inventory.setShelf(dataQuery.getShelfName());
        inventory.setCargoCode(dataQuery.getSkuCode());
        inventory.setCargoName(dataQuery.getSkuName());
        inventory.setQuantityInventory(dataQuery.getQty().doubleValue());
        inventory.setStatus(Integer.parseInt(PassiveAutoInventoryStatusEnum.NO_INVENTORY.getCode()));
        preSave(inventory);
    }

    /**
     * 环境过滤
     *
     * @param autoInventoryList 盘点清单
     */
    private void envFiltration(List<PassiveAutoInventory> autoInventoryList)
    {
        // 校验当前服务环境，pre、prod环境执行第三方接口调用
        Optional<String> appEnvName = AppConfig.getAppEnvName();
        if (appEnvName.isPresent()) {
            String appEnv = appEnvName.get();
            if (CollectionUtil.contains(
                    CollectionUtil.newArrayList(AppConstant.PRE_CODE, AppConstant.PROD_CODE),
                    appEnv)) {
                // 当前用户
                UserQuery data = this.getUserQuery();
                // 启动盘点
                this.startThirdPartyInventory(autoInventoryList, data);
            }
        }
    }

    private void startThirdPartyInventory(List<PassiveAutoInventory> autoInventoryList, UserQuery userQuery)
    {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String timestamp = LocalDateTime.now().format(formatter);
        // 仓库库区地址分组
        autoInventoryList.stream().collect(Collectors.groupingBy(inventory ->
        {
            String warehouseName = inventory.getWarehouseName();
            return warehouseName ;
        })).forEach((key, value) ->
        {
            // 根据不同地区调用无源接口
            AssetStartRequest request = new AssetStartRequest();
            request.setTaskname("Task"+timestamp);
            request.setLocation(key);// 位置单元全称（部署系统时定义的位置单元）
            request.setReinventory("true");
            ThirdResponse thirdResponse = thirdTaskInfoService.ableTaskInfo(request);
            if (!thirdResponse.getIsSuccess() && thirdResponse.getData() != null) {
                AssetStartResponse startResponse = JSONUtil.toBean(JSONUtil.toJsonStr(thirdResponse.getData()), AssetStartResponse.class);
                if (ObjectUtil.isNotEmpty(startResponse)) {
                    String taskId = startResponse.getTaskId();
                    value.forEach(inventory ->
                            inventory.setInventoryTaskNo(taskId));
                }
            }
        });

    }

    /**
     * 查询仓库库区树形列表
     *
     * @return {@link List }<{@link WarehouseTreeQuery }>
     */
    public List<WarehouseTreeQuery> getWarehouseTree() {
        // 查询仓库
        List<Warehouse> warehouseQueryList = warehouseService.findAll();

        return warehouseQueryList.stream().map(warehouseQuery -> {
            // 仓库信息
            WarehouseTreeQuery warehouseTreeQuery = new WarehouseTreeQuery();
            warehouseTreeQuery.setValue(Convert.toStr(warehouseQuery.getId()));
            warehouseTreeQuery.setLabel(warehouseQuery.getWarehouseName());
            return warehouseTreeQuery;
        }).collect(Collectors.toList());
    }

    /**
     * 停止盘点
     */
    @Transactional(rollbackFor = Exception.class)
    public void stopInventory()
    {
        // 自动盘点最新日志查询
        PassiveAutoInventoryLog inventoryLog = passiveAutoInventoryLogDao.newLog(null);
        if (EmptyUtil.isEmpty(inventoryLog)) {
            throw new ServiceException(PassiveExceptionEnum.PASSIVE_AUTO_INVENTORY_NOW_NULL.getMsg());
        }
        PassiveAutoInventoryCondition condition = new PassiveAutoInventoryCondition();
        condition.setEventNo(inventoryLog.getEventNo());
        List<PassiveAutoInventory> inventoryTaskList = dao.getInventoryTaskNoByEventNo(condition);
        if (EmptyUtil.isEmpty(inventoryTaskList)) {
            return;
        }
        List<String> inventoryTaskNoList = inventoryTaskList.stream().map(PassiveAutoInventory::getInventoryTaskNo).collect(Collectors.toList());
        //异步更新盘点结果
        UserQuery userQuery = this.getUserQuery();
        CompletableFuture.runAsync(() -> updateThirdPartyInventoryResult(inventoryLog.getEventNo(), userQuery));

        // 校验当前服务环境，pre、prod环境执行第三方接口调用
        Optional<String> appEnvName = AppConfig.getAppEnvName();
        if (appEnvName.isPresent()) {
            String appEnv = appEnvName.get();
            if (CollectionUtil.contains(
                    CollectionUtil.newArrayList(AppConstant.PRE_CODE, AppConstant.PROD_CODE),
                    appEnv)) {

                // 当前用户
                UserQuery data = this.getUserQuery();
                // 停止盘点
                this.stopThirdPartyInventory(inventoryTaskNoList, data);
            }
        }

        // 更新盘点日志->盘点结束时间
        inventoryLog.setEndTime(new Date());
        passiveAutoInventoryLogService.preSave(inventoryLog);
        passiveAutoInventoryLogService.update(inventoryLog);
    }

    /**
     * 停止第三方无源盘点
     *
     * @param inventoryTaskNoList 盘点任务单号列表
     * @param userQuery 用户查询
     */
    private void stopThirdPartyInventory(List<String> inventoryTaskNoList, UserQuery userQuery)
    {
        // 盘点任务列表
        inventoryTaskNoList.forEach(inventoryTaskNo ->
        {
            AssetStopRequest request = new AssetStopRequest();
            request.setTaskId(Integer.valueOf(inventoryTaskNo));
            thirdTaskInfoService.stopInout(request);
        });
    }


    /**
     * 第三方无源盘点结果查询
     *
     * @param inventoryTaskNoList 盘点任务单号列表
     * @param userQuery 用户查询
     */
    private void thirdPartyInventoryResult(List<String> inventoryTaskNoList, UserQuery userQuery)
    {
        List<String> epcIdTotalList = new ArrayList<>();
        // 盘点任务列表
        inventoryTaskNoList.forEach(inventoryTaskNo ->
        {
            int page = 1;
            queryAutoInventoryResult(userQuery, inventoryTaskNo, page, epcIdTotalList);
        });

        // 第三方盘点结果，重复查询结果过滤
        if (EmptyUtil.isNotEmpty(epcIdTotalList)) {
            // 1. 查询所有相关明细记录（添加悲观锁 FOR UPDATE 防止并发更新）
            List<PassiveAutoInventoryDetails> inventoryDetails = passiveAutoInventoryDetailsDao.findForUpdate(epcIdTotalList);

            if (EmptyUtil.isEmpty(inventoryDetails)) {
                return;
            }

            // 2. 按inventory_uuid分组
            Map<String, List<PassiveAutoInventoryDetails>> uuidDetailsMap = inventoryDetails.stream()
                    .collect(Collectors.groupingBy(PassiveAutoInventoryDetails::getInventoryUuid));

            // 存储每个主表UUID对应的无源数量
            Map<String, BigDecimal> uuidTotalQuantityMap = new HashMap<>();
            // 存储需要更新的明细记录ID
            List<Long> detailIdsToUpdate = new ArrayList<>();

            for (Map.Entry<String, List<PassiveAutoInventoryDetails>> entry : uuidDetailsMap.entrySet()) {
                String uuid = entry.getKey();
                List<PassiveAutoInventoryDetails> details = entry.getValue();

                // 检查是否存在商品标签记录（容器号为null或"*"）
                boolean hasInvalidContainer = details.stream()
                        .anyMatch(d -> isEmptyContainer(d.getPalletNum()));

                BigDecimal totalQuantity = BigDecimal.ZERO;

                if (hasInvalidContainer) {
                    // 规则：存在商品标签时，只累加商品标签的数量
                    for (PassiveAutoInventoryDetails detail : details) {
                        if (isEmptyContainer(detail.getPalletNum()) && ObjectUtil.equal(detail.getScan(),StatusEnum.NO.getCode())) {
                            totalQuantity = totalQuantity.add(detail.getQuantity());
                            detailIdsToUpdate.add(detail.getId());
                        }
                    }
                } else {
                    // 规则：存在容器标签时，累加所有记录的数量
                    for (PassiveAutoInventoryDetails detail : details) {
                        if ( ObjectUtil.equal(detail.getScan(),StatusEnum.NO.getCode())) {
                            totalQuantity = totalQuantity.add(detail.getQuantity());
                            detailIdsToUpdate.add(detail.getId());
                        }
                    }
                }

                uuidTotalQuantityMap.put(uuid, totalQuantity);
            }

            // 3. 更新明细表扫描状态
            if (!detailIdsToUpdate.isEmpty()) {

                passiveAutoInventoryDetailsDao.updateScanStatusByIds(
                        detailIdsToUpdate
                );
            }

            // 4. 处理主表更新
            List<String> uuidsToUpdate = new ArrayList<>(uuidTotalQuantityMap.keySet());
            if (uuidsToUpdate.isEmpty()) {
                return;
            }

            // 查询需要更新的主表记录
            ConditionRule conditionRule = ConditionRule.getInstance();
            conditionRule.andIn(PassiveAutoInventory::getUuid, uuidsToUpdate);
            List<PassiveAutoInventory> passiveAutoInventories = dao.find(conditionRule);


            for (PassiveAutoInventory inventory : passiveAutoInventories) {
                PassiveAutoInventoryUpdateItem passiveAutoInventoryUpdateItem = new PassiveAutoInventoryUpdateItem();
                String uuid = inventory.getUuid();
                BigDecimal totalQuantity = uuidTotalQuantityMap.get(uuid);

                if (totalQuantity != null) {

                    // 获取无源盘点总数
                    double passiveInventory = totalQuantity.doubleValue();

                    // 获取账面数据
                    double bookInventory = Optional.ofNullable(inventory.getQuantityInventory())
                            .orElse(0.0);

                    // 设置无源盘点数
                    double currentPassive = Optional.ofNullable(inventory.getPassiveInventory())
                            .orElse(0.0);
                    passiveAutoInventoryUpdateItem.setPassiveInventory(currentPassive + passiveInventory);
                    // 计算盘存比例
                    double totalPassive = currentPassive + passiveInventory;
                    double inventoryStock = (bookInventory > 0) ?
                            (totalPassive / bookInventory) * 100 : 0;

                    passiveAutoInventoryUpdateItem.setInventoryStock(inventoryStock);
                    // 标记为已盘点
                    passiveAutoInventoryUpdateItem.setStatus(Integer.parseInt(PassiveAutoInventoryStatusEnum.YES_INVENTORY.getCode()));


                    preSave(passiveAutoInventoryUpdateItem);
                    //设计到只对相关字段做更改，无法批量修改
                    dao.update(passiveAutoInventoryUpdateItem);
                }
            }

        }
    }

    // 判断是否为无效容器号
    private boolean isEmptyContainer(String palletNum) {
        return palletNum == null || palletNum.trim().isEmpty() || "*".equals(palletNum.trim());
    }    /**
     * 查询自动盘点结果
     *
     * @param userQuery 用户查询
     * @param inventoryTaskNo 库存任务单号
     * @param page 分页
     * @param epcIdTotalList 标签总列表
     */
    private void queryAutoInventoryResult(UserQuery userQuery, String inventoryTaskNo, int page, List<String> epcIdTotalList)
    {
        AssetQueryRequest request = new AssetQueryRequest();
        request.setTaskId(Integer.valueOf(inventoryTaskNo));

        log.info("查询自动盘点结果，request：{}", JSONUtil.toJsonStr(request));
        ThirdResponse thirdResponse = thirdTaskInfoService.queryInoutInfo(request);
        if (thirdResponse.getIsSuccess() && thirdResponse.getData() != null){
            PassiveInventoryResultResponse data = JSONUtil.toBean(JSONUtil.toJsonStr(thirdResponse.getData()), PassiveInventoryResultResponse.class);
            if (EmptyUtil.isNotEmpty(data)) {
                // 本次查询结果
                List<PassiveInventoryResultRecord> recordList = data.getRecords();
                List<String> epcIdList = recordList.stream().map(PassiveInventoryResultRecord::getEpcId).toList();
                epcIdTotalList.addAll(epcIdList);
                // 剩余结果查询
                int total = EmptyUtil.isEmpty(data.getTotal()) ? 0 : Integer.parseInt(data.getTotal());
                if (total > 1000) {
                    page++;
                    queryAutoInventoryResult(userQuery, inventoryTaskNo, page, epcIdTotalList);
                }
            }
        }
    }

    public List<WarehouseTreeQuery> getWarehouseTreeWithPermission() {
        // 查询仓库（限制最大数量1000）
        WarehousePageCondition warehousePageCondition = new WarehousePageCondition();
        warehousePageCondition.setPageSize(1000);
        warehousePageCondition.setPageNumber(1);
        PageResult<WarehouseQuery> warehousePage = warehouseService.page(warehousePageCondition);

        if (CollectionUtil.isEmpty(warehousePage.getRows())) {
            return Collections.emptyList();
        }

        // 批量查询所有库区
        List<WarehouseZone> allZones = warehouseZoneService.findAll();

        // 按仓库ID分组库区（过滤掉无仓库ID的库区）
        Map<Long, List<WarehouseZone>> zonesByWarehouseId = allZones.stream()
                .filter(zone -> EmptyUtil.isNotEmpty(zone.getWarehouseId()))
                .collect(Collectors.groupingBy(WarehouseZone::getWarehouseId));

        // 构建树形结构
        return warehousePage.getRows().stream()
                .map(warehouse -> {
                    WarehouseTreeQuery treeNode = new WarehouseTreeQuery();
                    treeNode.setValue(Convert.toStr(warehouse.getId()));
                    treeNode.setLabel(warehouse.getWarehouseName());

                    Optional.ofNullable(zonesByWarehouseId.get(warehouse.getId()))
                            .ifPresent(zones -> {
                                List<WarehouseZoneTreeQuery> children = zones.stream()
                                        .map(zone -> {
                                            WarehouseZoneTreeQuery w = new WarehouseZoneTreeQuery();
                                            w.setValue(Convert.toStr(zone.getId()));
                                            w.setLabel(zone.getZoneName());
                                            return w;
                                        })
                                        .collect(Collectors.toList());
                                treeNode.setChildren(children);
                            });

                    return treeNode;
                })
                .filter(node -> EmptyUtil.isNotEmpty(node.getChildren()))
                .collect(Collectors.toList());
    }

    /**
     *@Description 生成无源盘点任务数据
     *@Param item
     *@Return Void
     *@Date 2025/7/22 17:39
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void startInventoryNew(PassiveAutoInventoryStartItem item) {
        //根据仓库ID查询库存数据
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InvLotLoc::getWarehouseId, item.getWarehouseIdList());
        conditionRule.andGreaterThan(InvLotLocSn::getQty, BigDecimal.ZERO);
        List<InvLotLoc> invLotLocList = invLotLocService.find(InvLotLoc.class, conditionRule);
        if(CollectionUtil.isEmpty(invLotLocList)){
            throw new ServiceException(StockExcepitonEnum.WAREHOUSE_QUERY_NO_LOC_DATA);
        }
        // 日志
        String eventNo = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
        PassiveAutoInventoryLog passiveAutoInventoryLog = new PassiveAutoInventoryLog();
        passiveAutoInventoryLog.setEventNo(eventNo);
        passiveAutoInventoryLog.setStartTime(DateUtil.date());
        passiveAutoInventoryLog.setCompleteLevel(NumberUtil.div(0, 100));
        passiveAutoInventoryLogService.saveOrUpdate(passiveAutoInventoryLog);
        List<PassiveAutoInventory> result = new ArrayList<>();
        List<PassiveAutoInventoryDetails> passiveAutoInventoryDetailsList = new ArrayList<>();
        //生成盘点任务
        Map<String,List<InventoryStocktakeTaskDetails>> taskDetailsMap;
        if(YesNoEnum.YES.getCode().equals(item.getCreateStockTakeOrder())){
            List<InventoryStocktakeTaskDetails> taskDetails = inventoryStocktakeOrderService.generateTaskDetails(invLotLocList);
            log.info("生成盘点任务数据：{}", JSONUtil.toJsonStr(taskDetails));
            //根据仓库ID、商品ID转map
            taskDetailsMap = taskDetails.stream().collect(Collectors.groupingBy(x -> x.getWarehouseId() + "_" +x.getSkuId()));
        } else {
            taskDetailsMap = new HashMap<>();
        }
        //抽取容器号
        List<String> palletNumList = invLotLocList.stream().map(InvLotLoc::getPalletNum).distinct().collect(Collectors.toList());
        Map<String,PassiveTag> palletNumTagMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(palletNumList)){
            //根据容器号查询已绑定且绑定对象为容器的的标签号
            List<PassiveTag> palletNumTagList = passiveTagService.findByPalletNumList(palletNumList);
            if(CollectionUtil.isNotEmpty(palletNumTagList)){
                palletNumTagMap = palletNumTagList.stream().collect(Collectors.toMap(PassiveTag::getSubjectNo, Function.identity(), (a, b) -> a));
            }
        }
        //抽取商品ID
        List<Long> skuIdList = invLotLocList.stream().map(InvLotLoc::getSkuId).collect(Collectors.toList());
        //根据商品查询对应标签号
        List<PassiveTag> tagList = passiveTagService.findBySkuIdList(skuIdList);
        Map<Long, List<PassiveTag>> tagMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(tagList)){
            //根据商品分组
            tagMap = tagList.stream().collect(Collectors.groupingBy(PassiveTag::getSkuId));
        }
        //查询商品信息
        List<Sku> skuList = skuService.findByIds(skuIdList.toArray(new Long[0]));
        //转map
        Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (a, b) -> a));
        //根据仓库ID、商品ID进行分组
        Map<String, List<InvLotLoc>> invLotLocMap = invLotLocList.stream().collect(Collectors.groupingBy(invLotLoc -> invLotLoc.getWarehouseId() + "_" +invLotLoc.getSkuId()));
        Map<String, PassiveTag> finalPalletNumTagMap = palletNumTagMap;
        Map<Long, List<PassiveTag>> finalTagMap = tagMap;
        invLotLocMap.forEach((key, value) -> {
            String uuid = StrUtil.uuid();
            PassiveAutoInventory passiveAutoInventory = new PassiveAutoInventory();
            passiveAutoInventory.setEventNo(eventNo);
            passiveAutoInventory.setUuid(uuid);
            //仓库ID
            passiveAutoInventory.setWarehouseId(value.get(0).getWarehouseId());
            //仓库名称
            passiveAutoInventory.setWarehouseName(value.get(0).getWarehouseName());
            //库位ID
            passiveAutoInventory.setWarehouseLocId(value.get(0).getLocId());
            //商品ID
            passiveAutoInventory.setSkuId(value.get(0).getSkuId());
            //商品代码
            Sku sku = skuMap.get(value.get(0).getSkuId());
            if(ObjectUtil.isNotNull(sku)){
                passiveAutoInventory.setCargoCode(sku.getSkuCode());
                passiveAutoInventory.setCargoName(sku.getSkuName());
            }
            //状态默认未盘
            passiveAutoInventory.setStatus(Integer.valueOf(PassiveAutoInventoryStatusEnum.NO_INVENTORY.getCode()));
            BigDecimal qty = value.stream().map(InvLotLoc::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            passiveAutoInventory.setQuantityInventory(qty.doubleValue());
            //当前对应的库存数据根据容器号进行分组 如果是绑定容器号一个商品对应多个容器号 一个容器号对应一个标签，标签对应数量就是当前商品库存总数量
            Map<String, List<InvLotLoc>> palletNumInvLotLocMap = value.stream().collect(Collectors.groupingBy(InvLotLoc::getPalletNum));
            palletNumInvLotLocMap.forEach((palletNum, palletNumInvLotLocList) -> {
                //从当前分组库存数据上拿到容器对应的标签号
                PassiveTag passiveTag = finalPalletNumTagMap.get(palletNum);
                if(ObjectUtil.isNotNull(passiveTag)){
                    PassiveAutoInventoryDetails passiveAutoInventoryDetails = new PassiveAutoInventoryDetails();
                    passiveAutoInventoryDetails.setScan(YesNoEnum.NO.getCode());
                    passiveAutoInventoryDetails.setInventoryUuid(uuid);
                    passiveAutoInventoryDetails.setPalletNum(palletNum);
                    passiveAutoInventoryDetails.setTagNo(passiveTag.getTagNo());
                    passiveAutoInventoryDetails.setQuantity(palletNumInvLotLocList.stream().map(InvLotLoc::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    passiveAutoInventoryDetailsService.preSave(passiveAutoInventoryDetails);
                    passiveAutoInventoryDetailsList.add(passiveAutoInventoryDetails);
                }
            });
            //如果是绑定货物一个商品对应多个标签，拿到每个标签对应的ea数
            List<PassiveTag> skuTagList = finalTagMap.get(passiveAutoInventory.getSkuId());
            if (CollectionUtil.isNotEmpty(skuTagList)){
                skuTagList.forEach(skuTag -> {
                    PassiveAutoInventoryDetails passiveAutoInventoryDetails = new PassiveAutoInventoryDetails();
                    passiveAutoInventoryDetails.setScan(YesNoEnum.NO.getCode());
                    passiveAutoInventoryDetails.setInventoryUuid(uuid);
                    passiveAutoInventoryDetails.setTagNo(skuTag.getTagNo());
                    passiveAutoInventoryDetails.setQuantity(new BigDecimal(ObjectUtil.isNull(skuTag.getMinEa())? 0: skuTag.getMinEa()));
                    passiveAutoInventoryDetailsService.preSave(passiveAutoInventoryDetails);
                    passiveAutoInventoryDetailsList.add(passiveAutoInventoryDetails);
                });
            }
            //赋值人工盘点任务号
            List<InventoryStocktakeTaskDetails> taskDetails = taskDetailsMap.get(key);
            if (CollectionUtil.isNotEmpty(taskDetails)){
                passiveAutoInventory.setInventorySubTasks(taskDetails.get(0).getTaskNo());
            }
            preSave(passiveAutoInventory);
            result.add(passiveAutoInventory);
        });
        if(CollectionUtil.isNotEmpty(passiveAutoInventoryDetailsList)){
            passiveAutoInventoryDetailsService.batchInsert(passiveAutoInventoryDetailsList);
        }
        if(CollectionUtil.isNotEmpty(result)){
            //易载物接口生成任务号及回填
            envFiltration( result);
            dao.batchInsert(result);
        }
    }

    /**
     *@Description 根据任务号更新无源盘点手工盘点数
     *@Param taskNoList
     *@Return Void
     *@Date 2025/7/24 14:35
     *<AUTHOR>
     **/
    public void modifyTaskCount(List<String> taskNoList) {
        //根据任务号查询无源盘点任务
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(PassiveAutoInventory::getInventorySubTasks, taskNoList);
        List<PassiveAutoInventory> passiveAutoInventoryList = dao.find(conditionRule);
        List<ManualInventoryModifyItem> updateList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(passiveAutoInventoryList)){
            //根据任务号进行分组
            Map<String, List<PassiveAutoInventory>> taskNoMap = passiveAutoInventoryList.stream().collect(Collectors.groupingBy(PassiveAutoInventory::getInventorySubTasks));
            List<String> findTaskNoList = taskNoMap.keySet().stream().toList();
            //查询对应的手工盘点任务明细
            List<InventoryStocktakeTaskDetails> taskDetailsList = inventoryStocktakeTaskDetailsService.findByTaskNoList(findTaskNoList);
            if(CollectionUtil.isNotEmpty(taskDetailsList)){
                Map<String, List<InventoryStocktakeTaskDetails>> taskDetailsMap = taskDetailsList.stream().collect(Collectors.groupingBy(InventoryStocktakeTaskDetails::getTaskNo));
                taskNoMap.forEach((key,value)->{
                    List<InventoryStocktakeTaskDetails> taskDetails = taskDetailsMap.get(key);
                    if(CollectionUtil.isNotEmpty(taskDetails)){
                        //根据商品分组
                        Map<Long, List<InventoryStocktakeTaskDetails>> skuIdMap = taskDetails.stream().collect(Collectors.groupingBy(InventoryStocktakeTaskDetails::getSkuId));
                        value.forEach(passiveAutoInventory -> {
                            List<InventoryStocktakeTaskDetails> skuIdList = skuIdMap.get(passiveAutoInventory.getSkuId());
                            if(CollectionUtil.isNotEmpty(skuIdList)){
                                //实盘数量是否都为空
                                boolean flag = skuIdList.stream().anyMatch(x -> ObjectUtil.isNotNull(x.getActualCount()));
                                if(flag){
                                    ManualInventoryModifyItem item = new ManualInventoryModifyItem();
                                    item.setId(passiveAutoInventory.getId());
                                    item.setManualInventory(skuIdList.stream().filter(x -> ObjectUtil.isNotNull(x.getActualCount())).mapToDouble(InventoryStocktakeTaskDetails::getActualCount).sum());
                                    preSave(item);
                                    updateList.add(item);
                                }
                            }
                        });
                    }
                });
            }
        }
        if(CollectionUtil.isNotEmpty(updateList)){
            dao.batchUpdateItemToSqlExecution(updateList);
        }
    }
}
