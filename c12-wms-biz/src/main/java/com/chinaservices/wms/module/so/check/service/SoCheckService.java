package com.chinaservices.wms.module.so.check.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.chinaservices.auth.module.dict.domain.DictDataCondition;
import com.chinaservices.auth.module.dict.domain.DictDataQuery;
import com.chinaservices.auth.module.dict.feign.RemoteDictDataService;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.module.constant.YesNoEnum;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constants.DictDataTypeConstants;
import com.chinaservices.wms.common.enums.DocumentTypeEnum;
import com.chinaservices.wms.common.enums.PackStatusEnum;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.enums.so.SoRecheckStatusEnum;
import com.chinaservices.wms.common.enums.stock.TraceBackBusinessTypeEnum;
import com.chinaservices.wms.common.exception.SoExcepitonEnum;
import com.chinaservices.wms.module.basic.carrier.model.Carrier;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.archive.archives.service.LogisticsFileAutoGenerateService;
import com.chinaservices.wms.module.folder.logistics.domain.LogisticsFolderDetailLinkCondition;
import com.chinaservices.wms.module.so.check.dao.SoRecheckDetailDao;
import com.chinaservices.wms.module.so.check.dao.SoRecheckHeaderDao;
import com.chinaservices.wms.module.so.check.model.SoRecheckDetail;
import com.chinaservices.wms.module.so.check.model.SoRecheckHeader;
import com.chinaservices.wms.module.so.pack.dao.PackDetailDao;
import com.chinaservices.wms.module.so.pack.domain.CustomerAddressKey;
import com.chinaservices.wms.module.so.pack.domain.PackBase;
import com.chinaservices.wms.module.so.pack.domain.PackBoxInfo;
import com.chinaservices.wms.module.so.pack.domain.PackHeaderInfo;
import com.chinaservices.wms.module.so.pack.service.PackHeaderService;
import com.chinaservices.wms.module.so.picking.model.SoPicking;
import com.chinaservices.wms.module.so.so.dao.SoAllocationDao;
import com.chinaservices.wms.module.so.so.domain.*;
import com.chinaservices.wms.module.so.so.model.SoAllocation;
import com.chinaservices.wms.module.so.so.model.SoAllocationPallet;
import com.chinaservices.wms.module.so.so.model.SoDetail;
import com.chinaservices.wms.module.so.so.model.SoHeader;
import com.chinaservices.wms.module.so.so.service.SoAllocationPalletService;
import com.chinaservices.wms.module.so.so.service.SoAllocationPickingService;
import com.chinaservices.wms.module.so.so.service.SoDetailService;
import com.chinaservices.wms.module.so.so.service.SoAllocationPalletService;
import com.chinaservices.wms.module.so.so.service.SoAllocationPickingService;
import com.chinaservices.wms.module.so.so.service.SoHeaderService;
import com.chinaservices.wms.module.so.wv.dao.WvDetailDao;
import com.chinaservices.wms.module.so.wv.model.WvDetail;
import com.chinaservices.wms.module.so.wv.model.WvHeader;
import com.chinaservices.wms.module.so.wv.service.WvHeaderService;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.back.model.TraceBackRecordItem;
import com.chinaservices.wms.module.stock.back.service.TraceBackRecordService;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 复核
 */
@Service
public class SoCheckService extends ModuleBaseServiceSupport<SoRecheckHeaderDao, SoRecheckHeader, Long>
{
    @Autowired
    private NumberGenerator numberGenerator;
    @Autowired
    private SoRecheckDetailDao soRecheckDetailDao;
    @Autowired
    private SoRecheckDetailService soRecheckDetailService;
    @Autowired
    private WvDetailDao wvDetailDao;
    @Autowired
    private SoHeaderService soHeaderService;
    @Autowired
    private WvHeaderService wvHeaderService;
    @Autowired
    private SoDetailService soDetailService;
    @Autowired
    private PackDetailDao packDetailDao;
    @Autowired
    private PackHeaderService packHeaderService;
    @Autowired
    private StockService stockService;
    @Autowired
    private SoAllocationPickingService soAllocationPickingService;
    @Autowired
    private SoAllocationPalletService soAllocationPalletService;
    @Autowired
    private TaskExecutor taskExecutor;
    @Autowired
    private LogisticsFileAutoGenerateService logisticsFileAutoGenerateService;
    @Autowired
    private RemoteDictDataService remoteDictDataService;

    @Autowired
    private TraceBackRecordService traceBackRecordService;
    @Autowired
    private SoAllocationDao soAllocationDao;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private OwnerService ownerService;
    /*===== 新出库复核 ====*/

    /**
     * 保存复核--出库单拣货完成
     *
     * @param soPicking 拣货信息
     * @param allocationList 拣货任务列表
     */
    public void saveRecheckBySoNo(SoPicking soPicking, List<SoAllocation> allocationList)
    {
        if (EmptyUtil.isEmpty(soPicking) || EmptyUtil.isEmpty(allocationList)) {
            throw new ServiceException(SoExcepitonEnum.CHECK_PICKING_NOT_EXIST);
        }
        // 复核单信息
        SoRecheckHeader recheckHeader = new SoRecheckHeader();
        recheckHeader.setPickingNo(soPicking.getPickingNo());
        recheckHeader.setSoNo(soPicking.getSoNo());
        Long warehouseId = soPicking.getWarehouseId();
        // 校验如果拣货单的仓库信息为空，就去查询出库单的仓库信息
        if (EmptyUtil.isEmpty(warehouseId)) {
            SoHeader first = soHeaderService.findById(soPicking.getSoId());
            if (EmptyUtil.isNotEmpty(first)) {
                warehouseId = first.getWarehouseId();
            }
        }
        recheckHeader.setWarehouseId(warehouseId);
        recheckHeader.setCheckStatus(SoRecheckStatusEnum.un_recheck.getCode());
        String recheckNo = numberGenerator.nextValue(IdRuleConstant.RECHECK_NO);
        recheckHeader.setRecheckNo(recheckNo);
        // 复核单详情
        List<SoRecheckDetail> recheckDetailList = new ArrayList<>();
        assembleRecheckDetail(recheckDetailList, recheckNo, allocationList);
        // 保存数据
        dao.saveOrUpdate(recheckHeader);
        soRecheckDetailDao.batchInsertToSqlExecution(recheckDetailList);
        // 物流文件自动生成
        taskExecutor.execute(() ->
                this.pickingLogisticsFileAutoGenerate(allocationList, recheckNo)
        );
    }

    /**
     * 拣货物流文件自动生成
     *
     * @param allocationList 件货详情集合
     * @param recheckNo      复核单号
     */
    private void pickingLogisticsFileAutoGenerate(List<SoAllocation> allocationList, String recheckNo) {
        // 文件参数处理
        List<Long> soDetailIdList = allocationList.stream().map(SoAllocation::getSoDetailId).toList();
        List<SoDetail> detailList = soDetailService.findByIds(soDetailIdList.toArray(new Long[0]));
        List<String> soNoList = detailList.stream().map(SoDetail::getSoNo).filter(EmptyUtil::isNotEmpty).distinct().toList();

        // 复核单集合
        List<LogisticsFolderDetailLinkCondition> linkConditions = new ArrayList<>();
        for (String soNo : soNoList) {
            ArrayList<String> objects = new ArrayList<>();
            objects.add(recheckNo);
            LogisticsFolderDetailLinkCondition logisticsFolderDetailLinkCondition = new LogisticsFolderDetailLinkCondition();
            logisticsFolderDetailLinkCondition.setDocumentNos(objects);
            logisticsFolderDetailLinkCondition.setUpDocumentNo(soNo);
            linkConditions.add(logisticsFolderDetailLinkCondition);
        }

        // 复核物流文件
        logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                DocumentTypeEnum.CHECKING, linkConditions, SessionContext.getSessionUserInfo().getCompanyId(),
                SessionContext.getSessionUserInfo().getTenancy(), null);

        logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                DocumentTypeEnum.SO_CHECKING, linkConditions, SessionContext.getSessionUserInfo().getCompanyId(),
                SessionContext.getSessionUserInfo().getTenancy(), null);
    }

    /**
     * 保存复核--波次单完成二次分拣
     *
     * @param soPicking 拣货信息
     * @param allocationList 拣货任务列表
     */
    public void saveRecheckByWvNo(SoPicking soPicking, List<SoAllocation> allocationList)
    {
        if (EmptyUtil.isEmpty(soPicking) || EmptyUtil.isEmpty(allocationList)) {
            throw new ServiceException(SoExcepitonEnum.CHECK_PICKING_NOT_EXIST);
        }
        Map<Long, List<SoAllocation>> listMap = allocationList.stream().collect(Collectors.groupingBy(SoAllocation::getSoDetailId));

        // 查询波次单明细
        String wvNo = soPicking.getWvNo();
        List<Long> soDetailIdList = allocationList.stream()
                .map(SoAllocation::getSoDetailId)
                .filter(EmptyUtil::isNotEmpty).toList();
        ConditionRule rule = new ConditionRule().andEqual(WvDetail::getWvNo, wvNo).andIn(WvDetail::getSoDetailId, soDetailIdList);
        List<WvDetail> detailList = wvDetailDao.find(rule);
        if (EmptyUtil.isEmpty(detailList)) {
            throw new ServiceException(SoExcepitonEnum.WV_NOT_EXIST);
        }
        // 查询仓库id
        Long warehouseId;
        if (EmptyUtil.isEmpty(soPicking.getWarehouseId())) {
            Long wvId = soPicking.getWvId();
            WvHeader wvHeader = wvHeaderService.findById(wvId);
            warehouseId = wvHeader.getWarehouseId();
        } else {
            warehouseId = soPicking.getWarehouseId();
        }

        List<SoRecheckHeader> recheckHeaderList = new ArrayList<>();
        List<SoRecheckDetail> recheckDetailList = new ArrayList<>();
        Map<String, List<WvDetail>> detailListMap = detailList.stream().collect(Collectors.groupingBy(WvDetail::getSoNo));
        detailListMap.forEach((key, value) ->
        {
            // 复核单信息
            SoRecheckHeader recheckHeader = new SoRecheckHeader();
            recheckHeader.setPickingNo(soPicking.getPickingNo());
            recheckHeader.setSoNo(key);
            recheckHeader.setWarehouseId(warehouseId);
            recheckHeader.setCheckStatus(SoRecheckStatusEnum.un_recheck.getCode());
            String recheckNo = numberGenerator.nextValue(IdRuleConstant.RECHECK_NO);
            recheckHeader.setRecheckNo(recheckNo);
            preSave(recheckHeader);
            recheckHeaderList.add(recheckHeader);

            value.forEach(wvDetail ->
            {
                List<SoAllocation> list = listMap.get(wvDetail.getSoDetailId());
                if (EmptyUtil.isEmpty(list)) {
                    return;
                }
                // 组装复核明细
                assembleRecheckDetail(recheckDetailList, recheckNo, list);
            });
        });
        // 保存数据
        dao.batchInsertToSqlExecution(recheckHeaderList);
        soRecheckDetailDao.batchInsertToSqlExecution(recheckDetailList);

        // 物流文件自动生成
        taskExecutor.execute(() ->
                this.sortingLogisticsFileAutoGenerate(soDetailIdList, recheckDetailList)
        );
    }

    /**
     * 分拣物流文件自动生成
     *
     * @param soDetailIdList    出库单商品详情列表
     * @param recheckDetailList 复核明细表
     */
    private void sortingLogisticsFileAutoGenerate(List<Long> soDetailIdList, List<SoRecheckDetail> recheckDetailList) {
        // 文件参数处理
        List<SoDetail> soDetailList = soDetailService.findByIds(soDetailIdList.toArray(new Long[0]));
        Map<String, String> soIdMap = soDetailList.stream().collect(Collectors.toMap(SoDetail::getSoDetailNo, SoDetail::getSoNo));

        // 复核单集合
        List<LogisticsFolderDetailLinkCondition> linkConditions = new ArrayList<>();
        for (SoRecheckDetail detail : recheckDetailList) {
            String soNo = soIdMap.get(detail.getSoDetailNo());
            ArrayList<String> objects = new ArrayList<>();
            objects.add(detail.getRecheckNo());
            LogisticsFolderDetailLinkCondition logisticsFolderDetailLinkCondition = new LogisticsFolderDetailLinkCondition();
            logisticsFolderDetailLinkCondition.setDocumentNos(objects);
            logisticsFolderDetailLinkCondition.setUpDocumentNo(soNo);
            linkConditions.add(logisticsFolderDetailLinkCondition);
        }

        // 复核物流文件
        logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                DocumentTypeEnum.CHECKING, linkConditions, SessionContext.getSessionUserInfo().getCompanyId(),
                SessionContext.getSessionUserInfo().getTenancy(), null);

        logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                DocumentTypeEnum.SO_CHECKING, linkConditions, SessionContext.getSessionUserInfo().getCompanyId(),
                SessionContext.getSessionUserInfo().getTenancy(), null);
    }

    /**
     * 组装复核单详情
     *
     * @param recheckDetailList 复核详情列表
     * @param recheckNo 复核单号
     * @param list 列表
     */
    private void assembleRecheckDetail(List<SoRecheckDetail> recheckDetailList, String recheckNo, List<SoAllocation> list)
    {
        list.forEach(soAllocation ->
        {
            SoRecheckDetail detail = new SoRecheckDetail();
            detail.setPickingTaskNo(soAllocation.getPickingTaskNo());
            detail.setSoDetailNo(soAllocation.getSoDetailNo());
            detail.setSkuId(soAllocation.getSkuId());
            detail.setLotNum(soAllocation.getLotNum());
            detail.setQtyPickingEa(soAllocation.getPickingEa());
            detail.setCheckStatus(SoRecheckStatusEnum.un_recheck.getCode());
            detail.setRecheckNo(recheckNo);
            soRecheckDetailService.preSave(detail);
            recheckDetailList.add(detail);
        });
    }

    /**
     * 出库复核分页
     *
     * @param condition 分页条件
     * @return {@link PageResult }<{@link SoHeaderCheckQuery }>
     */
    public PageResult<SoHeaderCheckQuery> page(SoHeaderCheckCondition condition)
    {
        PageResult<SoHeaderCheckQuery> recheckPage = dao.page(condition);
        if (ObjectUtil.isNotEmpty(recheckPage) && CollectionUtils.isNotEmpty(recheckPage.getRows())){
            for (SoHeaderCheckQuery row : recheckPage.getRows()) {
                boolean isRecheck = ObjectUtil.isNotEmpty(row.getCheckStatus()) && SoRecheckStatusEnum.complete_recheck.getCode().equals(row.getCheckStatus());
                row.setIsRecheck(!isRecheck);

                // 上架任务号集合
                row.setPaNoNum(0L);
                String paNo = row.getPaNo();
                if (EmptyUtil.isNotEmpty(paNo)) {
                    String[] split = paNo.split(",");
                    List<String> paNoList = Arrays.stream(split).toList();
                    row.setPaNoNum((long) paNoList.size());
                    row.setPaNoList(paNoList);
                }
            }
        }
        return recheckPage;
    }

    /**
     * 根据复核单号查询复核单信息-单据预览数据查询
     * @param recheckNo
     * @return
     */
    public SoRecheckPreviewQuery getSoRecheckPreviewQueryByRecheckNo(String recheckNo) {
        SoRecheckPreviewQuery result = new SoRecheckPreviewQuery();
        SoHeaderCheckQuery soHeaderCheckQuery = dao.getSoRecheckHeaderQueryGetByRecheckNo(recheckNo);
        if(EmptyUtil.isEmpty(soHeaderCheckQuery)){
            throw new ServiceException(SoExcepitonEnum.SO_RECHECK_NOT_EXIST);
        }
        BeanUtil.copyProperties(soHeaderCheckQuery, result);
        // 查询数据字典 组装复核状态
        if(EmptyUtil.isNotEmpty(soHeaderCheckQuery.getCheckStatus())) {
            DictDataCondition dictDataCondition = new DictDataCondition();
            dictDataCondition.setType(DictDataTypeConstants.RECHECK_STATUS);
            ResponseData<List<DictDataQuery>> responseData = remoteDictDataService.getDictDataList(dictDataCondition);
            if(responseData.getSuccess()){
                List<DictDataQuery> data = responseData.getData();
                Map<String, String> dictMap = data.stream().collect(Collectors.toMap(DictDataQuery::getCode, DictDataQuery::getValue, (key1, key2) -> key1));
                if(EmptyUtil.isNotEmpty(dictMap) && dictMap.containsKey(soHeaderCheckQuery.getCheckStatus())) {
                    soHeaderCheckQuery.setCheckStatusStr(dictMap.get(soHeaderCheckQuery.getCheckStatus()));
                }
            }
        }
        result.setCheckQueryList(List.of(soHeaderCheckQuery));
        return result;
    }


    /**
     * 出库复核详情列表
     *
     * @param condition 条件
     * @return {@link List }<{@link SoCheckSkuQuery }>
     */
    public List<SoCheckSkuQuery> checkDetailList(SoCheckSkuCondition condition)
    {
        // 参数校验
        if (EmptyUtil.isEmpty(condition.getRecheckNo()) && EmptyUtil.isEmpty(condition.getRecheckDetailId())) {
            return new ArrayList<>();
        }
        // 查询复核数据
        List<SoCheckSkuQuery> soCheckSkuQueries = soRecheckDetailDao.checkDetailList(condition);
        soCheckSkuQueries.forEach(query ->
        {
            if (BigDecimal.ZERO.compareTo(query.getUnQtyCheckEa()) < 0 && EmptyUtil.isEmpty(query.getPaNo())) {
                query.setRecheck(true);
            }
        });
        return soCheckSkuQueries;
    }

    /**
     * 复核确认
     */
    @Transactional(rollbackFor = Exception.class)
    public void recheckConfirm(SoCheckConfirmCondition condition)
    {
        //操作人姓名
        String operator = SessionContext.getSessionUserInfo().getRealName();

        ConditionRule rule = new ConditionRule();
        rule.andEqual(SoRecheckDetail::getSoDetailNo, condition.getSoDetailNo());
        rule.andEqual(SoRecheckDetail::getRecheckNo, condition.getRecheckNo());
        rule.andEqual(SoRecheckDetail::getId, condition.getRecheckDetailId());
        SoRecheckDetail soDetails = soRecheckDetailDao.findFirst(rule);
        // 已复核数量
        BigDecimal qtyCheckEa = soDetails.getQtyCheckEa() == null ? BigDecimal.ZERO : soDetails.getQtyCheckEa();
        // 本次复核数量
        BigDecimal checkEa = condition.getCheckEa();
        // 累计复核数量
        BigDecimal totalCheckedEa = qtyCheckEa.add(checkEa);
        // 已拣货数|待复核数
        BigDecimal qtyPickedEa = soDetails.getQtyPickingEa() == null ? BigDecimal.ZERO : soDetails.getQtyPickingEa();
        if (totalCheckedEa.compareTo(qtyPickedEa) > 0) {
            throw new ServiceException(SoExcepitonEnum.CHECK_LT_EXCESSIVE, qtyPickedEa.longValue());
        }
        soDetails.setQtyCheckEa(totalCheckedEa);
        if (totalCheckedEa.compareTo(qtyPickedEa) == 0) {
            soDetails.setCheckStatus(SoRecheckStatusEnum.complete_recheck.getCode());
        } else {
            soDetails.setCheckStatus(SoRecheckStatusEnum.partial_recheck.getCode());
        }
        soDetails.setCheckOp(operator);
        soDetails.setCheckTime(new Date());
        soRecheckDetailDao.saveOrUpdate(soDetails);

        // 更新存货数量
        this.updateInvLotLocQty(soDetails, checkEa);

        // 复核单状态更新
        ConditionRule headerRule = new ConditionRule().andEqual(SoRecheckHeader::getRecheckNo, condition.getRecheckNo());
        SoRecheckHeader recheckHeader = dao.findFirst(headerRule);
        // 查询全部复核单详情状态
        List<SoRecheckDetail> recheckDetailList = soRecheckDetailDao.find(headerRule);
        boolean allMatch = recheckDetailList.stream()
                .allMatch(recheckDetail ->
                        SoRecheckStatusEnum.complete_recheck.getCode().equals(recheckDetail.getCheckStatus()));

        // 组装更新复核状态
        if (allMatch) {
            recheckHeader.setCheckStatus(SoRecheckStatusEnum.complete_recheck.getCode());
            //默认未生成打包任务
            recheckHeader.setIsGeneratePackTask(YesNoEnum.NO.getCode());
            recheckHeader.setCheckDoneTime(new Date());
            dao.update(recheckHeader);
        } else if (SoRecheckStatusEnum.un_recheck.getCode().equals(recheckHeader.getCheckStatus())){
            recheckHeader.setCheckStatus(SoRecheckStatusEnum.partial_recheck.getCode());
            dao.update(recheckHeader);
        }
        // 序列号追溯记录保存 -- skuSnList为空不执行
        String pickingTaskNo = soDetails.getPickingTaskNo();
        // 查询序列号列表
        SoAllocation allocation = soAllocationDao.findFirst(new ConditionRule().andEqual(SoAllocation::getPickingTaskNo, pickingTaskNo));
        String pickingSnNoStr = allocation.getPickingSnNoStr();
        if (EmptyUtil.isNotEmpty(pickingSnNoStr)) {
            String[] split = pickingSnNoStr.split(",");
            List<String> snNo = Arrays.asList(split);
            // 记录保存
            this.snNoLitSaveExtracted(snNo, recheckHeader, allocation.getOwnerId(), allocation.getWarehouseId());
        }
    }

    /**
     * 序列号列表保存追溯记录
     *
     * @param skuSnList     序列号列表
     * @param recheckHeader 复核单
     */
    private void snNoLitSaveExtracted(List<String> skuSnList, SoRecheckHeader recheckHeader, Long ownerId, Long warehouseId) {
        if (EmptyUtil.isNotEmpty(skuSnList)) {
            TraceBackRecordItem item = new TraceBackRecordItem();
            // 货主名称
            Owner owner = ownerService.findById(ownerId);
            item.setOwnerName(owner.getOwnerName());
            // 仓库名称
            Warehouse warehouse = warehouseService.findById(warehouseId);
            item.setWarehouseName(warehouse.getWarehouseName());
            // 业务单号=复核单号
            item.setBusinessNo(recheckHeader.getRecheckNo());
            // 业务类型=复核
            item.setBusinessType(TraceBackBusinessTypeEnum.CHECKING);
            // 来源单号=出库单号
            item.setSourceNo(recheckHeader.getSoNo());
            // 序列号列表
            item.setSnNoList(skuSnList);
            traceBackRecordService.execute(item);
        }
    }

    /**
     * 生成打包任务
     *
     * @param ids ids
     * @return {@link ResponseData }<{@link Boolean }>
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean generatePackTask(Long[] ids) {
        // 校验订单存在性
        List<SoRecheckHeader> soRecheckHeaders = dao.findByIds(ids);
        if (CollectionUtils.isEmpty(soRecheckHeaders)) {
            throw new ServiceException(SoExcepitonEnum.RECHECK_NOT_EXIST);
        }

        // 校验复核状态（必须为"完全复核"）
        List<SoRecheckHeader> invalidHeaders = soRecheckHeaders.stream()
                .filter(header -> !SoRecheckStatusEnum.complete_recheck.getCode().equals(header.getCheckStatus()))
                .toList();
        if (!invalidHeaders.isEmpty()) {
            throw new ServiceException(SoExcepitonEnum.RECHECK_STATUS_IS_NOT_A_COMPLETE_RECHECK,
                    StrUtil.join("、", invalidHeaders.stream().map(SoRecheckHeader::getId).toArray()));
        }

        // 校验是否已生成过打包任务
        List<SoRecheckHeader> generatedHeaders = soRecheckHeaders.stream()
                .filter(header -> YesNoEnum.YES.getCode().equals(header.getIsGeneratePackTask()))
                .toList();
        if (!generatedHeaders.isEmpty()) {
            throw new ServiceException(SoExcepitonEnum.PACKAGING_TASK_HAS_BEEN_GENERATED,
                    StrUtil.join("、", generatedHeaders.stream().map(SoRecheckHeader::getId).toArray()));
        }

        // 按客户+地址分组
        // 获取订单基础信息
        List<PackBase> packBaseList = soHeaderService.findPackInfoByIds(ids);

        // 执行分组
        Map<CustomerAddressKey, List<PackBase>> groupedOrders = packBaseList.stream()
                .collect(Collectors.groupingBy(
                        CustomerAddressKey::new,
                        Collectors.toList()
                ));
        Map<String,String> updateRecheckHeaderMaps = new HashMap<>();
        // 生成打包任务
        groupedOrders.forEach((addressKey, orders) -> {

            // 生成打包单号
            String packNo = numberGenerator.nextValue(IdRuleConstant.PACK_NO);
            PackHeaderInfo packHeaderInfo = new PackHeaderInfo();
            packHeaderInfo.setPackNo(packNo);
            packHeaderInfo.setPackStatus(PackStatusEnum.UNPACK.getCode());
            packHeaderInfo.setCustomerId(addressKey.getCustomerId());
            packHeaderInfo.setCustomerName(orders.getFirst().getCustomerName());

            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andIn(SoRecheckDetail::getRecheckNo,orders.getFirst().getRecheckNo());
            List<SoRecheckDetail> soRecheckDetails = soRecheckDetailDao.find(conditionRule);

            Set<Long> carrierIdSet  = orders.stream().map(PackBase::getCarrierId).filter(Objects::nonNull).collect(Collectors.toSet());
            Set<String> carrierNameSet  = orders.stream().map(PackBase::getCarrierName).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            if(CollUtil.isNotEmpty(carrierIdSet) && CollUtil.isNotEmpty(carrierNameSet )){

                packHeaderInfo.setCarrierId(carrierIdSet.iterator().next());

                packHeaderInfo.setCarrierName(carrierNameSet.iterator().next());
            }

            Warehouse warehouse = soDetailService.findWarehouseBySoDetailNo(soRecheckDetails.getFirst().getSoDetailNo());
            packHeaderInfo.setWarehouseId(warehouse.getId());
            packHeaderInfo.setWarehouseCode(warehouse.getWarehouseCode());
            packHeaderInfo.setWarehouseName(warehouse.getWarehouseName());
            for (PackBase order : orders) {
                PackBoxInfo packBoxInfo = new PackBoxInfo();
                packBoxInfo.setSoNo(order.getSoNo());
                packBoxInfo.setPackNo(packNo);
                packDetailDao.saveOrUpdate(packBoxInfo);
                updateRecheckHeaderMaps.put(order.getRecheckNo(),packNo);
            }
            packHeaderService.preSave(packHeaderInfo);
            packHeaderService.saveOrUpdate(packHeaderInfo);
        });
        soRecheckHeaders.forEach(header->{
            header.setIsGeneratePackTask(YesNoEnum.YES.getCode());
            header.setPackNo(updateRecheckHeaderMaps.get(header.getRecheckNo()));
        });
        //更新原订单标记
        dao.preSaveList(soRecheckHeaders);
        dao.batchUpdate(soRecheckHeaders);
        // 异步任务
        taskExecutor.execute(() -> {
            SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();
            Map<String, List<SoRecheckHeader>> recheckHeadersMap = soRecheckHeaders.stream().collect(Collectors.groupingBy(SoRecheckHeader::getSoNo));
            //打包单集合
            for (String soNo : recheckHeadersMap.keySet()) {
                List<LogisticsFolderDetailLinkCondition> asnOrderConditionList = new ArrayList<>();
                List<SoRecheckHeader> recheckHeaderList = recheckHeadersMap.get(soNo);
                Long warehouseId = recheckHeaderList.getFirst().getWarehouseId();
                LogisticsFolderDetailLinkCondition logisticsFolderDetailLinkCondition = new LogisticsFolderDetailLinkCondition();
                logisticsFolderDetailLinkCondition.setUpDocumentNo(soNo);
                logisticsFolderDetailLinkCondition.setDocumentNos(recheckHeaderList.stream().map(SoRecheckHeader::getPackNo).distinct().toList());
                asnOrderConditionList.add(logisticsFolderDetailLinkCondition);
                //这⾥第三个参数，为上级的单号，这⾥需替换成出库单号号码
                logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                        DocumentTypeEnum.PACKING, asnOrderConditionList, sessionUserInfo.getCompanyId(), sessionUserInfo.getTenancy(), warehouseId);
                //这⾥第三个参数，为上级的单号，这⾥需替换成出库单号号码
                logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                        DocumentTypeEnum.SO_PACKING, asnOrderConditionList, sessionUserInfo.getCompanyId(), sessionUserInfo.getTenancy(), warehouseId);
            }
        });
        return Boolean.TRUE;
    }

    /**
     * 更新存货数量
     *
     * @param soDetails 所以细节
     * @param checkEa   复核数量
     */
    private void updateInvLotLocQty(SoRecheckDetail soDetails, BigDecimal checkEa) {
        // 查询拣货详情
        String pickingTaskNo = soDetails.getPickingTaskNo();
        SoAllocation allocation = soAllocationPickingService.findFirst(new ConditionRule().andEqual(SoRecheckDetail::getPickingTaskNo, pickingTaskNo));
        List<SoAllocationPallet> soAllocationPallets = soAllocationPalletService.find(new ConditionRule().andEqual(SoRecheckDetail::getPickingTaskNo, pickingTaskNo));
        String palletNum = soAllocationPallets.stream().map(SoAllocationPallet::getToPalletNum).filter(EmptyUtil::isNotEmpty).distinct().findFirst().orElse(null);

        // 库存更新
        InvLotLocQtyBO invLotLocQtyBO = new InvLotLocQtyBO()
                .setLocId(allocation.getToLocId())
                .setLotNum(soDetails.getLotNum())
                .setOrderNo(soDetails.getRecheckNo())
                .setUpdateNum(checkEa)
                .setWarehouseId(allocation.getWarehouseId())
                .setSkuId(soDetails.getSkuId())
                .setOwnerId(allocation.getOwnerId())
                .setTransactionType(TransactionType.TRAN_OUT_QA)
                .setPalletNum(palletNum)
                .setToPallet(palletNum)
                .setToLocId(allocation.getToLocId())
                .setSkuSn(null);
        logger.info("复核库存更新：{}", JSONObject.toJSONString(invLotLocQtyBO));
        stockService.exec(invLotLocQtyBO);
    }
}
