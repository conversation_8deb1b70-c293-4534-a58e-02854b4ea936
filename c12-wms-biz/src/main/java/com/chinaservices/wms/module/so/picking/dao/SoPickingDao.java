package com.chinaservices.wms.module.so.picking.dao;

import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.sqlid.so.picking.SoPickingSqlId;
import com.chinaservices.wms.module.so.picking.domain.SoPickingCondition;
import com.chinaservices.wms.module.so.picking.domain.SoPickingPageCondition;
import com.chinaservices.wms.module.so.picking.domain.SoPickingQuery;
import com.chinaservices.wms.module.so.picking.model.SoPicking;
import com.chinaservices.wms.module.so.so.domain.SoAllocationQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 拣货管理dao层
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Repository
public class SoPickingDao extends ModuleBaseDaoSupport<SoPicking, Long>
{

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return {@link PageResult }<{@link SoPickingQuery }>
     */
    public PageResult<SoPickingQuery> page(SoPickingPageCondition condition)
    {
        return sqlExecutor.page(SoPickingQuery.class, SoPickingSqlId.SO_PICKING_QUERY_GET_PAGE_LIST, condition);
    }

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return {@link PageResult }<{@link SoPickingQuery }>
     */
    public PageResult<SoPickingQuery> pdaPage(SoPickingPageCondition condition)
    {
        return sqlExecutor.page(SoPickingQuery.class, SoPickingSqlId.SO_PICKING_QUERY_GET_PDA_PAGE_LIST, condition);
    }

    public PageResult<SoAllocationQuery> getByIdPda(SoPickingPageCondition condition) {
        return sqlExecutor.page(SoAllocationQuery.class, SoPickingSqlId.SO_PICKING_QUERY_GET_BY_ID_PDA, condition);
    }

    /**
     * 拣货表查询
     */
    public List<SoPicking> findByCondition(SoPickingCondition condition){
        return sqlExecutor.find(SoPicking.class, SoPickingSqlId.SO_PICKING_FIND_LIST, condition);
    }

    public Double findAvgTimeByWarehouseId(Long warehouseId) {
        return sqlExecutor.findDouble(SoPickingSqlId.FIND_AVG_TIME_BY_WAREHOUSEID,"warehouseId",warehouseId);
    }

    /**
     * 根据拣货员查询拣货单号
     */
    public List<SoPicking> getPickingNoByPickingOpName(String pickingOpName)
    {
        return sqlExecutor.find(SoPicking.class, SoPickingSqlId.SO_PICKING_QUERY_GET_PICKING_NO_BY_PICKING_OP_NAME,
                "pickingOpName", pickingOpName);
    }

    /**
     * 根据拣货单号查询拣货表信息
     * @param pickingNo
     * @return
     */
    public SoPicking getByPickingNo(String pickingNo) {
        if(EmptyUtil.isEmpty(pickingNo)){
            return null;
        }
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(SoPicking::getPickingNo, pickingNo);
        return findFirst(conditionRule);
    }
}
