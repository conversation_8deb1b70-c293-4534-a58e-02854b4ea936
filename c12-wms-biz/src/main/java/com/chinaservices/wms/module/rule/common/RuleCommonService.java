package com.chinaservices.wms.module.rule.common;


import com.chinaservices.wms.module.rule.rotation.domain.RuleRotationHeaderItem;
import com.chinaservices.wms.module.rule.rotation.model.RuleRotationHeader;
import org.springframework.stereotype.Service;

@Service
public class RuleCommonService {


    public void getRuleRotationHeaderWhereItem(RuleRotationHeaderItem item, RuleRotationHeader header) {
        header.setRuleName(item.getRuleName());
        header.setWarehouseId(item.getWarehouseId());
        header.setWarehouseName(item.getWarehouseName());
        header.setLotId(item.getLotId());
        header.setLotCode(item.getLotCode());
        header.setLotName(item.getLotName());
    }
}
