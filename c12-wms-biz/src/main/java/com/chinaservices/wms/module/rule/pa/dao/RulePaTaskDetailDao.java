package com.chinaservices.wms.module.rule.pa.dao;

import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.sqlid.rule.pa.RulePaTaskDetailSqlId;
import com.chinaservices.wms.module.rule.pa.domain.RulePaTaskDetailLocItem;
import com.chinaservices.wms.module.rule.pa.domain.RulePaTaskDetailPageCondition;
import com.chinaservices.wms.module.rule.pa.domain.RulePaTaskDetailQuery;
import com.chinaservices.wms.module.rule.pa.model.RulePaTaskDetail;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class RulePaTaskDetailDao extends ModuleBaseDaoSupport<RulePaTaskDetail,Long> {

    public PageResult<RulePaTaskDetailQuery> page(RulePaTaskDetailPageCondition rulePaTaskDetailPageCondition){
        return sqlExecutor.page(RulePaTaskDetailQuery.class, RulePaTaskDetailSqlId.RULE_PA_TASK_DETAIL_QUERY_PAGE_LIST,rulePaTaskDetailPageCondition);
    }

    public List<RulePaTaskDetailLocItem> getDetailByHeaderId(Long ruleId){
        return sqlExecutor.find(RulePaTaskDetailLocItem.class, RulePaTaskDetailSqlId.RULE_PA_TASK_DETAIL_BY_RULE_ID,"ruleId",ruleId);
    }

    /**
     * 批量查询规则明细
     */
    public List<RulePaTaskDetailLocItem> getDetailByHeaderIds(Long[] ruleIds){
        return sqlExecutor.find(RulePaTaskDetailLocItem.class, RulePaTaskDetailSqlId.RULE_PA_TASK_DETAIL_BY_RULE_IDS,"ruleIds",ruleIds);
    }
}
