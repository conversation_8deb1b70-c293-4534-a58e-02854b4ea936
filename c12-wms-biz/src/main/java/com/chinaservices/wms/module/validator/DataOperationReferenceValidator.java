package com.chinaservices.wms.module.validator;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.wms.common.enums.common.DataOperationValidatorEnum;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailDao;
import com.chinaservices.wms.module.asn.asn.model.AsnDetail;
import com.chinaservices.wms.module.basic.sku.dao.SkuDao;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.rule.picking.model.RulePickingStaff;
import com.chinaservices.wms.module.rule.picking.service.RulePickingStaffService;
import com.chinaservices.wms.module.so.pack.dao.PackHeaderDao;
import com.chinaservices.wms.module.so.pack.model.PackHeader;
import com.chinaservices.wms.module.so.so.dao.SoHeaderDao;
import com.chinaservices.wms.module.so.so.model.SoHeader;
import com.chinaservices.wms.module.so.wv.dao.WvHeaderDao;
import com.chinaservices.wms.module.so.wv.model.WvHeader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class DataOperationReferenceValidator {

    @Autowired
    private SkuDao skuDao;

    @Autowired
    private SoHeaderDao soHeaderDao;

    @Autowired
    private PackHeaderDao packHeaderDao;

    @Autowired
    private AsnDetailDao asnDetailDao;

    @Autowired
    private WvHeaderDao wvHeaderDao;

    @Autowired
    private RulePickingStaffService rulePickingStaffService;

    public void validateOwner(DataOperationValidationEvent event) {
        Map<Long, List<Sku>> skuMap = skuDao.find(new ConditionRule().andIn(Sku::getOwnerId, event.getIds()))
                .stream().collect(Collectors.groupingBy(Sku::getOwnerId));
        event.getIds().forEach(id -> {
            List<Sku> skuList = skuMap.getOrDefault(id, CollectionUtil.newArrayList());
            if (CollectionUtil.isNotEmpty(skuList)) {
                String skuNames = skuList.stream().map(Sku::getSkuName).collect(Collectors.joining(StrUtil.COMMA));
                event.addValidationResult(DataOperationValidationResult.invalid(DataOperationValidatorEnum.OwnerReferenceType.SKU.getName(), skuNames));
            }
        });
    }

    public void validateCustomer(DataOperationValidationEvent event) {
        Map<String, List<SoHeader>> soHeaderMap = soHeaderDao.find(new ConditionRule().andIn(SoHeader::getClientId, event.getIds()))
                .stream().collect(Collectors.groupingBy(SoHeader::getClientId));
        event.getIds().forEach(id -> {
            List<SoHeader> soHeaderList = soHeaderMap.getOrDefault(Convert.toStr(id), CollectionUtil.newArrayList());
            if (CollectionUtil.isNotEmpty(soHeaderList)) {
                String soNos = soHeaderList.stream().map(SoHeader::getSoNo).collect(Collectors.joining(StrUtil.COMMA));
                event.addValidationResult(DataOperationValidationResult.invalid(DataOperationValidatorEnum.CustomerReferenceType.SO.getName(), soNos));
            }
        });
    }

    public void validateCarrier(DataOperationValidationEvent event) {
        Map<Long, List<SoHeader>> soHeaderMap = soHeaderDao.find(new ConditionRule().andIn(SoHeader::getCarrierId, event.getIds()))
                .stream().collect(Collectors.groupingBy(SoHeader::getCarrierId));
        event.getIds().forEach(id -> {
            List<SoHeader> soHeaderList = soHeaderMap.getOrDefault(id, CollectionUtil.newArrayList());
            if (CollectionUtil.isNotEmpty(soHeaderList)) {
                String soNos = soHeaderList.stream().map(SoHeader::getSoNo).collect(Collectors.joining(StrUtil.COMMA));
                event.addValidationResult(DataOperationValidationResult.invalid(DataOperationValidatorEnum.CarrierReferenceType.SO.getName(), soNos));
            }
        });
        Map<Long, List<PackHeader>> packHeaderMap = packHeaderDao.find(new ConditionRule().andIn(PackHeader::getCarrierId, event.getIds()))
                .stream().collect(Collectors.groupingBy(PackHeader::getCarrierId));
        event.getIds().forEach(id -> {
            List<PackHeader> packHeaderList = packHeaderMap.getOrDefault(id, CollectionUtil.newArrayList());
            if (CollectionUtil.isNotEmpty(packHeaderList)) {
                String packNos = packHeaderList.stream().map(PackHeader::getPackNo).collect(Collectors.joining(StrUtil.COMMA));
                event.addValidationResult(DataOperationValidationResult.invalid(DataOperationValidatorEnum.CarrierReferenceType.PACK.getName(), packNos));
            }
        });
    }

    public void validatePackage(DataOperationValidationEvent event) {
        Map<Long, List<Sku>> skuMap = skuDao.find(new ConditionRule().andIn(Sku::getPackageId, event.getIds()))
                .stream().collect(Collectors.groupingBy(Sku::getPackageId));
        event.getIds().forEach(id -> {
            List<Sku> skuList = skuMap.getOrDefault(id, CollectionUtil.newArrayList());
            if (CollectionUtil.isNotEmpty(skuList)) {
                String skuNames = skuList.stream().map(Sku::getSkuName).collect(Collectors.joining(StrUtil.COMMA));
                event.addValidationResult(DataOperationValidationResult.invalid(DataOperationValidatorEnum.PackageReferenceType.SKU.getName(), skuNames));
            }
        });
    }

    public void validateSku(DataOperationValidationEvent event) {
        Map<Long, List<AsnDetail>> asnDetailMap = asnDetailDao.find(new ConditionRule().andIn(AsnDetail::getSkuId, event.getIds()))
                .stream().collect(Collectors.groupingBy(AsnDetail::getSkuId));
        event.getIds().forEach(id -> {
            List<AsnDetail> asnDetailList = asnDetailMap.getOrDefault(id, CollectionUtil.newArrayList());
            if (CollectionUtil.isNotEmpty(asnDetailList)) {
                String asnNos = asnDetailList.stream().map(AsnDetail::getAsnNo).collect(Collectors.joining(StrUtil.COMMA));
                event.addValidationResult(DataOperationValidationResult.invalid(DataOperationValidatorEnum.SkuReferenceType.ASN_DETAIL.getName(), asnNos));
            }
        });
    }

    public void validatePaTaskRule(DataOperationValidationEvent event) {
        Map<Long, List<Sku>> skuMap = skuDao.find(new ConditionRule().andIn(Sku::getPackageId, event.getIds()))
                .stream().collect(Collectors.groupingBy(Sku::getPaRuleId));
        event.getIds().forEach(id -> {
            List<Sku> skuList = skuMap.getOrDefault(id, CollectionUtil.newArrayList());
            if (CollectionUtil.isNotEmpty(skuList)) {
                String skuNames = skuList.stream().map(Sku::getSkuName).collect(Collectors.joining(StrUtil.COMMA));
                event.addValidationResult(DataOperationValidationResult.invalid(DataOperationValidatorEnum.PaTaskRuleReferenceType.SKU.getName(), skuNames));
            }
        });
    }

    public void validateAllocRule(DataOperationValidationEvent event) {
        Map<Long, List<Sku>> skuMap = skuDao.find(new ConditionRule().andIn(Sku::getPackageId, event.getIds()))
                .stream().collect(Collectors.groupingBy(Sku::getAllocRuleId));
        event.getIds().forEach(id -> {
            List<Sku> skuList = skuMap.getOrDefault(id, CollectionUtil.newArrayList());
            if (CollectionUtil.isNotEmpty(skuList)) {
                String skuNames = skuList.stream().map(Sku::getSkuName).collect(Collectors.joining(StrUtil.COMMA));
                event.addValidationResult(DataOperationValidationResult.invalid(DataOperationValidatorEnum.AllocRuleReferenceType.SKU.getName(), skuNames));
            }
        });
        List<RulePickingStaff> pickingStaffDetailList = rulePickingStaffService.getPickingStaffListByAllocRuleId(event.getIds());
        if (CollectionUtil.isNotEmpty(pickingStaffDetailList)){
            String ruleNames = pickingStaffDetailList.stream().map(RulePickingStaff::getRuleName).collect(Collectors.joining(StrUtil.COMMA));
            event.addValidationResult(DataOperationValidationResult.invalid(DataOperationValidatorEnum.AllocReferenceType.PICKING_STAFF.getName(), ruleNames));
        }
    }

    public void validateRotationRule(DataOperationValidationEvent event) {
        Map<Long, List<Sku>> skuMap = skuDao.find(new ConditionRule().andIn(Sku::getPackageId, event.getIds()))
                .stream().collect(Collectors.groupingBy(Sku::getRotationRuleId));
        event.getIds().forEach(id -> {
            List<Sku> skuList = skuMap.getOrDefault(id, CollectionUtil.newArrayList());
            if (CollectionUtil.isNotEmpty(skuList)) {
                String skuNames = skuList.stream().map(Sku::getSkuName).collect(Collectors.joining(StrUtil.COMMA));
                event.addValidationResult(DataOperationValidationResult.invalid(DataOperationValidatorEnum.RotationRuleReferenceType.SKU.getName(), skuNames));
            }
        });
    }

    public void validateWvRule(DataOperationValidationEvent event) {
        Map<Long, List<WvHeader>> wvHeaderMap = wvHeaderDao.find(new ConditionRule().andIn(AsnDetail::getSkuId, event.getIds()))
                .stream().collect(Collectors.groupingBy(WvHeader::getWvRuleId));
        event.getIds().forEach(id -> {
            List<WvHeader> wvHeaderList = wvHeaderMap.getOrDefault(id, CollectionUtil.newArrayList());
            if (CollectionUtil.isNotEmpty(wvHeaderList)) {
                String wvNos = wvHeaderList.stream().map(WvHeader::getWvNo).collect(Collectors.joining(StrUtil.COMMA));
                event.addValidationResult(DataOperationValidationResult.invalid(DataOperationValidatorEnum.WvRuleReferenceType.WV.getName(), wvNos));
            }
        });
    }
}