package com.chinaservices.wms.module.asn.pa.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.enums.asn.PaStatusEnum;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.asn.asn.service.AsnDetailSnService;
import com.chinaservices.wms.module.asn.pa.dao.CsPaTaskDetailDao;
import com.chinaservices.wms.module.asn.pa.domain.*;
import com.chinaservices.wms.module.asn.pa.model.CsPaTask;
import com.chinaservices.wms.module.asn.pa.model.CsPaTaskDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description: 上架任务 Service
 */
@Service
public class CsPaTaskDetailService extends ModuleBaseServiceSupport<CsPaTaskDetailDao, CsPaTaskDetail, Long> {

    @Autowired
    private CsPaTaskDetailDao csPaTaskDetailDao;
    @Autowired
    private AsnDetailSnService asnDetailSnService;

    public List<CsPaTaskDetail> findByReceiveNos(Set<String> receiveNoSet){
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(CsPaTaskDetail::getReceiveNo, receiveNoSet);
        return this.find(conditionRule);
    }
    public List<CsPaTaskDetail> findByPaNo(String paNo){
        PaTaskDetaiCondition paTaskDetaiCondition = new PaTaskDetaiCondition();
        paTaskDetaiCondition.setPaNo(paNo);
        return this.find(paTaskDetaiCondition);
    }

    public List<CsPaTaskDetail> findByPaNos(Set<String> paNoSet){
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(CsPaTaskDetail::getPaNo, paNoSet);
        return this.find(conditionRule);
    }

    public List<CsPaTaskDetail> buildPaTaskDetailConfirmItemList(List<PaTaskConfirmItem> paTaskConfirmItemList, Map<Long, CsPaTask> csPaTaskMap){
        List<CsPaTaskDetail> csPaTaskDetailList = new ArrayList<>();
        for (PaTaskConfirmItem paTaskConfirmItem : paTaskConfirmItemList) {
            paTaskConfirmItem.setStatus(PaStatusEnum.SHELVESED.getCode());
            CsPaTaskDetail csPaTaskDetail = new CsPaTaskDetail();
            CsPaTask csPaTask = csPaTaskMap.get(paTaskConfirmItem.getId());
            BeanUtil.copyProperties(csPaTask, csPaTaskDetail);
            csPaTaskDetail.setId(null);
            csPaTaskDetail.setCreator(null);
            csPaTaskDetail.setCreateTime(null);
            csPaTaskDetail.setModifier(null);
            csPaTaskDetail.setModifyTime(null);
            csPaTaskDetail.setQtyPaEa(paTaskConfirmItem.getQtyPaEa());
            csPaTaskDetail.setToLocId(paTaskConfirmItem.getToLocId());
            csPaTaskDetail.setToLocName(paTaskConfirmItem.getToLocName());
            csPaTaskDetail.setToPalletId(paTaskConfirmItem.getToPalletId());
            csPaTaskDetail.setToPalletNo(paTaskConfirmItem.getToPalletNo());
            csPaTaskDetail.setStatus(PaStatusEnum.SHELVESED.getCode());
            csPaTaskDetail.setPaId(csPaTask.getId());
            csPaTaskDetail.setPaNo(csPaTask.getPaNo());
            String maxPaDetailNoByPaId = this.getMaxPaDetailNoByPaId(csPaTaskDetail.getPaId());
            if (StrUtil.isBlank(maxPaDetailNoByPaId)){
                csPaTaskDetail.setPaDetailNo(csPaTaskDetail.getPaNo() + "-1");
            }else {
                String[] parts = maxPaDetailNoByPaId.split("-");
                int i = Integer.parseInt(parts[1]) + 1;
                csPaTaskDetail.setPaDetailNo(parts[0] + "-" + i);

            }
            csPaTaskDetail.setPaTime(DateUtil.date());
            preSave(csPaTaskDetail);

            csPaTaskDetailList.add(csPaTaskDetail);
        }
        return csPaTaskDetailList;
    }

    public String getMaxPaDetailNoByPaId(Long paId){
        PaTaskDetaiCondition paTaskDetaiCondition = new PaTaskDetaiCondition();
        paTaskDetaiCondition.setPaId(paId);
        List<CsPaTaskDetail> csPaTaskDetailList = this.find(paTaskDetaiCondition);
        CsPaTaskDetail csPaTaskDetail = csPaTaskDetailList.stream()
                .sorted(Comparator.comparing(CsPaTaskDetail::getPaDetailNo).reversed())
                .findFirst().orElse(null);
        if (ObjectUtil.isEmpty(csPaTaskDetail)){
            return null;
        }
        return csPaTaskDetail.getPaDetailNo();
    }


    public PageResult<PaTaskDetailQuery> paTaskDetailPage(PaTaskDetailPageCondition condition){
        PageResult<PaTaskDetailQuery> page = csPaTaskDetailDao.page(condition);
        if (CollUtil.isNotEmpty(page.getRows())){
            Set<Long> set = page.getRows().stream().map(PaTaskDetailQuery::getReceiveNo).collect(Collectors.toSet());
            List<AsnDetailSn> snList = asnDetailSnService.find(new ConditionRule().andIn(AsnDetailSn::getReceiveCode, set));
            if (CollUtil.isEmpty(snList)){
                return page;
            }else{
                Map<String, List<AsnDetailSn>> listMap = snList.stream().collect(Collectors.groupingBy(AsnDetailSn::getReceiveCode));
                page.getRows().forEach(item -> {
                    List<AsnDetailSn> asnDetailSns = listMap.get(item.getReceiveNo());
                    if (CollUtil.isNotEmpty(asnDetailSns)){
                        item.setIsExistenceSn(Boolean.TRUE);
                    }
                });
            }
        }
        return page;
    }

}
