package com.chinaservices.wms.module.so.check.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

@Data
@Entity
@Table(name = "cs_so_recheck_header")
public class SoRecheckHeader extends ModuleBaseModel
{

    /**
     * 复核单号
     */
    private String recheckNo;

    /**
     * 拣货单号
     */
    private String pickingNo;

    /**
     * 出库单号
     */
    private String soNo;

    /**
     * 复核状态
     */
    private String checkStatus;

    /**
     * 复核完成时间
     */
    private Date checkDoneTime;

    /**
   	 * 仓库id
   	 */
   	private Long warehouseId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 打包任务号
     */
    private String packNo;

    /**
     * 是否生成打包任务
     */
    private String isGeneratePackTask;

}