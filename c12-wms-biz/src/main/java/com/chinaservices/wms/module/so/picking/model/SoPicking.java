package com.chinaservices.wms.module.so.picking.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;


/**
 * 拣货表
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Entity
@Table(name = "cs_so_picking")
@Data
public class SoPicking extends ModuleBaseModel {

	/**
	* 拣货单号
	*/
	private String pickingNo;

	/**
	* 状态
	*/
	private String status;

	/**
	* 库存周转规则主键
	*/
	private Long rotationRuleId;

	/**
	* 库存周转规则名称
	*/
	private String rotationRuleName;

	/**
	* 分配规则主键
	*/
	private Long allocRuleId;

	/**
	* 分配规则名称
	*/
	private String allocRuleName;

	/**
	 * 仓库id
	 */
	private Long warehouseId;

	/**
	* 出库单Id
	*/
	private Long soId;

	/**
	* 出库单号
	*/
	private String soNo;

	/**
	* 波次单Id
	*/
	private Long wvId;

	/**
	* 波次单号
	*/
	private String wvNo;

	/**
	* 备注
	*/
	private String remark;

	/**
	 * 加工单单号
	 */
	private String processOrderNo;

	/**
	 * 加工单类型：1-加工单；2-拆分单
	 */
	private String processOrderType;
}
