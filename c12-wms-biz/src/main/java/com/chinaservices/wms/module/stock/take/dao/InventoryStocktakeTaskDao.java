package com.chinaservices.wms.module.stock.take.dao;


import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.sqlid.stock.take.InventoryStocktakeSqlId;
import com.chinaservices.wms.module.performance.domain.PerformanceAnalysisPageCondition;
import com.chinaservices.wms.module.performance.domain.StocktakeTaskPerformanceAnalysisQuery;
import com.chinaservices.wms.module.stock.take.domain.StocktakeTaskPageCondition;
import com.chinaservices.wms.module.stock.take.domain.StocktakeTaskPdaPageCondition;
import com.chinaservices.wms.module.stock.take.domain.StocktakeTaskPdaQuery;
import com.chinaservices.wms.module.stock.take.domain.StocktakeTaskQuery;
import com.chinaservices.wms.module.stock.take.model.InventoryStocktakeTask;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName InventoryStocktakeTaskDao
 * <AUTHOR>
 * @Date 2025/1/11 15:51
 * @Description
 * @Version 1.0
 */
@Repository
public class InventoryStocktakeTaskDao extends ModuleBaseDaoSupport<InventoryStocktakeTask,Long> {

    /**
     *@Description 盘点任务分页查询
     *@Param condition
     *@Return * {@link PageResult< StocktakeTaskQuery> }
     *@Date 2025/1/13 19:05
     *<AUTHOR>
     **/
    public PageResult<StocktakeTaskQuery> page(StocktakeTaskPageCondition condition) {
        return sqlExecutor.page(StocktakeTaskQuery.class, InventoryStocktakeSqlId.INVENTORY_STOCKTAKE_TASK_PAGE_LIST,condition);
    }

    /**
     *@Description 根据盘点单号查询最大值
     *@Param stocktakeNo
     *@Return * {@link Long }
     *@Date 2025/1/21 17:40
     *<AUTHOR>
     **/
    public Long getMaxTaskNo(String stocktakeNo) {
        return sqlExecutor.findLong(InventoryStocktakeSqlId.INVENTORY_STOCKTAKE_TASK_GET_MAX_TASK_NO,"stocktakeNo",stocktakeNo);
    }

    /**
     *@Description 根据条件查询
     *@Param condition
     *@Return * {@link List< InventoryStocktakeTask> }
     *@Date 2025/3/12 9:11
     *<AUTHOR>
     **/
    public List<InventoryStocktakeTask> findListByCondition(StocktakeTaskPageCondition condition) {
        return sqlExecutor.find(InventoryStocktakeTask.class, InventoryStocktakeSqlId.INVENTORY_STOCKTAKE_TASK_PAGE_LIST,condition);
    }

    /**
     *@Description 盘点作业分析分页查询
     *@Param condition
     *@Return * {@link PageResult< StocktakeTaskPerformanceAnalysisQuery> }
     *@Date 2025/4/17 11:28
     *<AUTHOR>
     **/
    public PageResult<StocktakeTaskPerformanceAnalysisQuery> performanceAnalysisPage(PerformanceAnalysisPageCondition condition) {
        return sqlExecutor.page(StocktakeTaskPerformanceAnalysisQuery.class, InventoryStocktakeSqlId.INVENTORY_STOCKTAKE_TASK_PERFORMANCE_ANALYSIS_PAGE_LIST,condition);
    }

    /**
     *@Description 盘点任务pda分页查询
     *@Param condition
     *@Return * {@link PageResult< StocktakeTaskPdaQuery> }
     *@Date 2025/6/5 16:24
     *<AUTHOR>
     **/
    public PageResult<StocktakeTaskPdaQuery> pdaPage(StocktakeTaskPdaPageCondition condition) {
        return sqlExecutor.page(StocktakeTaskPdaQuery.class, InventoryStocktakeSqlId.INVENTORY_STOCKTAKE_TASK_PDA_PAGE_LIST,condition);
    }
}
