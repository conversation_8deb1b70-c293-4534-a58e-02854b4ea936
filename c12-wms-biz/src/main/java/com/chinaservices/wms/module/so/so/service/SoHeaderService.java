package com.chinaservices.wms.module.so.so.service;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import com.chinaservices.auth.module.administrativeregion.domain.AdministrativeRegionCondition;
import com.chinaservices.auth.module.administrativeregion.domain.AdministrativeRegionQuery;
import com.chinaservices.auth.module.administrativeregion.feign.RemoteAdministrativeRegionService;
import com.chinaservices.auth.module.dict.domain.DictDataCondition;
import com.chinaservices.auth.module.dict.domain.DictDataQuery;
import com.chinaservices.auth.module.dict.domain.SelectItem;
import com.chinaservices.auth.module.dict.feign.RemoteDictDataService;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.edi.module.asn.domain.StatusUpdateCondition;
import com.chinaservices.edi.module.asn.domain.enums.OperationTypeEnum;
import com.chinaservices.edi.module.so.feign.RemoteSoStatusPushToCrzFegin;
import com.chinaservices.auth.module.dict.domain.SelectItem;
import com.chinaservices.auth.module.dict.feign.RemoteDictDataService;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ErrorResponseData;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.*;
import com.chinaservices.wms.common.constants.DictDataTypeConstants;
import com.chinaservices.wms.common.domain.LotAttribute;
import com.chinaservices.wms.common.domain.LotDetailItem;
import com.chinaservices.wms.common.enums.DocumentTypeEnum;
import com.chinaservices.wms.common.enums.asn.PaStatusEnum;
import com.chinaservices.wms.common.enums.common.AuditStatusEnum;
import com.chinaservices.wms.common.enums.order.OrderStatusEnum;
import com.chinaservices.wms.common.enums.so.*;
import com.chinaservices.wms.common.exception.OrderExceptionEnum;
import com.chinaservices.wms.common.exception.AsnExceptionEnum;
import com.chinaservices.wms.common.exception.SoExcepitonEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.wms.common.exception.SoPaTaskExceptionEnum;
import com.chinaservices.wms.common.util.LotUtil;
import com.chinaservices.wms.module.archive.archives.service.LogisticsFileAutoGenerateService;
import com.chinaservices.wms.module.asn.asn.dao.AsnHeaderDao;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.pa.dao.CsPaTaskAssociationDao;
import com.chinaservices.wms.module.asn.pa.dao.CsPaTaskDetailDao;
import com.chinaservices.wms.module.asn.pa.domain.CsPaTaskAssociationQuery;
import com.chinaservices.wms.module.asn.pa.model.CsPaTaskAssociation;
import com.chinaservices.wms.module.asn.pa.model.CsPaTaskDetail;
import com.chinaservices.wms.module.attachment.domain.CsAttachmentCondition;
import com.chinaservices.wms.module.attachment.domain.CsAttachmentQuery;
import com.chinaservices.wms.module.basic.cspackage.dao.PackageDao;
import com.chinaservices.wms.module.basic.cspackage.dao.PackageUnitDao;
import com.chinaservices.wms.module.basic.cspackage.model.Package;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.customer.dao.CustomerDao;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.basic.customer.dao.CustomerDao;
import com.chinaservices.wms.module.basic.customer.model.Customer;
import com.chinaservices.wms.module.basic.customer.service.CustomerService;
import com.chinaservices.wms.module.basic.lot.dao.WarehouseLotDetailDao;
import com.chinaservices.wms.module.basic.owner.dao.OwnerDao;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.basic.sku.dao.SkuDao;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.supplier.dao.SupplierDao;
import com.chinaservices.wms.module.so.pack.service.PackHeaderService;
import com.chinaservices.wms.module.folder.logistics.domain.LogisticsFolderDetailLinkCondition;
import com.chinaservices.wms.module.print.domain.SoPrintHeader;
import com.chinaservices.wms.module.so.pack.domain.PackBase;
import com.chinaservices.wms.module.order.model.Order;
import com.chinaservices.wms.module.order.model.OrderRelation;
import com.chinaservices.wms.module.order.model.OrderSku;
import com.chinaservices.wms.module.order.service.OrderRelationService;
import com.chinaservices.wms.module.order.service.OrderService;
import com.chinaservices.wms.module.order.service.OrderSkuService;
import com.chinaservices.wms.module.print.domain.SoPrintHeader;
import com.chinaservices.wms.module.so.pack.domain.PackBase;
import com.chinaservices.wms.module.so.pack.service.PackHeaderService;
import com.chinaservices.wms.module.so.picking.domain.SoPickingUpdateStatusItem;
import com.chinaservices.wms.module.so.picking.lock.SoPickingLock;
import com.chinaservices.wms.module.so.picking.model.SoPicking;
import com.chinaservices.wms.module.so.picking.service.SoPickingService;
import com.chinaservices.wms.module.so.so.dao.SoDetailDao;
import com.chinaservices.wms.module.so.so.dao.SoHeaderDao;
import com.chinaservices.wms.module.so.so.domain.*;
import com.chinaservices.wms.module.so.so.model.SoDetail;
import com.chinaservices.wms.module.so.so.model.SoHeader;
import com.chinaservices.wms.module.so.wv.dao.WvHeaderDao;
import com.chinaservices.wms.module.so.wv.domain.WvSkuQuery;
import com.chinaservices.wms.module.so.wv.model.WvDetail;
import com.chinaservices.wms.module.so.wv.service.WvDetailService;
import com.chinaservices.wms.module.warehouse.warehouse.dao.WarehouseDao;
import com.chinaservices.wms.module.warehouse.warehouse.domain.WarehouseControlParamItem;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseControlParamService;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.hibernate.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.regex.Pattern;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR> Vickyy
 * @Description: 发货管理Service
 */
@Service
@Slf4j
public class SoHeaderService extends ModuleBaseServiceSupport<SoHeaderDao, SoHeader, Long> {
    /**
     * 更新出库单状态
     *
     * @param ids
     * @param userId
     */
    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private SoDetailDao soDetailDao;
    @Autowired
    @Lazy
    private SoDetailService soDetailService;
    @Autowired
    private SoHeaderDao soHeaderDao;
    @Autowired
    private AsnHeaderDao asnHeaderDao;
     @Autowired
     private NumberGenerator numberGenerator;
     @Autowired
     private RemoteAdministrativeRegionService remoteAdministrativeRegionService;
     @Autowired
     private WarehouseControlParamService warehouseControlParamService;
     @Autowired
     private WvHeaderDao wvHeaderDao;
     @Autowired
     private SupplierDao supplierDao;
     @Autowired
     private SoPickingLock soPickingLock;
     @Autowired
     private SoPickingService soPickingService;
     @Autowired
     private PackHeaderService packHeaderService;

     @Autowired
     private LotUtil lotUtil;
     @Autowired
     private WarehouseLotDetailDao warehouseLotDetailDao;
    @Autowired
    private TaskExecutor taskExecutor;
     @Autowired
     private OrderRelationService orderRelationService;

     @Autowired
     private OrderSkuService orderSkuService;

     @Autowired
     private OrderService orderService;

     @Autowired
     private PackageUnitService packageUnitService;

    /**
     * 时间
     */
    private final static String TIME_STAMP = "23:59:59";
    @Autowired
    private WvDetailService wvDetailService;
    @Autowired
    private OwnerService ownerService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private SkuDao skuDao;
    @Autowired
    private PackageDao packageDao;
    @Autowired
    private PackageUnitDao packageUnitDao;
    @Autowired
    private RemoteSoStatusPushToCrzFegin remoteSoStatusPushToCrzFegin;
    @Autowired
    private WarehouseDao warehouseDao;
    @Autowired
    private OwnerDao ownerDao;
    @Autowired
    private RemoteDictDataService remoteDictDataService;
    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private LogisticsFileAutoGenerateService logisticsFileAutoGenerateService;
    @Autowired
    private CsPaTaskDetailDao csPaTaskDao;
    @Autowired
    private SoCreatePaTaskService  soCreatePaTaskService;
    @Autowired
    private CsPaTaskAssociationDao csPaTaskAssociationDao;

    /**
     * 根据条件获取查询分页列表
     *
     * @param soHeaderPageQuery
     * @return PageResult<warehouseQuery>
     */
    public PageResult<SoHeaderQuery> page(SoHeardPageQuery soHeaderPageQuery) {
        if (EmptyUtil.isNotEmpty(soHeaderPageQuery.getCreateStartTime())) {
            soHeaderPageQuery.setCreateStartTime(soHeaderPageQuery.getCreateStartTime());
        }
        PageResult<SoHeaderQuery> page = dao.page(soHeaderPageQuery);
        List<SoHeaderQuery> rows = page.getRows();
        if (CollUtil.isEmpty(rows)){
            return page;
        }
        //数据准备前置
        Set<String> ownerIds = rows.stream().map(SoHeaderQuery::getOwnerId).collect(Collectors.toSet());
        Set<Long> warehouseIds = rows.stream().map(SoHeaderQuery::getWarehouseId).collect(Collectors.toSet());
        Set<String> clientIds = rows.stream().map(SoHeaderQuery::getClientId).collect(Collectors.toSet());
        //准备接收分数组局的初始化MAP
        Map<Long,String> ownerMap = new HashMap<>();
        Map<Long,String> warehouseMap = new HashMap<>();
        Map<Long,String> clientMap = new HashMap<>();
        //分别查出三个集合的数据 并对数据进行数据分MAP,id=key,name=value
        List<Owner> ownerList = ownerService.find(new ConditionRule().andIn(Owner::getId, ownerIds));
        if (CollUtil.isNotEmpty(ownerList)){
            ownerMap = ownerList.stream().collect(Collectors.toMap(Owner::getId, Owner::getOwnerName, (o1, o2) -> o1));
        }
        List<Warehouse> warehouseList = warehouseService.find(new ConditionRule().andIn(Warehouse::getId, warehouseIds));
        if (CollUtil.isNotEmpty(warehouseList)){
            warehouseMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, Warehouse::getWarehouseName, (o1, o2) -> o1));
        }
        List<Customer> customers = customerService.find(new ConditionRule().andIn(Customer::getId, clientIds));
        if (CollUtil.isNotEmpty(customers)){
            clientMap = customers.stream().collect(Collectors.toMap(Customer::getId, Customer::getCustomerName, (o1, o2) -> o1));
        }
        //对rows集合中的数据进行拣货信息判断，先取出所有需要验证拣货信息的so_no
        Map<String, List<SoPicking>> soPickingGroupMap = new HashMap<>();
        Set<String> soNoSet = rows.stream().map(SoHeaderQuery::getSoNo).collect(Collectors.toSet());
        List<SoPicking> soPickingList = soPickingService.find(new ConditionRule().andIn(SoPicking::getSoNo, soNoSet)
                .andIn(SoPicking::getStatus,SoPickingStatusEnum.NOT_GENERATE.getCode(), SoPickingStatusEnum.UNPICKED.getCode()));
        if (CollUtil.isNotEmpty(soPickingList)){
            soPickingGroupMap = soPickingList.stream().collect(Collectors.groupingBy(SoPicking::getSoNo));
        }

        List<WvDetail> wvDetailList = wvDetailService.find(new ConditionRule().andIn(WvDetail::getSoNo, soNoSet)
                .andEqual(WvDetail::getStatus, "0"));
        Map<String, Long> soDetailCountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(wvDetailList)){
            Map<String, List<WvDetail>> map = wvDetailList.stream().collect(Collectors.groupingBy(WvDetail::getSoNo));
            map.keySet().forEach(key -> {
                long count = map.get(key).stream().map(WvDetail::getSoDetailId).distinct().count();
                soDetailCountMap.put(key, count);
            });
        }
        //对rows 进行数据封装
        for (SoHeaderQuery headerQuery : rows) {
            headerQuery.setOwnerName(ownerMap.get(Convert.toLong(headerQuery.getOwnerId())));
            headerQuery.setWarehouseName(warehouseMap.get(headerQuery.getWarehouseId()));
            headerQuery.setClientName(clientMap.get(Convert.toLong(headerQuery.getClientId())));
            List<SoPicking> list = soPickingGroupMap.get(headerQuery.getSoNo());
            if (CollUtil.isNotEmpty(list)){
                headerQuery.setPickStatus("1");
            }else{
                headerQuery.setPickStatus("0");
            }
            Long count = soDetailCountMap.get(headerQuery.getSoNo());
            headerQuery.setSoDetailCount(ObjUtil.isNull(count) ? 0L : count);
        }

        List<WvSkuQuery> soDetailList = soDetailDao.findWvSkuQueryBySoNos(soNoSet);
        if (StrUtil.isEmpty(soHeaderPageQuery.getIsCreatePaTask()) || YesNoEnum.YES.getCode().equals(soHeaderPageQuery.getIsCreatePaTask())){
            List<CsPaTaskAssociationQuery> associationList = csPaTaskAssociationDao.findBySoNoSet(soNoSet);
            if (CollUtil.isNotEmpty(associationList)){
                Map<String, List<CsPaTaskAssociationQuery>> taskAssMap = associationList.stream().collect(Collectors.groupingBy(CsPaTaskAssociationQuery::getSoDetailNo));
                soDetailList.forEach(item -> {
                    List<CsPaTaskAssociationQuery> taskAssociationList = taskAssMap.get(item.getSoDetailNo());
                    if (CollUtil.isNotEmpty(taskAssociationList)){
                        item.setPaTaskNoList(taskAssociationList.stream().map(CsPaTaskAssociationQuery::getPaNo).distinct().toList());
                        boolean allMatch = taskAssociationList.stream().allMatch(t -> PaStatusEnum.SHELVESED.getCode().equals(t.getStatus()));
                        if (allMatch){
                            item.setPaStatus(PaStatusEnum.SHELVESED.getCode());
                        }else {
                            item.setPaStatus(PaStatusEnum.SHELVESING.getCode());
                        }
                    }
                });
            }
        }
        Map<String, List<WvSkuQuery>> soNoGroupMap = soDetailList.stream().collect(Collectors.groupingBy(WvSkuQuery::getSoNo));
        rows.forEach(e -> {
            List<WvSkuQuery> soDetails = soNoGroupMap.get(e.getSoNo());
            soDetails = CollUtil.isEmpty(soDetails) ? Lists.newArrayList() : soDetails;
            e.setSoDetail(soDetails);
            String pickStatus = e.getPickStatus();
            int size = soDetails.size();
            // 订单未取消才可以进入以下操作
            if (SoOrderStatusEnum.CREATE.getCode().equals(e.getStatus())) {
                // 2. 编辑按钮/审核：未审核
                if (OutboundAuditStatusEnum.AUDIT_UN.getCode().equals(e.getAuditStatus())) {
                    // 1.取消订单：未审核的出库单才可以取消
                    e.setIsCancel(Boolean.TRUE);
                    e.setIsEdit(Boolean.TRUE);
                    e.setIsAudit(Boolean.TRUE);
                    return;
                }

                // 分配逻辑
                switch (Objects.requireNonNull(SoAllocStatusEnum.fromCode(e.getAllocStatus()))) {
                    case UN_ALLOC: // 未分配
                        if (size > e.getSoDetailCount()){
                            e.setIsCancelAudit(Boolean.TRUE);// 3. 取消审核
                            e.setIsAllocated(Boolean.TRUE);// 可分配
                        }
                        // 拣货状态存在 为未生成或未拣货时，可以分配
                        if (YesNo.YES.equals(pickStatus)) {
                            e.setIsAllocated(Boolean.TRUE);
                        }
                        break;

                    case PARTIAL: //
                        // 部分分配
                        e.setIsViewDetail(Boolean.TRUE); // 可查看分配明细
                        e.setIsComplete(Boolean.TRUE); // 订单可完结
                        if (size > e.getSoDetailCount()){
                            e.setIsAllocated(Boolean.TRUE);// 可分配
                        }
                        // 拣货状态存在 为未生成或未拣货时，可以分配
                        if (YesNo.YES.equals(pickStatus)) {
                            e.setIsAllocated(Boolean.TRUE);
                        }
                        break;

                    case FULL: // 已分配
                        // 拣货状态存在 为未生成或未拣货时，可以分配
                        if (YesNo.YES.equals(pickStatus)) {
                            e.setIsAllocated(Boolean.TRUE);
                        }
                        e.setIsViewDetail(Boolean.TRUE); // 查看分配明细
                        e.setIsComplete(Boolean.TRUE);  // 已分配后订单可完结
                        break;
                    default:
                        // 其他状态无需操作
                        break;
                }
            }else {
                if (e.getAllocStatus() == null){
                    return;
                }
                // 分配逻辑
                switch (Objects.requireNonNull(SoAllocStatusEnum.fromCode(e.getAllocStatus()))) {
                    case PARTIAL: // 部分分配
                        e.setIsViewDetail(Boolean.TRUE); // 可查看分配明细
                        break;
                    case FULL: // 已分配
                        e.setIsViewDetail(Boolean.TRUE); // 查看分配明细
                        break;
                    default:
                        // 其他状态无需操作
                        break;
                }
            }
        });
        return page;
    }

    /**
     * 新增或更新商品信息
     *
     * @param soHeaderItem
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(SoHeaderItem soHeaderItem) {
        if (StrUtil.isNotBlank(soHeaderItem.getLogisticNo())) {
            if (Validator.hasChinese(soHeaderItem.getLogisticNo())){
                throw new ServiceException(SoExcepitonEnum.LOGISTIC_NO_GENERAL_WITH_CHINESE.getMsg());
            }
            if (OrderSourceTypeEnum.WMSPUSH.getCode().equals(soHeaderItem.getOrderSource())){
                //验证货主,仓库,商品,包装,包装单位是否存在本系统中
                if (ObjUtil.isNull(soHeaderItem.getOwnerId())){
                    throw new ServiceException(SoExcepitonEnum.SO_OWNER_INFO_INCORRECT.getMsg());
                }
                Owner owner = ownerService.findFirst(new ConditionRule().andEqual(Owner::getUpstreamOwnerCode,soHeaderItem.getUpstreamOwnerCode()));
                if (ObjUtil.isNull(owner)){
                    throw new ServiceException(SoExcepitonEnum.SO_OWNER_INFO_INCORRECT.getMsg());
                }
                soHeaderItem.setOwnerId(owner.getId());

                if (ObjUtil.isNull(soHeaderItem.getWarehouseId())){
                    throw new ServiceException(SoExcepitonEnum.SO_WAREHOUSE_INFO_INCORRECT.getMsg());
                }
                Warehouse warehouse = warehouseService.findFirst(new ConditionRule().andEqual(Warehouse::getUpWarehouseCode,soHeaderItem.getUpWarehouseCode()));
                if (ObjUtil.isNull(warehouse)){
                    throw new ServiceException(SoExcepitonEnum.SO_WAREHOUSE_INFO_INCORRECT.getMsg());
                }
                soHeaderItem.setWarehouseId(warehouse.getId());

                if (CollUtil.isEmpty(soHeaderItem.getSoDetailItems())){
                    throw new ServiceException(SoExcepitonEnum.SO_SKU_INFO_INCORRECT.getMsg());
                }
                for (SoDetailItem soDetailItem : soHeaderItem.getSoDetailItems()) {
                    if (ObjUtil.isNull(soDetailItem.getSkuId())){
                        throw new ServiceException(SoExcepitonEnum.SO_SKU_INFO_INCORRECT.getMsg());
                    }
                    Sku sku = skuDao.findFirst(new ConditionRule().andEqual(Sku::getUpSkuCode,soDetailItem.getSkuCode()));
                    if (ObjUtil.isNull(sku)){
                        throw new ServiceException(SoExcepitonEnum.SO_SKU_INFO_INCORRECT.getMsg());
                    }
                    soDetailItem.setSkuCode(sku.getSkuCode());
                    soDetailItem.setSkuId(sku.getId());
                    soDetailItem.setSkuName(sku.getSkuName());

                    //包装,包装单位
                    if (ObjUtil.isNull(soDetailItem.getPackageId())){
                        throw new ServiceException(SoExcepitonEnum.SO_PACKIGE_INFO_INCORRECT.getMsg());
                    }
                    Package aPackage = packageDao.findFirst(new ConditionRule().andEqual(Package::getUpstreamPackageCode,soDetailItem.getUpstreamPackageCode()));
                    if (ObjUtil.isNull(aPackage)){
                        throw new ServiceException(SoExcepitonEnum.SO_PACKIGE_INFO_INCORRECT.getMsg());
                    }
                    soDetailItem.setPackageId(aPackage.getId());
                    soDetailItem.setPackageName(aPackage.getName());

                    if (ObjUtil.isNull(soDetailItem.getPackageId())){
                        throw new ServiceException(SoExcepitonEnum.SO_PACKIGE_INFO_INCORRECT.getMsg());
                    }
                    PackageUnit unit = packageUnitDao.findFirst(new ConditionRule().andEqual(PackageUnit::getPackageId,soDetailItem.getPackageId())
                            .andEqual(PackageUnit::getCode,soDetailItem.getPackageUnitCode()));
                    if (ObjUtil.isNull(unit)){
                        throw new ServiceException(SoExcepitonEnum.SO_PACKIGE_INFO_INCORRECT.getMsg());
                    }
                    soDetailItem.setPackageUnitId(unit.getId());
                    soDetailItem.setPackageUnitName(unit.getName());
                }
            }
            //验证是否有相同的上游id 存在相同的上游id 则执行更新操作
            SoHeader soHeader = dao.findFirst(new ConditionRule().andEqual(SoHeader::getLogisticNo, soHeaderItem.getLogisticNo()));
            if (ObjUtil.isNotNull(soHeader)){
                soHeaderItem.setId(soHeader.getId());
            }

            if (soHeaderItem.getSoType().equals("AO") && soHeaderItem.getOrderSource().equals(OrderSourceTypeEnum.WMSPUSH.getCode())){
                soHeaderItem.setSoType(SoTypeEnum.TO.getCode());
            }
        }
        if (EmptyUtil.isEmpty(soHeaderItem.getId())) {
            String outboundNo = numberGenerator.nextValue(IdRuleConstant.SO_NO);
            soHeaderItem.setSoNo(outboundNo);
            soHeaderItem.setAuditStatus(OutboundAuditStatusEnum.AUDIT_UN.getCode());
            soHeaderItem.setStatus(SoOrderStatusEnum.CREATE.getCode());
            soHeaderItem.setAllocStatus(null);
            if (StrUtil.isEmpty(soHeaderItem.getOrderSource())){
                soHeaderItem.setOrderSource(OrderSourceTypeEnum.MANUALLYCREATE.getCode());
            }
        }else {
            SoHeader byId = dao.findById(soHeaderItem.getId());
            soHeaderItem.setSoNo(byId.getSoNo());
            soHeaderItem.setAuditStatus(byId.getAuditStatus());
            soHeaderItem.setStatus(byId.getStatus());
            soHeaderItem.setAllocStatus(byId.getAllocStatus());
            soHeaderItem.setOrderSource(byId.getOrderSource());
            if(YesNoEnum.NO.getCode().equals(soHeaderItem.getEditRelation())){
                soHeaderItem.setRelationOrder(byId.getRelationOrder());
                soHeaderItem.setSaleOrderNo(byId.getSaleOrderNo());
            }
        }
        mergeAsnDetail(soHeaderItem);
        List<SoDetailItem> soDetailItems = soHeaderItem.getSoDetailItems();
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn("soNo",soHeaderItem.getSoNo());
        soDetailDao.delete(conditionRule);
        if(YesNoEnum.YES.getCode().equals(soHeaderItem.getRelationOrder())){
            List<Order> orderList = orderService.findByOrderNoList(Arrays.asList(soHeaderItem.getSaleOrderNo()));
            if(CollectionUtil.isNotEmpty(orderList)){
                if(AuditStatusEnum.UNREVIEWED.getCode().equals(orderList.get(0).getAuditStatus())){
                    throw new ServiceException(OrderExceptionEnum.ORDER_RELATION_AUDIT_STATUS_ERROR);
                }
            }
            // 根据入库单号查询关联信息
            List<OrderRelation> orderRelationList = orderRelationService.findByBusinessNo(Arrays.asList(soHeaderItem.getSoNo()));
            if(CollectionUtil.isNotEmpty(orderRelationList)){
                //修改剩余量
                orderSkuService.batchUpdateRemainingAmount(Boolean.TRUE,orderRelationList);
                //根据入库单号删除关联信息
                orderRelationService.delByBusinessNo(Arrays.asList(soHeaderItem.getSoNo()));
            }

            //重新保存关联信息
            if(CollectionUtil.isNotEmpty(soDetailItems)){
                List<OrderRelation> insertList = new ArrayList<>();
                soDetailItems.forEach(x ->{
                    OrderRelation relation = new OrderRelation();
                    relation.setBusinessNo(soHeaderItem.getSoNo());
                    relation.setOrderSkuId(x.getOrderSkuId());
                    relation.setSkuId(x.getSkuId());
                    relation.setPackageId(x.getPackageId());
                    relation.setPackageUnitId(x.getPackageUnitId());
                    relation.setCurrentAmount(x.getQtySo());
                    relation.setOrderNo(soHeaderItem.getSaleOrderNo());
                    orderRelationService.preSave(relation);
                    insertList.add(relation);
                });
                if(CollectionUtil.isNotEmpty(insertList)){
                    orderRelationService.batchInsert(insertList);
                    //修改剩余量
                    orderSkuService.batchUpdateRemainingAmount(Boolean.FALSE,insertList);
                }
                orderService.updateOrderStatus(soHeaderItem.getSaleOrderNo(), OrderStatusEnum.PROCESSING.getCode());
            }
        }
        if (CollUtil.isNotEmpty(soDetailItems)) {
            for (SoDetailItem soDetailItem : soDetailItems) {
                soDetailItem.setId(null);
                soDetailItem.setSoNo(soHeaderItem.getSoNo());
                String soDetailNo = numberGenerator.nextValue(IdRuleConstant.SO_DETAIL_NO);
                soDetailItem.setSoDetailNo(soDetailNo);
                SoDetail soDetail = new SoDetail();
                BeanUtil.copyProperties(soDetailItem,soDetail);
                soDetail.setQtyAllocationEa(BigDecimal.ZERO);
                soDetail.setQtyPickedEa(BigDecimal.ZERO);
                soDetail.setQtyShippedEa(BigDecimal.ZERO);
                soDetailDao.saveOrUpdate(soDetail);
            }
        }
        boolean b = dao.saveOrUpdate(soHeaderItem);

        // 出库档案
        taskExecutor.execute(() -> {
            //⼊库单集合
            ArrayList<String> objects = new ArrayList<>();
            objects.add(soHeaderItem.getSoNo());
            List<LogisticsFolderDetailLinkCondition> linkConditions = new ArrayList<>();
            LogisticsFolderDetailLinkCondition logisticsFolderDetailLinkCondition = new LogisticsFolderDetailLinkCondition();
            logisticsFolderDetailLinkCondition.setDocumentNos(objects);
            logisticsFolderDetailLinkCondition.setUpDocumentNo(null);
            linkConditions.add(logisticsFolderDetailLinkCondition);

            logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                    DocumentTypeEnum.SO_ORDER, linkConditions, SessionContext.getSessionUserInfo().getCompanyId(),
                    SessionContext.getSessionUserInfo().getTenancy(), null);

            List<LogisticsFolderDetailLinkCondition> asnCondition  = new ArrayList<>();
            asnCondition.add(new LogisticsFolderDetailLinkCondition().setUpDocumentNo(soHeaderItem.getSaleOrderNo()).setDocumentNos(List.of(soHeaderItem.getSoNo())));
            logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                    DocumentTypeEnum.DN, asnCondition, SessionContext.getSessionUserInfo().getCompanyId(),
                    SessionContext.getSessionUserInfo().getTenancy(), null);
        });
        return b;
    }


    /**
     * 合并出库单
     * @param soHeaderItem
     */
    public void mergeAsnDetail(SoHeaderItem soHeaderItem){
        List<SoDetailItem> soDetailItems = soHeaderItem.getSoDetailItems();
        Map<SoDetailAttribute,List<SoDetailItem>> map = new HashMap<>();
        soDetailItems.forEach(e->{
            SoDetailAttribute soDetailAttribute = new SoDetailAttribute();
            BeanUtil.copyProperties(e,soDetailAttribute);
            List<SoDetailItem> orDefault = map.getOrDefault(soDetailAttribute, new ArrayList<>());
            orDefault.add(e);
            map.put(soDetailAttribute,orDefault);
        });
        List<SoDetailItem> list = new ArrayList<>();
        for (SoDetailAttribute soDetailAttribute : map.keySet()) {
            List<SoDetailItem> soDetailItemList = map.get(soDetailAttribute);
            SoDetailItem soDetailItem = soDetailItemList.get(0);
            for (int i = 1; i < soDetailItemList.size(); i++) {
                soDetailItem.setQtySoEa(soDetailItem.getQtySoEa().add(soDetailItemList.get(i).getQtySoEa()));
                soDetailItem.setQtySo(soDetailItem.getQtySo().add(soDetailItemList.get(i).getQtySo()));
            }
            list.add(soDetailItem);
        }
        soHeaderItem.setSoDetailItems(list);
    }


    /**
     * 根据Id查询出库信息
     *
     * @param id
     * @return ReceiveContract
     */
    public SoHeaderInfo findBySoHeaderId(Long id) {
        SoHeaderInfo soHeaderInfo = dao.findBySoHeaderId(id);
        SoDetailsCondition soDetailsCondition = new SoDetailsCondition();
        soDetailsCondition.setSoNos(Arrays.asList(soHeaderInfo.getSoNo()));
        List<SoDetailInfo> soDetails = soDetailDao.findBySoId(soDetailsCondition);
        Map<String,SoDetailDateSyncItem> waybillNoMap = initSyncResultMap(soDetails);
        List<CompletableFuture<String>> futures = new ArrayList<>();
        soDetails.forEach(e->{
            LotAttribute lotAttribute = new LotAttribute();
            BeanUtil.copyProperties(e,lotAttribute);
            List<LotDetailItem> lotDetailItems = lotUtil.getLotDetailItemsByLotId(e.getLotId());
            e.setAttribute(lotUtil.lotAttributeToStr(lotDetailItems,lotAttribute));
            if (ObjUtil.isNotNull(lotAttribute.getSupplierId())) {
                e.setSupplierId(lotAttribute.getSupplierId());
                e.setSupplierName(lotAttribute.getSupplierName());
            }
            CompletableFuture<String> future = packHeaderService.syncShippedOrderNoBySoNoAndSkuId(e.getSoDetailNo(),e.getSkuId(),waybillNoMap);
            futures.add(future);
        });
        Map<String, SoDetailInfo> detailInfoMap = soDetails.stream().collect(Collectors.toMap(f -> f.getSoDetailNo() + "," + f.getSkuId(), Function.identity(), (a, b) -> a));
        futures.forEach(future -> {
            try {
                //.get()方法自动抛出异常，无需判断
                String key = future.get();
                SoDetailInfo detailInfo = detailInfoMap.get(key);
                SoDetailDateSyncItem dateSyncItem = waybillNoMap.get(key);
                if (ObjUtil.isNotNull(detailInfo)) {
                    log.info("线程：{} 执行成功，对标数据：{} ,线程数据：{}",key, JSONUtil.toJsonStr(detailInfo), JSONUtil.toJsonStr(dateSyncItem));
                    detailInfo.setWaybillNoList(dateSyncItem.getWayBillNoList());
                    detailInfo.setQtyPaEa(dateSyncItem.getQtyPaEa());
                }
            } catch (InterruptedException | ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof ServiceException) {
                    //组合不同线程提供的不同错误业务信息
                    throw (ServiceException) cause;
                }else{
                    log.error("线程执行失败,系统异常",e);
                    throw new ServiceException(GlobalExceptionEnum.SYSTEM_ERROR);
                }
            }
        });

        soHeaderInfo.setSoDetailInfos(soDetails);
        return soHeaderInfo;
    }

    /**
     * 根据出库单号查询出库信息
     * @param soNo
     * @return
     */
    public SoHeaderInfo findBySoHeaderNo(String soNo) {
        SoHeaderInfo soHeaderInfo = dao.findBySoNo(soNo);
        // 查询数据字典 组装订单来源
        if(EmptyUtil.isNotEmpty(soHeaderInfo.getSoType())) {
            DictDataCondition dictDataCondition = new DictDataCondition();
            dictDataCondition.setType(DictDataTypeConstants.SO_TYPE);
            ResponseData<List<DictDataQuery>> responseData = remoteDictDataService.getDictDataList(dictDataCondition);
            if(responseData.getSuccess()){
                List<DictDataQuery> data = responseData.getData();
                Map<String, String> dictMap = data.stream().collect(Collectors.toMap(DictDataQuery::getCode, DictDataQuery::getValue, (key1, key2) -> key1));
                if(EmptyUtil.isNotEmpty(dictMap) && dictMap.containsKey(soHeaderInfo.getSoType())) {
                    soHeaderInfo.setSoTypeStr(dictMap.get(soHeaderInfo.getSoType()));
                }
            }
        }
        // 查询数据字典 组装订单优先级别
        if(EmptyUtil.isNotEmpty(soHeaderInfo.getPriority())) {
            DictDataCondition dictDataCondition = new DictDataCondition();
            dictDataCondition.setType(DictDataTypeConstants.PRIORITY);
            ResponseData<List<DictDataQuery>> responseData = remoteDictDataService.getDictDataList(dictDataCondition);
            if(responseData.getSuccess()){
                List<DictDataQuery> data = responseData.getData();
                Map<String, String> dictMap = data.stream().collect(Collectors.toMap(DictDataQuery::getCode, DictDataQuery::getValue, (key1, key2) -> key1));
                if(EmptyUtil.isNotEmpty(dictMap) && dictMap.containsKey(soHeaderInfo.getSoType())) {
                    soHeaderInfo.setPriorityStr(dictMap.get(soHeaderInfo.getPriority()));
                }
            }
        }

        SoDetailsCondition soDetailsCondition = new SoDetailsCondition();
        soDetailsCondition.setSoNos(Arrays.asList(soHeaderInfo.getSoNo()));
        List<SoDetailInfo> soDetails = soDetailDao.findBySoId(soDetailsCondition);
        soDetails.forEach(e->{
            LotAttribute lotAttribute = new LotAttribute();
            BeanUtil.copyProperties(e,lotAttribute);
            List<LotDetailItem> lotDetailItems = lotUtil.getLotDetailItemsByLotId(e.getLotId());
            e.setAttribute(lotUtil.lotAttributeToStr(lotDetailItems,lotAttribute));
            if (ObjUtil.isNotNull(lotAttribute.getSupplierId())) {
                e.setSupplierId(lotAttribute.getSupplierId());
                e.setSupplierName(lotAttribute.getSupplierName());
            }
        });
        soHeaderInfo.setSoDetailInfos(soDetails);
        return soHeaderInfo;
    }

    private Map<String, SoDetailDateSyncItem> initSyncResultMap(List<SoDetailInfo> detailInfos) {
        Map<String, SoDetailDateSyncItem> map = new HashMap<>();
        detailInfos.forEach(e -> map.put(e.getSoDetailNo() + "," + e.getSkuId(), new SoDetailDateSyncItem()));
        return map;
    }

    public List<SoHeader> findBySoNos(Set<String> soNoSet) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(SoHeader::getSoNo, soNoSet);
        return this.find(conditionRule);
    }

    /**
     * 设置基础条件
     * @param soHeaderPageQuery
     * @return
     */
    private SoHeardPageQuery setCommonCondition(SoHeardPageQuery soHeaderPageQuery) {
        //获取查询条件数组
        if (EmptyUtil.isNotEmpty(soHeaderPageQuery.getOwnerId())) {
            String[] ownerIds = soHeaderPageQuery.getOwnerId().split(",");
            soHeaderPageQuery.setOwnerIdArray(ownerIds);
        }
        if (EmptyUtil.isNotEmpty(soHeaderPageQuery.getWarehouseId())) {
            String[] warehouseIds = soHeaderPageQuery.getWarehouseId().split(",");
            soHeaderPageQuery.setWarehouseIdArray(warehouseIds);
        }
        if (EmptyUtil.isNotEmpty(soHeaderPageQuery.getSkuId())) {
            String[] skuIds = soHeaderPageQuery.getSkuId().split(",");
            soHeaderPageQuery.setSkuIdArray(skuIds);
        }
        return soHeaderPageQuery;
    }

    public PageResult<CsAttachmentQuery> pageAtt(CsAttachmentCondition csAttachmentConditon) {
        if (EmptyUtil.isEmpty(csAttachmentConditon.getOutboundId()) || csAttachmentConditon.getOutboundId() ==-1){
            csAttachmentConditon.setContractId("-1");
        }else {
            SoHeader soHeader = dao.findById(csAttachmentConditon.getOutboundId());
            csAttachmentConditon.setContractId(soHeader.getSoNo());
        }
        PageResult<CsAttachmentQuery> page = dao.pageAtt(csAttachmentConditon);
        return page;
    }

    /**
     * 更新出库单订单属性
     *
     * @param id
     */
    public void updateSoAttributeById(Object id) {
        if (null == id) {
            return;
        }
        // 查询出库单商品数量
        Integer eaQty = dao.findInt(id);

        // 更新出库单订单属性
        SoHeaderCondition soHeaderCondition = new SoHeaderCondition();
        if (0 == eaQty) {
            soHeaderCondition.setSoAttribute(null);
            soHeaderCondition.setId(Long.valueOf(id.toString()));
        } else if (1 == eaQty) {
            soHeaderCondition.setSoAttribute(SoAttribute.ONE_ONE);
            soHeaderCondition.setId(Long.valueOf(id.toString()));
        } else {
            soHeaderCondition.setSoAttribute(SoAttribute.ONE_MORE);
            soHeaderCondition.setId(Long.valueOf(id.toString()));
        }
        dao.updateByIds(soHeaderCondition);
    }


    /**
     * 更新状态
     *
     * @param operateType 审核：00，取消审核：99
     * @param ids 需要更新的数据
     * @return
     */
    public List<SoHeader> operateSoHandler(String operateType, Long[] ids) {
        // 一.根据id查询数据
        List<SoHeader> soHeaders = dao.findByIds(ids);

        // 二.订单已取消，无法操作
        soHeaders.stream()
                .filter(e -> SoOrderStatusEnum.CANCEL.getCode().equals(e.getStatus()))
                .findFirst()
                .ifPresent(e -> {
                    throw new ServiceException(SoExcepitonEnum.SO_AUDIT_ERROR_HAVE_AUDIT.getMsg());
                });

        // 三.已分配的订单不可以操作审核和取消审核
        soHeaders.stream()
                .filter(e -> !(Objects.isNull(e.getAllocStatus()) || SoAllocStatusEnum.UN_ALLOC.getCode().equals(e.getAllocStatus())) )
                .findFirst()
                .ifPresent(e -> {
                    throw new ServiceException(SoExcepitonEnum.SO_AUDIT_ERROR_HAVE_ALLOCATE.getMsg());
                });

        // 四.检查是否存在相同的操作类型
        soHeaders.stream()
                .filter(e -> !operateType.equals(e.getAuditStatus()))
                .findFirst()
                .ifPresent(e -> {
                    throw new ServiceException(
                            OutboundAuditStatusEnum.AUDITED.getCode().equals(operateType)
                                    ? SoExcepitonEnum.SO_CANCEL_AUDIT_ERROR_HAVE_UNREVIEWED
                                    : SoExcepitonEnum.SO_AUDIT_ERROR_HAVE_AUDIT
                    );
                });

        // 如果是取消审核，需要未生成波次订单
        if (OutboundAuditStatusEnum.AUDITED.getCode().equals(operateType)) {
            List<SoDetail> wvNoByDetail = wvHeaderDao.getSoNoByWvDetailSoNo(SoDetail.class,"soIds",ids);
            if (wvNoByDetail != null && !wvNoByDetail.isEmpty()) {
                String wvNumbers = wvNoByDetail.stream()
                        .map(SoDetail::getSoNo)
                        .distinct()
                        .collect(Collectors.joining("、"));

                throw new ServiceException(SoExcepitonEnum.SO_CANCEL_AUDIT_ERROR_HAVE_SAVE_WV,wvNumbers);
            }else{
                List<SoHeader> list = soHeaders.stream().filter(e -> StrUtil.isNotEmpty(e.getLogisticNo())).toList();
                if (CollUtil.isNotEmpty(list)){
                    StatusUpdateCondition condition = new StatusUpdateCondition();
                    condition.setOperationType(OperationTypeEnum.CANCEL_EXAMINE.getCode());
                    condition.setCode(list.stream().map(SoHeader::getLogisticNo).toList());
                    remoteSoStatusPushToCrzFegin.updateSoStatus(condition);
                }
            }
        }else{
            List<SoHeader> list = soHeaders.stream().filter(e -> StrUtil.isNotEmpty(e.getLogisticNo())).toList();
            if (CollUtil.isNotEmpty(list)){
                StatusUpdateCondition condition = new StatusUpdateCondition();
                condition.setOperationType(OperationTypeEnum.EXAMINE.getCode());
                condition.setCode(list.stream().map(SoHeader::getLogisticNo).toList());
                remoteSoStatusPushToCrzFegin.updateSoStatus(condition);
            }
        }

        // 五：修改审核状态
        soHeaders.forEach(e ->{
            e.setAuditStatus(OutboundAuditStatusEnum.AUDITED.getCode().equals(operateType) ? OutboundAuditStatusEnum.AUDIT_UN.getCode() : OutboundAuditStatusEnum.AUDITED.getCode());
            e.setAllocStatus(OutboundAuditStatusEnum.AUDITED.getCode().equals(operateType) ? null : SoAllocStatusEnum.UN_ALLOC.getCode());
            preSave(e);
            e.setAuditOpId(e.getModifier());
            e.setAuditTime(new Date());
        });
        dao.batchUpdate(soHeaders);
        return soHeaders;
    }


    public void updateSoHeaderStatus(Long[] ids, Long userId) {

        SoHeaderCondition soHeaderCondition = new SoHeaderCondition();
        soHeaderCondition.setIds(ids);
        soHeaderCondition.setModifier(userId);
        dao.updateSoHeaderStatus(soHeaderCondition);
        //清除hibernate缓存
        Session session = (Session) entityManager.getDelegate();
        session.clear();
    }

    public void updateStatusBySoNo(String soNo, String status){
        SoHeaderCondition soHeaderCondition = new SoHeaderCondition();
        soHeaderCondition.setSoNo(soNo);
        soHeaderCondition.setAllocStatus(status);
        soHeaderDao.updateStatusBySoNo(soHeaderCondition);
    }

    /**
     * 校验出库单各状态,取消关闭状态、拦截状态、冻结状态
     *
     * @param soId
     * @param warehouseId
     * @param actionCode
     */
    public ResponseData<SoHeaderQuery> checkStatus(Long soId, Long warehouseId, String actionCode) {
        ResponseData<SoHeaderQuery> responseData = this.getById(soId, warehouseId);
        if(!responseData.getSuccess()){
            return responseData;
        }
        SoHeaderQuery soHeaderQuery = (SoHeaderQuery) responseData.getData();
        String soNo = soHeaderQuery.getSoNo();
        if (null != actionCode && "".equals("")) {
            //保存、删除
            if (ActionCode.SO_AUDIT.equals(actionCode)) {
//                if (!(soHeaderQuery.getStatus().equals(SoStatus.SO_NEW) && soHeaderQuery.getAuditStatus().equals(AuditStatus.AUDIT_NEW))) {
//                    //不是创建状态
//                    return ResponseData.error(MsgConstant.AUDIT_ONLY_CREATE_AND_NOT_AUDIT,soNo);
//                }
            }

            if (ActionCode.SO_CANCEL.equals(actionCode)) {
//                if (!(soHeaderQuery.getStatus().equals(SoStatus.SO_NEW) && soHeaderQuery.getAuditStatus().equals(AuditStatus.AUDIT_NEW))) {
//                    //不是创建状态
//                    return ResponseData.error(MsgConstant.CANCELSO_ONLY_CREATE_AND_NOT_AUDIT,soNo);
//                }
            }

            if (ActionCode.SO_CANCEL_AUDIT.equals(actionCode)) {
//                if (!(soHeaderQuery.getStatus().equals(SoStatus.SO_NEW) && soHeaderQuery.getAuditStatus().equals(AuditStatus.AUDIT_CLOSE))) {
//                    //不是创建状态
//                    return ResponseData.error(MsgConstant.CANCELAUDIT_ONLY_CREATE_AND_AUDIT,soNo);
//                }
            }

            if (ActionCode.SO_CLOSE.equals(actionCode)) {
//                if (!soHeaderQuery.getStatus().equals(SoStatus.SO_PART_SHIPPING) && !soHeaderQuery.getStatus().equals(SoStatus.SO_FULL_SHIPPING)) {
//                    //不是创建状态
//                    return ResponseData.error(MsgConstant.CLOSE_ONLY_PART_AND_FULL_SHIPPING);
//                }
            }
        }
        responseData.setSuccess(true);
        responseData.setData(soHeaderQuery);
        return responseData;
    }

    /**
     * 根据出库单号获取出库单
     *
     * @param soId
     * @param warehouseId
     * @return
     */
    private ResponseData<SoHeaderQuery> getById(Long soId, Long warehouseId){
        ResponseData<SoHeaderQuery> responseData = new ResponseData<>();
        try {
            SoHeaderCondition condition = new SoHeaderCondition();
            condition.setId(soId);
            if (EmptyUtil.isNotEmpty(warehouseId)) {
                condition.setWarehouseId(warehouseId);
            }
            List<SoHeaderQuery> soHeaderList = dao.find(SoHeaderQuery.class, condition);
            if (soHeaderList.size() == 0) {
                return ResponseData.error(MsgConstant.SOHEADER_NOT_FOUND);
            }
            responseData.setSuccess(true);
            responseData.setData(soHeaderList.get(0));
            return responseData;
        } catch (RuntimeException e) {
            return ResponseData.error(MsgConstant.SOHEADER_NOT_FOUND);
        }
    }

    /**
     * 发运订单审核
     * 1.校验订单是否存在商品
     *
     * @param ids
     * @return
     */
//    private String auditSo(Long[] ids) {
//        StringBuilder failMsg = new StringBuilder();
//        for (int i = 0; i < ids.length; i++) {
//            SoHeader soHeader = dao.findById(ids[i]);
//            SoHeaderItem soHeaderItem = new SoHeaderItem();
//            BeanUtil.copyPropertiesIsNotNull(soHeader, soHeaderItem);
//            SoDetailsCondition soDetailsCondition = new SoDetailsCondition();
//            soDetailsCondition.setOutboundId(ids[i]);
//            List<Map<String, Object>> soDetail = soDetailDao.findSoDetailList(soDetailsCondition);
//
//            if (null != soDetail && soDetail.size() > 0) {
//                soHeaderItem.setAuditStatus(AuditStatus.AUDIT_CLOSE); //已审核
//                if (null != SessionContext.getSessionUserInfo()) {
//                    if (null != SessionContext.getSessionUserInfo().getUserId()) {
////                        soHeaderItem.setAuditOp(SessionContext.getSessionUserInfo().getRealName());
//                    }
//                }
////                soHeaderItem.setAuditTime(new Date());
//                dao.saveOrUpdate(soHeaderItem);
//            } else {
//                failMsg.append(soHeader.getSoNo());
//                if (i < (ids.length - 1)) {
//                    failMsg.append("、");
//                }
//                continue;
//            }
//        }
//        if (EmptyUtil.isNotEmpty(failMsg.toString())) {
//            failMsg.append("不存在商品明细，审核失败");
//        }
//        return failMsg.toString();
//    }

    /**
     * Description : 批量关闭发货订单
     *
     * @param soIds
     * @Author: Joan.Zhang
     * @Create Date: 2019-10-15
     */
    public ResponseData<SoHeader> outboundCloseSo(List<Object> soIds) {
        ResponseData<SoHeader> responseData = new ResponseData<>();
        StringBuilder error = new StringBuilder();
        for (Object soId : soIds) {
            try {
                ResponseData<SoHeader> closeMsg = this.closeSo(Long.valueOf(soId.toString()));
                if (!closeMsg.getSuccess()) {
                    error.append(closeMsg.getMsg());
                    responseData.setSuccess(false);
                }
            } catch (Exception e) {
                error.append(e.getMessage());
                responseData.setSuccess(false);
            }
        }
        if (EmptyUtil.isNotEmpty(error.toString())) {
            responseData.setSuccess(false);
            responseData.setMsg(error.toString());
            return responseData;
        }
        responseData.setSuccess(true);
        return responseData;
    }


    /**
     * Description :出库单关闭
     *
     * @param soId 出库单主键
     * @Author: Joan.Zhang
     */
    public ResponseData<SoHeader> closeSo(Long soId) {
        ResponseData responseData = new ResponseData<>();
        //获取出库单
        SoHeaderCondition soHeaderCondition = new SoHeaderCondition();
        soHeaderCondition.setSoId(soId);
        SoHeader soHeader = dao.findFirstById(soHeaderCondition);
        //拦截状态，不能关闭
//        if (SystemStatus.YES.equals(soHeader.getIsCallback())) {
//            //订单{0}已被拦截
//            return ResponseData.error(MsgConstant.SOHEADER_IS_CALLBACK,soHeader.getSoNo());
//        }
        //如果订单有预配数、分配数、拣货数，那么提示不可关闭
        SoDetailCondition soDetailCondition = new SoDetailCondition();
        soDetailCondition.setSoId(soId);
        Double qtyOpEa = soDetailDao.findDouble(soDetailCondition);
        if (qtyOpEa != null && qtyOpEa > 0) {
            //{0}存在分配数或拣货数，不能操作
            return ResponseData.error(MsgConstant.EXIST_QTYOPEA_AND_QTYOPEA,soHeader.getSoNo());
        }
        //订单状态 70/80时关闭
        if (SoStatus.SO_PART_SHIPPING.equals(soHeader.getStatus()) || SoStatus.SO_FULL_SHIPPING.equals(soHeader.getStatus())) {
            //状态更新close
            soHeader.setStatus(SoStatus.SO_CLOSE);
            AdministrativeRegionCondition administrativeRegionCondition = new AdministrativeRegionCondition();
            administrativeRegionCondition.setRegionLevel("province");
//            administrativeRegionCondition.setName(soHeader.getConsigneeProvinceName());
            ResponseData<List<AdministrativeRegionQuery>> response = remoteAdministrativeRegionService.getRegionList(administrativeRegionCondition);
            if(!response.getSuccess()){
                responseData.setMsg(response.getMsg());
                responseData.setSuccess(false);
                return responseData;
            }
            if(EmptyUtil.isNotEmpty(response.getData())){
                AdministrativeRegionQuery regionQuery = response.getData().get(0);
//                soHeader.setConsigneeProvinceCode(regionQuery.getCode());
            }
            //保存
            dao.update(soHeader);
        } else {
            //{0}非部分发运、非完全发运状态，不能操作"
            return ResponseData.error(MsgConstant.NOT_SO_PART_OR_FULL_SHIPPING,soHeader.getSoNo());
        }
        //返回信息，数据
        responseData.setSuccess(true);
        responseData.setData(soHeader);
        return responseData;
    }

    /**
     * Description : 校验订单拦截状态，抛出提示
     *
     * @param processByCode
     * @param ids
     * @Author: Joan.Zhang
     * @Create Date: 2019-10-14
     */
    public ResponseData checkBatchIsCallback(String processByCode, Object[] ids) {
        CheckIsCallbackCondition condition = new CheckIsCallbackCondition();
        condition.setIsCallback(SystemStatus.YES);
        //按出库单号
        if (processByCode.equals(ProcessByCode.BY_SO)) {
            condition.setSoIds(ids);
        } else if (processByCode.equals(ProcessByCode.BY_ALLOC)) {//按分配ID
            condition.setAllocIds(ids);
        } else if (processByCode.equals(ProcessByCode.BY_WAVE)) {//按波次单号
            condition.setWaveIds(ids);
        }
        List<SoHeader> items = soHeaderDao.findIsCallback(condition);
        if (items.size() > 0) {
            StringBuilder listStr = new StringBuilder("");
            for (SoHeader item : items) {
                listStr.append(item.getSoNo());
            }
            //{0}已拦截，不能操作
            return ResponseData.error(MsgConstant.ISCALLBACK_NOT_OPERATE,listStr.toString());
        }
        return ResponseData.success();
    }


    /**
     * 获取拣选单表头信息
     *
     * @param id
     * @return 拣选单表头信息
     */
    public SoPrintHeader getSoPrintHeaderById(Object id) {
        SoHeaderCondition soHeaderCondition = new SoHeaderCondition();
        soHeaderCondition.setId(Long.valueOf(id.toString()));
        return  dao.findFirstSoPrintHeaderById(soHeaderCondition);
    }

    /**
     * Description :发货完成，是否自动关闭订单
     *
     * @param ids
     * @Author: Joan.Zhang
     */
    public void checkShipAutoCloseSo(Object[] ids, String processCode, Long warehouseId) {
        try {
            //发货参数：完全发货后是否自动关闭SO（1：自动关闭；0：不自动关闭）
            WarehouseControlParamItem whControlParam = warehouseControlParamService.getWarehouseControlParam("SHIP_AUTO_CLOSE_SO", warehouseId);
            if (null != whControlParam && EmptyUtil.isNotEmpty(whControlParam.getCpValue())) {
                String shipAutoCloseSo =  whControlParam.getCpValue();
                if (SystemStatus.YES.equals(shipAutoCloseSo)) {
                    //完全关闭后自动关闭
                    this.outboundBatchCloseSo(processCode, ids);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * Description : 批量关闭发货订单
     *
     * @param processByCode 类型
     * @Author: Joan.Zhang
     */
    public ResponseData outboundBatchCloseSo(String processByCode, Object[] ids) {
        StringBuilder error = new StringBuilder();
        //拦截状态校验
        ResponseData responseData = this.checkBatchIsCallback(processByCode, ids);
        if (!responseData.getSuccess()) {
            error.append(responseData.getMsg());
        }
        //2、按波次单主键/按出库单主键/按分配主键获取soIds
        List<Object> soIds = this.getSoIdByProcessByCode(processByCode, ids, SoStatus.SO_FULL_SHIPPING);
        if (soIds.size() == 0) {
            error.append("没有可以操作的出库订单");
        }
        if (EmptyUtil.isNotEmpty(error.toString())) {
            responseData.setSuccess(false);
            responseData.setMsg(error.toString());
            return responseData;
        }
        //按分配明细发货
        responseData = this.outboundCloseSo(soIds);
        responseData.setSuccess(true);
        return responseData;
    }

    /**
     * Description :获取发运订单主键，过滤出库单 关闭、取消状态, 过滤 拦截订单
     *
     * @param processByCode 按波次单主键、按出库单主键、按分配主键
     * @param ids
     * @param status        状态-完全发运(执行操作：关闭订单)
     * @Author: Joan.Zhang
     * @Create 2019-10-15
     */
    public List<Object> getSoIdByProcessByCode(String processByCode, Object[] ids, String status) {
        //获取分配明细
        SoHeaderCondition condition = new SoHeaderCondition();
        condition.setStatus(status);
        //按波次单主键
        if (processByCode.equals(ProcessByCode.BY_WAVE)) {
            condition.setWvIds(ids);
        } else if (processByCode.equals(ProcessByCode.BY_ALLOC)) {
            condition.setAllocIds(ids);
        }
        if (processByCode.equals(ProcessByCode.BY_SO)) {
            condition.setSoIds(ids);
        }
        List<SoHeader> soHeaders = soHeaderDao.getSoIdsByProcessIds(condition);

        List<Object> soIds = new ArrayList<>();
        if (soHeaders.size() > 0) {
            soHeaders.forEach(soHeader -> soIds.add(soHeader.getId()));
        }
        return soIds;
    }

    /**
     * ERP发起取消订单，自动取消审核并自动取消订单，目前只为接口服务
     *
     * @param id
     * @return
     */
    public ResponseData orderCancel(String id, String orderType) {
        if (ActionCode.SO_ORDER.equals(orderType)) {       //出库单
            return this.cancelSoHeader(id);
        } else {        //入货单
            return this.cancelAsnHeader(id);
        }
    }

    private ResponseData cancelSoHeader(String id) {
        ResponseData responseData = new ResponseData();
        SoHeader soHeader = dao.findById(Long.valueOf(id));
        //ERP发起取消订单，要自动取消审核并取消订单，先判断状态是否为创建且是已审核的状态
//        if (soHeader.getStatus().equals(SoStatus.SO_FULL_SHIPPING) || (soHeader.getStatus().equals(SoStatus.SO_PART_SHIPPING) || soHeader.getStatus().equals(SoStatus.SO_CLOSE))) {
//            //不是创建状态
//            return ResponseData.error(MsgConstant.SO_PART_OR_FULL_SHIPPING_NOT_CANCELORDER);
//        }else if(soHeader.getStatus().equals(SoStatus.SO_NEW) && (soHeader.getAuditStatus().equals(AuditStatus.AUDIT_CLOSE) || soHeader.getAuditStatus().equals(AuditStatus.AUDIT_NEW))){
//            soHeader.setAuditStatus(SoStatus.SO_NEW);
//            soHeader.setStatus(SoStatus.SO_CANCEL);
//            soHeader.setIsCancel(StatusConstant.YES);
//        }else{
//            soHeader.setIsCancel(StatusConstant.YES);
//        }
        //更新审核状态
        soHeader.setModifier(-1L);

        if (dao.update(soHeader)) {
            responseData.setData(id);
            responseData.setSuccess(true);
        } else {
            responseData.setData(id);
            ErrorResponseData error = ResponseData.error(MsgConstant.STATUS_UPDATE_FAIL);
            responseData.setMsg(error.getMsg());
            responseData.setSuccess(false);
        }
        return responseData;
    }

    private ResponseData cancelAsnHeader(String id) {
        ResponseData responseData = new ResponseData();
        AsnHeader asnHeader = asnHeaderDao.findById(Long.valueOf(id));
        //ERP发起取消订单，要自动取消审核并取消订单，先判断状态是否为创建且是已审核的状态
        if (!(asnHeader.getStatus().equals(SoStatus.SO_NEW) && (asnHeader.getAuditStatus().equals(AuditStatus.AUDIT_CLOSE) || asnHeader.getAuditStatus().equals(AuditStatus.AUDIT_NEW)))) {
            //不是创建状态
            return ResponseData.error(MsgConstant.CREATE_AND_AUDIT_CAN_CANCEL,asnHeader.getAsnNo());
        }
        //更新审核状态
        asnHeader.setAuditStatus(SoStatus.SO_NEW);
        asnHeader.setStatus(SoStatus.SO_CANCEL);
        asnHeader.setModifier(-1L);

        if (asnHeaderDao.update(asnHeader)) {
            responseData.setData(id);
            responseData.setSuccess(true);
        } else {
            responseData.setData(id);
            ErrorResponseData error = ResponseData.error(MsgConstant.STATUS_UPDATE_FAIL);
            responseData.setMsg(error.getMsg());
            responseData.setSuccess(false);
        }
        return responseData;
    }

    public SoHeader queryBySoNo(String soNo){
        SoHeaderCondition soHeaderCondition = new SoHeaderCondition();
        soHeaderCondition.setSoNo(soNo);
        return this.findFirst(soHeaderCondition);
    }

    /**
     * 订单完结
     *
     * @param id
     */
    public void orderFinishByIds(Long id) {
        SoHeader soHeader = dao.findById(id);
        // 1.校验是否存在未分配订单
        if (Objects.equals(SoAllocStatusEnum.UN_ALLOC.getCode(),soHeader.getAllocStatus())) {
            throw new ServiceException(SoExcepitonEnum.SO_FINISH_ERROR_HAVE_UN_ALLOCATE.getMsg());
        }
        // 2.校验是否存在订单取消
        if (Objects.equals(SoOrderStatusEnum.CANCEL.getCode(),soHeader.getStatus()) || Objects.equals(SoOrderStatusEnum.CLOSE.getCode(),soHeader.getStatus())) {
            throw new ServiceException(SoExcepitonEnum.SO_FINISH_ERROR_HAVE_CANCEL_FINISH.getMsg());
        }
        // 3.完成出库单
        soHeader.setStatus(SoOrderStatusEnum.CLOSE.getCode());
        dao.update(soHeader);
        //调用edi 推送护具到 crz系统
        if (StrUtil.isNotEmpty(soHeader.getLogisticNo())){
            StatusUpdateCondition condition = new StatusUpdateCondition();
            condition.setOperationType(OperationTypeEnum.CLOSE.getCode());
            condition.setCode(Collections.singletonList(soHeader.getLogisticNo()));
            remoteSoStatusPushToCrzFegin.updateSoStatus(condition);
        }
        //关联销售订单数据处理
        modifyRelationOrderStatus(Arrays.asList(soHeader.getSoNo()));
        // 确认完结后订单内已分配但未生成拣货单号的单子强制生成拣货单号
        List<SoPicking> soPickingList = soPickingService.findBySoNo(soHeader.getSoNo());
        if (CollectionUtil.isEmpty(soPickingList)){
            return;
        }
        List<String> soPicking = soPickingList.stream()
                .filter(e -> SoPickingStatusEnum.NOT_GENERATE.getCode().equals(e.getStatus()))
                .map(SoPicking::getPickingNo).toList();
        soPicking.forEach(e -> {
            SoPickingUpdateStatusItem item = new SoPickingUpdateStatusItem();
            item.setPickingNo(e);
            item.setStatus(SoPickingStatusEnum.UNPICKED.getCode());
            soPickingLock.updateStatus(item);
        });
        List<WvDetail> list = wvDetailService.find(new ConditionRule().andEqual(WvDetail::getSoNo, soHeader.getSoNo()));
        //取出list中所有的未分配数
        if (CollUtil.isEmpty(list)){
            return;
        }
        list.forEach(item -> {
            item.setQtyAllocationEa(item.getQtyAllocationEa() - (item.getQtyAllocationEa() - item.getQtyAllocatedEa()));
            wvDetailService.update(item);
        });
    }
    /**
     *@Description 更新关联订单状态
     *@Param ids
     *@Return Void
     *@Date 2025/5/16 15:13
     *<AUTHOR>
     **/
    public void modifyRelationOrderStatus(List<String> soNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(SoHeader::getSoNo, soNoList);
        List<SoHeader> soHeaderList = dao.find(conditionRule);
        if(CollectionUtil.isEmpty(soHeaderList)){
            return;
        }
        List<SoHeader> relationAsnHeaderList = soHeaderList.stream().filter(x -> YesNoEnum.YES.getCode().equals(x.getRelationOrder())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(relationAsnHeaderList)){
            return;
        }
        List<String> businessNoList = relationAsnHeaderList.stream().map(SoHeader::getSoNo).distinct().collect(Collectors.toList());
        // 根据出库单号查询关联订单信息
        List<OrderRelation> orderRelationList = orderRelationService.findByBusinessNo(businessNoList);
        if (CollectionUtil.isEmpty(orderRelationList)){
            return;
        }
        //根据出库单集合查询对应明细数据
        List<SoDetail> soDetailList = soDetailService.findBySoNoList(businessNoList);
        Map<Long,List<SoDetail>> groupByOrderSkuIdMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(soDetailList)){
            //过滤分配数比订货数要小的数据
            List<SoDetail> hitDetailsList = soDetailList.stream().filter(x -> (ObjectUtil.isNotNull(x.getQtyAllocationEa()) && ObjectUtil.isNotNull(x.getQtySoEa()))
                    &&  x.getQtyAllocationEa().compareTo(x.getQtySoEa()) < 0).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(hitDetailsList)){
                groupByOrderSkuIdMap = hitDetailsList.stream().collect(Collectors.groupingBy(SoDetail::getOrderSkuId));
            }
        }
        //抽取订单号
        List<String> orderNoList = orderRelationList.stream().map(OrderRelation::getOrderNo).distinct().collect(Collectors.toList());
        //根据订单号集合查询订单对应的所有的关联数据
        List<OrderRelation> allOrderRelationList = orderRelationService.findByOrderNoList(orderNoList);
        //转map
        Map<String,List<OrderRelation>> allOrderRelationMap = allOrderRelationList.stream().collect(Collectors.groupingBy(OrderRelation::getOrderNo));
        //抽取出库单号
        List<String> asnNoList = allOrderRelationList.stream().map(OrderRelation::getBusinessNo).distinct().collect(Collectors.toList());
        //查询出库单
        List<SoHeader> allSoHeaderList = dao.find(ConditionRule.getInstance().andIn(SoHeader::getSoNo,asnNoList));
        //查询入库单详情
        List<SoDetail> AllSoDetailList = soDetailService.find(ConditionRule.getInstance().andIn(SoHeader::getSoNo,asnNoList));
        Map<String,List<SoDetail>> allSoDetailMap = AllSoDetailList.stream().collect(Collectors.groupingBy(SoDetail::getSoNo));
        //查询订单
        List<Order> allOrderList = orderService.findByOrderNoList(orderNoList);
        Map<String,Order> allOrderMap = allOrderList.stream().collect(Collectors.toMap(Order::getOrderNo,x->x));
        //查询订单关联商品
        List<OrderSku> orderSkuList = orderSkuService.listByOrderNoList(orderNoList);
        List<Long> packageUnitIdList = orderSkuList.stream().map(OrderSku::getPackageUnitId).distinct().collect(Collectors.toList());
        List<PackageUnit> packageUnitList = packageUnitService.findByIds(packageUnitIdList.toArray(new Long[]{}));
        Map<Long,PackageUnit> packageUnitMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(packageUnitList)){
            packageUnitMap = packageUnitList.stream().collect(Collectors.toMap(PackageUnit::getId,x->x));
        }
        //处理
        Map<Long, List<SoDetail>> finalGroupByOrderSkuIdMap = groupByOrderSkuIdMap;
        Map<Long, PackageUnit> finalPackageUnitMap = packageUnitMap;
        List<OrderSku> upadateOrderSkuList = new ArrayList<>();
        orderSkuList.forEach(x -> {
            List<SoDetail> currentSoDetailList = finalGroupByOrderSkuIdMap.get(x.getId());
            BigDecimal addAmount = BigDecimal.ZERO;
            //剩余ea数 = 订单数量ea - 订单已分配数量ea
            if(CollectionUtil.isNotEmpty(currentSoDetailList)){
                //添加已完结出库单数据过滤
                addAmount = currentSoDetailList.stream()
                        .filter(detail -> soNoList.contains(detail.getSoNo()))
                        .map(item -> item.getQtySoEa().subtract(ObjectUtil.isNull(item.getQtyAllocationEa())? BigDecimal.ZERO :item.getQtyAllocationEa())).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            //订单关联商品数量 = 订单关联商品数量 + 剩余ea数
            if(addAmount.compareTo(BigDecimal.ZERO) > 0){
                x.setRemainCountEa(x.getRemainCountEa().add(addAmount));
                PackageUnit packageUnit = finalPackageUnitMap.get(x.getPackageUnitId());
                if(ObjectUtil.isNotNull(packageUnit)){
                    x.setRemainCount(x.getRemainCountEa().divide(new BigDecimal(packageUnit.getMinQuantity())));
                }
                orderSkuService.preSave(x);
                upadateOrderSkuList.add(x);
            }
        });

        Map<String,List<OrderSku>> orderSkuMap = orderSkuList.stream().collect(Collectors.groupingBy(OrderSku::getOrderNo));
        List<Order> updateList = new ArrayList<>();
        allOrderRelationMap.forEach((orderNo,currentOrderRelationList) ->{
            boolean soFlag = false;
            List<String> currentSoNoList = currentOrderRelationList.stream().map(OrderRelation::getBusinessNo).distinct().collect(Collectors.toList());
            List<SoDetail> currentSoDetailList = new ArrayList<>();
            allSoDetailMap.forEach((soNo,currentSoDetail) ->{
                if(currentSoNoList.contains(soNo)){
                    currentSoDetailList.addAll(currentSoDetail);
                }
            });
            List<SoHeader> currentHeadList = allSoHeaderList.stream().filter(x -> currentSoNoList.contains(x.getSoNo())).collect(Collectors.toList());
            boolean statusFlag = Boolean.FALSE;
            if(CollectionUtil.isNotEmpty(currentHeadList)){
                statusFlag = currentHeadList.stream().allMatch(x -> SoOrderStatusEnum.CLOSE.getCode().equals(x.getStatus()));
            }
            if (CollectionUtil.isNotEmpty(currentSoDetailList)){
                //所有发运量都等于分配量 证明已完全分配
                soFlag = currentSoDetailList.stream().allMatch(x -> ObjectUtil.isNotNull(x.getQtyShippedEa()) && x.getQtyShippedEa().compareTo(x.getQtyAllocationEa()) == 0);
            }
            List<OrderSku> currentOrderSkuList = orderSkuMap.get(orderNo);
            boolean osFlag = false;
            //采购单对应商品的剩余量是否都为0
            if(CollectionUtil.isNotEmpty(currentOrderSkuList)){
                osFlag = currentOrderSkuList.stream().allMatch(x -> x.getRemainCount().compareTo(BigDecimal.ZERO) == 0);
            }
            Order order = allOrderMap.get(orderNo);
            if(soFlag && osFlag && statusFlag){
                //将销售单状态修改为已完成
                order.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
                orderService.preSave(order);
                updateList.add(order);
            }
        });
        if (CollectionUtil.isNotEmpty(upadateOrderSkuList)){
            orderSkuService.batchUpdate(upadateOrderSkuList);
        }
        if(CollectionUtil.isNotEmpty(updateList)){
            orderService.batchUpdate(updateList);
        }
    }

    /**
     * 订单取消
     */
    @Transactional(rollbackFor = Exception.class)
    public void orderCancelByIds(List<SoHeader> soHeaders) {
        long count = soHeaders.stream().filter(e -> AuditStatusEnum.AUDITED.getCode().equals(e.getAuditStatus())).count();
        if (count>0){
            throw new ServiceException(SoExcepitonEnum.SO_AUDIT_ERROR_HAVE_AUDIT.getMsg());
        }
        soHeaders.stream().forEach(e->{
            e.setStatus(SoOrderStatusEnum.CANCEL.getCode());
            preSave(e);
        });
        batchUpdate(soHeaders);
        //调用edi 推送护具到 crz系统
        List<SoHeader> list = soHeaders.stream().filter(e -> StrUtil.isNotEmpty(e.getLogisticNo())).toList();
        if (CollectionUtil.isNotEmpty(list)){
            StatusUpdateCondition condition = new StatusUpdateCondition();
            condition.setOperationType(OperationTypeEnum.CANCEL.getCode());
            condition.setCode(soHeaders.stream().map(SoHeader::getLogisticNo).toList());
            remoteSoStatusPushToCrzFegin.updateSoStatus(condition);
        }
        //回退销售单关联数据
        callbackOrderRelation(soHeaders);
    }

    /**
     *@Description 销售单相关数据回退
     *@Param soHeaders
     *@Return Void
     *@Date 2025/5/14 17:03
     *<AUTHOR>
     **/
    private void callbackOrderRelation(List<SoHeader> soHeaders) {
        List<SoHeader> orderRelationHeadList = soHeaders.stream().filter(e -> YesNoEnum.YES.getCode().equals(e.getRelationOrder())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(orderRelationHeadList)){
            //抽取入库单号
            List<String> businessNoList = orderRelationHeadList.stream().map(SoHeader::getSoNo).distinct().collect(Collectors.toList());
            //根据入库单号查询对应关联商品信息
            List<OrderRelation> orderRelationList = orderRelationService.findByBusinessNo(businessNoList);
            Map<String, List<OrderRelation>> orderRelationMap = orderRelationList.stream().collect(Collectors.groupingBy(OrderRelation::getBusinessNo));
            List<Long> orderSkuIdList = orderRelationList.stream().map(OrderRelation::getOrderSkuId).distinct().collect(Collectors.toList());
            List<OrderSku> orderSkuList = orderSkuService.findByIds(orderSkuIdList.toArray(new Long[0]));
            if(CollectionUtil.isNotEmpty(orderSkuList)){
                List<PackageUnit> packageUnitList = packageUnitService.findByIds(orderSkuList.stream().map(OrderSku::getPackageUnitId).distinct().collect(Collectors.toList()).toArray(new Long[0]));
                Map<Long, PackageUnit> packageUnitMap = new HashMap<>();
                if(CollectionUtil.isNotEmpty(packageUnitList)){
                    packageUnitMap = packageUnitList.stream().collect(Collectors.toMap(PackageUnit::getId, Function.identity(),(v1, v2) -> v2));
                }
                Map<Long, OrderSku> orderSkuMap = orderSkuList.stream().collect(Collectors.toMap(OrderSku::getId, Function.identity(),(v1, v2) -> v2));
                Map<Long, PackageUnit> finalPackageUnitMap = packageUnitMap;
                List<OrderSku> updateList = new ArrayList<>();
                orderRelationMap.forEach((businessNo, currentOrderRelationList) -> {
                    if(CollectionUtil.isNotEmpty(currentOrderRelationList)){
                        currentOrderRelationList.forEach(x -> {
                            OrderSku orderSku = orderSkuMap.get(x.getOrderSkuId());
                            orderSku.setRemainCount(orderSku.getRemainCount().add(x.getCurrentAmount()));
                            PackageUnit packageUnit = finalPackageUnitMap.get(orderSku.getPackageUnitId());
                            if(ObjectUtil.isNotNull(packageUnit)){
                                if(ObjectUtil.isNotNull(packageUnit.getMinQuantity())){
                                    BigDecimal minQuantity = new BigDecimal(packageUnit.getMinQuantity());
                                    orderSku.setRemainCountEa(orderSku.getRemainCount().multiply(minQuantity).setScale(2,BigDecimal.ROUND_HALF_UP));
                                }
                            }
                            orderSkuService.preSave(orderSku);
                            updateList.add(orderSku);
                        });
                    }
                });
                if(CollectionUtil.isNotEmpty(updateList)){
                    orderSkuService.batchUpdate(updateList);
                    //抽取订单号
                    List<String> orderNoList = updateList.stream().map(OrderSku::getOrderNo).distinct().collect(Collectors.toList());
                    List<Order> orderList = orderService.findByOrderNoList(orderNoList);
                    List<Order> updateOrderList = new ArrayList<>();
                    //根据订单号查询所有商品信息
                    List<OrderSku> allOrderSkuList = orderSkuService.listByOrderNoList(orderNoList);
                    //根据编号分组
                    Map<String, List<OrderSku>> allOrderSkuMap = allOrderSkuList.stream().collect(Collectors.groupingBy(OrderSku::getOrderNo));
                    orderList.forEach(x ->{
                        List<OrderSku> currentOrderSkuList = allOrderSkuMap.get(x.getOrderNo());
                        if(CollectionUtil.isNotEmpty(currentOrderSkuList)){
                            BigDecimal totalCount = currentOrderSkuList.stream().map(OrderSku::getTotalCount).reduce(BigDecimal.ZERO,BigDecimal::add);
                            BigDecimal remainCount = currentOrderSkuList.stream().map(OrderSku::getRemainCount).reduce(BigDecimal.ZERO,BigDecimal::add);
                            //总数大于 0 且 和可用数一致
                            String code;
                            if (totalCount.compareTo(BigDecimal.ZERO) > 0 && totalCount.compareTo(remainCount) == 0){
                                code = OrderStatusEnum.CREATE.getCode();
                            }else{
                                code = OrderStatusEnum.PROCESSING.getCode();
                            }
                            x.setOrderStatus(code);
                            orderService.preSave(x);
                            updateOrderList.add(x);
                        }
                    });
                    if(CollectionUtil.isNotEmpty(updateOrderList)){
                        orderService.batchUpdate(updateOrderList);
                    }
                }
            }
            orderRelationHeadList.forEach(x ->{
                x.setRelationOrder(YesNoEnum.NO.getCode());
                x.setSaleOrderNo(null);
                preSave(x);
            });
            batchUpdate(orderRelationHeadList);
            //删除关联数据
            orderRelationService.delByBusinessNo(businessNoList);
        }
    }

    /**
     *@Description 根据编号集合进行查询
     *@Param orderNoList
     *@Return * {@link List< String> }
     *@Date 2025/5/27 14:53
     *<AUTHOR>
     **/
    public List<SoHeader> findBySoNoList(List<String> orderNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(SoHeader::getSoNo,orderNoList);
        return dao.find(conditionRule);
    }

    /**
     *@Description 根据上游单号集合查询
     *@Param orderNoList
     *@Return * {@link List< SoHeader> }
     *@Date 2025/5/27 16:55
     *<AUTHOR>
     **/
    public List<SoHeader> findBySaleOrderNoList(List<String> orderNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(SoHeader::getSaleOrderNo,orderNoList);
        return dao.find(conditionRule);
    }

    /**
     * 根据ids同步运输单号
     * @return Boolean
     */
    public Boolean syncShippingOrder(Long[] ids) {
        if(ArrayUtil.isEmpty(ids)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_OUTBOUND_ORDER_IS_EMPTY);
        }
        List<SoHeader> soHeaderList = dao.findByIds(ids);
        if(CollectionUtil.isNotEmpty(soHeaderList) && soHeaderList.size() == ids.length){
            List<String> soNoList = soHeaderList.stream().map(SoHeader::getSoNo).toList();
            Map<String, String> shipOrderNoBySoNoList = packHeaderService.getShipOrderNoBySoNoList(soNoList);
            logger.info("-------shipOrderNoBySoNoList获取成功-------\n{}", shipOrderNoBySoNoList);
            for (SoHeader soHeader : soHeaderList) {
                String shippingOrderNo = MapUtil.getStr(shipOrderNoBySoNoList, soHeader.getSoNo());
                soHeader.setShippingOrderNo(shippingOrderNo);
                preSave(soHeader);
            }
            return dao.batchUpdate(soHeaderList) > 0;
        }
        throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_OUTBOUND_ORDER_IS_EMPTY);
    }

    /**
     * 打印出库订单
     * @param id 主键
     * @return SoHeaderPrintQuery
     */
    public SoHeaderPrintQuery print(Long id) {
        SoHeader soHeader = dao.findById(id);
        if(ObjUtil.isNull(soHeader)){
            throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "出库", id);
        }
        Warehouse warehouse = warehouseDao.findById(soHeader.getWarehouseId());
        if(ObjUtil.isNull(warehouse)){
            throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "仓库", soHeader.getWarehouseId());
        }
        Owner owner = ownerDao.findById(soHeader.getOwnerId());
        if(ObjUtil.isNull(owner)){
            throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "货主", soHeader.getOwnerId());
        }
        // 获取数据字典真实数值
        ResponseData<Map<String, List<SelectItem>>> responseData = remoteDictDataService.getDictDataAll();
        if(responseData.getSuccess()){
            Map<String, List<SelectItem>> dictDataAll = responseData.getData();
            List<SelectItem> soTypeList = dictDataAll.get("so_type");
            List<SelectItem> priorityList = dictDataAll.get("priority");
            SoHeaderPrintQuery query = new SoHeaderPrintQuery();
            // 赋值出库订单信息
            query.setSoNo(ObjUtil.defaultIfNull(soHeader.getSoNo(), StrUtil.EMPTY));
            query.setWarehouseName(ObjUtil.defaultIfNull(warehouse.getWarehouseName(), StrUtil.EMPTY));
            query.setOwnerName(ObjUtil.defaultIfNull(owner.getOwnerName(), StrUtil.EMPTY));
            if(StrUtil.isNotBlank(soHeader.getSoType())){
                Optional<SelectItem> selectItem = soTypeList.stream().filter(e -> StrUtil.equals(e.getValue(), soHeader.getSoType())).findFirst();
                selectItem.ifPresent(item -> query.setSoType(item.getLabel()));
            }
            if(StrUtil.isNotBlank(soHeader.getPriority())){
                Optional<SelectItem> selectItem = priorityList.stream().filter(e -> StrUtil.equals(e.getValue(), soHeader.getPriority())).findFirst();
                selectItem.ifPresent(item -> query.setPriority(item.getLabel()));
            }
            if(StrUtil.isNotBlank(soHeader.getClientId())){
                Customer customer = customerDao.findById(Long.parseLong(soHeader.getClientId()));
                query.setClientName(ObjUtil.defaultIfNull(customer.getCustomerName(), StrUtil.EMPTY));
                query.setClientPhone(ObjUtil.defaultIfNull(customer.getTel(), StrUtil.EMPTY));
                query.setAddress(StrUtil.format("{}{}{}{}",
                        ObjUtil.defaultIfNull(customer.getProvinceName(), StrUtil.EMPTY),
                        ObjUtil.defaultIfNull(customer.getCityName(), StrUtil.EMPTY),
                        ObjUtil.defaultIfNull(customer.getCountyName(), StrUtil.EMPTY),
                        ObjUtil.defaultIfNull(customer.getAddress(), StrUtil.EMPTY)
                ));
            }
            query.setLogisticNo(ObjUtil.defaultIfNull(soHeader.getLogisticNo(), StrUtil.EMPTY));
            query.setOrderTime(soHeader.getOrderTime());
            if(ObjUtil.isNotNull(soHeader.getStartTime()) && ObjUtil.isNotNull(soHeader.getEndTime())){
                query.setStartTime(soHeader.getStartTime());
                query.setEndTime(soHeader.getEndTime());
            }
            // 赋值出库订单明细信息
            Map<Long, Sku> skuMap = skuDao.findAll().stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (k1, k2) -> k1));;
            Map<Long, Package> packageMap  = packageDao.findAll().stream().collect(Collectors.toMap(Package::getId, Function.identity(), (k1, k2) -> k1));;
            List<SoDetail> soDetailList = soDetailDao.find(new ConditionRule().andEqual("soNo", soHeader.getSoNo()));
            if(CollectionUtil.isNotEmpty(soDetailList)){
                for (SoDetail soDetail : soDetailList) {
                    SoDetailPrintQuery detailQuery = new SoDetailPrintQuery();
                    BeanUtil.copyProperties(soDetail, detailQuery);
                    Sku sku = MapUtil.get(skuMap, soDetail.getSkuId(), Sku.class);
                    if(ObjUtil.isNotNull(sku)){
                        detailQuery.setSkuCode(ObjUtil.defaultIfNull(sku.getSkuCode(), StrUtil.EMPTY));
                        detailQuery.setSkuName(ObjUtil.defaultIfNull(sku.getSkuName(), StrUtil.EMPTY));
                        detailQuery.setLotId(sku.getLotId());
                    }
                    Package pack = MapUtil.get(packageMap, soDetail.getPackageId(), Package.class);
                    if(ObjUtil.isNotNull(pack)){
                        detailQuery.setPackageName(ObjUtil.defaultIfNull(pack.getName(), StrUtil.EMPTY));
                    }
                    detailQuery.setQtySo(ObjUtil.isNotNull(soDetail.getQtySo()) && soDetail.getQtySo().compareTo(BigDecimal.ZERO) != 0 ? NumberUtil.toStr(soDetail.getQtySo().stripTrailingZeros()) + soDetail.getPackageUnitName() : StrUtil.EMPTY);
                    detailQuery.setQtySoEa(ObjUtil.isNotNull(soDetail.getQtySoEa()) && soDetail.getQtySoEa().compareTo(BigDecimal.ZERO) != 0 ? NumberUtil.toStr(soDetail.getQtySoEa().stripTrailingZeros()) + soDetail.getPackageUnitName() : StrUtil.EMPTY);
                    detailQuery.setSumWeight(ObjUtil.isNotNull(soDetail.getSumWeight()) && soDetail.getSumWeight().compareTo(BigDecimal.ZERO) != 0 ? NumberUtil.toStr(soDetail.getSumWeight().stripTrailingZeros()) + "kg" : StrUtil.EMPTY);
                    detailQuery.setSumGrossWeight(ObjUtil.isNotNull(soDetail.getSumGrossWeight()) && soDetail.getSumGrossWeight().compareTo(BigDecimal.ZERO) != 0 ? NumberUtil.toStr(soDetail.getSumGrossWeight().stripTrailingZeros()) + "kg" : StrUtil.EMPTY);
                    query.getDetailList().add(detailQuery);
                }
                query.getDetailList().forEach(e -> {
                    if(ObjUtil.isNotNull(e.getLotId())){
                        LotAttribute lotAttribute = new LotAttribute();
                        BeanUtil.copyProperties(e, lotAttribute);
                        List<LotDetailItem> lotDetailItems = lotUtil.getLotDetailItemsByLotId(e.getLotId());
                        e.setAttribute(lotUtil.lotAttributeToStr(lotDetailItems,lotAttribute));
                        if (ObjUtil.isNotNull(lotAttribute.getSupplierId())) {
                            e.setSupplierId(lotAttribute.getSupplierId());
                            e.setSupplierName(lotAttribute.getSupplierName());
                        }
                    }
                });
            }
            return query;
        } else {
            return new SoHeaderPrintQuery();
        }
    }

    public List<PackBase> findPackInfoByIds(Long[] ids) {
        return dao.findPackInfoByIds(ids);
    }

    /**
     * 前置基础校验
     * @param id
     * @return
     */
    public ValidateOrderForDateItem validateOrderForCompletion(Long id) {
        SoHeader soHeader = findById(id);
        List<SoDetail> soDetails = soDetailDao.find(new ConditionRule().andEqual(SoDetail::getSoNo, soHeader.getSoNo()));
        BigDecimal sumQtySoEa = soDetails.stream().map(SoDetail::getQtySoEa).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal sumQtyShippedEa = soDetails.stream().map(SoDetail::getQtyShippedEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (sumQtySoEa.compareTo(sumQtyShippedEa) == 0) {
            //订单已发运，确认完结订单
            return new ValidateOrderForDateItem().setType(ValidateOrderForTypeEnum.VALID_TYPE_0.getCode());
        }
        BigDecimal sumQtyAllocationEa = soDetails.stream().map(SoDetail::getQtyAllocationEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        //计算未分配数
        BigDecimal sumQtyNotAllocEa = sumQtySoEa.subtract(sumQtyAllocationEa);
        //公式 未分配数=订单总数-发运总数，订单不存在进行中的任务
        BigDecimal decimal = sumQtySoEa.subtract(sumQtyShippedEa);
        if (sumQtyNotAllocEa.compareTo(decimal) == 0) {
            //订单不存在进行中任务，确认完结订单
            return new ValidateOrderForDateItem().setType(ValidateOrderForTypeEnum.VALID_TYPE_1.getCode());
        } else {
            //生成上架任务
            return new ValidateOrderForDateItem().setType(ValidateOrderForTypeEnum.VALID_TYPE_2.getCode());
        }
    }

    /**
     * 生成出库上架任务
     * @param id
     * @return
     */
    @Transactional
    public OrderForCreatePaTaskItem orderForCreatePaTask(Long id) {
        SoHeader soHeader = findById(id);
        List<SoDetail> soDetails = soDetailDao.find(new ConditionRule().andEqual(SoDetail::getSoNo, soHeader.getSoNo()));
        BigDecimal sumQtySoEa = soDetails.stream().map(SoDetail::getQtySoEa).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal sumQtyShippedEa = soDetails.stream().map(SoDetail::getQtyShippedEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal sumQtyPickedEa = soDetails.stream().map(SoDetail::getQtyPickedEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        //订单部分发运
        //判断条件：订单预计发货数>订单已发货数，订单部分发运；
        if (sumQtySoEa.compareTo(sumQtyPickedEa) > 0 && BigDecimal.ZERO.compareTo(sumQtyShippedEa) < 0) {
            //部分发运先判断是否需要生成上架任务，当已发货数=已拣货数，不需要生成上架任务，已发货数<已拣货数，需要生成上架任务。
            if (sumQtyShippedEa.compareTo(sumQtyPickedEa) == 0) {
                //执行部分发运业务逻辑 不生成上架任务
                soHeader.setStatus(SoOrderStatusEnum.CLOSE.getCode());
                //取消订单
                OrderForCreatePaTaskItem paTaskItem = soCreatePaTaskService.executePartialShipmentServiceNotCreate(soHeader, soDetails);
                //orderCancelByIds(com.google.common.collect.Lists.newArrayList(soHeader));
                soHeader.setStatus(SoOrderStatusEnum.CLOSE.getCode());
                soHeaderDao.update(soHeader);
                return paTaskItem;
            }
            //生成上架任务业务场景
            //判断条件：订单预计发货数>订单已发货数&已发货数<已拣货数
            if (sumQtyShippedEa.compareTo(sumQtyPickedEa) < 0){
                //执行部分发运业务逻辑 生成上架任务
                OrderForCreatePaTaskItem paTaskItem = soCreatePaTaskService.executePartialShipmentServiceCreate(soHeader, soDetails);
                //orderFinishByIds(soHeader.getId());
                soHeader.setStatus(SoOrderStatusEnum.CLOSE.getCode());
                soHeaderDao.update(soHeader);
                return paTaskItem;
            }
            throw new ServiceException(SoPaTaskExceptionEnum.DATE_ERROR_NOT_IF);
        }else{
            //订单未发运
            OrderForCreatePaTaskItem paTaskItem = soCreatePaTaskService.executeAllShipmentServiceCreate(soHeader, soDetails);
            soHeader.setStatus(SoOrderStatusEnum.CLOSE.getCode());
            soHeaderDao.update(soHeader);
            return paTaskItem;
        }
    }


}
