package com.chinaservices.wms.module.so.so.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.enums.StatusEnum;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.edi.module.so.domain.InvLotLocQty;
import com.chinaservices.edi.module.so.feign.RemoteSoStatusPushToCrzFegin;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.jpa.support.rule.OrderRule;
import com.chinaservices.module.base.ModuleBaseModel;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.ActionCode;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.domain.LotAttribute;
import com.chinaservices.wms.common.domain.LotDetailItem;
import com.chinaservices.wms.common.enums.AllocationQueryScopeEnum;
import com.chinaservices.wms.common.enums.AllocationTypeEnum;
import com.chinaservices.wms.common.enums.so.*;
import com.chinaservices.wms.common.enums.process.ProcessOrderTypeEnum;
import com.chinaservices.wms.common.enums.so.SoAllocStatusEnum;
import com.chinaservices.wms.common.enums.so.SoPickingStatusEnum;
import com.chinaservices.wms.common.enums.so.WvRuleDesignEnum;
import com.chinaservices.wms.common.enums.so.WvStatusEnum;
import com.chinaservices.wms.common.enums.stock.ExpiryWarningSaveStageEnum;
import com.chinaservices.wms.common.exception.SoExcepitonEnum;
import com.chinaservices.wms.common.properties.ExecutorThreadProperties;
import com.chinaservices.wms.common.util.LotUtil;
import com.chinaservices.wms.module.asn.pa.model.CsPaTaskAssociation;
import com.chinaservices.wms.module.asn.pa.service.CsPaTaskAssociationService;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.basic.lot.dao.WarehouseLotDetailDao;
import com.chinaservices.wms.module.basic.lot.model.WarehouseLotDetail;
import com.chinaservices.wms.module.basic.lot.model.WarehouseLotHeader;
import com.chinaservices.wms.module.basic.lot.service.WarehouseLotDetailService;
import com.chinaservices.wms.module.basic.lot.service.WarehouseLotHeaderService;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.basic.pallet.model.Pallet;
import com.chinaservices.wms.module.basic.pallet.service.PalletService;
import com.chinaservices.wms.module.basic.sku.domain.SkuDetailQuery;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.process.order.dao.ProcessOrderDao;
import com.chinaservices.wms.module.process.order.dao.ProcessTaskDao;
import com.chinaservices.wms.module.process.order.dao.ProcessTaskDetailsDao;
import com.chinaservices.wms.module.process.order.model.ProcessOrder;
import com.chinaservices.wms.module.process.order.model.ProcessTask;
import com.chinaservices.wms.module.process.order.model.ProcessTaskDetails;
import com.chinaservices.wms.module.rule.alloc.model.RuleAllocDetail;
import com.chinaservices.wms.module.rule.alloc.model.RuleAllocHeader;
import com.chinaservices.wms.module.rule.alloc.service.RuleAllocDetailService;
import com.chinaservices.wms.module.rule.alloc.service.RuleAllocHeaderService;
import com.chinaservices.wms.module.rule.rotation.model.RuleRotationDetail;
import com.chinaservices.wms.module.rule.rotation.model.RuleRotationHeader;
import com.chinaservices.wms.module.rule.rotation.service.RuleRotationDetailService;
import com.chinaservices.wms.module.rule.rotation.service.RuleRotationHeaderService;
import com.chinaservices.wms.module.rule.wv.model.CsRuleWv;
import com.chinaservices.wms.module.rule.wv.model.CsRuleWvGroup;
import com.chinaservices.wms.module.rule.wv.service.CsRuleWvGroupService;
import com.chinaservices.wms.module.rule.wv.service.CsRuleWvService;
import com.chinaservices.wms.module.so.picking.domain.PickingDetail;
import com.chinaservices.wms.module.so.picking.domain.SoPickingItem;
import com.chinaservices.wms.module.so.picking.model.SoPicking;
import com.chinaservices.wms.module.so.so.dao.SoAllocationDao;
import com.chinaservices.wms.module.so.so.domain.*;
import com.chinaservices.wms.module.so.so.model.SoAllocation;
import com.chinaservices.wms.module.so.so.model.SoDetail;
import com.chinaservices.wms.module.so.so.model.SoHeader;
import com.chinaservices.wms.module.so.wv.domain.WvPickingDetail;
import com.chinaservices.wms.module.so.wv.domain.WvRuleCodeEnum;
import com.chinaservices.wms.module.so.wv.model.WvDetail;
import com.chinaservices.wms.module.so.wv.model.WvHeader;
import com.chinaservices.wms.module.so.wv.service.WvDetailService;
import com.chinaservices.wms.module.so.wv.service.WvHeaderService;
import com.chinaservices.wms.module.so.wv.service.WvRuleDesignService;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.expirywarning.service.ExpiryWarningService;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageQuery;
import com.chinaservices.wms.module.stock.loc.enums.LotSearchTypeEnum;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分配相关功能业务类
 * <AUTHOR>
 * @date 2025/1/15 15:21
 * @Description 分配相关功能业务类
 */
@Service
@Slf4j
public class SoAllocationService extends ModuleBaseServiceSupport<SoAllocationDao, SoAllocation, Long> {

    @Autowired
    private WvHeaderService wvHeaderService;
    @Autowired
    private CsRuleWvService csRuleWvService;

    @Autowired
    private WvDetailService wvDetailService;
    @Autowired
    private com.chinaservices.wms.module.so.picking.service.SoPickingService soPickingService;
    @Autowired
    private SoAllocationPickingService sopickingServiceAllocation;

    @Autowired
    private SoDetailService soDetailService;
    @Autowired
    private PackageUnitService packageUnitService;
    @Autowired
    private SkuService skuService;
    @Autowired
    private SoAllocationDao soAllocationDao;
    @Autowired
    private WarehouseLocService warehouseLocService;
    @Autowired
    private SoHeaderService soHeaderService;
    @Autowired
    private WarehouseLotHeaderService warehouseLotHeaderService;
    @Autowired
    private WarehouseLotDetailService warehouseLotDetailService;
    @Autowired
    private NumberGenerator numberGenerator;
    @Autowired
    private StockService stockService;
    @Autowired
    private PalletService palletService;
    @Autowired
    private LotUtil lotUtil;
    @Autowired
    private RuleRotationDetailService ruleRotationDetailService;
    @Autowired
    private CsRuleWvGroupService csRuleWvGroupService;
    @Autowired
    private RuleAllocDetailService ruleAllocDetailService;
    @Autowired
    private AutoAllocationService autoAllocationService;
    @Autowired
    private RuleAllocHeaderService ruleAllocHeaderService;

    @Autowired
    private RuleRotationHeaderService ruleRotationHeaderService;

    @Autowired
    private WarehouseLotDetailDao warehouseLotDetailDao;
    @Autowired
    private RemoteSoStatusPushToCrzFegin remoteSoStatusPushToCrzFegin;
    @Autowired
    @Qualifier(value = ExecutorThreadProperties.VIRTUAL_THREAD_EXECUTOR)
    private ExecutorService virtualThreadExecutor;
    @Autowired
    private OwnerService ownerService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private ExpiryWarningService expiryWarningService;
    @Autowired
    private CsPaTaskAssociationService csPaTaskAssociationService;
    @Autowired
    private ProcessTaskDetailsDao processTaskDetailsDao;
    @Autowired
    private ProcessOrderDao processOrderDao;
    @Autowired
    private ProcessTaskDao processTaskDao;

    /**
     * 获取左侧商品列表数据
     * @param condition
     * @return
     */
    public List<GetLeftGoodsListQuery> getLeftGoodsList(GetLeftGoodsListCondition condition) {
        AllocationTypeEnum allocationEnum = AllocationTypeEnum.getByCode(condition.getAllocationType());
        if (ObjUtil.isNull(allocationEnum)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        }
        return switch (allocationEnum) {
            case OUTBOUND -> outboundGetLeftGoodsList(condition);
            case WAVE_TIMES -> waveTimesGetLeftGoodsList(condition);
            case PROCES_ALLOC -> procesGetLeftGoodsList(condition);
            case CFD_ALLOC -> cfdGetLeftGoodsList(condition);
            default -> throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        };
    }

    /**
     * 分配页面-获取右侧拣货单列表
     * @param condition
     * @return
     */
    public List<GetRightPickingListQuery> getRightPickingList(GetRightPickingListCondition condition) {
        AllocationTypeEnum allocationEnum = AllocationTypeEnum.getByCode(condition.getAllocationType());
        if (ObjUtil.isNull(allocationEnum)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        }
        return switch (allocationEnum) {
            case OUTBOUND -> outboundGetRightPickingList(condition);
            case WAVE_TIMES -> waveTimesGetRightPickingList(condition.getWvNo());
            case PROCES_ALLOC -> procesGetRightPickingList(condition);
            case CFD_ALLOC -> cfdGetRightPickingList(condition);
            default -> throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        };
    }

    /**
     * 加工单右侧拣货任务列表明细查询
     * @param condition
     * @return
     */
    private List<GetRightPickingListQuery> procesGetRightPickingList(GetRightPickingListCondition condition) {
        List<GetRightPickingListQuery> queries = cfdGetRightPickingList(condition);
        queries.forEach(query -> {
            query.setAllocationType(AllocationTypeEnum.PROCES_ALLOC.getCode());
        });
        return queries;
    }

    /**
     * 拆分单右侧拣货任务列表明细查询
     * @param condition
     * @return
     */
    private List<GetRightPickingListQuery> cfdGetRightPickingList(GetRightPickingListCondition condition) {
        String processOrderNo = condition.getProcessOrderNo();
        if (ObjectUtil.isEmpty(processOrderNo)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_PROCES_NO_NOT_EMPTY);
        }
        List<SoPicking> pickingList = soPickingService.find(new ConditionRule().andEqual(SoPicking::getProcessOrderNo, processOrderNo),
                new OrderRule().addAscOrder("create_time"));
        if (ObjUtil.isNull(pickingList)){
            return new ArrayList<>();
        }
        List<String> pickingNoList = pickingList.stream().map(SoPicking::getPickingNo).toList();
        List<SoAllocation> allocation = sopickingServiceAllocation.find(new ConditionRule().andIn(SoAllocation::getPickingNo,pickingNoList));
        Map<String,List<SoAllocation>> groupSoAllocationMap = CollUtil.isEmpty(allocation) ? new HashMap<>() :
                allocation.stream().collect(Collectors.groupingBy(SoAllocation::getPickingNo));
        List<GetRightPickingListQuery> list = new ArrayList<>();
        pickingList.forEach(item -> {
            GetRightPickingListQuery query = new GetRightPickingListQuery();
            query.setPickingNo(item.getPickingNo());
            query.setAllocationType(AllocationTypeEnum.CFD_ALLOC.getCode());
            query.setId(item.getId());
            List<SoAllocation> procesList = groupSoAllocationMap.get(item.getPickingNo());
            List<PickingDetail> detail = new ArrayList<>();
            if (CollUtil.isNotEmpty(procesList)){
                for (SoAllocation soAllocation : procesList) {
                    PickingDetail pickingDetail = new PickingDetail();
                    BeanUtil.copyProperties(soAllocation,pickingDetail);
                    if (!com.chinaservices.wms.common.enums.so.SoPickingStatusEnum.NOT_GENERATE.getCode().equals(soAllocation.getPickingStatus())) {
                        pickingDetail.setCancelAllocBtn(Boolean.FALSE);
                    }
                    detail.add(pickingDetail);
                }
            }
            query.setProcessOrderList(detail);
            list.add(query);
        });
        return list;
    }

    /**
     * 出库单右侧拣货任务列表明细查询
     * @param condition
     * @return
     */
    private List<GetRightPickingListQuery> outboundGetRightPickingList(GetRightPickingListCondition condition){
        String soNo = condition.getSoNo();
        if (ObjectUtil.isEmpty(soNo)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_OUTBOUND_ORDER_IS_EMPTY);
        }

        String queryScope = condition.getQueryScope();
        if (StrUtil.isBlank(queryScope)){
            queryScope = AllocationQueryScopeEnum.OUTBOUND.getCode();
        }


        List<SoDetail> soDetailList = soDetailService.findBySoNo(soNo);
        Set<Long> soDetailIdSet = soDetailList.stream().map(SoDetail::getId).collect(Collectors.toSet());

        List<SoPicking> soPickingList = soPickingService.findBySoNo(soNo);

        if (AllocationQueryScopeEnum.OUTBOUND_WAVE.getCode().equals(queryScope)){
            List<WvDetail> wvDetailList = wvDetailService.findBySoDetailids(soDetailIdSet);
            if (CollUtil.isNotEmpty(wvDetailList)){
                Set<String> wvNoSet = wvDetailList.stream().map(WvDetail::getWvNo).collect(Collectors.toSet());
                List<SoPicking> soPickingByWvNoList = soPickingService.findByWvNos(wvNoSet);
                soPickingList.addAll(soPickingByWvNoList);
            }
        }

        if (CollUtil.isEmpty(soPickingList)){
            return new ArrayList<>();
        }
        List<String> pickingNoList = soPickingList.stream().map(SoPicking::getPickingNo).collect(Collectors.toList());
        List<SoAllocation> soAllocationList = this.findByPickingNos(pickingNoList);
        if (CollUtil.isEmpty(soAllocationList)){
            return new ArrayList<>();
        }

        soAllocationList = soAllocationList.stream().filter(item -> soDetailIdSet.contains(item.getSoDetailId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(soAllocationList)){
            return new ArrayList<>();
        }
        Set<Long> skuIdSet = soAllocationList.stream().map(SoAllocation::getSkuId).collect(Collectors.toSet());
        List<Sku> skuList = skuService.findByIds(ArrayUtil.toArray(skuIdSet, Long.class));
        Map<Long,Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Function.identity()));
        Map<String, List<SoAllocation>> pickingNoToSoAllocationListMap = new HashMap<>();
        if (CollUtil.isNotEmpty(soAllocationList)){
            pickingNoToSoAllocationListMap = soAllocationList.stream().collect(Collectors.groupingBy(SoAllocation::getPickingNo));
        }

        Set<Long> locIdSet = soAllocationList.stream().map(SoAllocation::getLocId).collect(Collectors.toSet());
        List<WarehouseLoc> warehouseLocList = warehouseLocService.findByIds(ArrayUtil.toArray(locIdSet, Long.class));
        Map<Long, WarehouseLoc> idToWarehouseLocMap = new HashMap<>();
        if (CollUtil.isNotEmpty(warehouseLocList)){
            idToWarehouseLocMap = warehouseLocList.stream().collect(Collectors.toMap(WarehouseLoc::getId, Function.identity()));
        }
        List<GetRightPickingListQuery> getRightPickingListQueryList = new ArrayList<>();
        List<String> list = soAllocationList.stream().map(SoAllocation::getPickingTaskNo).toList();
        List<CsPaTaskAssociation> associations = csPaTaskAssociationService.find(new ConditionRule().andIn(CsPaTaskAssociation::getPickingTaskNo, list));
        Map<String, CsPaTaskAssociation> associationMap = associations.stream().collect(Collectors.toMap(CsPaTaskAssociation::getPickingTaskNo, Function.identity(), (a, b) -> a));
        for (SoPicking soPicking : soPickingList) {
            GetRightPickingListQuery getRightPickingListQuery = new GetRightPickingListQuery();
            getRightPickingListQuery.setPickingNo(soPicking.getPickingNo());
            getRightPickingListQuery.setId(soPicking.getId());
            if (!com.chinaservices.wms.common.enums.so.SoPickingStatusEnum.NOT_GENERATE.getCode().equals(soPicking.getStatus())){
                getRightPickingListQuery.setCreatePickingBtn(false);
            }

            List<SoAllocation> soAllocationSubList = pickingNoToSoAllocationListMap.get(soPicking.getPickingNo());
            if (CollUtil.isEmpty(soAllocationSubList)) {
                continue;
            }
            List<PickingDetail> pickingDetailList = new ArrayList<>();
            for (SoAllocation soAllocation : soAllocationSubList) {
                PickingDetail pickingDetail = new PickingDetail();
                BeanUtil.copyProperties(soAllocation, pickingDetail);
                Sku sku = skuMap.get(soAllocation.getSkuId());
                if (ObjectUtil.isNotNull(sku)){
                    pickingDetail.setSkuCode(sku.getSkuCode());
                    pickingDetail.setSkuName(sku.getSkuName());
                }
                WarehouseLoc warehouseLoc = idToWarehouseLocMap.get(soAllocation.getLocId());
                if (ObjectUtil.isNotNull(warehouseLoc)){
                    pickingDetail.setLocName(warehouseLoc.getLocName());
                }
                CsPaTaskAssociation csPaTaskAssociation = associationMap.get(soAllocation.getPickingTaskNo());
                if (ObjUtil.isNotNull(csPaTaskAssociation)){
                    pickingDetail.setPaNo(csPaTaskAssociation.getPaNo());
                }
                pickingDetail.setTaskStatus(soAllocation.getTaskStatus());
                pickingDetail.setStatus(soAllocation.getPickingStatus());
                pickingDetail.setPickingEa(soAllocation.getPickingEa());
                pickingDetail.setPackageUnitNameEa(soAllocation.getPackageUnitName());
                if (!com.chinaservices.wms.common.enums.so.SoPickingStatusEnum.NOT_GENERATE.getCode().equals(pickingDetail.getStatus())){
                    pickingDetail.setCancelAllocBtn(false);
                }
                pickingDetailList.add(pickingDetail);
            }
            getRightPickingListQuery.setOutboundDetailList(pickingDetailList);
            getRightPickingListQueryList.add(getRightPickingListQuery);
        }
        return getRightPickingListQueryList;
    }

    public List<SoAllocation> findByPickingNos(List<String> pickingNoList){
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(SoPicking::getPickingNo, pickingNoList);
        return this.find(conditionRule);
    }

    /**
     * 波次分配-获取右侧拣货单列表
     * @param wvNo 波次号
     * @return 数据结果列表
     * @see GetRightPickingListQuery 结果参照类
     */
    private List<GetRightPickingListQuery> waveTimesGetRightPickingList(String wvNo) {
        List<SoPicking> pickingList = soPickingService.find(new ConditionRule().andEqual(SoPicking::getWvNo, wvNo),
                new OrderRule().addAscOrder("create_time"));
        if (ObjUtil.isNull(pickingList)){
            return new ArrayList<>();
        }
        List<GetRightPickingListQuery> list = new ArrayList<>();
        pickingList.forEach(item -> {
            GetRightPickingListQuery query = new GetRightPickingListQuery();
            query.setPickingNo(item.getPickingNo());
            query.setId(item.getId());
            List<WvPickingDetail> wvDetailList = getWvDetailList(item);
            if (CollUtil.isNotEmpty(wvDetailList)){
                WvPickingDetail first = wvDetailList.getFirst();
                if (CollUtil.isNotEmpty(first.getDetailList())){
                    PickingDetail pickingDetail = first.getDetailList().getFirst();
                    if (!com.chinaservices.wms.common.enums.so.SoPickingStatusEnum.NOT_GENERATE.getCode().equals(pickingDetail.getStatus())){
                        query.setCreatePickingBtn(Boolean.FALSE);
                    }
                }
            }
            query.setWvDetailList(wvDetailList);
            list.add(query);
        });
        return list;
    }

    /**
     * 加工分配-获取左侧商品列表数据
     * @param condition 条件对象
     * @return 数据结果列表
     * @see GetLeftGoodsListQuery 结果参照类
     */
    private List<GetLeftGoodsListQuery> procesGetLeftGoodsList(GetLeftGoodsListCondition condition) {
        if (StrUtil.isEmpty(condition.getProcessOrderNo())){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_PROCES_NO_NOT_EMPTY);
        }
        List<GetLeftGoodsListQuery> detailsList = processTaskDetailsDao.getAllocLeftGoodsListQuery(condition.getKeyword());
        if (CollUtil.isEmpty(detailsList)){
            return new ArrayList<>();
        }
        return detailsList;
    }

    /**
     * 拆分分配-获取左侧商品列表数据
     * @param condition 条件对象
     * @return 数据结果列表
     * @see GetLeftGoodsListQuery 结果参照类
     */
    private List<GetLeftGoodsListQuery> cfdGetLeftGoodsList(GetLeftGoodsListCondition condition) {
        if (StrUtil.isEmpty(condition.getProcessOrderNo())){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_PROCES_NO_NOT_EMPTY);
        }
        List<GetLeftGoodsListQuery> detailsList = processTaskDetailsDao.getAllocLeftGoodsListByCfdQuery(condition.getKeyword());
        if (CollUtil.isEmpty(detailsList)){
            return new ArrayList<>();
        }
        return detailsList;
    }

    /**
     * 波次分配-获取左侧商品列表数据
     * @param  condition
     * @return 数据结果列表
     * @see GetLeftGoodsListQuery 结果参照类
     */
    private List<GetLeftGoodsListQuery> waveTimesGetLeftGoodsList(GetLeftGoodsListCondition condition) {
        if (StrUtil.isEmpty(condition.getWvNo())){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_WV_NO_NOT_EMPTY);
        }
        //查询出波次规则和改规则绑定的数据值
        WvHeader wvHeader = wvHeaderService.findFirst(new ConditionRule().andEqual(WvHeader::getWvNo, condition.getWvNo()));
        CsRuleWv ruleInfo = csRuleWvService.findById(wvHeader.getWvRuleId());
        CsRuleWvGroup ruleWvGroup = csRuleWvGroupService.findFirst(new ConditionRule().andEqual(CsRuleWvGroup::getRuleWvId, ruleInfo.getId()));
        if (ObjUtil.isNull(ruleWvGroup)){
            WvRuleDesignService service = WvRuleDesignEnum.execute(WvRuleCodeEnum.DEFAULT);
            return service.getSkuList(wvHeader, ruleInfo,condition);
        }else{
            WvRuleCodeEnum designEnum = EnumUtil.getBy(WvRuleCodeEnum::getCode, ruleWvGroup.getConditionCode(), WvRuleCodeEnum.DEFAULT);
            WvRuleDesignService service = WvRuleDesignEnum.execute(designEnum);
            return service.getSkuList(wvHeader, ruleInfo,condition);
        }
    }

    /**
     * 出库分配-获取左侧商品列表数据
     * @return 数据结果列表
     * @see GetLeftGoodsListQuery 结果参照类
     */
    private List<GetLeftGoodsListQuery> outboundGetLeftGoodsList(GetLeftGoodsListCondition condition) {
        if (StrUtil.isBlank(condition.getSoNo())){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_INBOUND_NO_CANNOT_BE_EMPTY);
        }

        List<GetLeftGoodsListQuery> getLeftGoodsListQueryList = soAllocationDao.outboundGetLeftGoodsList(condition);
        if (CollUtil.isEmpty(getLeftGoodsListQueryList)){
            return new ArrayList<>();
        }

        Set<Long> soDetailIdSet = getLeftGoodsListQueryList.stream().map(GetLeftGoodsListQuery::getSoDetailId).collect(Collectors.toSet());
        List<WvDetail> wvDetailList = wvDetailService.findBySoDetailids(soDetailIdSet);

        Map<Long, List<WvDetail>> soDetailIdToWvDetailsMap = new HashMap<>();
        Map<Long, WvHeader> wvHeaderMap = new HashMap<>();

        if (CollUtil.isNotEmpty(wvDetailList)) {
            soDetailIdToWvDetailsMap = wvDetailList.stream().collect(Collectors.groupingBy(WvDetail::getSoDetailId));
            Set<Long> wvIdSet = wvDetailList.stream().map(WvDetail::getWvId).collect(Collectors.toSet());
            List<WvHeader> wvHeaderList = wvHeaderService.findByIds(ArrayUtil.toArray(wvIdSet, Long.class));
            wvHeaderMap = wvHeaderList.stream().collect(Collectors.toMap(WvHeader::getId, Function.identity()));
        }

        for (GetLeftGoodsListQuery getLeftGoodsListQuery : getLeftGoodsListQueryList) {
            BigDecimal unAllocQty = getLeftGoodsListQuery.getUnAllocQty();
            BigDecimal qtySoEa = getLeftGoodsListQuery.getQtySoEa();
            String status = null;
            if (unAllocQty.compareTo(qtySoEa) == 0){
                status = SoAllocStatusEnum.UN_ALLOC.getCode();
            } else if (unAllocQty.compareTo(qtySoEa) < 0 && unAllocQty.compareTo(BigDecimal.ZERO) > 0) {
                status = SoAllocStatusEnum.PARTIAL.getCode();
            } else if (unAllocQty.compareTo(BigDecimal.ZERO) == 0) {
                getLeftGoodsListQuery.setAllocBtn(false);
                status = SoAllocStatusEnum.FULL.getCode();
            }
            getLeftGoodsListQuery.setAllocStatus(status);

            List<WvDetail> wvDetailNewList = soDetailIdToWvDetailsMap.get(getLeftGoodsListQuery.getSoDetailId());
            if (CollUtil.isEmpty(wvDetailNewList)) {
                continue;
            }
            for (WvDetail wvDetail : wvDetailNewList) {
                WvHeader wvHeader = wvHeaderMap.get(wvDetail.getWvId());
                if (ObjectUtil.isEmpty(wvHeader)) {
                    continue;
                }
                if (!WvStatusEnum.WV_CANCEL.getCode().equals(wvHeader.getStatus())){
                    getLeftGoodsListQuery.setAllocBtn(false);
                }
            }
        }
        return getLeftGoodsListQueryList;
    }

    /**
     * 根据拣货单号搜索拣货单详情
     * @param picking
     * @return
     */
    private List<WvPickingDetail> getWvDetailList(SoPicking picking) {
        SoAllocationCondition condition = new SoAllocationCondition();
        condition.setPickingNo(picking.getPickingNo());
        List<SoAllocation> allocation = sopickingServiceAllocation.findSoAllocationByCondition(condition);
        if (CollUtil.isEmpty(allocation)){
            SoPicking first = soPickingService.findFirst(new ConditionRule().andEqual(SoPicking::getPickingNo, picking.getPickingNo()));
            if (ObjUtil.isNull(first)){
                log.error("拣货单号：{}，拣货单详情为空 SoPicking:{}",picking.getPickingNo(), JSONUtil.toJsonStr(picking));
                throw new ServiceException(SoExcepitonEnum.ALLOCATION_SO_ALLOCATION_NOT_EXIST);
            }else{
                return Lists.newArrayList();
            }
        }
        Map<String, List<SoAllocation>> detailGroup = allocation.stream().filter(item -> StrUtil.isNotEmpty(item.getPkWvNo())).collect(Collectors.groupingBy(SoAllocation::getPkWvNo));
        List<WvPickingDetail> rest = new ArrayList<>();
        detailGroup.keySet().forEach(pkWvNo -> {
            WvPickingDetail detail = new WvPickingDetail();
            detail.setWvNo(pkWvNo);
            List<SoAllocation> list = detailGroup.get(pkWvNo);
            List<PickingDetail> pickingDetailList = new ArrayList<>();
            //获取包装ID集合
            Set<Long> ids = list.stream().map(SoAllocation::getPackageId).collect(Collectors.toSet());
            List<PackageUnit> unitList = packageUnitService.find(new ConditionRule().andIn(PackageUnit::getPackageId, ArrayUtil.toArray(ids, Long.class))
                    .andEqual(PackageUnit::getIsDefault, YesNoEnum.YES.getCode()));
            Map<Long, String> unitMap = unitList.stream().collect(Collectors.toMap(PackageUnit::getId, PackageUnit::getName, (v1, v2) -> v1));
            //获取SKU信息
            Set<Long> skuIds = list.stream().map(SoAllocation::getSkuId).collect(Collectors.toSet());
            List<Sku> skuList = skuService.findByIds(ArrayUtil.toArray(skuIds, Long.class));
            Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (v1, v2) -> v1));
            //获取库位信息
            Set<Long> locIds = list.stream().map(SoAllocation::getLocId).collect(Collectors.toSet());
            List<WarehouseLoc> locList = warehouseLocService.findByIds(ArrayUtil.toArray(locIds, Long.class));
            Map<Long, WarehouseLoc> locMap = locList.stream().collect(Collectors.toMap(WarehouseLoc::getId, Function.identity(), (v1, v2) -> v1));

            list.forEach(item -> {
                PickingDetail pickingDetail = new PickingDetail();
                SoDetail soDetail = soDetailService.findFirst(new ConditionRule().andEqual(SoDetail::getSoDetailNo, item.getSoDetailNo()));
                pickingDetail.setSoNo(soDetail.getSoNo());
                pickingDetail.setId(item.getId());
                pickingDetail.setPickingNo(item.getPickingNo());
                pickingDetail.setStatus(item.getPickingStatus());
                if (!com.chinaservices.wms.common.enums.so.SoPickingStatusEnum.NOT_GENERATE.getCode().equals(item.getPickingStatus())) {
                    pickingDetail.setCancelAllocBtn(Boolean.FALSE);
                }
                pickingDetail.setQtyPack(item.getQtyPack());
                pickingDetail.setPackageUnitName(item.getPackageUnitName());
                pickingDetail.setPackageUnitNameEa(unitMap.get(item.getPackageUnitId()));
                pickingDetail.setQtyEa(item.getQtyEa());
                Sku sku = skuMap.get(item.getSkuId());
                pickingDetail.setSkuName(sku.getSkuName());
                pickingDetail.setSkuId(sku.getId());
                pickingDetail.setSkuCode(sku.getSkuCode());
                WarehouseLoc warehouseLoc = locMap.get(item.getLocId());
                pickingDetail.setLocName(warehouseLoc.getLocCode());
                pickingDetail.setLocId(warehouseLoc.getId());
                pickingDetail.setLotNum(item.getLotNum());
                pickingDetail.setPickingTaskNo(item.getPickingTaskNo());
                pickingDetailList.add(pickingDetail);
            });
            detail.setDetailList(pickingDetailList);
            rest.add(detail);
        });
        return rest;
    }

    /**
     * 拣货分配-基础信息（商品信息、规则信息、周转信息）
     * @param condition
     * @return
     */
    public SkuInfoQuery pickingBasics(PickingBasicsCondition condition){
        AllocationTypeEnum allocationEnum = AllocationTypeEnum.getByCode(condition.getAllocationType());
        if (ObjUtil.isNull(allocationEnum)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        }
        return switch (allocationEnum) {
            case OUTBOUND -> outboundPickingBasics(condition);
            case WAVE_TIMES -> waveTimesPickingBasics(condition);
            case PROCES_ALLOC -> procesPickingBasics(condition);
            case CFD_ALLOC -> cfdPickingBasics(condition);
            default -> throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        };
    }

    /**
     * 加工单基础信息（商品信息、规则信息、周转信息）
     * @param condition
     * @return
     */
    private SkuInfoQuery procesPickingBasics(PickingBasicsCondition condition) {
        PickingBasicsCondition.ProcessTaskDetail processTaskDetail = condition.getProcessTaskDetail();
        ProcessTask processTask = processTaskDao.findFirst(new ConditionRule().andEqual(ProcessTask::getProcessTaskNo,processTaskDetail.getProcessTaskNo()));
        if (ObjectUtil.isEmpty(processTask)){
            return new SkuInfoQuery();
        }
        Long skuId = condition.getSkuId();
        Sku sku = skuService.findById(skuId);
        if (ObjectUtil.isEmpty(sku)){
            return new SkuInfoQuery();
        }
        SkuInfoQuery skuInfoQuery = new SkuInfoQuery();
        skuInfoQuery.setSkuId(sku.getId());
        skuInfoQuery.setSkuCode(sku.getSkuCode());
        skuInfoQuery.setSkuName(sku.getSkuName());
        skuInfoQuery.setQtySoEa(processTask.getProcessQuantityEa());
        skuInfoQuery.setAllocationType(AllocationTypeEnum.PROCES_ALLOC.getCode());
        skuInfoQuery.setProcessOrderNo(processTask.getProcessOrderNo());
        skuInfoQuery.setProcessTaskNo(processTask.getProcessTaskNo());

        //分配规则
        getAllocationAllRule(sku, skuInfoQuery);

        return skuInfoQuery;
    }

    /**
     * 拆分单基础信息（商品信息、规则信息、周转信息）
     * @param condition
     * @return
     */
    private SkuInfoQuery cfdPickingBasics(PickingBasicsCondition condition) {
        PickingBasicsCondition.ProcessTaskDetail processTaskDetail = condition.getProcessTaskDetail();
        ProcessTaskDetails taskDetails = processTaskDetailsDao.findById(processTaskDetail.getProcessTaskDetailsId());
        if (ObjectUtil.isEmpty(taskDetails)){
            return new SkuInfoQuery();
        }
        Long skuId = condition.getSkuId();
        Sku sku = skuService.findById(skuId);
        if (ObjectUtil.isEmpty(sku)){
            return new SkuInfoQuery();
        }
        SkuInfoQuery skuInfoQuery = new SkuInfoQuery();
        skuInfoQuery.setSkuId(sku.getId());
        skuInfoQuery.setSkuCode(sku.getSkuCode());
        skuInfoQuery.setSkuName(sku.getSkuName());
        skuInfoQuery.setQtySoEa(taskDetails.getDetailsProcessQuantityEa());
        skuInfoQuery.setAllocationType(AllocationTypeEnum.CFD_ALLOC.getCode());
        skuInfoQuery.setProcessOrderNo(taskDetails.getProcessOrderNo());
        skuInfoQuery.setProcessTaskDetailsId(taskDetails.getId());

        //分配规则
        getAllocationAllRule(sku, skuInfoQuery);

        return skuInfoQuery;
    }

    private void getAllocationAllRule(Sku sku, SkuInfoQuery skuInfoQuery) {
        SkuInfoQuery.RuleInfo allocRole = new SkuInfoQuery.RuleInfo();
        allocRole.setId(sku.getAllocRuleId());
        allocRole.setRuleName(sku.getAllocRuleName());

        skuInfoQuery.setAllocRole(allocRole);

        //周转规则
        SkuInfoQuery.RuleInfo rotationRole = new SkuInfoQuery.RuleInfo();
        rotationRole.setId(sku.getRotationRuleId());
        rotationRole.setRuleName(sku.getRotationRuleName());

        skuInfoQuery.setRotationRole(rotationRole);
    }

    public SkuInfoQuery outboundPickingBasics(PickingBasicsCondition condition){
        String soDetailNo = condition.getOutboundDetail().getSoDetailNo();
        SoDetail soDetail = soDetailService.queryBySoDetailNo(soDetailNo);
        if (ObjectUtil.isEmpty(soDetail)){
            return new SkuInfoQuery();
        }
        Long skuId = condition.getSkuId();
        Sku sku = skuService.findById(skuId);
        if (ObjectUtil.isEmpty(sku)){
            return new SkuInfoQuery();
        }
        SkuInfoQuery skuInfoQuery = new SkuInfoQuery();
        skuInfoQuery.setSkuId(sku.getId());
        skuInfoQuery.setSkuCode(sku.getSkuCode());
        skuInfoQuery.setSkuName(sku.getSkuName());
        skuInfoQuery.setQtySoEa(soDetail.getQtySoEa());
        skuInfoQuery.setSoDetailNo(soDetail.getSoDetailNo());

        //分配规则
        getAllocationAllRule(sku, skuInfoQuery);

        return skuInfoQuery;
    }

    /**
     * 波次-拣货分配-基础信息
     * @param pickingBasicsCondition
     * @return
     */
    private SkuInfoQuery waveTimesPickingBasics(PickingBasicsCondition pickingBasicsCondition) {
        //查询出波次规则和改规则绑定的数据值
        WvHeader wvHeader = wvHeaderService.findFirst(new ConditionRule().andEqual(WvHeader::getWvNo, pickingBasicsCondition.getWaveTimesDetail().getWvNo()));
        CsRuleWv ruleInfo = csRuleWvService.findById(wvHeader.getWvRuleId());

        if (ObjUtil.isNull(ruleInfo)){
            WvRuleDesignService service = WvRuleDesignEnum.execute(WvRuleCodeEnum.DEFAULT);
            return service.getPickingBasics(wvHeader, ruleInfo,pickingBasicsCondition);
        }else{
            WvRuleCodeEnum designEnum = EnumUtil.getBy(WvRuleCodeEnum::getCode, ruleInfo.getRuleCode(), WvRuleCodeEnum.DEFAULT);
            WvRuleDesignService service = WvRuleDesignEnum.execute(designEnum);
            return service.getPickingBasics(wvHeader, ruleInfo,pickingBasicsCondition);
        }
    }

    public PageResult<AssignLocQuery> assignLocPage(PickingAssignLocPageCondition condition){
        LocListPageCondition pageCondition = new LocListPageCondition();
        SoDetail detail = soDetailService.findFirst(new ConditionRule().andEqual(SoDetail::getSoDetailNo, condition.getSoDetailNo()));
        SoHeader soHeader = soHeaderService.findFirst(new ConditionRule().andEqual(SoHeader::getSoNo, detail.getSoNo()));
        pageCondition.setSkuIds(Collections.singletonList(detail.getSkuId()));
        pageCondition.setLotSearchType(EnumUtil.getBy(LotSearchTypeEnum::getCode, StrUtil.isEmpty(condition.getType()) ? 1 : Convert.toInt(condition.getType()), LotSearchTypeEnum.LOC_NO));
        pageCondition.setKeyword(condition.getKeyWord());
        pageCondition.setTransactionType(TransactionType.TRAN_INA);
        pageCondition.setAttMap(this.getSoDetailAttMap(detail));
        pageCondition.setHideLocList(Convert.toStrArray(condition.getHideLocList()));
        pageCondition.setWarehouseId(soHeader.getWarehouseId());
        BeanUtil.copyProperties(condition,pageCondition);
        PageResult<LocListPageQuery> result = stockService.exec(pageCondition);
        PageResult<AssignLocQuery> convertRest = new PageResult<>();
        convertRest.setCount(result.getCount());
        convertRest.setLastPage(result.isLastPage());
        List<AssignLocQuery> convert = new ArrayList<>();
        if (CollUtil.isNotEmpty(result.getRows())){
            result.getRows().forEach(item -> {
                AssignLocQuery assignLocQuery = new AssignLocQuery();
                BeanUtil.copyProperties(item, assignLocQuery);
                LotAttribute attribute = new LotAttribute();
                BeanUtil.copyProperties(item, attribute);
                List<LotDetailItem> lotDetailItems = lotUtil.getLotDetailItemsByLotId(item.getLotId());
                String lotAtt = lotUtil.lotAttributeToStr(lotDetailItems,attribute);
                assignLocQuery.setLotAtt(lotAtt);
                assignLocQuery.setLocName(item.getLocNum());
                if (item.getInvAvailableNum() != null && item.getInvAvailableNum() > 0){
                    assignLocQuery.setQtyAvailableNumber(Convert.toBigDecimal(item.getInvAvailableNum()));
                }else{
                    assignLocQuery.setQtyAvailableNumber(BigDecimal.ZERO);
                }
                assignLocQuery.setPalletNo(item.getPalletNum());
                assignLocQuery.setWareHouseId(item.getWarehouseId());
                assignLocQuery.setWareHouseName(item.getWarehouseName());
                convert.add(assignLocQuery);
            });
        }
        convertRest.setRows(convert);
        return convertRest;
    }

    public Map<String, Object> getSoDetailAttMap(SoDetail detail) {
        Map<String, Object> resultMap = new HashMap<>();

        // 获取类的所有字段
        Field[] fields = detail.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);  // 使私有字段可访问
            try {
                Object value = field.get(detail);  // 获取字段的值
                if (value != null && field.getName().contains("lotAtt")) {
                    resultMap.put(field.getName(), value);  // 将字段名和值放入map
                }
            } catch (IllegalAccessException e) {
                logger.error("分配库位查询时批次属性异常");
            }
        }
        return resultMap;
    }

    public PageResult<AssignLocQuery> outboundAssignLocPage(PickingAssignLocPageCondition condition){
        //调用库存请求参数
        String type = condition.getType();
        String keyWord = condition.getKeyWord();

        String soDetailNo = condition.getSoDetailNo();
        SoDetail soDetail = soDetailService.queryBySoDetailNo(soDetailNo);
        if (ObjectUtil.isEmpty(soDetail)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_OUTBOUND_ORDER_DETAILS_ARE_BLANK);
        }
        String soNo = soDetail.getSoNo();
        SoHeader soHeader = soHeaderService.queryBySoNo(soNo);
        if (ObjectUtil.isEmpty(soHeader)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_OUTBOUND_ORDER_IS_EMPTY);
        }

        Long skuId = soDetail.getSkuId();
        Long ownerId = soHeader.getOwnerId();
        Long warehouseId = soHeader.getWarehouseId();

        Sku sku = skuService.findById(skuId);

        if (ObjectUtil.isEmpty(sku)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_PRODUCT_CANNOT_BE_EMPTY);
        }

        Long lotId = sku.getLotId();
        WarehouseLotHeader warehouseLotHeader = warehouseLotHeaderService.findById(lotId);
        if (ObjectUtil.isEmpty(warehouseLotHeader)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_BATCH_ATTRIBUTES_CANNOT_BE_EMPTY);
        }

        //批次属性
        JSONObject lotAttObject = new JSONObject();
        List<WarehouseLotDetail> warehouseLotDetailList = warehouseLotDetailService.listByLotId(lotId);
        for (WarehouseLotDetail warehouseLotDetail : warehouseLotDetailList) {
            this.setLotAtt(lotAttObject, soDetail, warehouseLotDetail);
        }

        //TODO HJH skuId、ownerId、warehouseId、lotAttObject 缺少炬哥的接口


        return null;

    }

    public void setLotAtt(JSONObject jsonObject, SoDetail soDetail, WarehouseLotDetail warehouseLotDetail) {
        try {
            String lotAtt = warehouseLotDetail.getLotAtt();

            // 获取类的 Class 对象
            Class<?> clazz = soDetail.getClass();

            // 获取指定字段
            Field field = clazz.getDeclaredField(lotAtt);

            // 如果字段是私有的，我们需要设置它为可访问
            field.setAccessible(true);

            // 获取字段的值
            Object fieldValue = field.get(soDetail);
            if (ObjectUtil.isEmpty(fieldValue)) {
                return;
            }

            jsonObject.set(lotAtt, fieldValue);
        }catch (NoSuchFieldException noSuchFieldException){
            logger.error("分配明细单号为,{},分配时分配库位的批次属性异常", soDetail.getSoDetailNo());
        }catch (IllegalAccessException illegalAccessException){
            logger.error("分配明细单号为,{},分配时分配库位的批次属性异常", soDetail.getSoDetailNo());
        }

    }

    /**
     * 拣货分配-生成拣货任务
     * @param condition
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    public void createPickingTasks(CreatePickingTasksItem condition){
        AllocationTypeEnum allocationEnum = AllocationTypeEnum.getByCode(condition.getAllocationType());
        if (ObjUtil.isNull(allocationEnum)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        }
        switch (allocationEnum) {
            case OUTBOUND -> outboundCreatePickingTasks(condition);
            case WAVE_TIMES -> waveTimesCreatePickingTasks(condition.getCreatePickingTaskItemList());
            case PROCES_ALLOC -> procesCreatePickingTasks(condition);
            case CFD_ALLOC -> cfdCreatePickingTasks(condition);
            default -> throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        };
    }

    private void procesCreatePickingTasks(CreatePickingTasksItem condition) {
        List<CreatePickingTaskItem> createPickingTaskItemList = condition.getCreatePickingTaskItemList();
        String processTaskNo = createPickingTaskItemList.get(0).getProcessTaskNo();
        if (EmptyUtil.isEmpty(processTaskNo)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_PROCES_NO_NOT_EMPTY);
        }
        ProcessTask processTask = processTaskDao.findFirst(new ConditionRule().andEqual(ProcessTask::getProcessTaskNo,processTaskNo));
        if (ObjUtil.isNull(processTask)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_PROCES_DETAIL_ERROR);
        }
        ProcessOrder processOrder = processOrderDao.findFirst(new ConditionRule().andEqual(ProcessOrder::getProcessOrderNo, processTask.getProcessOrderNo()));
        if (ObjUtil.isNull(processOrder)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_PROCES_NO_ERROR);
        }
        //验证是否有存在未创建的拣货单分配明细，如果存在则沿用拣货单号 不存在则新建
        SoPicking soPicking = soPickingService.findFirst(new ConditionRule().andEqual(SoPicking::getProcessOrderNo, processOrder.getProcessOrderNo())
                .andEqual(SoPicking::getStatus, com.chinaservices.wms.common.enums.so.SoPickingStatusEnum.NOT_GENERATE.getCode()));
        SoPickingItem soPickingItem = new SoPickingItem();
        if (ObjUtil.isNull(soPicking)){
            soPickingItem.setProcessOrderType(ProcessOrderTypeEnum.SPLIT_ORDER.getCode());
            soPickingItem.setProcessOrderNo(processOrder.getProcessOrderNo());
            soPickingItem.setWarehouseId(processOrder.getWarehouseId());
            boolean save = soPickingService.save(soPickingItem);
            if (!save){
                throw new ServiceException(SoExcepitonEnum.ALLOCATION_CREATE_PICKING_TASK_ERROR);
            }
        }else{
            soPickingItem.setPickingNo(soPicking.getPickingNo());
        }
        log.info("【生成拣货任务】 生成拣货单号完成，结果：{}",soPickingItem.getPickingNo());

        long qtyEa = createPickingTaskItemList.stream().mapToLong(CreatePickingTaskItem::getQtyAllocationEa).sum();
        //计算加上当前分配数量的总分配数
        BigDecimal sumAllocationEa = processTask.getQtyAllocationEa().add(BigDecimal.valueOf(qtyEa));
        SkuDetailQuery skuInfo = skuService.getById(processTask.getCombinationsSkuId());
        if (sumAllocationEa.compareTo(processTask.getProcessQuantityEa()) > 0){
            //超出商品订货出
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_ERROR_SKU_EA_SUM,skuInfo.getSkuName());
        }

        //提前定义以更改的波次单详情数据
        List<SoAllocation> insert = new ArrayList<>();
        createPickingTaskItemList.forEach(itemList -> {
            SoAllocation soAllocation = new SoAllocation();
            Sku sku = skuService.findById(itemList.getSkuId());
            soAllocation.setWarehouseId(itemList.getWareHouseId());
            if (ObjUtil.isNotNull(sku)){
                soAllocation.setOwnerId(sku.getOwnerId());
                soAllocation.setSkuId(sku.getId());
            }
            soAllocation.setPackageId(processTask.getCombinationsPackageId());
            Long packageUnitId = processTask.getCombinationsPackageUnitId();
            soAllocation.setPackageUnitId(packageUnitId);
            soAllocation.setPackageUnitName(processTask.getCombinationsPackageUnitName());
            soAllocation.setLocId(itemList.getLocId());
            soAllocation.setToLocId(itemList.getLocId());
            soAllocation.setPalletId(itemList.getPalletNo());
            soAllocation.setLotNum(itemList.getLotNum());
            //PackageUnit packageUnit = packageUnitService.findById(packageUnitId);
            //UnitConversionUtil.ConvertResult result = UnitConversionUtil.convertToNotEa(item.getQtyAllocationEa(), packageUnit.getQuantity());
            //soAllocation.setQtyPack();
            soAllocation.setQtyEa(Convert.toBigDecimal(itemList.getQtyAllocationEa()));
            soAllocation.setPickingPack(BigDecimal.ZERO);
            soAllocation.setPickingEa(BigDecimal.ZERO);
            soAllocation.setPickingTaskNo(numberGenerator.nextValue(IdRuleConstant.AI_CODE));
            soAllocation.setIsManualPicking(YesNoEnum.YES.getCode());
            soAllocation.setPickingStatus(SoPickingStatusEnum.NOT_GENERATE.getCode());
            soAllocation.setPickingNo(soPickingItem.getPickingNo());
            soAllocation.setProcessOrderNo(processOrder.getProcessOrderNo());
            soAllocation.setProcessTaskNo(processTask.getProcessTaskNo());
            soAllocation.setProcessOrderType(ProcessOrderTypeEnum.PROCESS_ORDER.getCode());
            //更新库存
            stockService.exec(new InvLotLocQtyBO()
                    .setOrderNo(itemList.getSoDetailNo())
                    .setWarehouseId(itemList.getWareHouseId())
                    .setLocId(itemList.getLocId())
                    .setSkuId(sku.getId())
                    .setUpdateNum(soAllocation.getQtyEa())
                    .setPalletNum(itemList.getPalletNo())
                    .setLotNum(itemList.getLotNum())
                    .setOwnerId(sku.getOwnerId())
                    .setTransactionType(TransactionType.TRAN_INA));
            preSave(soAllocation);
            insert.add(soAllocation);
        });
        batchInsert(insert);
        //更新taskDetails和processOrder分配状态 分配数等信息
        processTask.setQtyAllocationEa(sumAllocationEa);
        if (sumAllocationEa.compareTo(processTask.getProcessQuantityEa()) == 0){
            //完全分配
            processTask.setAllocStatus(SoAllocStatusEnum.FULL.getCode());
        }else{
            //部分分配
            processTask.setAllocStatus(SoAllocStatusEnum.PARTIAL.getCode());
        }
        processTaskDao.update(processTask);
        //更新最外层分配状态
        List<ProcessTask> detailsList = processTaskDao.find(new ConditionRule().andEqual(ProcessTask::getProcessOrderNo, processTask.getProcessOrderNo()));
        boolean full = detailsList.stream().allMatch(m -> SoAllocStatusEnum.FULL.getCode().equals(m.getAllocStatus()));
        if (full){
            processOrder.setAllocationStatus(SoAllocStatusEnum.FULL.getCode());
        }else{
            processOrder.setAllocationStatus(SoAllocStatusEnum.PARTIAL.getCode());
        }
        processOrderDao.update(processOrder);
    }

    private void cfdCreatePickingTasks(CreatePickingTasksItem createPickingTasksItem) {
        List<CreatePickingTaskItem> createPickingTaskItemList = createPickingTasksItem.getCreatePickingTaskItemList();
        Long processOrderDetailId = createPickingTaskItemList.get(0).getProcessTaskDetailsId();
        if (ObjUtil.isNull(processOrderDetailId)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_PROCES_NO_NOT_EMPTY);
        }
        ProcessTaskDetails taskDetails = processTaskDetailsDao.findById(processOrderDetailId);
        if (ObjUtil.isNull(taskDetails)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_PROCES_DETAIL_ERROR);
        }
        ProcessOrder processOrder = processOrderDao.findFirst(new ConditionRule().andEqual(ProcessOrder::getProcessOrderNo, taskDetails.getProcessOrderNo()));
        if (ObjUtil.isNull(processOrder)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_PROCES_NO_ERROR);
        }

        CreatePickingTaskItem first = createPickingTaskItemList.getFirst();
        //验证是否有存在未创建的拣货单分配明细，如果存在则沿用拣货单号 不存在则新建
        SoPicking soPicking = soPickingService.findFirst(new ConditionRule().andEqual(SoPicking::getProcessOrderNo, first.getProcessOrderNo())
                .andEqual(SoPicking::getStatus, com.chinaservices.wms.common.enums.so.SoPickingStatusEnum.NOT_GENERATE.getCode()));
        SoPickingItem soPickingItem = new SoPickingItem();
        if (ObjUtil.isNull(soPicking)){
            soPickingItem.setProcessOrderType(ProcessOrderTypeEnum.SPLIT_ORDER.getCode());
            soPickingItem.setProcessOrderNo(processOrder.getProcessOrderNo());
            soPickingItem.setWarehouseId(processOrder.getWarehouseId());
            boolean save = soPickingService.save(soPickingItem);
            if (!save){
                throw new ServiceException(SoExcepitonEnum.ALLOCATION_CREATE_PICKING_TASK_ERROR);
            }
        }else{
            soPickingItem.setPickingNo(soPicking.getPickingNo());
        }
        log.info("【生成拣货任务】 生成拣货单号完成，结果：{}",soPickingItem.getPickingNo());
        long qtyEa = createPickingTaskItemList.stream().mapToLong(CreatePickingTaskItem::getQtyAllocationEa).sum();

        //计算加上当前分配数量的总分配数
        BigDecimal sumAllocationEa = taskDetails.getQtyAllocationEa().add(BigDecimal.valueOf(qtyEa));
        SkuDetailQuery skuInfo = skuService.getById(first.getSkuId());
        if (sumAllocationEa.compareTo(taskDetails.getDetailsProcessQuantityEa()) > 0){
            //超出商品订货出
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_ERROR_SKU_EA_SUM,skuInfo.getSkuName());
        }

        //提前定义以更改的波次单详情数据
        List<SoAllocation> insert = new ArrayList<>();
        createPickingTaskItemList.forEach(itemList -> {
            SoAllocation soAllocation = new SoAllocation();
            Sku sku = skuService.findById(itemList.getSkuId());
            soAllocation.setWarehouseId(itemList.getWareHouseId());
            if (ObjUtil.isNotNull(sku)){
                soAllocation.setOwnerId(sku.getOwnerId());
                soAllocation.setSkuId(sku.getId());
            }
            soAllocation.setPackageId(taskDetails.getPackageId());
            Long packageUnitId = taskDetails.getPackageUnitId();
            soAllocation.setPackageUnitId(packageUnitId);
            soAllocation.setPackageUnitName(taskDetails.getPackageUnitName());
            soAllocation.setLocId(itemList.getLocId());
            soAllocation.setToLocId(itemList.getLocId());
            soAllocation.setPalletId(itemList.getPalletNo());
            soAllocation.setLotNum(itemList.getLotNum());
            //PackageUnit packageUnit = packageUnitService.findById(packageUnitId);
            //UnitConversionUtil.ConvertResult result = UnitConversionUtil.convertToNotEa(item.getQtyAllocationEa(), packageUnit.getQuantity());
            //soAllocation.setQtyPack();
            soAllocation.setQtyEa(Convert.toBigDecimal(itemList.getQtyAllocationEa()));
            soAllocation.setPickingPack(BigDecimal.ZERO);
            soAllocation.setPickingEa(BigDecimal.ZERO);
            soAllocation.setPickingTaskNo(numberGenerator.nextValue(IdRuleConstant.AI_CODE));
            soAllocation.setIsManualPicking(YesNoEnum.YES.getCode());
            soAllocation.setPickingStatus(SoPickingStatusEnum.NOT_GENERATE.getCode());
            soAllocation.setPickingNo(soPickingItem.getPickingNo());
            soAllocation.setProcessOrderNo(processOrder.getProcessOrderNo());
            soAllocation.setProcessTaskNo(taskDetails.getProcessTaskNo());
            soAllocation.setProcessTaskDetailsId(taskDetails.getId());
            soAllocation.setProcessOrderType(ProcessOrderTypeEnum.SPLIT_ORDER.getCode());
            //更新库存
            stockService.exec(new InvLotLocQtyBO()
                    .setOrderNo(itemList.getSoDetailNo())
                    .setWarehouseId(itemList.getWareHouseId())
                    .setLocId(itemList.getLocId())
                    .setSkuId(sku.getId())
                    .setUpdateNum(soAllocation.getQtyEa())
                    .setPalletNum(itemList.getPalletNo())
                    .setLotNum(itemList.getLotNum())
                    .setOwnerId(sku.getOwnerId())
                    .setTransactionType(TransactionType.TRAN_INA));
            preSave(soAllocation);
            insert.add(soAllocation);
        });
        batchInsert(insert);
        //更新taskDetails和processOrder分配状态 分配数等信息
        taskDetails.setQtyAllocationEa(sumAllocationEa);
        if (sumAllocationEa.compareTo(taskDetails.getDetailsProcessQuantityEa()) == 0){
            //完全分配
            taskDetails.setAllocStatus(SoAllocStatusEnum.FULL.getCode());
        }else{
            //部分分配
            taskDetails.setAllocStatus(SoAllocStatusEnum.PARTIAL.getCode());
        }
        processTaskDetailsDao.update(taskDetails);
        //更新最外层分配状态
        List<ProcessTaskDetails> detailsList = processTaskDetailsDao.find(new ConditionRule().andEqual(ProcessTaskDetails::getProcessOrderNo, taskDetails.getProcessOrderNo()));
        boolean full = detailsList.stream().allMatch(m -> SoAllocStatusEnum.FULL.getCode().equals(m.getAllocStatus()));
        if (full){
            processOrder.setAllocationStatus(SoAllocStatusEnum.FULL.getCode());
        }else{
            processOrder.setAllocationStatus(SoAllocStatusEnum.PARTIAL.getCode());
        }
        processOrderDao.update(processOrder);
    }

    public void outboundCreatePickingTasks(CreatePickingTasksItem item){
        List<CreatePickingTaskItem> createPickingTaskItemList = item.getCreatePickingTaskItemList();
        String soDetailNo = createPickingTaskItemList.get(0).getSoDetailNo();
        if (StrUtil.isBlank(soDetailNo)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_OUTBOUND_DETAIL_ORDER_NUMBER_CANNOT_BE_EMPTY);
        }
        SoDetail soDetail = soDetailService.queryBySoDetailNo(soDetailNo);
        if (ObjectUtil.isEmpty(soDetail)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_OUTBOUND_ORDER_DETAILS_ARE_BLANK);
        }

        String soNo = soDetail.getSoNo();
        SoHeader soHeader = soHeaderService.queryBySoNo(soNo);
        if (ObjectUtil.isEmpty(soHeader)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_OUTBOUND_ORDER_IS_EMPTY);
        }

        //1.创建拣货单号
        List<SoPicking> soPickingList = soPickingService.findBySoNo(soNo);
        SoPickingItem soPickingItem = new SoPickingItem();
        SoPicking soPicking = soPickingList.stream().filter(obj -> com.chinaservices.wms.common.enums.so.SoPickingStatusEnum.NOT_GENERATE.getCode().equals(obj.getStatus())).findFirst().orElse(null);
        if (ObjectUtil.isNotEmpty(soPicking)){
            soPickingItem.setPickingNo(soPicking.getPickingNo());
        }else {
            soPickingItem = this.createSoPicking(soHeader.getId(), soHeader.getSoNo(), soHeader.getWarehouseId());
        }
        //2.构建分配明细
        List<SoAllocation> soAllocationList = this.buildSoAllocationList(createPickingTaskItemList, soPickingItem, soHeader, soDetail);
        //3.增加当前库位的分配数
        this.changeInvLotLoc(createPickingTaskItemList, soHeader, soDetail);

        //4.回写出库订单明细的分配数
        BigDecimal totalQtyEa = soAllocationList.stream()
                .map(SoAllocation::getQtyEa)
                .reduce(BigDecimal.ZERO, BigDecimal::add);  // 累加所有 BigDecimal 值
        if (soDetail.getQtySoEa().subtract(soDetail.getQtyAllocationEa()).compareTo(totalQtyEa) < 0){
            Sku sku = skuService.findById(soDetail.getSkuId());
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_ERROR_SKU_EA_SUM, sku.getSkuName());
        }
        soDetailService.increaseQtyAllocationEa(soDetail.getId(), totalQtyEa);
        //取出 lot、loc、sku三者一致合并成一条拣货任务号
        groupAllocation(soAllocationList,item);
        //5.创建分配明细
        this.batchInsertToSqlExecution(soAllocationList);
        //6.效期预警校验
        for (SoAllocation soAllocation : soAllocationList) {
            expiryWarningService.addExpiryWarning(ExpiryWarningSaveStageEnum.OUTBOUND.getCode(), soAllocation.getLotNum(), soAllocation.getOwnerId(), soAllocation.getWarehouseId(), soAllocation.getSkuId());
        }
    }

    private void groupAllocation(List<SoAllocation> soAllocationList,CreatePickingTasksItem item) {
        List<Long> ids = new ArrayList<>();
        CreatePickingTaskItem first = item.getCreatePickingTaskItemList().getFirst();
        soAllocationList.forEach(allocation -> {
            List<SoAllocation> list = dao.find(new ConditionRule()
                    .andEqual(SoAllocation::getLotNum, allocation.getLotNum())
                    .andEqual(SoAllocation::getLocId, allocation.getLocId())
                    .andEqual(SoAllocation::getSkuId, allocation.getSkuId())
                    .andEqual(SoAllocation::getPickingNo, allocation.getPickingNo())
                    .andEqual(SoAllocation::getSoDetailNo, allocation.getSoDetailNo()));
            if (CollUtil.isNotEmpty(list)){

                if (list.size() > 1){
                    //存在多条旧数据，进行修正
                    allocation.setQtyEa(allocation.getQtyEa().add(list.stream().map(SoAllocation::getQtyEa).reduce(BigDecimal.ZERO, BigDecimal::add)));

                    if (AllocationTypeEnum.WAVE_TIMES.getCode().equals(item.getAllocationType())){
                        //清除soDetail 和 wvDetail 原以分配数据，公示：原始数据 - 去合并数据
                        SoDetail detail = soDetailService.findFirst(new ConditionRule().andEqual(SoDetail::getSoDetailNo, allocation.getSoDetailNo()));
                        if (ObjUtil.isNotNull(detail.getQtyAllocationEa())){
                            detail.setQtyAllocationEa(detail.getQtyAllocationEa().subtract(list.stream().map(SoAllocation::getQtyEa).reduce(BigDecimal.ZERO, BigDecimal::add)));
                            soDetailService.update(detail);
                        }
                        WvDetail wvDetail = wvDetailService.findFirst(new ConditionRule().andEqual(WvDetail::getWvNo, first.getWvNo()).andEqual(WvDetail::getSoDetailId, detail.getId()));
                        if (ObjUtil.isNotNull(wvDetail.getQtyAllocatedEa())){
                            wvDetail.setQtyAllocatedEa(wvDetail.getQtyAllocatedEa() - Convert.toLong(list.stream().map(SoAllocation::getQtyEa).reduce(BigDecimal.ZERO, BigDecimal::add)));
                            wvDetailService.update(wvDetail);
                        }
                    }

                    ids.addAll(list.stream().map(SoAllocation::getId).toList());
                }else {
                    SoAllocation listFirst = list.getFirst();
                    allocation.setQtyEa(allocation.getQtyEa().add(listFirst.getQtyEa()));

                    if (AllocationTypeEnum.WAVE_TIMES.getCode().equals(item.getAllocationType())){
                        SoDetail detail = soDetailService.findFirst(new ConditionRule().andEqual(SoDetail::getSoDetailNo, allocation.getSoDetailNo()));
                        if (ObjUtil.isNotNull(detail.getQtyAllocationEa())){
                            detail.setQtyAllocationEa(detail.getQtyAllocationEa().subtract(listFirst.getQtyEa()));
                            soDetailService.update(detail);
                        }
                        WvDetail wvDetail = wvDetailService.findFirst(new ConditionRule().andEqual(WvDetail::getWvNo, first.getWvNo()).andEqual(WvDetail::getSoDetailId, detail.getId()));
                        if (ObjUtil.isNotNull(wvDetail.getQtyAllocatedEa())){
                            wvDetail.setQtyAllocatedEa(wvDetail.getQtyAllocatedEa() - Convert.toLong(listFirst.getQtyEa()));
                            wvDetailService.update(wvDetail);
                        }
                    }

                    ids.addAll(list.stream().map(SoAllocation::getId).toList());
                }
            }
        });
        if (CollUtil.isNotEmpty(ids)){
            dao.delete(new ConditionRule().andIn(SoAllocation::getId,ids));
        }
    }

    public void changeInvLotLoc(List<CreatePickingTaskItem> createPickingTaskItemList, SoHeader soHeader, SoDetail soDetail){
        List<Long> locIdList = createPickingTaskItemList.stream().map(CreatePickingTaskItem::getLocId).toList();
        List<WarehouseLoc> locList = warehouseLocService.find(new ConditionRule().andIn(WarehouseLoc::getId, locIdList));
        Map<Long, WarehouseLoc> locMap = locList.stream().collect(Collectors.toMap(ModuleBaseModel::getId, Function.identity()));
        List<Runnable> tasks = new ArrayList<>();
        for (CreatePickingTaskItem createPickingTaskItem : createPickingTaskItemList) {

            Long skuId = soDetail.getSkuId();
            Long ownerId = soHeader.getOwnerId();
            Long warehouseId = soHeader.getWarehouseId();
            String lotNum = createPickingTaskItem.getLotNum();
            Long fmLocId = createPickingTaskItem.getLocId();
            String fmPalletNo = createPickingTaskItem.getPalletNo();
            BigDecimal qtyPaEa = new BigDecimal(createPickingTaskItem.getQtyAllocationEa());
            Long toLocId = createPickingTaskItem.getLocId();
            String toPalletNo = createPickingTaskItem.getPalletNo();
            //更新库位库存
            stockService.exec(new InvLotLocQtyBO()
                    .setSkuId(skuId)
                    .setOwnerId(ownerId)
                    .setWarehouseId(warehouseId)
                    .setLotNum(lotNum)
                    .setLocId(fmLocId)
                    .setToLocId(toLocId)
                    .setPalletNum(fmPalletNo)
                    .setToPallet(toPalletNo)
                    .setUpdateNum(qtyPaEa)
                    .setOrderNo(createPickingTaskItem.getPickingTaskNo())
                    .setTransactionType(TransactionType.TRAN_INA));
            WarehouseLoc warehouseLoc = locMap.get(fmLocId);
            if (StrUtil.isNotEmpty(soHeader.getLogisticNo())){
                tasks.add(() -> pushInventoryhanges(ownerId, skuId, lotNum, warehouseLoc, fmPalletNo, warehouseId,qtyPaEa,soHeader));
            }
        }
        if (CollUtil.isNotEmpty(tasks)){
            List<CompletableFuture<Void>> futures = tasks.stream()
                    .map(task -> CompletableFuture.runAsync(task, virtualThreadExecutor)
                            .exceptionally(e -> {
                                log.error("任务执行异常 ",e);
                                return null; // 吞掉异常，继续执行其他任务
                            }))
                    .toList();
            //启动futures集合中的线程
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }

    private void pushInventoryhanges(Long ownerId, Long skuId, String lotNum, WarehouseLoc warehouseLoc, String fmPalletNo, Long warehouseId, BigDecimal qtyPaEa, SoHeader soHeader) {
        InvLotLocQty invLotLocItem = new InvLotLocQty();
        Owner owner = ownerService.findById(ownerId);
        invLotLocItem.setOwnerId(owner.getId());
        invLotLocItem.setOwnerCode(owner.getUpstreamOwnerCode());
        invLotLocItem.setOwnerName(owner.getOwnerName());

        invLotLocItem.setAction(ActionCode.ALLOCATION);

        Sku sku = skuService.findById(skuId);
        invLotLocItem.setSkuId(sku.getId());
        invLotLocItem.setSkuCode(sku.getUpSkuCode());
        invLotLocItem.setSkuName(sku.getSkuName());

        invLotLocItem.setLotNum(lotNum);
        invLotLocItem.setLocId(warehouseLoc.getId());
        invLotLocItem.setLocCode(warehouseLoc.getUpLocCode());

        invLotLocItem.setPalletNum(fmPalletNo);

        Warehouse warehouse = warehouseService.findById(warehouseId);
        invLotLocItem.setWarehouseId(warehouse.getId());
        invLotLocItem.setWarehouseCode(warehouse.getUpWarehouseCode());
        invLotLocItem.setWarehouseName(warehouse.getWarehouseName());

        invLotLocItem.setUpdateNum(qtyPaEa);
        invLotLocItem.setOrderNo(soHeader.getLogisticNo());
        remoteSoStatusPushToCrzFegin.updateTotalInventory(invLotLocItem);
    }

    public SoPickingItem createSoPicking(Long soId, String soNo, Long warehouseId){
        SoPickingItem soPickingItem = new SoPickingItem();
        soPickingItem.setSoId(soId);
        soPickingItem.setSoNo(soNo);
        soPickingItem.setWarehouseId(warehouseId);
        soPickingService.save(soPickingItem);
        return soPickingItem;
    }

    public List<SoAllocation> buildSoAllocationList(List<CreatePickingTaskItem> createPickingTaskItemList, SoPickingItem soPickingItem, SoHeader soHeader, SoDetail soDetail){
        List<SoAllocation> soAllocationList = new ArrayList<>();
        for (CreatePickingTaskItem createPickingTaskItem : createPickingTaskItemList) {
            SoAllocation soAllocation = new SoAllocation();
            BeanUtil.copyProperties(createPickingTaskItem, soAllocation);

            //前端传参
            String pickingTaskNo = numberGenerator.nextValue(IdRuleConstant.AI_CODE);
            soAllocation.setPickingTaskNo(pickingTaskNo);
            soAllocation.setPickingNo(soPickingItem.getPickingNo());
            soAllocation.setPickingStatus(com.chinaservices.wms.common.enums.so.SoPickingStatusEnum.NOT_GENERATE.getCode());
            soAllocation.setTaskStatus(TaskStatusEnum.NOT_STARTED.getCode());
            soAllocation.setQtyEa(new BigDecimal(createPickingTaskItem.getQtyAllocationEa()));

            //后端取得
            soAllocation.setSoDetailId(soDetail.getId());
            soAllocation.setWarehouseId(soHeader.getWarehouseId());
            soAllocation.setOwnerId(soHeader.getOwnerId());
            soAllocation.setPackageId(soDetail.getPackageId());
            soAllocation.setPackageUnitId(soDetail.getPackageUnitId());
            soAllocation.setPackageUnitName(soDetail.getPackageUnitName());
            soAllocation.setToLocId(createPickingTaskItem.getLocId());
            soAllocation.setPalletId(createPickingTaskItem.getPalletNo());
            createPickingTaskItem.setPickingTaskNo(pickingTaskNo);
            preSave(soAllocation);
            soAllocationList.add(soAllocation);
        }
        return soAllocationList;
    }



    /**
     * 拣货分配-生成拣货任务波次 手动分配
     * @param createPickingTaskItemList
     * @return
     */
    private Boolean waveTimesCreatePickingTasks(List<CreatePickingTaskItem> createPickingTaskItemList) {
        CreatePickingTaskItem first = createPickingTaskItemList.getFirst();
        //验证是否有存在未创建的拣货单分配明细，如果存在则沿用拣货单号 不存在则新建
        SoPicking soPicking = soPickingService.findFirst(new ConditionRule().andEqual(SoPicking::getWvNo, first.getWvNo())
                .andEqual(SoPicking::getStatus, com.chinaservices.wms.common.enums.so.SoPickingStatusEnum.NOT_GENERATE.getCode()));
        SoPickingItem soPickingItem = new SoPickingItem();
        if (ObjUtil.isNull(soPicking)){
            WvHeader wvHeader = wvHeaderService.findFirst(new ConditionRule().andEqual(WvHeader::getWvNo, first.getWvNo()));
            soPickingItem.setWvId(wvHeader.getId());
            soPickingItem.setWvNo(wvHeader.getWvNo());
            soPickingItem.setWarehouseId(wvHeader.getWarehouseId());
            boolean save = soPickingService.save(soPickingItem);
            if (!save){
                throw new ServiceException(SoExcepitonEnum.ALLOCATION_CREATE_PICKING_TASK_ERROR);
            }
        }else{
            soPickingItem.setPickingNo(soPicking.getPickingNo());
        }
        log.info("【生成拣货任务】 生成拣货单号完成，结果：{}",soPickingItem.getPickingNo());
        long qtyEa = createPickingTaskItemList.stream().mapToLong(CreatePickingTaskItem::getQtyAllocationEa).sum();

        SoDetail soDetail = soDetailService.findFirst(new ConditionRule().andEqual(SoDetail::getSoDetailNo, first.getSoDetailNo()));
        BigDecimal allocationSum = soDetail.getQtyAllocationEa().add(Convert.toBigDecimal(createPickingTaskItemList.stream().mapToLong(CreatePickingTaskItem::getQtyAllocationEa).sum()));
        SoHeader soHeader = soHeaderService.findFirst(new ConditionRule().andEqual(SoHeader::getSoNo, soDetail.getSoNo()));
        if (soDetail.getQtySoEa().compareTo(allocationSum) > 0) {
            //部分收货
            soHeader.setAllocStatus(SoAllocStatusEnum.PARTIAL.getCode());
        }else if (soDetail.getQtySoEa().compareTo(allocationSum) == 0){
            //完全收货
            List<SoDetail> list = soDetailService.find(new ConditionRule().andEqual(SoDetail::getSoNo, soDetail.getSoNo()).andNotEqual(SoDetail::getSoDetailNo, soDetail.getSoDetailNo()));
            if (CollUtil.isNotEmpty(list)){
                long fullSize = list.stream().filter(item -> item.getQtySoEa().compareTo(item.getQtyAllocationEa()) == 0).count();
                if (fullSize == list.size()){
                    //完全收货
                    soHeader.setAllocStatus(SoAllocStatusEnum.FULL.getCode());
                }else{
                    //部分收货
                    soHeader.setAllocStatus(SoAllocStatusEnum.PARTIAL.getCode());
                }
            }else{
                soHeader.setAllocStatus(SoAllocStatusEnum.FULL.getCode());
            }
        }
        soHeaderService.update(soHeader);
        //计算加上当前分配数量的总分配数
        BigDecimal sumAllocationEa = soDetail.getQtyAllocationEa().add(BigDecimal.valueOf(qtyEa));
        SkuDetailQuery skuInfo = skuService.getById(first.getSkuId());
        if (sumAllocationEa.compareTo(soDetail.getQtySoEa()) > 0){
            //超出商品订货出
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_ERROR_SKU_EA_SUM,skuInfo.getSkuName());
        }

        //自动分配或手动分配，若分配结果 loc+lot+sku一致，则合并拣货任务在一个拣货波次内；三者不一致的情况下，拣货波次和拣货任务为一对一的关系
        Map<String, List<CreatePickingTaskItem>> map = createPickingTaskItemList.stream()
                .collect(Collectors.groupingBy(item -> item.getLocId() + item.getLotNum() + item.getSkuId()));

        //提前定义以更改的波次单详情数据
        List<WvDetail> wvDetails = new ArrayList<>();
        map.keySet().forEach(keyValue -> {
            List<CreatePickingTaskItem> list = map.get(keyValue);
            List<SoAllocation> insert = new ArrayList<>();
            if (list.size() == 1){
                //若分配结果 loc+lot+sku一致，则合并拣货任务在一个拣货波次内
                CreatePickingTaskItem item = list.getFirst();
                //判断库中是否存在相同商品的拣货分配信息，如果存在则使用同一个波次
                SoAllocation wvAllocation = sopickingServiceAllocation.findFirst(new ConditionRule()
                        .andEqual(SoAllocation::getLocId,item.getLocId())
                        .andEqual(SoAllocation::getLotNum,item.getLotNum())
                        .andEqual(SoAllocation::getSkuId, item.getSkuId())
                        .andEqual(SoAllocation::getPickingNo,soPickingItem.getPickingNo()));
                SoAllocation soAllocation = new SoAllocation();
                soAllocation.setSoDetailNo(item.getSoDetailNo());
                soAllocation.setSoDetailId(soDetail.getId());
                if (ObjUtil.isNull(wvAllocation)){
                    soAllocation.setPkWvNo(numberGenerator.nextValue(IdRuleConstant.PKWV_CODE));
                }else{
                    soAllocation.setPkWvNo(wvAllocation.getPkWvNo());
                }
                setSoAllocationInfo(item, soAllocation, soDetail, soPickingItem);
                insert.add(soAllocation);
            } else {
                String pkWvNo = "";
                CreatePickingTaskItem pickingTaskItem = list.getFirst();
                SoAllocation wvAllocation = sopickingServiceAllocation.findFirst(new ConditionRule()
                        .andEqual(SoAllocation::getLocId,pickingTaskItem.getLocId())
                        .andEqual(SoAllocation::getLotNum,pickingTaskItem.getLotNum())
                        .andEqual(SoAllocation::getSkuId, pickingTaskItem.getSkuId())
                        .andEqual(SoAllocation::getPickingNo,soPickingItem.getPickingNo()));
                if (ObjUtil.isNull(wvAllocation)){
                    pkWvNo = numberGenerator.nextValue(IdRuleConstant.PKWV_CODE);
                }else{
                    pkWvNo = wvAllocation.getPkWvNo();
                }
                String finalPkWvNo = pkWvNo;
                list.forEach(item -> {
                    //若分配结果 loc+lot+sku一致，则合并拣货任务在一个拣货波次内
                    SoAllocation soAllocation = new SoAllocation();
                    soAllocation.setSoDetailNo(item.getSoDetailNo());
                    SoDetail detail = soDetailService.findFirst(new ConditionRule().andEqual(SoDetail::getSoDetailNo, item.getSoDetailNo()));
                    soAllocation.setSoDetailId(detail.getId());
                    soAllocation.setPkWvNo(finalPkWvNo);
                    setSoAllocationInfo(item, soAllocation, soDetail, soPickingItem);
                    insert.add(soAllocation);
                });
            }
            groupAllocation(insert,new CreatePickingTasksItem().setCreatePickingTaskItemList(createPickingTaskItemList).setAllocationType(AllocationTypeEnum.WAVE_TIMES.getCode()));
            //执行新增
            sopickingServiceAllocation.batchInsert(insert);
            //效期预警校验
            for (SoAllocation soAllocation : insert) {
                expiryWarningService.addExpiryWarning(ExpiryWarningSaveStageEnum.OUTBOUND.getCode(), soAllocation.getLotNum(), soAllocation.getOwnerId(), soAllocation.getWarehouseId(), soAllocation.getSkuId());
            }
            //写会分配数
            Map<String, List<SoAllocation>> listMap = insert.stream().collect(Collectors.groupingBy(SoAllocation::getSoDetailNo));
            List<SoDetail> soDetails = soDetailService.find(new ConditionRule().andIn(SoDetail::getSoDetailNo, listMap.keySet()));
            soDetails.forEach(item -> {
                List<SoAllocation> allocationList = listMap.get(item.getSoDetailNo());
                if (CollUtil.isNotEmpty(allocationList)){
                    //将allocationList中的 BigDecimal类型的qtyEa字段相加计算总和
                    if (ObjUtil.isNull(item.getQtyAllocationEa())){
                        item.setQtyAllocationEa(BigDecimal.ZERO);
                    }
                    item.setQtyAllocationEa(item.getQtyAllocationEa().add(allocationList.stream().map(SoAllocation::getQtyEa).reduce(BigDecimal.ZERO, BigDecimal::add)));

                    if (item.getQtyAllocationEa().compareTo(item.getQtySoEa()) == 0){
                        //完全分配
                        item.setStatus(SoAllocStatusEnum.FULL.getCode());
                    }else {
                        //部分分配
                        item.setStatus(SoAllocStatusEnum.PARTIAL.getCode());
                    }
                    soDetailService.update(item);
                    WvDetail wvDetail = wvDetailService.findFirst(new ConditionRule().andEqual(WvDetail::getSoDetailId, item.getId())
                            .andEqual(WvDetail::getWvNo, first.getWvNo()));
                    wvDetail.setQtyAllocatedEa(wvDetail.getQtyAllocatedEa() + Convert.toLong(allocationList.stream().map(SoAllocation::getQtyEa).reduce(BigDecimal.ZERO, BigDecimal::add)));
                    wvDetailService.update(wvDetail);
                    wvDetails.add(wvDetail);
                }
            });
        });
        //查询所有波次单数据
        List<WvDetail> details = wvDetailService.find(new ConditionRule().andEqual(WvDetail::getWvNo, first.getWvNo()));
        long yfpAll = details.stream().filter(item -> Objects.equals(item.getQtyAllocationEa(), item.getQtyAllocatedEa())).count();
        WvHeader header = wvHeaderService.findFirst(new ConditionRule().andEqual(WvHeader::getWvNo, first.getWvNo()));
        if (details.size() != yfpAll){
            header.setStatus(WvStatusEnum.WV_NEW.getCode());
            header.setAllocStatus(SoAllocStatusEnum.PARTIAL.getCode());
        } else if (details.size() == yfpAll){
            header.setStatus(WvStatusEnum.WV_COMPLETE.getCode());
            header.setAllocStatus(SoAllocStatusEnum.FULL.getCode());
        }else{
            header.setStatus(WvStatusEnum.WV_NEW.getCode());
            header.setAllocStatus(SoAllocStatusEnum.PARTIAL.getCode());
        }
        wvHeaderService.update(header);
        return Boolean.TRUE;
    }

    /**
     * 封装重复代码
     * @param item
     * @param soAllocation
     * @param soDetail
     * @param soPickingItem
     */
    private void setSoAllocationInfo(CreatePickingTaskItem item, SoAllocation soAllocation, SoDetail soDetail, SoPickingItem soPickingItem) {
        Sku sku = skuService.findById(item.getSkuId());
        //WarehouseLoc warehouseLoc = warehouseLocService.findById(item.getLocId());
        if (ObjUtil.isNull(item.getWareHouseId())){
            SoHeader soHeader = soHeaderService.findFirst(new ConditionRule().andEqual(SoHeader::getSoNo, item.getSoNo()));
            soAllocation.setWarehouseId(soHeader.getWarehouseId());
        }else{
            soAllocation.setWarehouseId(item.getWareHouseId());
        }
        if (ObjUtil.isNotNull(sku)){
            soAllocation.setOwnerId(sku.getOwnerId());
            soAllocation.setSkuId(sku.getId());
        }
        soAllocation.setPackageId(soDetail.getPackageId());
        Long packageUnitId = soDetail.getPackageUnitId();
        soAllocation.setPackageUnitId(packageUnitId);
        soAllocation.setPackageUnitName(soDetail.getPackageUnitName());
        soAllocation.setLocId(item.getLocId());
        soAllocation.setToLocId(item.getLocId());
        soAllocation.setPalletId(item.getPalletNo());
        soAllocation.setLotNum(item.getLotNum());
        //PackageUnit packageUnit = packageUnitService.findById(packageUnitId);
        //UnitConversionUtil.ConvertResult result = UnitConversionUtil.convertToNotEa(item.getQtyAllocationEa(), packageUnit.getQuantity());
        //soAllocation.setQtyPack();
        soAllocation.setQtyEa(Convert.toBigDecimal(item.getQtyAllocationEa()));
        soAllocation.setPickingPack(BigDecimal.ZERO);
        soAllocation.setPickingEa(BigDecimal.ZERO);
        soAllocation.setPickingTaskNo(numberGenerator.nextValue(IdRuleConstant.AI_CODE));
        soAllocation.setIsManualPicking(YesNoEnum.YES.getCode());
        soAllocation.setPickingStatus(SoPickingStatusEnum.NOT_GENERATE.getCode());
        soAllocation.setPickingNo(soPickingItem.getPickingNo());

        //更新库存
        stockService.exec(new InvLotLocQtyBO()
                .setOrderNo(item.getSoDetailNo())
                .setWarehouseId(item.getWareHouseId())
                .setLocId(item.getLocId())
                .setSkuId(sku.getId())
                .setUpdateNum(soAllocation.getQtyEa())
                .setPalletNum(item.getPalletNo())
                .setLotNum(item.getLotNum())
                .setOwnerId(sku.getOwnerId())
                .setTransactionType(TransactionType.TRAN_INA));
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelSave(CancalSaveItem item){
        AllocationTypeEnum allocationEnum = AllocationTypeEnum.getByCode(item.getAllocationType());
        if (ObjUtil.isNull(allocationEnum)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        }
        switch (allocationEnum) {
            case OUTBOUND -> outboundCancelSave(item);
            case WAVE_TIMES -> waveTimesCancelSave(item);
            case CFD_ALLOC -> cfdCancelSave(item);
            case PROCES_ALLOC -> processCancelSave(item);
            default -> throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        };

    }

    private void processCancelSave(CancalSaveItem item) {
        List<Long> idList = item.getIdList();

        //1.查询待取消分配集合
        List<SoAllocation> soAllocationList = this.findByIds(ArrayUtil.toArray(idList, Long.class));
        if (CollUtil.isEmpty(soAllocationList)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_ASSIGNMENT_DETAIL_CANNOT_BE_EMPTY);
        }

        Map<String, List<SoAllocation>> taskNoMap = soAllocationList.stream().collect(Collectors.groupingBy(SoAllocation::getProcessTaskNo));
        Set<String> taskNoSet = taskNoMap.keySet();

        List<ProcessTask> tasks = processTaskDao.find(new ConditionRule().andIn(ProcessTask::getProcessTaskNo,taskNoSet));
        for (ProcessTask task : tasks) {
            List<SoAllocation> allocations = taskNoMap.get(task.getProcessTaskNo());
            if (CollUtil.isEmpty(allocations)){
                continue;
            }
            BigDecimal reduce = allocations.stream().map(SoAllocation::getQtyEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            task.setQtyAllocationEa(task.getQtyAllocationEa().subtract(reduce));
            if (task.getQtyAllocationEa().compareTo(BigDecimal.ZERO) == 0){
                //未分配
                task.setAllocStatus(SoAllocStatusEnum.UN_ALLOC.getCode());
            }else if (task.getQtyAllocationEa().compareTo(BigDecimal.ZERO) > 0 &&
                task.getProcessQuantityEa().compareTo(task.getQtyAllocationEa()) > 0){
                //部分收货
                task.setAllocStatus(SoAllocStatusEnum.FULL.getCode());
            }
        }

        Set<String> processOrderNoSet = tasks.stream().map(ProcessTask::getProcessOrderNo).collect(Collectors.toSet());
        List<ProcessTask> taskList = processTaskDao.find(new ConditionRule().andIn(ProcessTask::getProcessOrderNo, processOrderNoSet));
        List<ProcessOrder> processOrders = processOrderDao.findByIds(processOrderNoSet.toArray(Long[]::new));
        Map<String, ProcessOrder> orderMap = processOrders.stream().collect(Collectors.toMap(ProcessOrder::getProcessOrderNo, Function.identity(), (one, two) -> one));
        Map<String, List<ProcessTask>> listMap = taskList.stream().collect(Collectors.groupingBy(ProcessTask::getProcessOrderNo));

        listMap.forEach((key, list) -> {
            ProcessOrder processOrder = orderMap.get(key);
            boolean full = list.stream().allMatch(a -> SoAllocStatusEnum.FULL.getCode().equals(a.getAllocStatus()));
            boolean unAlloc = list.stream().allMatch(a -> SoAllocStatusEnum.UN_ALLOC.getCode().equals(a.getAllocStatus()));
            if (full){
                processOrder.setAllocationStatus(SoAllocStatusEnum.FULL.getCode());
            }else if(unAlloc){
                processOrder.setAllocationStatus(SoAllocStatusEnum.UN_ALLOC.getCode());
            }else{
                processOrder.setAllocationStatus(SoAllocStatusEnum.PARTIAL.getCode());
            }
        });

        soAllocationList.forEach(soAllocation -> {
            Long skuId = soAllocation.getSkuId();
            Long ownerId = soAllocation.getOwnerId();
            Long warehouseId = soAllocation.getWarehouseId();
            String lotNum = soAllocation.getLotNum();
            Long fmLocId = soAllocation.getLocId();
            String fmPalletNo = soAllocation.getPalletId();
            BigDecimal qtyPaEa = soAllocation.getQtyEa();
            Long toLocId = soAllocation.getLocId();
            String toPalletNo = soAllocation.getPalletId();
            //更新库位库存
            stockService.exec(new InvLotLocQtyBO()
                    .setSkuId(skuId)
                    .setOwnerId(ownerId)
                    .setWarehouseId(warehouseId)
                    .setLotNum(lotNum)
                    .setLocId(fmLocId)
                    .setToLocId(toLocId)
                    .setPalletNum(fmPalletNo)
                    .setToPallet(toPalletNo)
                    .setUpdateNum(qtyPaEa)
                    .setOrderNo(soAllocation.getPickingTaskNo())
                    .setTransactionType(TransactionType.TRAN_CR_INA));
        });

        if (!item.isCancelPicking()) {
            //5.删除拣货单号
            this.deletePickingByAlloctions(soAllocationList);
            //6.删除分配明细记录
            dao.delete(ArrayUtil.toArray(idList, Long.class));
        }
        processTaskDao.batchUpdate(tasks);
        processOrderDao.batchUpdate(processOrders);

    }

    private void cfdCancelSave(CancalSaveItem item) {
        List<Long> idList = item.getIdList();

        //1.查询待取消分配集合
        List<SoAllocation> soAllocationList = this.findByIds(ArrayUtil.toArray(idList, Long.class));
        if (CollUtil.isEmpty(soAllocationList)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_ASSIGNMENT_DETAIL_CANNOT_BE_EMPTY);
        }
        Map<Long, List<SoAllocation>> taskDetailsIdMap = soAllocationList.stream().collect(Collectors.groupingBy(SoAllocation::getProcessTaskDetailsId));
        Set<Long> taskDetailSet = soAllocationList.stream().map(SoAllocation::getProcessTaskDetailsId).collect(Collectors.toSet());
        List<ProcessTaskDetails> taskDetails = processTaskDetailsDao.findByIds(taskDetailSet.toArray(Long[]::new));
        for (ProcessTaskDetails taskDetail : taskDetails) {
            List<SoAllocation> allocations = taskDetailsIdMap.get(taskDetail.getId());
            if (CollUtil.isEmpty(allocations)){
                continue;
            }
            BigDecimal reduce = allocations.stream().map(SoAllocation::getQtyEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            taskDetail.setQtyAllocationEa(taskDetail.getQtyAllocationEa().subtract(reduce));
            if (taskDetail.getQtyAllocationEa().compareTo(BigDecimal.ZERO) == 0){
                //未分配
                taskDetail.setAllocStatus(SoAllocStatusEnum.UN_ALLOC.getCode());
            }else if (taskDetail.getQtyAllocationEa().compareTo(BigDecimal.ZERO) > 0 &&
                    taskDetail.getDetailsProcessQuantityEa().compareTo(taskDetail.getQtyAllocationEa()) > 0){
                //部分收货
                taskDetail.setAllocStatus(SoAllocStatusEnum.FULL.getCode());
            }
        }
        Set<String> processOrderNoSet = taskDetails.stream().map(ProcessTaskDetails::getProcessOrderNo).collect(Collectors.toSet());
        List<ProcessTaskDetails> detailsList = processTaskDetailsDao.find(new ConditionRule().andIn(ProcessTaskDetails::getProcessOrderNo, processOrderNoSet));
        List<ProcessOrder> processOrders = processOrderDao.findByIds(processOrderNoSet.toArray(Long[]::new));
        Map<String, ProcessOrder> orderMap = processOrders.stream().collect(Collectors.toMap(ProcessOrder::getProcessOrderNo, Function.identity(), (one, two) -> one));
        Map<String, List<ProcessTaskDetails>> listMap = detailsList.stream().collect(Collectors.groupingBy(ProcessTaskDetails::getProcessOrderNo));
        listMap.forEach((key, list) -> {
            ProcessOrder processOrder = orderMap.get(key);
            boolean full = list.stream().allMatch(a -> SoAllocStatusEnum.FULL.getCode().equals(a.getAllocStatus()));
            boolean unAlloc = list.stream().allMatch(a -> SoAllocStatusEnum.UN_ALLOC.getCode().equals(a.getAllocStatus()));
            if (full){
                processOrder.setAllocationStatus(SoAllocStatusEnum.FULL.getCode());
            }else if(unAlloc){
                processOrder.setAllocationStatus(SoAllocStatusEnum.UN_ALLOC.getCode());
            }else{
                processOrder.setAllocationStatus(SoAllocStatusEnum.PARTIAL.getCode());
            }
        });
        soAllocationList.forEach(soAllocation -> {
            Long skuId = soAllocation.getSkuId();
            Long ownerId = soAllocation.getOwnerId();
            Long warehouseId = soAllocation.getWarehouseId();
            String lotNum = soAllocation.getLotNum();
            Long fmLocId = soAllocation.getLocId();
            String fmPalletNo = soAllocation.getPalletId();
            BigDecimal qtyPaEa = soAllocation.getQtyEa();
            Long toLocId = soAllocation.getLocId();
            String toPalletNo = soAllocation.getPalletId();
            //更新库位库存
            stockService.exec(new InvLotLocQtyBO()
                    .setSkuId(skuId)
                    .setOwnerId(ownerId)
                    .setWarehouseId(warehouseId)
                    .setLotNum(lotNum)
                    .setLocId(fmLocId)
                    .setToLocId(toLocId)
                    .setPalletNum(fmPalletNo)
                    .setToPallet(toPalletNo)
                    .setUpdateNum(qtyPaEa)
                    .setOrderNo(soAllocation.getPickingTaskNo())
                    .setTransactionType(TransactionType.TRAN_CR_INA));
        });

        if (!item.isCancelPicking()) {
            //5.删除拣货单号
            this.deletePickingByAlloctions(soAllocationList);
            //6.删除分配明细记录
            dao.delete(ArrayUtil.toArray(idList, Long.class));
        }
        processTaskDetailsDao.batchUpdate(taskDetails);
        processOrderDao.batchUpdate(processOrders);
    }

    public void outboundCancelSave(CancalSaveItem item){
        List<Long> idList = item.getIdList();

        //1.查询待取消分配集合
        List<SoAllocation> soAllocationList = this.findByIds(ArrayUtil.toArray(idList, Long.class));
        if (CollUtil.isEmpty(soAllocationList)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_ASSIGNMENT_DETAIL_CANNOT_BE_EMPTY);
        }

        //2.减少出库订单分配数
        this.decreaseQtyAllocationEa(soAllocationList);

        //3.增加波次单和出库单关联分配数
        this.increaseVwQtyAllocationEa(soAllocationList);

        //4.调整库存
        this.changeInvLotLoc(soAllocationList);

        if (!item.isCancelPicking()) {
            //5.删除拣货单号
            this.deletePickingByAlloctions(soAllocationList);
            //6.删除分配明细记录
            dao.delete(ArrayUtil.toArray(idList, Long.class));
        }
    }

    public void deletePickingByAlloctions(List<SoAllocation> soAllocationList){
        Map<String, List<SoAllocation>> soAllocationMap = soAllocationList.stream().collect(Collectors.groupingBy(SoAllocation::getPickingNo));

        Set<String> pickingNoSet = soAllocationList.stream().map(SoAllocation::getPickingNo).collect(Collectors.toSet());
        List<SoAllocation> soAllocationSourceList = this.findByPickingNos(new ArrayList<>(pickingNoSet));
        Map<String, List<SoAllocation>> soAllocationSourceMap = soAllocationSourceList.stream().filter(obj -> !SoPickingStatusEnum.CANCELLED.getCode().equals(obj.getPickingStatus())).collect(Collectors.groupingBy(SoAllocation::getPickingNo));

        Set<String> pickingNoDeleteSet = new HashSet<>();
        for (Map.Entry<String, List<SoAllocation>> entry : soAllocationSourceMap.entrySet()) {
            String pickingNo = entry.getKey();
            List<SoAllocation> soAllocationNewList = soAllocationMap.get(pickingNo);
            if (CollUtil.isEmpty(soAllocationNewList)){
                continue;
            }
            if (soAllocationNewList.size() == entry.getValue().size()){
                pickingNoDeleteSet.add(pickingNo);
            }
        }
        if (CollUtil.isNotEmpty(pickingNoDeleteSet)){
            soPickingService.deleteByPickingNos(pickingNoDeleteSet);
        }
    }


    public void changeInvLotLoc(List<SoAllocation> soAllocationList){
        Set<String> palletIdSet =
                soAllocationList.stream().map(SoAllocation::getPalletId).collect(Collectors.toSet());
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(Pallet::getId, palletIdSet);
        List<Pallet> palletList = palletService.find(conditionRule);

        for (SoAllocation soAllocation : soAllocationList) {

            Long skuId = soAllocation.getSkuId();
            Long ownerId = soAllocation.getOwnerId();
            Long warehouseId = soAllocation.getWarehouseId();
            String lotNum = soAllocation.getLotNum();
            Long fmLocId = soAllocation.getLocId();
            String fmPalletNo = soAllocation.getPalletId();
            BigDecimal qtyPaEa = soAllocation.getQtyEa();
            Long toLocId = soAllocation.getLocId();
            String toPalletNo = soAllocation.getPalletId();
            //更新库位库存
            stockService.exec(new InvLotLocQtyBO()
                    .setSkuId(skuId)
                    .setOwnerId(ownerId)
                    .setWarehouseId(warehouseId)
                    .setLotNum(lotNum)
                    .setLocId(fmLocId)
                    .setToLocId(toLocId)
                    .setPalletNum(fmPalletNo)
                    .setToPallet(toPalletNo)
                    .setUpdateNum(qtyPaEa)
                    .setOrderNo(soAllocation.getPickingTaskNo())
                    .setTransactionType(TransactionType.TRAN_CR_INA));
        }
    }

    public void increaseVwQtyAllocationEa(List<SoAllocation> soAllocationList){
        //查询波次出库单关联
        Set<Long> soDetailIdSet = soAllocationList.stream().map(SoAllocation::getSoDetailId).collect(Collectors.toSet());
        List<WvDetail> wvDetailList = wvDetailService.findBySoDetailids(soDetailIdSet);
        if (CollUtil.isEmpty(wvDetailList)){
            return;
        }
        Map<Long, Long> soDetailIdToWvIdMap = wvDetailList.stream().collect(Collectors.toMap(WvDetail::getSoDetailId, WvDetail::getWvId));
        Set<Long> wvIdSet = wvDetailList.stream().map(WvDetail::getWvId).collect(Collectors.toSet());
        List<WvHeader> wvHeaderList = wvHeaderService.findByIds(ArrayUtil.toArray(wvIdSet, Long.class));
        if (CollUtil.isEmpty(wvHeaderList)){
            return;
        }
        Map<Long, WvHeader> idToWvHeaderMap = wvHeaderList.stream().collect(Collectors.toMap(WvHeader::getId, Function.identity()));
        Set<Long> wvIdUpdateSet = new HashSet<>();
        //增加波次出库单关联应分配数
        for (SoAllocation soAllocation : soAllocationList) {
            wvDetailService.increaseQtyAllocationEa(soAllocation.getSoDetailId(), soAllocation.getQtyEa());
            Long wvId = soDetailIdToWvIdMap.get(soAllocation.getSoDetailId());
            if (ObjectUtil.isEmpty(wvId)) {
                continue;
            }
            WvHeader wvHeader = idToWvHeaderMap.get(wvId);
            if (ObjectUtil.isEmpty(wvHeader)){
                continue;
            }
            if (!SoAllocStatusEnum.FULL.getCode().equals(wvHeader.getAllocStatus())){
                continue;
            }
            wvIdUpdateSet.add(wvId);
        }
        //更新波次订单分配状态
        if (CollUtil.isNotEmpty(wvIdUpdateSet)){
            wvHeaderService.updateAllocStatusByIds(SoAllocStatusEnum.PARTIAL.getCode(), wvIdUpdateSet);

        }
    }

    public void decreaseQtyAllocationEa(List<SoAllocation> soAllocationList){
        for (SoAllocation soAllocation : soAllocationList) {
            soDetailService.decreaseQtyAllocationEa(soAllocation.getSoDetailNo(), soAllocation.getQtyEa());
        }
    }

    /**
     * 波次分配 取消分配
     * @param cancelItem 取消参数
     */
    private void waveTimesCancelSave(CancalSaveItem cancelItem) {
        List<Long> idList = cancelItem.getIdList();
        if (CollUtil.isEmpty(idList)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_THE_DETAIL_ID_CANNOT_BE_EMPTY);
        }
        List<SoAllocation> allocations = dao.findByIds(idList.toArray(new Long[]{}));
        Set<String> palletIdSet = allocations.stream().map(SoAllocation::getPalletId).collect(Collectors.toSet());
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(Pallet::getId, palletIdSet);
        List<Pallet> palletList = palletService.find(conditionRule);
        if (CollUtil.isNotEmpty(allocations)){
            List<SoDetail> updateList = new ArrayList<>();
            allocations.forEach(item -> {
                //回退明细分配数
                SoDetail detail = soDetailService.findFirst(new ConditionRule().andEqual(SoDetail::getSoDetailNo, item.getSoDetailNo()));
                detail.setQtyAllocationEa(detail.getQtyAllocationEa().subtract(item.getQtyEa()));
                if (detail.getQtyAllocationEa().compareTo(BigDecimal.ZERO) <= 0){
                    //未分配
                    detail.setStatus(SoAllocStatusEnum.UN_ALLOC.getCode());
                }else if (detail.getQtySoEa().compareTo(detail.getQtyAllocationEa()) > 0){
                    detail.setStatus(SoAllocStatusEnum.PARTIAL.getCode());
                }
                //恢复库存数据
                stockService.exec(new InvLotLocQtyBO()
                        .setTransactionType(TransactionType.TRAN_CR_INA)
                        .setOrderNo(item.getSoDetailNo())
                        .setWarehouseId(item.getWarehouseId())
                        .setLocId(item.getLocId())
                        .setSkuId(item.getSkuId())
                        .setUpdateNum(item.getQtyEa())
                        .setPalletNum(item.getPalletId())
                        .setLotNum(item.getLotNum())
                        .setOwnerId(item.getOwnerId()));
                soDetailService.update(detail);
                updateList.add(detail);
            });
            SoAllocation allocation = allocations.getFirst();
            SoPicking first = soPickingService.findFirst(new ConditionRule().andEqual(SoPicking::getPickingNo, allocation.getPickingNo()));
            SoPicking soPicking = soPickingService.findFirst(new ConditionRule().andEqual(SoPicking::getPickingNo, allocation.getPickingNo()));
            List<WvDetail> wvDetails = wvDetailService.find(new ConditionRule().andEqual(WvDetail::getWvNo, soPicking.getWvNo()));
            Map<Long, WvDetail> wvDetailMap = wvDetails.stream().collect(Collectors.toMap(WvDetail::getSoDetailId, Function.identity(), (v1, v2) -> v1));
            Set<Long> collect = allocations.stream().map(SoAllocation::getSoDetailId).collect(Collectors.toSet());
            //删除wvDetailMap中key不等于collect的数据
            wvDetailMap.entrySet().removeIf(item -> !collect.contains(item.getKey()));
            Map<Long, SoAllocation> detailMap = allocations.stream().collect(Collectors.toMap(SoAllocation::getSoDetailId, Function.identity(), (v1, v2) -> v1));
            wvDetailMap.keySet().forEach(item -> {
                WvDetail wvDetail = wvDetailMap.get(item);
                SoAllocation allocation1 = detailMap.get(item);
                wvDetail.setQtyAllocatedEa(wvDetail.getQtyAllocatedEa() - Convert.toLong(allocation1.getQtyEa()));
                wvDetailService.update(wvDetail);
            });

            WvHeader header = wvHeaderService.findFirst(new ConditionRule().andEqual(WvHeader::getWvNo, first.getWvNo()));
            List<WvDetail> detailList = wvDetailService.find(new ConditionRule().andEqual(WvDetail::getWvNo, soPicking.getWvNo()));
            if (CollUtil.isNotEmpty(detailList)){
                //计算detailList 中类型为Long的 qtyAllocationEa 总和
                long qtyAllocatedEaSum = detailList.stream().mapToLong(WvDetail::getQtyAllocatedEa).sum();
                if (qtyAllocatedEaSum <= 0){
                    //未分配
                    header.setAllocStatus(SoAllocStatusEnum.UN_ALLOC.getCode());
                }else {
                    //部分分配
                    header.setAllocStatus(SoAllocStatusEnum.PARTIAL.getCode());
                }
            }
            List<Long> detailIds = allocations.stream().map(SoAllocation::getSoDetailId).toList();
            List<SoDetail> detail = soDetailService.findByIds(detailIds.toArray(new Long[]{}));
            //soHeader 可能会有多个也可能只有一个,这里更改原版用法改分组循环方式
            Map<String, List<SoDetail>> map = detail.stream().collect(Collectors.groupingBy(SoDetail::getSoNo));
            for (String key : map.keySet()) {
                SoHeader soHeader = soHeaderService.findFirst(new ConditionRule().andEqual(SoHeader::getSoNo, key));
                List<SoDetail> soDetails = soDetailService.find(new ConditionRule().andEqual(SoDetail::getSoNo, key));
                long count = soDetails.stream().filter(item -> item.getQtySoEa().compareTo(item.getQtyAllocationEa()) == 0).count();
                if (count == soDetails.size()){
                    //全部分配
                    soHeader.setAllocStatus(SoAllocStatusEnum.FULL.getCode());
                }else {
                    long counted = soDetails.stream().filter(item -> item.getQtySoEa().compareTo(item.getQtyAllocationEa()) > 0
                            && BigDecimal.ZERO.compareTo(item.getQtyAllocationEa()) < 0).count();
                    if (counted > 0){
                        //部分收货
                        soHeader.setAllocStatus(SoAllocStatusEnum.PARTIAL.getCode());
                    }else{
                        //未收货
                        soHeader.setAllocStatus(SoAllocStatusEnum.UN_ALLOC.getCode());
                    }
                }
                soHeaderService.update(soHeader);
            }

            header.setStatus(WvStatusEnum.WV_NEW.getCode());
            wvHeaderService.update(header);

            if (!cancelItem.isCancelPicking()) {
                //删除拣货单
                this.deletePickingByAlloctions(allocations);
                //删除分配记录
                dao.delete(idList.toArray(new Long[]{}));

            }
        }
    }

    /**
     * 分配页面-一键分配AND 单商品自动分配
     * @param item
     * @return
     */
    @Transactional(timeout = 120)
    public Boolean autoSave(AutoSaveItem item) {
        AllocationTypeEnum allocationEnum = AllocationTypeEnum.getByCode(item.getAllocationType());
        if (ObjUtil.isNull(allocationEnum)){
            throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        }
        return switch (allocationEnum) {
            case OUTBOUND -> outboundAutoSave(item);
            case WAVE_TIMES -> waveTimesAutoSave(item);
            case CFD_ALLOC -> cfdAutoSave(item);
            case PROCES_ALLOC -> processAutoSave(item);
            default -> throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        };
    }

    /**
     * 加工单一键分配
     * @param autoSaveItem
     * @return
     */
    private Boolean processAutoSave(AutoSaveItem autoSaveItem) {
        //获取出库单详情
        List<ProcessTask> details = processTaskDao.find(new ConditionRule().andIn(ProcessTask::getProcessTaskNo,autoSaveItem.getProcessTaskNoList().toArray(new String[]{})));
        //数据转换
        List<TaskAutoAllocationItem> taskAutoAllocationItem = details.stream().map(item -> BeanUtil.toBean(item, TaskAutoAllocationItem.class)).toList();
        taskAutoAllocationItem = taskAutoAllocationItem.stream().filter(item -> {
            item.setQtyAllocationEa(ObjUtil.isNull(item.getQtyAllocationEa()) ? BigDecimal.ZERO : item.getQtyAllocationEa());
            return item.getProcessQuantityEa().compareTo(item.getQtyAllocationEa()) != 0;
        }).toList();

        taskAutoAllocationItem.forEach(item -> {
            item.setUnitColEa(item.getProcessQuantityEa().subtract(ObjUtil.isNull(item.getQtyAllocationEa()) ? BigDecimal.ZERO : item.getQtyAllocationEa()));
            //根据包装规格查询
            List<PackageUnit> packageUnits = packageUnitService.find(new ConditionRule().andEqual(PackageUnit::getPackageId, item.getCombinationsPackageId())
                            .andEqual(PackageUnit::getStatus,YesNoEnum.YES.getCode())
                    , new OrderRule().addDescOrder("sequences_no"));
            PackageUnit itemUnit = packageUnits.stream().filter(unit -> unit.getId().equals(item.getCombinationsPackageUnitId())).findFirst().orElse(null);
            item.setSequencesNo(ObjUtil.isNull(itemUnit) ? packageUnits.getFirst().getSequencesNo() : itemUnit.getSequencesNo());
            for (PackageUnit unit : packageUnits) {
                if (unit.getMinQuantity() < Convert.toInt(item.getUnitColEa()) && unit.getSequencesNo() <= item.getSequencesNo()) {
                    Integer anInt = Convert.toInt(NumberUtil.div(item.getUnitColEa(), unit.getMinQuantity()));
                    int consume = unit.getMinQuantity() * anInt;
                    item.setUnitColEa(item.getUnitColEa().subtract(Convert.toBigDecimal(consume)));
                    String lowerCase = unit.getCode().toLowerCase();
                    //将lowerCase的首字母转换大写
                    String upperCase = lowerCase.substring(0, 1).toUpperCase() + lowerCase.substring(1);
                    String methodName = "setUnitCol"+upperCase;
                    ReflectUtil.invoke(item,methodName,Convert.toBigDecimal(anInt));
                }
            }
        });
        //START
        //获取出库单中的商品详情，并将其转换成HashMap 其中key=商品code，value=商品对象
        Set<Long> skuIds = details.stream().map(ProcessTask::getCombinationsSkuId).collect(Collectors.toSet());
        List<Sku> skuEntityList = skuService.find(new ConditionRule().andIn(Sku::getId, skuIds));
        Map<Long, Sku> skuEntityMap = skuEntityList.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (one, two) -> one));
        //END

        //START
        //获取所有需要用到的周转规则和分配规则
        //周转规则ID集合
        Set<Long> rotationRuleIds = skuEntityList.stream().map(Sku::getRotationRuleId).collect(Collectors.toSet());
        //分配规则ID集合
        Set<Long> allocRuleIds = skuEntityList.stream().map(Sku::getAllocRuleId).collect(Collectors.toSet());
        //只执行启用状态下的周转规则与分配规则
        allocRuleIds = ruleAllocHeaderService.find(new ConditionRule().andIn(RuleAllocHeader::getId, allocRuleIds)
                .andEqual(RuleAllocHeader::getStatus, StatusEnum.YES.getCode())).stream().map(RuleAllocHeader::getId).collect(Collectors.toSet());
        rotationRuleIds= ruleRotationHeaderService.find(new ConditionRule().andIn(RuleRotationHeader::getId,rotationRuleIds)
                        .andEqual(RuleRotationHeader::getStatus,StatusEnum.YES.getCode()))
                .stream().map(RuleRotationHeader::getId).collect(Collectors.toSet());

        List<RuleRotationDetail> rotationDetails = ruleRotationDetailService.find(new ConditionRule().andIn(RuleRotationDetail::getRuleRotationHeaderId,rotationRuleIds));

        Map<Long, List<RuleRotationDetail>> rotationRuleMap = rotationDetails.stream().collect(Collectors.groupingBy(RuleRotationDetail::getRuleRotationHeaderId));
        List<RuleAllocDetail> ruleAllocDetails = ruleAllocDetailService.find(new ConditionRule().andIn(RuleAllocDetail::getRuleAllocHeaderId, allocRuleIds));

        Map<Long, List<RuleAllocDetail>> allocRuleMap = ruleAllocDetails.stream().collect(Collectors.groupingBy(RuleAllocDetail::getRuleAllocHeaderId));
        //END

        //需要用到数据 details，skuEntityMap，rotationRuleMap，allocRuleMap
        Set<String> notRuleCount = new HashSet<>();
        Set<String> notAllocCount = new HashSet<>();
        Set<String> notLocValCount = new HashSet<>();
        for (TaskAutoAllocationItem taskDetails : taskAutoAllocationItem) {
            Sku sku = skuEntityMap.get(taskDetails.getCombinationsSkuId());
            List<RuleRotationDetail> rotationDetailListByTask = rotationRuleMap.get(sku.getRotationRuleId());
            List<RuleAllocDetail> ruleAllocDetailListByTask = allocRuleMap.get(sku.getAllocRuleId());
            if (CollUtil.isEmpty(rotationDetailListByTask) && CollUtil.isEmpty(ruleAllocDetailListByTask)){
                notRuleCount.add(sku.getSkuCode());
                continue;
            }
            if (CollUtil.isEmpty(ruleAllocDetailListByTask)){
                notAllocCount.add(sku.getSkuCode());
                continue;
            }
            //开启线程并行执行
            RuleAllocHeader ruleAllocHeader = ruleAllocHeaderService.findById(ruleAllocDetailListByTask.getFirst().getRuleAllocHeaderId());
            //核心方法
            List<CreatePickingTaskItem> createPickingTaskItemList = autoAllocationService.autoAllocationCoreFunction(taskDetails,sku,rotationDetailListByTask,ruleAllocDetailListByTask,ruleAllocHeader);
            if (CollUtil.isEmpty(createPickingTaskItemList)){
                notLocValCount.add(sku.getSkuCode());
                continue;
            }
            CreatePickingTasksItem condition = new CreatePickingTasksItem();
            condition.setAllocationType(autoSaveItem.getAllocationType());
            createPickingTaskItemList.forEach(item -> {
                item.setProcessTaskNo(taskDetails.getProcessTaskNo());
                item.setProcessOrderNo(taskDetails.getProcessOrderNo());
                item.setProcessTaskDetailsId(taskDetails.getId());
            });
            condition.setCreatePickingTaskItemList(createPickingTaskItemList);
            cfdCreatePickingTasks(condition);
        }
        Set<Long> restSkuIdSet = taskAutoAllocationItem.stream().map(TaskAutoAllocationItem::getCombinationsSkuId).collect(Collectors.toSet());
        if (notRuleCount.size() == restSkuIdSet.size()){
            //表示所有商品都缺失部分或全部规则数据
            throw new ServiceException(SoExcepitonEnum.SO_ALLOC_GOODS_NOT_RULE_ZZ_AND_FP,StrUtil.join("、",notRuleCount));
        }

        if (notLocValCount.size() == restSkuIdSet.size()){
            throw new ServiceException(SoExcepitonEnum.SO_ALLOC_GOODS_NOT_LOC_VALUE,StrUtil.join("、",notLocValCount));
        }

        if (notAllocCount.size() == restSkuIdSet.size()){
            throw new ServiceException(SoExcepitonEnum.SO_ALLOC_GOODS_NOT_ALLOC_RULE_ZH,StrUtil.join("、",notAllocCount));
        }
        return Boolean.TRUE;
    }

    /**
     * 拆分单一键分配
     * @param autoSaveItem
     * @return
     */
    private Boolean cfdAutoSave(AutoSaveItem autoSaveItem) {
        //获取出库单详情
        List<ProcessTaskDetails> details = processTaskDetailsDao.findByIds(autoSaveItem.getProcessTaskDetailIdList().toArray(new Long[]{}));
        //数据转换
        List<TaskDetailsAutoAllocationItem> taskDetailsAutoAllocationItem = details.stream().map(item -> BeanUtil.toBean(item, TaskDetailsAutoAllocationItem.class)).toList();
        taskDetailsAutoAllocationItem = taskDetailsAutoAllocationItem.stream().filter(item -> {
            item.setQtyAllocationEa(ObjUtil.isNull(item.getQtyAllocationEa()) ? BigDecimal.ZERO : item.getQtyAllocationEa());
            return item.getDetailsProcessQuantityEa().compareTo(item.getQtyAllocationEa()) != 0;
        }).toList();

        taskDetailsAutoAllocationItem.forEach(item -> {
            item.setUnitColEa(item.getDetailsProcessQuantityEa().subtract(ObjUtil.isNull(item.getQtyAllocationEa()) ? BigDecimal.ZERO : item.getQtyAllocationEa()));
            //根据包装规格查询
            List<PackageUnit> packageUnits = packageUnitService.find(new ConditionRule().andEqual(PackageUnit::getPackageId, item.getPackageId())
                            .andEqual(PackageUnit::getStatus,YesNoEnum.YES.getCode())
                    , new OrderRule().addDescOrder("sequences_no"));
            PackageUnit itemUnit = packageUnits.stream().filter(unit -> unit.getId().equals(item.getPackageUnitId())).findFirst().orElse(null);
            item.setSequencesNo(ObjUtil.isNull(itemUnit) ? packageUnits.getFirst().getSequencesNo() : itemUnit.getSequencesNo());
            for (PackageUnit unit : packageUnits) {
                if (unit.getMinQuantity() < Convert.toInt(item.getUnitColEa()) && unit.getSequencesNo() <= item.getSequencesNo()) {
                    Integer anInt = Convert.toInt(NumberUtil.div(item.getUnitColEa(), unit.getMinQuantity()));
                    int consume = unit.getMinQuantity() * anInt;
                    item.setUnitColEa(item.getUnitColEa().subtract(Convert.toBigDecimal(consume)));
                    String lowerCase = unit.getCode().toLowerCase();
                    //将lowerCase的首字母转换大写
                    String upperCase = lowerCase.substring(0, 1).toUpperCase() + lowerCase.substring(1);
                    String methodName = "setUnitCol"+upperCase;
                    ReflectUtil.invoke(item,methodName,Convert.toBigDecimal(anInt));
                }
            }
        });
        //START
        //获取出库单中的商品详情，并将其转换成HashMap 其中key=商品code，value=商品对象
        Set<Long> skuIds = details.stream().map(ProcessTaskDetails::getSkuId).collect(Collectors.toSet());
        List<Sku> skuEntityList = skuService.find(new ConditionRule().andIn(Sku::getId, skuIds));
        Map<Long, Sku> skuEntityMap = skuEntityList.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (one, two) -> one));
        //END

        //START
        //获取所有需要用到的周转规则和分配规则
        //周转规则ID集合
        Set<Long> rotationRuleIds = skuEntityList.stream().map(Sku::getRotationRuleId).collect(Collectors.toSet());
        //分配规则ID集合
        Set<Long> allocRuleIds = skuEntityList.stream().map(Sku::getAllocRuleId).collect(Collectors.toSet());
        //只执行启用状态下的周转规则与分配规则
        allocRuleIds = ruleAllocHeaderService.find(new ConditionRule().andIn(RuleAllocHeader::getId, allocRuleIds)
                .andEqual(RuleAllocHeader::getStatus, StatusEnum.YES.getCode())).stream().map(RuleAllocHeader::getId).collect(Collectors.toSet());
        rotationRuleIds= ruleRotationHeaderService.find(new ConditionRule().andIn(RuleRotationHeader::getId,rotationRuleIds)
                        .andEqual(RuleRotationHeader::getStatus,StatusEnum.YES.getCode()))
                .stream().map(RuleRotationHeader::getId).collect(Collectors.toSet());

        List<RuleRotationDetail> rotationDetails = ruleRotationDetailService.find(new ConditionRule().andIn(RuleRotationDetail::getRuleRotationHeaderId,rotationRuleIds));

        Map<Long, List<RuleRotationDetail>> rotationRuleMap = rotationDetails.stream().collect(Collectors.groupingBy(RuleRotationDetail::getRuleRotationHeaderId));
        List<RuleAllocDetail> ruleAllocDetails = ruleAllocDetailService.find(new ConditionRule().andIn(RuleAllocDetail::getRuleAllocHeaderId, allocRuleIds));

        Map<Long, List<RuleAllocDetail>> allocRuleMap = ruleAllocDetails.stream().collect(Collectors.groupingBy(RuleAllocDetail::getRuleAllocHeaderId));
        //END

        //需要用到数据 details，skuEntityMap，rotationRuleMap，allocRuleMap
        Set<String> notRuleCount = new HashSet<>();
        Set<String> notAllocCount = new HashSet<>();
        Set<String> notLocValCount = new HashSet<>();
        for (TaskDetailsAutoAllocationItem taskDetails : taskDetailsAutoAllocationItem) {
            Sku sku = skuEntityMap.get(taskDetails.getSkuId());
            List<RuleRotationDetail> rotationDetailListByTask = rotationRuleMap.get(sku.getRotationRuleId());
            List<RuleAllocDetail> ruleAllocDetailListByTask = allocRuleMap.get(sku.getAllocRuleId());
            if (CollUtil.isEmpty(rotationDetailListByTask) && CollUtil.isEmpty(ruleAllocDetailListByTask)){
                notRuleCount.add(sku.getSkuCode());
                continue;
            }
            if (CollUtil.isEmpty(ruleAllocDetailListByTask)){
                notAllocCount.add(sku.getSkuCode());
                continue;
            }
            //开启线程并行执行
            RuleAllocHeader ruleAllocHeader = ruleAllocHeaderService.findById(ruleAllocDetailListByTask.getFirst().getRuleAllocHeaderId());
            //核心方法
            List<CreatePickingTaskItem> createPickingTaskItemList = autoAllocationService.autoAllocationCoreFunction(taskDetails,sku,rotationDetailListByTask,ruleAllocDetailListByTask,ruleAllocHeader);
            if (CollUtil.isEmpty(createPickingTaskItemList)){
                notLocValCount.add(sku.getSkuCode());
                continue;
            }
            CreatePickingTasksItem condition = new CreatePickingTasksItem();
            condition.setAllocationType(autoSaveItem.getAllocationType());
            createPickingTaskItemList.forEach(item -> {
                item.setProcessTaskNo(taskDetails.getProcessTaskNo());
                item.setProcessOrderNo(taskDetails.getProcessOrderNo());
                item.setProcessTaskDetailsId(taskDetails.getId());
            });
            condition.setCreatePickingTaskItemList(createPickingTaskItemList);
            cfdCreatePickingTasks(condition);
        }
        Set<Long> restSkuIdSet = taskDetailsAutoAllocationItem.stream().map(TaskDetailsAutoAllocationItem::getSkuId).collect(Collectors.toSet());
        if (notRuleCount.size() == restSkuIdSet.size()){
            //表示所有商品都缺失部分或全部规则数据
            throw new ServiceException(SoExcepitonEnum.SO_ALLOC_GOODS_NOT_RULE_ZZ_AND_FP,StrUtil.join("、",notRuleCount));
        }

        if (notLocValCount.size() == restSkuIdSet.size()){
            throw new ServiceException(SoExcepitonEnum.SO_ALLOC_GOODS_NOT_LOC_VALUE,StrUtil.join("、",notLocValCount));
        }

        if (notAllocCount.size() == restSkuIdSet.size()){
            throw new ServiceException(SoExcepitonEnum.SO_ALLOC_GOODS_NOT_ALLOC_RULE_ZH,StrUtil.join("、",notAllocCount));
        }
        return Boolean.TRUE;
    }

    /**
     * 出库单一键分配 and 自动分配
     * @param saveItem
     * @return
     */
    private Boolean waveTimesAutoSave(AutoSaveItem saveItem) {
        //获取除了波次单分配的所有入库单明细，并根据周转规则进行自动分配
        return autoSaveFrontMethod(saveItem.getSoDetailIds(),AllocationTypeEnum.WAVE_TIMES,saveItem);
    }

    /**
     * 波次单一键分配 and 自动分配
     * @param saveItem
     * @return
     */
    private Boolean outboundAutoSave(AutoSaveItem saveItem) {
        //获取除了波次单分配的所有入库单明细，并根据周转规则进行自动分配
        List<WvDetail> detailList = wvDetailService.find(new ConditionRule().andIn(WvDetail::getSoDetailId, saveItem.getSoDetailIds()).andNotEqual(WvDetail::getStatus,YesNoEnum.YES.getCode()));
        List<Long> soDetailIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(detailList)){
            //去除与波次相关的id
            List<Long> ids = detailList.stream().map(WvDetail::getSoDetailId).toList();
            soDetailIdList = saveItem.getSoDetailIds().stream().filter(item -> !ids.contains(item)).toList();
        }else{
            soDetailIdList.addAll(saveItem.getSoDetailIds());
        }
        return autoSaveFrontMethod(soDetailIdList,AllocationTypeEnum.OUTBOUND,saveItem);
    }

    /**
     * 自动分配业务逻辑前置方法
     * @param soDetailIdList
     * @return
     */
    private @NotNull Boolean autoSaveFrontMethod(List<Long> soDetailIdList,AllocationTypeEnum typeEnum,AutoSaveItem saveItem) {
        if (CollUtil.isEmpty(soDetailIdList)){
            throw new ServiceException(SoExcepitonEnum.SO_ALLOC_GOODS_EMPTY);
        }
        //获取出库单详情
        List<SoDetail> details = soDetailService.findByIds(soDetailIdList.toArray(new Long[]{}));
        //数据转换
        List<SoDetailAutoAllocationItem> soDetailAutoAllocationItemList = details.stream().map(item -> BeanUtil.toBean(item, SoDetailAutoAllocationItem.class)).toList();
        soDetailAutoAllocationItemList = soDetailAutoAllocationItemList.stream().filter(item -> {
            item.setQtyAllocationEa(ObjUtil.isNull(item.getQtyAllocationEa()) ? BigDecimal.ZERO : item.getQtyAllocationEa());
            return item.getQtySoEa().compareTo(item.getQtyAllocationEa()) != 0;
        }).toList();

        soDetailAutoAllocationItemList.forEach(item -> {
            item.setUnitColEa(item.getQtySoEa().subtract(ObjUtil.isNull(item.getQtyAllocationEa()) ? BigDecimal.ZERO : item.getQtyAllocationEa()));
            //根据包装规格查询
            List<PackageUnit> packageUnits = packageUnitService.find(new ConditionRule().andEqual(PackageUnit::getPackageId, item.getPackageId())
                            .andEqual(PackageUnit::getStatus,YesNoEnum.YES.getCode())
                    , new OrderRule().addDescOrder("sequences_no"));
            PackageUnit itemUnit = packageUnits.stream().filter(unit -> unit.getId().equals(item.getPackageUnitId())).findFirst().orElse(null);
            item.setSequencesNo(ObjUtil.isNull(itemUnit) ? packageUnits.getFirst().getSequencesNo() : itemUnit.getSequencesNo());
            for (PackageUnit unit : packageUnits) {
                if (unit.getMinQuantity() < Convert.toInt(item.getUnitColEa()) && unit.getSequencesNo() <= item.getSequencesNo()) {
                    Integer anInt = Convert.toInt(NumberUtil.div(item.getUnitColEa(), unit.getMinQuantity()));
                    int consume = unit.getMinQuantity() * anInt;
                    item.setUnitColEa(item.getUnitColEa().subtract(Convert.toBigDecimal(consume)));
                    String lowerCase = unit.getCode().toLowerCase();
                    //将lowerCase的首字母转换大写
                    String upperCase = lowerCase.substring(0, 1).toUpperCase() + lowerCase.substring(1);
                    String methodName = "setUnitCol"+upperCase;
                    ReflectUtil.invoke(item,methodName,Convert.toBigDecimal(anInt));
                }
            }
        });
        //START
        //获取出库单中的商品详情，并将其转换成HashMap 其中key=商品code，value=商品对象
        Set<Long> skuIds = details.stream().map(SoDetail::getSkuId).collect(Collectors.toSet());
        List<Sku> skuEntityList = skuService.find(new ConditionRule().andIn(Sku::getId, skuIds));
        Map<Long, Sku> skuEntityMap = skuEntityList.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (one, two) -> one));
        //END

        //START
        //获取所有需要用到的周转规则和分配规则
        //周转规则ID集合
        Set<Long> rotationRuleIds = skuEntityList.stream().map(Sku::getRotationRuleId).collect(Collectors.toSet());
        //分配规则ID集合
        Set<Long> allocRuleIds = skuEntityList.stream().map(Sku::getAllocRuleId).collect(Collectors.toSet());
        //只执行启用状态下的周转规则与分配规则
       allocRuleIds = ruleAllocHeaderService.find(new ConditionRule().andIn(RuleAllocHeader::getId, allocRuleIds)
                .andEqual(RuleAllocHeader::getStatus, StatusEnum.YES.getCode())).stream().map(RuleAllocHeader::getId).collect(Collectors.toSet());
       rotationRuleIds= ruleRotationHeaderService.find(new ConditionRule().andIn(RuleRotationHeader::getId,rotationRuleIds)
               .andEqual(RuleRotationHeader::getStatus,StatusEnum.YES.getCode()))
               .stream().map(RuleRotationHeader::getId).collect(Collectors.toSet());

        List<RuleRotationDetail> rotationDetailList = ruleRotationDetailService.find(new ConditionRule().andIn(RuleRotationDetail::getRuleRotationHeaderId,rotationRuleIds));

        Map<Long, List<RuleRotationDetail>> rotationRuleMap = rotationDetailList.stream().collect(Collectors.groupingBy(RuleRotationDetail::getRuleRotationHeaderId));
        List<RuleAllocDetail> ruleAllocDetailList = ruleAllocDetailService.find(new ConditionRule().andIn(RuleAllocDetail::getRuleAllocHeaderId, allocRuleIds));

        Map<Long, List<RuleAllocDetail>> allocRuleMap = ruleAllocDetailList.stream().collect(Collectors.groupingBy(RuleAllocDetail::getRuleAllocHeaderId));
        //END

        //需要用到数据 details，skuEntityMap，rotationRuleMap，allocRuleMap
        return autoAllocationBatchExecution(soDetailAutoAllocationItemList,skuEntityMap,rotationRuleMap,allocRuleMap,typeEnum,saveItem);
    }

    /**
     * 自动分配-分批执行
     * @param soDetailAutoAllocationItemList 出库单明细集合
     * @param skuEntityMap 所需使用的商品MAP key=商品ID
     * @param rotationRuleMap 所需使用的周转规则MAP key=周转规则ID
     * @param allocRuleMap 所需使用的分配规则MAP key=分配规则ID
     * @return 整体执行成果，TRUE=成果，FLASE=失败
     */
    private Boolean autoAllocationBatchExecution(List<SoDetailAutoAllocationItem> soDetailAutoAllocationItemList, Map<Long, Sku> skuEntityMap,
                                                 Map<Long, List<RuleRotationDetail>> rotationRuleMap,
                                                 Map<Long, List<RuleAllocDetail>> allocRuleMap,AllocationTypeEnum typeEnum,AutoSaveItem saveItem){
        Set<String> notRuleCount = new HashSet<>();
        Set<String> notAllocCount = new HashSet<>();
        Set<String> notLocValCount = new HashSet<>();
        for (SoDetailAutoAllocationItem soDetail : soDetailAutoAllocationItemList) {
            Sku sku = skuEntityMap.get(soDetail.getSkuId());
            List<RuleRotationDetail> rotationDetailList = rotationRuleMap.get(sku.getRotationRuleId());
            List<RuleAllocDetail> ruleAllocDetailList = allocRuleMap.get(sku.getAllocRuleId());
            if (CollUtil.isEmpty(rotationDetailList) && CollUtil.isEmpty(ruleAllocDetailList)){
                notRuleCount.add(sku.getSkuCode());
                continue;
            }
            if (CollUtil.isEmpty(ruleAllocDetailList)){
                notAllocCount.add(sku.getSkuCode());
                continue;
            }
            //开启线程并行执行
            RuleAllocHeader ruleAllocHeader = ruleAllocHeaderService.findById(ruleAllocDetailList.getFirst().getRuleAllocHeaderId());
            //核心方法
            List<CreatePickingTaskItem> createPickingTaskItemList = autoAllocationService.autoAllocationCoreFunction(soDetail,sku,rotationDetailList,ruleAllocDetailList,ruleAllocHeader);
            if (CollUtil.isEmpty(createPickingTaskItemList)){
                notLocValCount.add(sku.getSkuCode());
                continue;
            }
            switch (typeEnum) {
                case OUTBOUND -> {
                    CreatePickingTasksItem condition = new CreatePickingTasksItem();
                    condition.setAllocationType(typeEnum.getCode());
                    createPickingTaskItemList.forEach(item -> {
                        item.setSoNo(saveItem.getSoNo());
                        item.setSoDetailNo(soDetail.getSoDetailNo());
                    });
                    condition.setCreatePickingTaskItemList(createPickingTaskItemList);
                    outboundCreatePickingTasks(condition);
                }
                case WAVE_TIMES -> {
                    createPickingTaskItemList.forEach(item -> {
                        item.setWvNo(saveItem.getWvNo());
                    });
                    waveTimesCreatePickingTasks(createPickingTaskItemList);
                }
                default -> throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
            };
        }
        Set<Long> restSkuIdSet = soDetailAutoAllocationItemList.stream().map(SoDetailAutoAllocationItem::getSkuId).collect(Collectors.toSet());
        if (notRuleCount.size() == restSkuIdSet.size()){
            //表示所有商品都缺失部分或全部规则数据
            throw new ServiceException(SoExcepitonEnum.SO_ALLOC_GOODS_NOT_RULE_ZZ_AND_FP,StrUtil.join("、",notRuleCount));
        }

        if (notLocValCount.size() == restSkuIdSet.size()){
            throw new ServiceException(SoExcepitonEnum.SO_ALLOC_GOODS_NOT_LOC_VALUE,StrUtil.join("、",notLocValCount));
        }

        if (notAllocCount.size() == restSkuIdSet.size()){
            throw new ServiceException(SoExcepitonEnum.SO_ALLOC_GOODS_NOT_ALLOC_RULE_ZH,StrUtil.join("、",notAllocCount));
        }
        return Boolean.TRUE;
    }
}
