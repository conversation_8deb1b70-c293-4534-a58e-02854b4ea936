package com.chinaservices.wms.module.stock.reportingLosses.service;


import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.wms.module.stock.reportingLosses.dao.InvLossesDetailsSnDao;
import com.chinaservices.wms.module.stock.reportingLosses.model.InvLossesDetailsSn;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InvLossesDetailsSnService
 * <AUTHOR>
 * @Date 2025/7/11 16:34
 * @Description
 * @Version 1.0
 */
@Service
public class InvLossesDetailsSnService extends ModuleBaseServiceSupport<InvLossesDetailsSnDao, InvLossesDetailsSn, Long> {


    /**
     *@Description 根据报损单号查询序列号
     *@Param list
     *@Return * {@link List< InvLossesDetailsSn> }
     *@Date 2025/7/11 17:26
     *<AUTHOR>
     **/
    public List<InvLossesDetailsSn> findByLossesNoList(List<String> list) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InvLossesDetailsSn::getLossesNo, list);
        return dao.find(conditionRule);
    }

    /**
     *@Description 删除对应序列号
     *@Param lossesNoList
     *@Return Void
     *@Date 2025/7/11 17:51
     *<AUTHOR>
     **/
    public void deleteByLossesNoList(List<String> lossesNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InvLossesDetailsSn::getLossesNo, lossesNoList);
        dao.delete(conditionRule);
    }
}
