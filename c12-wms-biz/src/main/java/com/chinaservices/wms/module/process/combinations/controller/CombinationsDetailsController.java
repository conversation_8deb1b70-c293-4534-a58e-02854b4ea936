package com.chinaservices.wms.module.process.combinations.controller;


import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.common.domain.IdCondition;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsDetailsPageCondition;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsDetailsQuery;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsPageCondition;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsQuery;
import com.chinaservices.wms.module.process.combinations.service.CombinationsDetailsService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 组合件子件
 * <AUTHOR>
 * @Date 2025/4/15 14:33
 * @Description
 * @Version 1.0
 */
@RestController
@RequestMapping("/api/process/combinations/details")
public class CombinationsDetailsController {

    @Autowired
    private CombinationsDetailsService combinationsDetailsService;

    /**
     *  组合件子件分页查询
     *@Param condition
     *@Return * {@link ResponseData< PageResult<CombinationsDetailsQuery>> }
     *@Date 2025/4/15 14:36
     *<AUTHOR>
     **/
    @PostMapping("/page")
    public ResponseData<PageResult<CombinationsDetailsQuery>> page(@RequestBody @Valid CombinationsDetailsPageCondition condition) {
        PageResult<CombinationsDetailsQuery> pageResult = combinationsDetailsService.page(condition);
        return ResponseData.success(pageResult);
    }

    /**
     *  根据主键id批量删除
     *@Param condition
     *@Return * {@link ResponseData< Void> }
     *@Date 2025/4/15 13:53
     *<AUTHOR>
     **/
    @PostMapping("/batchDel")
    public ResponseData<Void> batchDel(@RequestBody @Valid IdCondition condition) {
        combinationsDetailsService.batchDel(condition);
        return ResponseData.success();
    }
}
