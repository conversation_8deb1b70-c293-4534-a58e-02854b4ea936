package com.chinaservices.wms.module.stock.reportingLosses.dao;

import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.sqlid.stock.reportingLosses.ReportingLossesSqlId;
import com.chinaservices.wms.module.stock.reportingLosses.domain.InvReportingLossesPageCondition;
import com.chinaservices.wms.module.stock.reportingLosses.domain.InvReportingLossesQuery;
import com.chinaservices.wms.module.stock.reportingLosses.model.InvReportingLosses;
import org.springframework.stereotype.Repository;

@Repository
public class InvReportingLossesDao extends ModuleBaseDaoSupport<InvReportingLosses, Long> {

    /**
     *@Description 分页查询
     *@Param queryCondition
     *@Return * {@link PageResult< InvReportingLossesQuery> }
     *@Date 2025/7/11 16:44
     *<AUTHOR>
     **/
    public PageResult<InvReportingLossesQuery> getPage(InvReportingLossesPageCondition queryCondition) {
        return sqlExecutor.page(InvReportingLossesQuery.class, ReportingLossesSqlId.GET_PAGE, queryCondition);
    }
}