package com.chinaservices.wms.module.performance.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "cs_kpi_cycle")
public class KpiCycle extends ModuleBaseModel {

    /**
     * 考试周期类型
     */
    private String ruleType;

    /**
     * 考核开始时间
     */
    private Date ruleStartTime;

    /**
     * 考核结束时间
     */
    private Date ruleEndTime;

    /**
     * sign
     */
    private String sign;
}
