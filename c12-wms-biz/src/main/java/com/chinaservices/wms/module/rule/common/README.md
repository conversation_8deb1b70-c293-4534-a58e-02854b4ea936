# 批量上架规则处理功能

## 概述

基于现有的 `checkRulePaTaskAndGetLoc` 方法，新增了批量处理功能，支持大批量收货明细的上架规则处理。该功能在不修改原有方法的基础上，提供了高性能、事务安全的批量处理能力。

## 核心特性

### 1. 性能优化
- **批量查询**: 预先批量查询商品信息、上架规则和规则明细，减少数据库交互
- **并行处理**: 使用虚拟线程并行处理不同批次的数据
- **分批处理**: 将大数据集分割成小批次，避免单个事务过大
- **内存优化**: 合理使用缓存，避免重复查询

### 2. 事务管理
- **分批事务**: 每个批次独立事务，避免大事务锁定
- **异常隔离**: 单个记录异常不影响整个批次
- **回滚控制**: 使用 `@Transactional(rollbackFor = Exception.class)` 确保异常回滚

### 3. 异常处理
- **详细错误收集**: 记录每个失败记录的详细错误信息
- **错误分类统计**: 按错误类型进行统计分析
- **处理结果报告**: 提供完整的处理结果摘要

## 主要类和方法

### 1. RulePaCommonService (扩展)

#### 新增方法：
```java
/**
 * 批量检查上架规则并获取库位
 * @param asnReceiveList 收货明细列表
 * @return 批量处理结果
 */
public BatchRulePaResult batchCheckRulePaTaskAndGetLoc(List<AsnReceive> asnReceiveList)
```

#### 核心处理流程：
1. 批量查询商品信息
2. 批量查询上架规则信息  
3. 批量查询规则明细信息
4. 分批并行处理数据
5. 汇总处理结果

### 2. BatchRulePaService (新增)

业务服务层，提供更高级的批量处理功能：

```java
/**
 * 批量处理收货明细的上架规则
 */
public BatchRulePaResult processBatchRulePa(List<AsnReceive> asnReceiveList)

/**
 * 批量处理并更新收货明细的库位信息
 */
public BatchRulePaResult processBatchRulePaAndUpdate(List<AsnReceive> asnReceiveList)
```

### 3. 结果类

#### BatchRulePaResult
- 封装批量处理的完整结果
- 包含成功/失败统计
- 提供处理时间分析
- 支持失败原因摘要

#### RulePaProcessResult  
- 单个处理结果
- 支持成功/失败/无规则三种状态
- 包含详细错误信息

## 使用示例

### 基本使用

```java
@Autowired
private RulePaCommonService rulePaCommonService;

// 批量处理收货明细
List<AsnReceive> asnReceiveList = getAsnReceiveList();
BatchRulePaResult result = rulePaCommonService.batchCheckRulePaTaskAndGetLoc(asnReceiveList);

// 检查处理结果
if (result.isAllSuccess()) {
    log.info("全部处理成功，成功数量: {}", result.getSuccessCount());
} else {
    log.warn("部分处理失败，失败数量: {}, 失败详情: {}", 
             result.getFailureCount(), result.getFailureSummary());
}
```

### 高级使用

```java
@Autowired
private BatchRulePaService batchRulePaService;

// 批量处理并更新
BatchRulePaResult result = batchRulePaService.processBatchRulePaAndUpdate(asnReceiveList);

// 获取详细报告
String summary = batchRulePaService.getProcessingSummary(result);
log.info("处理报告:\n{}", summary);
```

## 配置参数

### 批量处理参数
- `DEFAULT_BATCH_SIZE = 100`: 默认批次大小
- `MAX_PARALLEL_THREADS = 10`: 最大并行线程数

### 性能调优建议
1. **批次大小**: 根据数据量和系统性能调整批次大小
2. **并行度**: 根据CPU核心数和数据库连接池大小调整并行度
3. **内存使用**: 监控内存使用情况，避免OOM

## 监控和日志

### 关键日志
- 批量处理开始/结束日志
- 性能统计日志（处理时间、速度等）
- 异常详情日志
- 处理结果摘要日志

### 监控指标
- 处理速度（条/秒）
- 成功率
- 平均处理时间
- 异常分布

## 测试

提供了完整的单元测试：
- 基本功能测试
- 大批量性能测试  
- 异常处理测试

运行测试：
```bash
mvn test -Dtest=RulePaCommonServiceBatchTest
```

## 最佳实践

### 1. 数据准备
- 确保输入数据的完整性
- 预先验证必要字段
- 合理控制批量大小

### 2. 异常处理
- 监控失败率，及时处理异常
- 分析失败原因，优化处理逻辑
- 建立异常重试机制

### 3. 性能优化
- 定期监控处理性能
- 根据业务量调整配置参数
- 优化数据库查询性能

### 4. 事务管理
- 合理设置批次大小，平衡性能和事务安全
- 监控事务执行时间
- 避免长时间锁定资源

## 扩展性

该批量处理框架具有良好的扩展性：
- 可以轻松调整批量处理策略
- 支持添加新的处理规则
- 可以集成到现有的业务流程中
- 支持自定义异常处理逻辑

## 注意事项

1. **数据一致性**: 分批处理可能导致部分成功，需要业务层面处理数据一致性
2. **资源消耗**: 并行处理会增加CPU和内存消耗，需要合理配置
3. **数据库连接**: 并行处理需要足够的数据库连接池
4. **异常恢复**: 建议实现失败记录的重试机制
