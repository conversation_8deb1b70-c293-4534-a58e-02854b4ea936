package com.chinaservices.wms.module.passive.take.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 无源任务明细表
 * cs_passive_auto_inventory_details
 */
@Data
@Entity
@Table(name = "cs_passive_auto_inventory_details")
public class PassiveAutoInventoryDetails extends ModuleBaseModel {

    /**
     * 主表uuid
     */
    private String inventoryUuid;

    /**
     * 容器号
     */
    private String palletNum;

    /**
     * 标签号
     */
    private String tagNo;

    /**
     * 数量ea
     */
    private BigDecimal quantity;

    /**
     * 是否扫描：0-否 1-是
     */
    private String scan;

}