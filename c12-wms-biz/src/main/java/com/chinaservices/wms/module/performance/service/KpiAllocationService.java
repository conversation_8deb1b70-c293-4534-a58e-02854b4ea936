package com.chinaservices.wms.module.performance.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.core.redis.util.RedisDelayQueue;
import com.chinaservices.core.redis.util.RedisKeyGenerator;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.jpa.support.rule.OrderRule;
import com.chinaservices.module.base.ModuleBaseModel;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.wms.common.constant.RedisKeys;
import com.chinaservices.wms.common.enums.performance.*;
import com.chinaservices.wms.common.enums.so.SoPickingStatusEnum;
import com.chinaservices.wms.common.exception.KpiExceptionEnum;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.asn.service.AsnHeaderService;
import com.chinaservices.wms.module.asn.pa.model.CsPaTask;
import com.chinaservices.wms.module.asn.pa.service.CsPaTaskService;
import com.chinaservices.wms.module.fee.fee.domain.IdsItem;
import com.chinaservices.wms.module.performance.domain.*;
import com.chinaservices.wms.module.performance.model.*;
import com.chinaservices.wms.module.performance.model.AutoScoreAsyncItem;
import com.chinaservices.wms.module.so.picking.model.SoPicking;
import com.chinaservices.wms.module.so.picking.service.SoPickingService;
import com.chinaservices.wms.module.stock.take.model.InventoryStocktakeTask;
import com.chinaservices.wms.module.stock.take.service.InventoryStocktakeTaskService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 人员绩效指标分配业务接口
 * <AUTHOR>
 */
@Slf4j
@Service
public class KpiAllocationService {

    @Autowired
    private KpiHeaderService kpiHeaderService;

    @Autowired
    private KpiCycleService kpiCycleService;

    @Autowired
    private KpiRecordService kpiRecordService;
    @Autowired
    private KpiIndexService kpiIndexService;
    @Autowired
    private KpiIndexIntervalService kpiIndexIntervalService;
    @Autowired
    private AsnHeaderService asnHeaderService;
    @Autowired
    private SoPickingService soPickingService;
    @Autowired
    private InventoryStocktakeTaskService inventoryStocktakeTaskService;
    @Autowired
    private CsPaTaskService csPaTaskService;

    /**
     * 分页查询
     * @param condition
     * @return
     */
    public PageResult<KpiHeaderQuery> page(KpiHeaderPageCondition condition) {
        //如果没有选择考核周期,则默认使用当前周期
//        if (StrUtil.isEmpty(condition.getSign())){
//            KpiCycle first = kpiCycleService.findFirst(new ConditionRule(), new OrderRule().addDescOrder("create_time"));
//            condition.setSign(first.getSign());
//        }
        PageResult<KpiHeaderQuery> page = kpiHeaderService.page(condition);
        List<KpiHeaderQuery> rows = page.getRows();
        if (CollUtil.isNotEmpty(rows)){
            rows.forEach(row -> {
                if (StrUtil.isNotEmpty(row.getKpiIndex())){
                    row.setIndexItems(JSONUtil.toList(row.getKpiIndex(), KpiIndexPageItem.class));
                    String join = row.getIndexItems().stream().map(KpiIndexPageItem::getIndexName).collect(Collectors.joining("、"));
                    row.setKpiIndexName(join);
                }
            });
        }
        return page;
    }

    /**
     * 查询明细
     * @param id
     * @return
     */
    public KpiHeaderQuery detailById(Long id) {
        KpiHeader kpiHeader = kpiHeaderService.findById(id);
        KpiHeaderQuery rest = new KpiHeaderQuery();
        if (kpiHeader != null){
            BeanUtil.copyProperties(kpiHeader,rest);
            if (StrUtil.isNotEmpty(rest.getKpiIndex())) {
                rest.setIndexItems(JSONUtil.toList(rest.getKpiIndex(), KpiIndexPageItem.class));
            }
        }
        return rest;
    }

    /**
     * 保存考核单
     * @param item
     * @return
     */
    public Boolean saveHeader(KpiHeaderItem item) {
        verification(item);
        KpiHeader header = new KpiHeader();
        BeanUtil.copyProperties(item,header);
        header.setKpiIndex(JSONUtil.toJsonStr(item.getIndexItems()));
        //生成流水号
        header.setKpiStatus(KpiStatusEnum.NO_INVENTORY.getCode());
        kpiHeaderService.preSave(header);
        return kpiHeaderService.insert(header);
    }

    /**
     * 验证新增和编辑时的数据完整性
     * @param item
     */
    private void verification(KpiHeaderItem item) {
        if (StrUtil.isEmpty(item.getUserName())){
            throw new ServiceException(KpiExceptionEnum.ASSESSMENT_USER_CANNOT_BE_EMPTY);
        }
        if (StrUtil.isEmpty(item.getPositionName())){
            throw new ServiceException(KpiExceptionEnum.ASSESSMENT_POST_CANNOT_BE_EMPTY);
        }
        if (StrUtil.isEmpty(item.getAssessor())){
            throw new ServiceException(KpiExceptionEnum.ASSESSMENT_CANNOT_BE_EMPTY);
        }
        if (CollUtil.isEmpty(item.getIndexItems())){
            throw new ServiceException(KpiExceptionEnum.ASSESSMENT_INDEX_CANNOT_BE_EMPTY);
        }
    }

    public Boolean deleteHeader(IdsItem item) {
        if (CollUtil.isEmpty(item.getIds())){
            throw new ServiceException(KpiExceptionEnum.ASSESSMENT_ID_LIST_CANNOT_BE_EMPTY);
        }
        kpiHeaderService.delete(item.getIds().toArray(new Long[0]));
        return Boolean.TRUE;
    }


    public Boolean editHeader(KpiHeaderItem item) {
        verification(item);
        KpiHeader header = new KpiHeader();
        BeanUtil.copyProperties(item,header);
        header.setKpiIndex(JSONUtil.toJsonStr(item.getIndexItems()));
        kpiHeaderService.preSave(header);
        return kpiHeaderService.update(header);
    }

    public Boolean examine(IdsItem item) {
        List<KpiHeader> service = kpiHeaderService.findByIds(item.getIds().toArray(new Long[0]));
        service.forEach(a -> a.setKpiStatus(KpiStatusEnum.YES_INVENTORY.getCode()));
//        List<KpiHeader> kpiHeaders = service.stream().filter(a -> ObjUtil.isNull(a.getAssessorFraction())).toList();
//        if (CollUtil.isEmpty(kpiHeaders)){
//            String string = kpiHeaders.stream().map(KpiHeader::getUserName).collect(Collectors.joining("、"));
//            throw new ServiceException(KpiExceptionEnum.ASSESSMENT_USER_NOT_SCORE, string);
//        }
        for (KpiHeader header : service) {
            List<KpiRecord> records = kpiRecordService.find(new ConditionRule().andEqual(KpiRecord::getKpiId, header.getId())
                    .andEqual(KpiRecord::getSign, header.getSign()));
            BigDecimal reduce = records.stream().map(KpiRecord::getSource).filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            List<KpiIndexPageItem> pageItems = JSONUtil.toList(header.getKpiIndex(), KpiIndexPageItem.class);
            header.setAssessorTime(new Date());
            header.setAssessorFraction(NumberUtil.div(reduce,pageItems.size()));
        }
        kpiHeaderService.batchUpdate(service);
        return Boolean.TRUE;
    }

    public Boolean editCycle(KpiCycleItem item) {
        Date date = new Date();
        KpiCycle cycle = new KpiCycle();
        BeanUtil.copyProperties(item,cycle);
        kpiCycleService.preSave(cycle);
        //设置考核周期时,增加sign,并更新原数据结束时间为当前时间
        KpiCycle first = kpiCycleService.findFirst(new ConditionRule(), new OrderRule().addDescOrder("create_time"));
        if (ObjUtil.isNotNull(first)){
            first.setRuleEndTime(date);
            kpiCycleService.update(first);
        }
        //删除原延迟队列数据
        cycle.setSign(StrUtil.uuid());
        if (ObjUtil.isNull(cycle.getRuleStartTime())){
            cycle.setRuleStartTime(date);
        }
        Date dateTime = null;
        if (PerformanceRuleTypeEnum.MANUAL.getCode().equals(cycle.getRuleType())){
            //设置结束时间
            dateTime = item.getRuleEndTime();

            if (DateUtil.compare(cycle.getRuleStartTime(), date) > 0){
                //开始时间大于当前时间,设置延迟队列在到达开始时间后更改header的状态
                long delay = (cycle.getRuleStartTime().getTime() - date.getTime()) / 1000;
                RedisDelayQueue.delayQueue(RedisKeyGenerator.gen(RedisKeys.KPI_CYCLE_QUEUE_FIRST), JSONUtil.toJsonStr(cycle),delay, TimeUnit.SECONDS);
            }else{
                //直接生效
                updateHeaderStatusByRedisDelay(cycle);
            }

        }else if (PerformanceRuleTypeEnum.WEEKLY.getCode().equals(cycle.getRuleType())){
            //每周
            dateTime = DateUtil.offsetWeek(cycle.getRuleStartTime(), 1);
            cycle.setRuleEndTime(dateTime);
        }else if (PerformanceRuleTypeEnum.MONTHLY.getCode().equals(cycle.getRuleType())){
            //每月
            dateTime = DateUtil.offsetMonth(cycle.getRuleStartTime(), 1);
        }
        cycle.setRuleEndTime(dateTime);
        kpiCycleService.insert(cycle);
        //设置延迟队列
        setRedisDelay(cycle);
        //设置开始时间的
        return Boolean.TRUE;
    }

    public void updateHeaderStatusByRedisDelay(KpiCycle cycle){
        List<KpiHeader> kpiHeaders = kpiHeaderService.find(new ConditionRule());
        kpiHeaders.forEach(a -> {
            a.setKpiStatus(KpiStatusEnum.NO_INVENTORY.getCode());
        });
        kpiHeaderService.batchUpdate(kpiHeaders);
    }

    private void setRedisDelay(KpiCycle cycle) {
        long delay = 0;
        PerformanceRuleTypeEnum ruleTypeEnum = EnumUtil.getBy(PerformanceRuleTypeEnum::getCode, cycle.getRuleType());
        if (PerformanceRuleTypeEnum.WEEKLY.equals(ruleTypeEnum)){
            //每周
            Date date = new Date();
            DateTime dateTime = DateUtil.offsetWeek(date, 1);
            delay = (dateTime.getTime() - date.getTime()) / 1000;
            RedisDelayQueue.delayQueue(RedisKeyGenerator.gen(RedisKeys.KPI_CYCLE_QUEUE), cycle.getSign(),delay, TimeUnit.SECONDS);
        }else if (PerformanceRuleTypeEnum.MONTHLY.equals(ruleTypeEnum)){
            //每月
            Date date = new Date();
            DateTime dateTime = DateUtil.offsetMonth(date, 1);
            delay = (dateTime.getTime() - date.getTime()) / 1000;
            RedisDelayQueue.delayQueue(RedisKeyGenerator.gen(RedisKeys.KPI_CYCLE_QUEUE), cycle.getSign(),delay, TimeUnit.SECONDS);
        }
        log.info("设置每周考核周期,距离下次考核周期还有{}秒,当前设置考核周期类型为:{}",delay,ruleTypeEnum.getName());
    }


    /**
     * 延迟队列使用方法
     * @param sign 对应到期考核周期
     */
    public void autoUpdateCycle(String sign){
        KpiCycle first = kpiCycleService.findFirst(new ConditionRule().andEqual(KpiCycle::getSign, sign));
        if (ObjUtil.isNull(first)){
            return;
        }
        KpiCycle addEntity = updateKpiCycleAndReturnNewKpiCycle(first);
        //设置用户状态
        List<KpiHeader> kpiHeaders = kpiHeaderService.find(new ConditionRule());
        kpiHeaders.forEach(a -> {
            a.setKpiStatus(KpiStatusEnum.NO_INVENTORY.getCode());
            if (!first.getSign().equals(a.getSign())){
                a.setAssessorFraction(null);
                a.setAssessorTime(null);
            }
        });
        kpiHeaderService.batchUpdate(kpiHeaders);
        //设置新的周期
        addEntity.setRuleStartTime(first.getRuleEndTime());
        addEntity.setSign(StrUtil.uuid());
        kpiCycleService.insert(addEntity);
        setRedisDelay(addEntity);
    }

    /**
     * 更新旧数据,并返回新的数据
     * @param first
     * @return
     */
    public KpiCycle updateKpiCycleAndReturnNewKpiCycle(KpiCycle first){
        KpiCycle addEntity = new KpiCycle();
        BeanUtil.copyProperties(first,addEntity);
        first.setRuleEndTime(new Date());
        first.setModifier(first.getCreator());
        first.setModifyTime(new Date());
        kpiCycleService.update(first);
        return addEntity;
    }

    public KpiCycleItem searchCycle() {
        KpiCycle cycle = kpiCycleService.findFirst(new ConditionRule(), new OrderRule().addDescOrder("create_time"));
        KpiCycleItem item = new KpiCycleItem();
        BeanUtil.copyProperties(cycle,item);
        if (DateUtil.compare(item.getRuleEndTime(),  new Date()) < 0){
            return null;
        }
        return item;
    }

    public KpiRecordQuery scoreRecord(KpiRecordCondition condition) {
        KpiHeader kpiHeader = kpiHeaderService.findById(condition.getKpiId());
        String sign = "";
        if (StrUtil.isEmpty(condition.getSign())){
            KpiCycle first = kpiCycleService.findFirst(new ConditionRule(), new OrderRule().addDescOrder("create_time"));
            sign = first.getSign();
        }else{
            sign = condition.getSign();
        }
        //
        //验证是否是当前周期的数据
        if (sign.equals(kpiHeader.getSign())){
            return getRecordQueryByEmpty(condition.getKpiId(), kpiHeader);
        }
        List<KpiRecord> recordList = kpiRecordService.find(new ConditionRule().andEqual(KpiRecord::getKpiId, condition.getKpiId()).andEqual(KpiRecord::getSign, sign));
        //验证是否是空数据
        if (CollUtil.isEmpty(recordList)){
            //无数据记录
            return getRecordQueryByEmpty(condition.getKpiId(), kpiHeader);
        }
        //存在记录
        List<KpiIndexPageItem> list = JSONUtil.toList(kpiHeader.getKpiIndex(), KpiIndexPageItem.class);
        List<KpiIndex> indexList = kpiIndexService.find(new ConditionRule().andIn(KpiIndex::getId, list.stream().map(KpiIndexPageItem::getId).toList()));
        Map<Long, KpiRecord> recordMap = recordList.stream().collect(Collectors.toMap(KpiRecord::getIndexId, Function.identity(), (a, b) -> a));
        List<KpiRecordItem> kpiRecordItems = new ArrayList<>();
        for (KpiIndex kpiIndex : indexList) {
            KpiRecord record = recordMap.get(kpiIndex.getId());
            KpiRecordItem item = new KpiRecordItem();
            item.setKpiId(condition.getKpiId());
            item.setIndexId(kpiIndex.getId());
            item.setIndexName(kpiIndex.getIndexName());
            item.setIndexType(kpiIndex.getIndexType());
            if (ObjUtil.isNull(record)){
                item.setSource(BigDecimal.ZERO);
                item.setExamineTime("");
                item.setRuleType("");
            }else {
                item.setSource(record.getSource());
                item.setExamineTime(record.getExamineTime());
                item.setRuleType(record.getRuleType());
            }
            kpiRecordItems.add(item);
        }
        //计算kpiRecordItems中source字段的总和
        BigDecimal zfs = kpiRecordItems.stream().map(KpiRecordItem::getSource).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        KpiRecordQuery kpiRecordQuery = new KpiRecordQuery();
        kpiRecordQuery.setZfs(zfs);
        kpiRecordQuery.setZdf(NumberUtil.div(kpiRecordQuery.getZfs(), new BigDecimal(kpiRecordItems.size())));
        kpiRecordQuery.setKpiRecordItems(kpiRecordItems);
        return kpiRecordQuery;
    }

    private KpiRecordQuery getRecordQueryByEmpty(Long id, KpiHeader kpiHeader) {
        List<KpiIndexPageItem> list = JSONUtil.toList(kpiHeader.getKpiIndex(), KpiIndexPageItem.class);
        List<KpiRecordItem> kpiRecordItems = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)){
            List<Long> longList = list.stream().map(KpiIndexPageItem::getId).toList();
            List<KpiIndex> indexList = kpiIndexService.find(new ConditionRule().andIn(KpiIndex::getId, longList));
            Map<Long, KpiIndex> indexMap = indexList.stream().collect(Collectors.toMap(ModuleBaseModel::getId, Function.identity(), (a, b) -> a));
            for (KpiIndexPageItem pageItem : list) {
                KpiIndex kpiIndex = indexMap.get(pageItem.getId());
                KpiRecordItem item = new KpiRecordItem();
                item.setKpiId(id);
                item.setIndexId(kpiIndex.getId());
                item.setIndexName(kpiIndex.getIndexName());
                item.setExamineTime("");
                item.setRuleType("");
                item.setIndexType(kpiIndex.getIndexType());
                item.setSource(BigDecimal.ZERO);
                kpiRecordItems.add(item);
            }
        }
        return new KpiRecordQuery().setZdf(BigDecimal.ZERO).setZfs(BigDecimal.ZERO)
                .setKpiRecordItems(kpiRecordItems);
    }

    public Boolean scoreHand(List<KpiRecordItem> item) {
        Long kpiId = item.getFirst().getKpiId();
        List<KpiRecordItem> addItemList = item.stream().filter(a -> ObjUtil.isNull(a.getId()) || a.getId() == 0L).toList();
        List<KpiRecordItem> updateItemList = item.stream().filter(a -> ObjUtil.isNotNull(a.getId()) && a.getId() > 0L).toList();
        KpiCycle first = kpiCycleService.findFirst(new ConditionRule(), new OrderRule().addDescOrder("create_time"));
        KpiHeader kpiHeader = kpiHeaderService.findById(kpiId);
        kpiHeader.setKpiStatus(KpiStatusEnum.SUBMITTED.getCode());
        if (StrUtil.isEmpty(kpiHeader.getSign()) || !first.getSign().equals(kpiHeader.getSign())){
            //执行更新
            kpiHeader.setSign(first.getSign());
            kpiHeaderService.preSave(kpiHeader);
        }
        //更新打分人数据
        SessionUserInfo userInfo = SessionContext.get();
        kpiHeader.setAssessor(userInfo.getUserName());
        kpiHeader.setAssessorId(userInfo.getUserId());
        kpiHeaderService.update(kpiHeader);
        if (CollUtil.isNotEmpty(addItemList)){
            List<KpiRecord> list = Lists.newArrayList();
            addItemList.stream().forEach(a -> {
                KpiRecord record = new KpiRecord();
                BeanUtil.copyProperties(a,record);
                record.setSign(kpiHeader.getSign());
                list.add(record);
            });
            kpiRecordService.batchInsert(list);
        }

        if (CollUtil.isNotEmpty(updateItemList)){
            List<KpiRecord> list = Lists.newArrayList();
            updateItemList.stream().forEach(a -> {
                KpiRecord record = new KpiRecord();
                BeanUtil.copyProperties(a,record);
                record.setSign(kpiHeader.getSign());
                list.add(record);
            });
            kpiRecordService.batchUpdate(list);
        }
        return Boolean.TRUE;
    }

    /**
     * 根据考合格单用户ID 查询相关考核指标信息和分数
     * 如果自\动考核分数为0则执行自动计算考核分数
     * @param id
     * @return
     */
    public KpiRecordQuery scoreRecordById(Long id) {
        //获取此次绩效周期的标识数据来定位该用户的考核信息
        KpiCycle first = kpiCycleService.findFirst(new ConditionRule(), new OrderRule().addDescOrder("create_time"));
        if (ObjUtil.isNull(first)){
            throw new ServiceException(KpiExceptionEnum.PLEASE_SET_THE_ASSESSMENT_CYCLE_FIRST);
        }
        KpiRecordQuery query = new KpiRecordQuery();
        //查询出所有考核指标信息
        KpiHeader kpiHeader = kpiHeaderService.findById(id);
        List<KpiIndexPageItem> pageItems = JSONUtil.toList(kpiHeader.getKpiIndex(), KpiIndexPageItem.class);
        List<KpiIndex> indexList = kpiIndexService.findByIds(pageItems.stream().map(KpiIndexPageItem::getId).toList().toArray(new Long[]{}));

        //查询评分记录
        List<KpiRecord> KpiRecords = kpiRecordService.find(new ConditionRule().andEqual(KpiRecord::getSign, first.getSign()));
        if (CollUtil.isEmpty(KpiRecords)){
            //没有查询出相关记录信息,筛选出自动评分项,执行自动计算
            List<KpiIndex> autoList = indexList.stream().filter(a -> PerformanceIndexTypeEnum.AUTO.getCode().equals(a.getIndexType())).toList();
            Map<String, KpiIndex> indexMap = indexList.stream().collect(Collectors.toMap(KpiIndex::getIndexNo, Function.identity(), (a, b) -> a));
            //将每个自动评分功能单独开一个线程并进行并行执行,在最后判断执行结果后执行合并
            List<CompletableFuture<AutoScoreAsyncItem>> futures = autoList.stream().map(a -> CompletableFuture.supplyAsync(() -> autoScoreAsync(a,kpiHeader,first))).toList();
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            try {
                // 等待所有任务完成
                allOf.get();
            }catch (Exception e){
                log.error(StrUtil.format("批量收货确认异常：{}",e.getMessage()),e);
                throw new ServiceException(KpiExceptionEnum.AUTOMATIC_RATING_FAILED);
            }
            //将线程中返回的结果取出并加载到新的集合中
            List<AutoScoreAsyncItem> collect = futures.stream().map(CompletableFuture::join).toList();
            List<KpiRecordItem> kpiRecordItems = new ArrayList<>();
            collect.forEach(a -> {
                KpiIndex kpiIndex = indexMap.get(a.getIndexNo());
                if (ObjUtil.isNotNull(kpiIndex)){
                    kpiRecordItems.add(new KpiRecordItem().setKpiId(kpiHeader.getId())
                            .setIndexName(kpiIndex.getIndexName())
                            .setSource(a.getScore())
                            .setRuleType(first.getRuleType())
                            .setExamineTime(DateUtil.now()).setIndexType(kpiIndex.getIndexType())
                            .setIndexId(kpiIndex.getId()));
                }
            });

            List<KpiIndex> handList = indexList.stream().filter(a -> PerformanceIndexTypeEnum.HAND.getCode().equals(a.getIndexType())).toList();
            if (CollUtil.isNotEmpty(handList)){
                handList.forEach(a -> {
                    kpiRecordItems.add(new KpiRecordItem().setKpiId(kpiHeader.getId())
                            .setIndexName(a.getIndexName())
                            .setSource(BigDecimal.ZERO)
                            .setRuleType(first.getRuleType())
                            .setIndexType(PerformanceIndexTypeEnum.HAND.getCode())
                            .setExamineTime(DateUtil.now())
                            .setIndexId(a.getId()));
                });
            }

            query.setKpiRecordItems(kpiRecordItems);
            query.setZfs(kpiRecordItems.stream().map(KpiRecordItem::getSource).reduce(BigDecimal::add).get());
            query.setZdf(NumberUtil.div(query.getZfs(),indexList.size()));
            return query;
        }else{
            //有评分记录
            //indexList = 需要手动或自动评分的指标集合
            //KpiRecords = 已经评分的指标记录和分数信息
            List<KpiRecordItem> kpiRecordItems = new ArrayList<>();
            //过滤出所有未评分指标
            List<Long> list = KpiRecords.stream().map(KpiRecord::getIndexId).toList();
            List<KpiIndex> wpfList = indexList.stream().filter(a -> !list.contains(a.getId())).toList();
            if (CollUtil.isNotEmpty(wpfList)){
                //01 验证自动评分项目是否存在未自动评分数据
                List<KpiIndex> autoList = wpfList.stream().filter(a -> PerformanceIndexTypeEnum.AUTO.getCode().equals(a.getIndexType())).toList();
                Map<String, KpiIndex> indexMap = indexList.stream().collect(Collectors.toMap(KpiIndex::getIndexNo, Function.identity(), (a, b) -> a));
                //将每个自动评分功能单独开一个线程并进行并行执行,在最后判断执行结果后执行合并
                List<CompletableFuture<AutoScoreAsyncItem>> futures = autoList.stream().map(a -> CompletableFuture.supplyAsync(() -> autoScoreAsync(a,kpiHeader,first))).toList();
                CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                try {
                    // 等待所有任务完成
                    allOf.get();
                }catch (Exception e){
                    log.error(StrUtil.format("批量收货确认异常：{}",e.getMessage()),e);
                    throw new ServiceException(KpiExceptionEnum.AUTOMATIC_RATING_FAILED);
                }
                //将线程中返回的结果取出并加载到新的集合中
                List<AutoScoreAsyncItem> collect = futures.stream().map(CompletableFuture::join).toList();
                //01-01 将未评分数据进行自动评分,并将结果封装到kpiRecordItems
                collect.forEach(a -> {
                    KpiIndex kpiIndex = indexMap.get(a.getIndexNo());
                    if (ObjUtil.isNotNull(kpiIndex)){
                        kpiRecordItems.add(new KpiRecordItem().setKpiId(kpiHeader.getId())
                                .setIndexName(kpiIndex.getIndexName())
                                .setSource(a.getScore())
                                .setRuleType(first.getRuleType())
                                .setExamineTime(DateUtil.now()).setIndexType(kpiIndex.getIndexType())
                                .setIndexId(kpiIndex.getId()));
                    }
                });
                //02 验证是否有未手动评分数据
                List<KpiIndex> handList = wpfList.stream().filter(a -> PerformanceIndexTypeEnum.HAND.getCode().equals(a.getIndexType())).toList();
                if (CollUtil.isNotEmpty(handList)){
                    //02-01 将未评分数据封装到 kpiRecordItems 中
                    handList.forEach(a -> {
                        kpiRecordItems.add(new KpiRecordItem().setKpiId(kpiHeader.getId())
                                .setIndexName(a.getIndexName())
                                .setSource(BigDecimal.ZERO)
                                .setRuleType(first.getRuleType()).setIndexType(PerformanceIndexTypeEnum.HAND.getCode())
                                .setExamineTime(DateUtil.now())
                                .setIndexId(a.getId()));
                    });
                }
            }else {
                Map<Long, KpiIndex> indexMap = indexList.stream().collect(Collectors.toMap(KpiIndex::getId, Function.identity(), (a, b) -> a));
                //将已评分的数据封装到kpiRecordItems中
                for (KpiRecord kpiRecord : KpiRecords) {
                    KpiIndex kpiIndex = indexMap.get(kpiRecord.getIndexId());
                    if (ObjUtil.isNull(kpiIndex)){
                        continue;
                    }
                    kpiRecordItems.add(new KpiRecordItem().setKpiId(kpiRecord.getKpiId())
                            .setIndexName(kpiIndex.getIndexName())
                            .setSource(kpiRecord.getSource())
                            .setRuleType(kpiRecord.getRuleType()).setIndexType(kpiIndex.getIndexType())
                            .setExamineTime(kpiRecord.getExamineTime())
                            .setIndexId(kpiIndex.getId()));
                }
            }
            query.setKpiRecordItems(kpiRecordItems);
            query.setZfs(kpiRecordItems.stream().map(KpiRecordItem::getSource).reduce(BigDecimal::add).get());
            query.setZdf(NumberUtil.div(query.getZfs(),indexList.size()));
            return query;
        }
    }


    /**
     * 自动评分总线
     * @param index
     * @return
     */
    private AutoScoreAsyncItem autoScoreAsync(KpiIndex index, KpiHeader kpiHeader, KpiCycle first){
        PerformanceDataSourceEnum dataSourceEnum = EnumUtil.getBy(PerformanceDataSourceEnum::getCode, index.getDataSource());
        return switch (dataSourceEnum) {
            case ASN -> asnAutoScore(index,kpiHeader,first);
            case RECEIVE -> receiveAutoScore(index,kpiHeader,first);
            case PICKING -> pickingAutoScore(index,kpiHeader,first);
            case PROCESS -> processAutoScore(index,kpiHeader,first);
            case COUNT -> countAutoScore(index,kpiHeader,first);
            case PA_TASK -> paTaskAutoScore(index,kpiHeader,first);
            case ARTIFICIAL -> new AutoScoreAsyncItem();
            default -> throw new ServiceException(KpiExceptionEnum.INCORRECT_TYPE);
        };
    }

    /**
     * 上架任务-自动评分
     * @param index
     * @return
     */
    private AutoScoreAsyncItem paTaskAutoScore(KpiIndex index, KpiHeader kpiHeader, KpiCycle first) {
        AutoScoreAsyncItem item = new AutoScoreAsyncItem();

        //获取日期范围
        DateIntervalItem interval = getDateInterval(first);
        //获取区间
        List<KpiIndexInterval> intervalList = kpiIndexIntervalService.find(new ConditionRule().andEqual(KpiIndexInterval::getIndexNo, index.getIndexNo())
                ,new OrderRule().addAscOrder("quantity"));
        //获取该用户创建的结算单

        Long count = csPaTaskService.count(new ConditionRule().andEqual(CsPaTask::getPaPersonId, kpiHeader.getUserId())
                .andGreaterEqual(AsnHeader::getCreateTime, interval.getStartTime()));
        //通过count匹配出所在区间,并计取出该区间的分数
        KpiIndexInterval indexInterval = findMatchingIntervalBinarySearch(intervalList, count);

        item.setScore(ObjUtil.isNull(indexInterval) ? BigDecimal.ZERO : indexInterval.getScoringRules());
        item.setIndexNo(index.getIndexNo());
        return item;
    }

    /**
     * 盘点单-自动评分
     * @param index
     * @return
     */
    private AutoScoreAsyncItem countAutoScore(KpiIndex index, KpiHeader kpiHeader, KpiCycle first) {
        AutoScoreAsyncItem item = new AutoScoreAsyncItem();

        //获取日期范围
        DateIntervalItem interval = getDateInterval(first);
        //获取区间
        List<KpiIndexInterval> intervalList = kpiIndexIntervalService.find(new ConditionRule().andEqual(KpiIndexInterval::getIndexNo, index.getIndexNo())
                ,new OrderRule().addAscOrder("quantity"));
        //获取该用户创建的结算单

        Long count = inventoryStocktakeTaskService.count(new ConditionRule().andEqual(InventoryStocktakeTask::getPersonnelId, kpiHeader.getUserId())
                .andGreaterEqual(AsnHeader::getCreateTime, interval.getStartTime()));
        //通过count匹配出所在区间,并计取出该区间的分数
        KpiIndexInterval indexInterval = findMatchingIntervalBinarySearch(intervalList, count);

        item.setScore(ObjUtil.isNull(indexInterval) ? BigDecimal.ZERO : indexInterval.getScoringRules());
        item.setIndexNo(index.getIndexNo());
        return item;
    }

    /**
     * 加工单-自动评分
     * TODO: 不知道查询那张表
     * @param index
     * @return
     */
    private AutoScoreAsyncItem processAutoScore(KpiIndex index, KpiHeader kpiHeader, KpiCycle first) {
        return null;
    }

    /**
     * 拣货单-自动评分
     * @param index
     * @return
     */
    private AutoScoreAsyncItem pickingAutoScore(KpiIndex index, KpiHeader kpiHeader, KpiCycle first) {
        AutoScoreAsyncItem item = new AutoScoreAsyncItem();

        //获取日期范围
        DateIntervalItem interval = getDateInterval(first);
        //获取区间
        List<KpiIndexInterval> intervalList = kpiIndexIntervalService.find(new ConditionRule().andEqual(KpiIndexInterval::getIndexNo, index.getIndexNo())
                ,new OrderRule().addAscOrder("quantity"));
        //获取该用户创建的结算单

        Long count = soPickingService.count(new ConditionRule().andEqual(SoPicking::getCreator, kpiHeader.getUserId())
                .andGreaterEqual(AsnHeader::getCreateTime, interval.getStartTime())
                .andEqual(SoPicking::getStatus,SoPickingStatusEnum.COMPLETE_PICKING.getCode()));
        //通过count匹配出所在区间,并计取出该区间的分数
        KpiIndexInterval indexInterval = findMatchingIntervalBinarySearch(intervalList, count);

        item.setScore(ObjUtil.isNull(indexInterval) ? BigDecimal.ZERO : indexInterval.getScoringRules());
        item.setIndexNo(index.getIndexNo());
        return item;
    }

    /**
     * 收货单-自动评分
     * @param index
     * @return
     */
    private AutoScoreAsyncItem receiveAutoScore(KpiIndex index, KpiHeader kpiHeader, KpiCycle first) {
        AutoScoreAsyncItem item = new AutoScoreAsyncItem();

        //获取日期范围
        DateIntervalItem interval = getDateInterval(first);
        //获取区间
        List<KpiIndexInterval> intervalList = kpiIndexIntervalService.find(new ConditionRule().andEqual(KpiIndexInterval::getIndexNo, index.getIndexNo())
                ,new OrderRule().addAscOrder("quantity"));
        //获取该用户创建的结算单
        Long count = asnHeaderService.count(new ConditionRule().andEqual(AsnHeader::getReceiverId, kpiHeader.getUserId())
                .andGreaterEqual(AsnHeader::getCreateTime, interval.getStartTime()));
        //通过count匹配出所在区间,并计取出该区间的分数
        KpiIndexInterval indexInterval = findMatchingIntervalBinarySearch(intervalList, count);

        item.setScore(ObjUtil.isNull(indexInterval) ? BigDecimal.ZERO : indexInterval.getScoringRules());
        item.setIndexNo(index.getIndexNo());
        return item;
    }

    /**
     * 入库单-自动评分
     * @param index
     * @return
     */
    private AutoScoreAsyncItem asnAutoScore(KpiIndex index, KpiHeader kpiHeader, KpiCycle first) {
        AutoScoreAsyncItem item = new AutoScoreAsyncItem();

        //获取日期范围
        DateIntervalItem interval = getDateInterval(first);
        //获取区间
        List<KpiIndexInterval> intervalList = kpiIndexIntervalService.find(new ConditionRule().andEqual(KpiIndexInterval::getIndexNo, index.getIndexNo())
                ,new OrderRule().addAscOrder("quantity"));
        //获取该用户创建的结算单
        Long count = asnHeaderService.count(new ConditionRule().andEqual(AsnHeader::getCreator, kpiHeader.getUserId())
                .andGreaterEqual(AsnHeader::getCreateTime, interval.getStartTime()));
        //通过count匹配出所在区间,并计取出该区间的分数
        KpiIndexInterval indexInterval = findMatchingIntervalBinarySearch(intervalList, count);

        item.setScore(ObjUtil.isNull(indexInterval) ? BigDecimal.ZERO : indexInterval.getScoringRules());
        item.setIndexNo(index.getIndexNo());
        return item;
    }

    /**
     * 使用二分查找优化匹配区间
     * @param intervals 有序列表 ASC排序
     * @param target 实际数值
     * @return 匹配项
     */
    public KpiIndexInterval findMatchingIntervalBinarySearch(List<KpiIndexInterval> intervals, Long target) {
        int low = 0;
        int high = intervals.size() - 1;
        KpiIndexInterval result = null;

        while (low <= high) {
            int mid = (low + high) / 2;
            KpiIndexInterval midInterval = intervals.get(mid);
            Long midValue = midInterval.getQuantity();

            if (midValue <= target) {
                result = midInterval; // 记录可能的匹配
                low = mid + 1; // 继续向右找更大的匹配
            } else {
                high = mid - 1; // 向左查找
            }
        }
        return result; // 返回最大的 <= target 的区间
    }

    private DateIntervalItem getDateInterval(KpiCycle first) {
        DateIntervalItem item = new DateIntervalItem();
        item.setStartTime(first.getRuleStartTime());
        return item;
    }

    public List<KpiAssessmentTimeQuery> assessmentTimeList(KpiRecordCondition condition) {
        List<KpiCycle> kpiCycles = new ArrayList<>();
        if (ObjUtil.isNull(condition.getKpiId())){
            //查询全部
            kpiCycles = kpiCycleService.find(new ConditionRule(),new OrderRule().addDescOrder("create_time"));
        }else{
            List<KpiRecord> recordList = kpiRecordService.find(new ConditionRule().andEqual(KpiRecord::getKpiId, condition.getKpiId()));
            if (CollUtil.isEmpty(recordList)){
                return null;
            }
            Set<String> kpiSignSet = recordList.stream().map(KpiRecord::getSign).collect(Collectors.toSet());
            kpiCycleService.find(new ConditionRule().andIn(KpiCycle::getSign, kpiSignSet),new OrderRule().addDescOrder("create_time"));
        }

        return kpiCycles.stream().map(item1 -> {
            KpiAssessmentTimeQuery query = new KpiAssessmentTimeQuery();
            query.setSign(item1.getSign());
            query.setExamineStartTime(item1.getRuleStartTime());
            query.setExamineEndTime(item1.getRuleEndTime());
            return query;
        }).toList();
    }

    public Boolean refuseExamine(IdsItem item) {
        List<KpiHeader> service = kpiHeaderService.findByIds(item.getIds().toArray(new Long[0]));
        service.forEach(a -> a.setKpiStatus(KpiStatusEnum.NO_INVENTORY.getCode()));
        List<KpiHeader> kpiHeaders = service.stream().filter(a -> ObjUtil.isNotNull(a.getAssessorFraction())).toList();
        if (CollUtil.isNotEmpty(kpiHeaders)){
            KpiCycle first = kpiCycleService.findFirst(new ConditionRule(),new OrderRule().addDescOrder("create_time"));
            for (KpiHeader kpiHeader : kpiHeaders) {
                List<KpiRecord> recordList = kpiRecordService.find(new ConditionRule().andEqual(KpiRecord::getKpiId, kpiHeader.getId())
                        .andEqual(KpiRecord::getSign, first.getSign()));
                if (CollUtil.isNotEmpty(recordList)){
                    List<Long> list = recordList.stream().map(KpiRecord::getId).toList();
                    kpiRecordService.delete(list.toArray(new Long[0]));
                }
            }
        }
//        for (KpiHeader header : service) {
//            header.setAssessor(null);
//            header.setAssessorId(null);
//            header.setAssessorTime(null);
//            header.setAssessorFraction(null);
//        }
        kpiHeaderService.batchUpdate(service);
        kpiRecordService.delete(new ConditionRule().andIn(KpiRecord::getSign,service.stream().map(KpiHeader::getSign).toList()));
        return Boolean.TRUE;
    }
}
