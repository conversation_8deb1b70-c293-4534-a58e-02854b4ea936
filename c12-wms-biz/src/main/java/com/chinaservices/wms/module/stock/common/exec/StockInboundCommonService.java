package com.chinaservices.wms.module.stock.common.exec;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.util.JsonUtil;
import com.chinaservices.wms.common.constant.StatusConstant;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.common.exception.SoExcepitonEnum;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.common.model.qty.bo.StockTransactionBO;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageQuery;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.chinaservices.wms.module.warehouse.loc.domain.WarehouseLocPageCondition;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

import static com.chinaservices.wms.common.constant.IdRuleConstant.TRACE_ID;
import static com.chinaservices.wms.common.exception.InvExceptionEnum.*;

/**
 * 入库相关
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class StockInboundCommonService extends StockCommonBaseService {

    @Override
    protected void checkInit(InvLotLocQtyBO invLotLocQtyBO) {
        log.info("【库存操作】实体相关逻辑处理。。");
        switch (invLotLocQtyBO.getTransactionType()) {
            case TransactionType.TRAN_RCV, TransactionType.TRAN_QC_RCV:
                if (StrUtil.isBlank(invLotLocQtyBO.getLotNum())) {
                    throw new ServiceException(LOC_NUM_IS_NULL);
                }
                if (ObjectUtil.isNull(invLotLocQtyBO.getLocId()) || StrUtil.isBlank(invLotLocQtyBO.getLocCode())) {
                    WarehouseLoc warehouse = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), invLotLocQtyBO.getWarehouseId());
                    invLotLocQtyBO.setLocId(warehouse.getId());
                    invLotLocQtyBO.setLocCode(warehouse.getLocCode());
                }
                //收货状态下，容器和目标容器一致
                invLotLocQtyBO.setToPallet(invLotLocQtyBO.getPalletNum());
                break;
            case TransactionType.TRAN_PA, TransactionType.TRAN_OUT_PA:
                if (ObjectUtil.isNull(invLotLocQtyBO.getToLocId())) {
                    throw new ServiceException(TO_LOC_IS_NULL_ID);
                }
                if (StatusConstant.YES.equals(invLotLocQtyBO.getWhetherSerialController()) && CollUtil.isEmpty(invLotLocQtyBO.getSkuSn())) {
                    throw new ServiceException(SKU_SN_IS_NULL, invLotLocQtyBO.getSkuCode());
                }
                //上架的时候 批次号就是当前批次
                super.checkInvMixSkuMixLot(new InvLotLocQtyBO().setLocId(invLotLocQtyBO.getToLocId()).setLocCode(invLotLocQtyBO.getToLocCode()).setWarehouseId(invLotLocQtyBO.getWarehouseId()).setSkuId(invLotLocQtyBO.getSkuId()).setLotNum(invLotLocQtyBO.getLotNum()));
                break;
            case TransactionType.TRAN_CR_PA, TransactionType.TRAN_CR_OUT_PA:
                if (ObjectUtil.isNull(invLotLocQtyBO.getToLocId())) {
                    throw new ServiceException(TO_LOC_IS_NULL_ID);
                }
                if (StatusConstant.YES.equals(invLotLocQtyBO.getWhetherSerialController()) && CollUtil.isEmpty(invLotLocQtyBO.getSkuSn())) {
                    throw new ServiceException(SKU_SN_IS_NULL, invLotLocQtyBO.getSkuCode());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void delete(InvLotLoc invLotLoc, String transactionType) {
        if (!Lists.newArrayList(TransactionType.TRAN_RCV, TransactionType.TRAN_QC_RCV).contains(transactionType)) {
            InvLotLoc temp = super.invLotLoc(invLotLoc);
            log.info("清理STAGE数据->{}", JsonUtil.toJson(temp));
            if (temp.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                invLotLocService.delete(temp.getId());
            }
        }
    }

    @Override
    protected List<InvLotLoc> compileInvLotLoc(InvLotLocQtyBO invLotLocQtyBO) {
        List<InvLotLoc> invLotLocs = Lists.newArrayList();
        WarehouseLoc warehouse;
        InvLotLoc invLotLoc;
        switch (invLotLocQtyBO.getTransactionType()) {
            case TransactionType.TRAN_RCV, TransactionType.TRAN_QC_RCV: //收货确认
                //增加库存数
                invLotLoc = getInvLotloc(invLotLocQtyBO, false);
                invLotLoc.setQty(invLotLocQtyBO.getUpdateNum());
                invLotLocs.add(invLotLoc);
                break;
            case TransactionType.TRAN_PA: //上架
                if (ObjectUtil.isNull(invLotLocQtyBO.getToLocId())) {
                    throw new ServiceException(TO_LOC_IS_NULL_ID);
                }
                //增加库位库存  目标库位增加库存
                invLotLoc = getInvLotloc(invLotLocQtyBO, false).setLocId(invLotLocQtyBO.getToLocId()).setLocCode(invLotLocQtyBO.getToLocCode()).setPalletNum(invLotLocQtyBO.getToPallet());
                invLotLocs.add(invLotLoc);
                //减去 stage库存
                warehouse = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), invLotLocQtyBO.getWarehouseId());
                invLotLocs.add(getInvLotloc(invLotLocQtyBO, true).setLocId(warehouse.getId()).setLocCode(warehouse.getLocCode()).setLotNum(invLotLocQtyBO.getLotNum()).setPalletNum(invLotLocQtyBO.getPalletNum()));
                break;
            case TransactionType.TRAN_CR_PA: //取消上架
                //目标库位减去库位库存
                invLotLoc = getInvLotloc(invLotLocQtyBO, true).setLocId(invLotLocQtyBO.getLocId()).setLocCode(invLotLocQtyBO.getLocCode()).setPalletNum(invLotLocQtyBO.getPalletNum());
                invLotLocs.add(invLotLoc);
                //增加 stage库存
                warehouse = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), invLotLocQtyBO.getWarehouseId());
                invLotLocs.add(getInvLotloc(invLotLocQtyBO, false).setLocId(warehouse.getId()).setLocCode(warehouse.getLocCode()).setPalletNum(invLotLocQtyBO.getToPallet()));
                break;
            case TransactionType.TRAN_OUT_PA: //出库上架
                //增加库位库存
                invLotLoc = getInvLotloc(invLotLocQtyBO, false)
                        .setLocId(invLotLocQtyBO.getToLocId())
                        .setLocCode(invLotLocQtyBO.getToLocCode())
                        .setPalletNum(invLotLocQtyBO.getToPallet());
                //获取原始库存
                InvLotLoc temp = super.invLotLoc(invLotLocQtyBO);
                //清空拣货数
                invLotLoc.setQtyPk(temp.getQtyPk().negate());
                invLotLocs.add(invLotLoc);
                //减去 stage库存
                warehouse = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), invLotLocQtyBO.getWarehouseId());
                invLotLocs.add(getInvLotloc(invLotLocQtyBO, true)
                        .setLocId(warehouse.getId())
                        .setLocCode(warehouse.getLocCode())
                        .setPalletNum(invLotLocQtyBO.getPalletNum()));
                break;
            case TransactionType.TRAN_CR_OUT_PA: //取消出库上架
                //减去目标库位库存
                invLotLocs.add(getInvLotloc(invLotLocQtyBO, true).setLocId(invLotLocQtyBO.getLocId()).setLocCode(invLotLocQtyBO.getLocCode()).setPalletNum(invLotLocQtyBO.getPalletNum()));
                //增加 stage库存
                warehouse = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), invLotLocQtyBO.getWarehouseId());
                invLotLocs.add(getInvLotloc(invLotLocQtyBO, false).setLocId(warehouse.getId()).setLocCode(warehouse.getLocCode()).setPalletNum(invLotLocQtyBO.getToLocNum()));
                break;
            default:
                break;
        }
        return invLotLocs;
    }

    @Override
    protected List<StockTransactionBO> compileStockTransaction(InvLotLocQtyBO invLotLocQtyBO) {
        List<StockTransactionBO> list = Lists.newArrayList();
        StockTransactionBO stockTransactionBO = BeanUtil.copyProperties(invLotLocQtyBO, StockTransactionBO.class);
        InvLotLocQtyBO from = getINvLotLocQtyFm(invLotLocQtyBO);
        InvLotLocQtyBO to = getINvLotLocQtyTo(invLotLocQtyBO);
        stockTransactionBO.setTotalQtyBO(to);
        stockTransactionBO.setFmLocQty(from);
        WarehouseLoc warehouseLoc;
        switch (invLotLocQtyBO.getTransactionType()) {
            case TransactionType.TRAN_RCV: //收货确认，质检收货确认
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), invLotLocQtyBO.getWarehouseId());
                to = getINvLotLocQtyFm(invLotLocQtyBO);
                //设置 stage库位 源库位
                to.setLocCode(warehouseLoc.getLocCode());
                to.setLocId(warehouseLoc.getId());
                from.setLocCode(null);
                from.setLocId(null);
                from.setPalletNum(TRACE_ID);
                list.add(stockTransactionBO);
                break;
            case TransactionType.TRAN_PA: //上架
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), invLotLocQtyBO.getWarehouseId());
                //源库位 stage
                from.setLocCode(warehouseLoc.getLocCode());
                from.setLocId(warehouseLoc.getId());
                //目标库位 = toLocId
                list.add(stockTransactionBO);
                break;
            case TransactionType.TRAN_OUT_PA: //出库上架
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), invLotLocQtyBO.getWarehouseId());
                //出库默认是SORTATION
                from.setLocCode(warehouseLoc.getLocCode());
                from.setLocId(warehouseLoc.getId());
                to.setPalletNum(invLotLocQtyBO.getToPallet());
                list.add(stockTransactionBO);
                break;
            case TransactionType.TRAN_CR_PA: //取消上架
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), invLotLocQtyBO.getWarehouseId());
                //源库位
                from.setLocCode(invLotLocQtyBO.getLocCode());
                from.setLocId(invLotLocQtyBO.getLocId());
                //目标库位 SORTATION
                to.setLocId(warehouseLoc.getId());
                to.setLocCode(warehouseLoc.getLocCode());
                list.add(stockTransactionBO);
                break;
            case TransactionType.TRAN_CR_OUT_PA: //取消出库上架
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), invLotLocQtyBO.getWarehouseId());
                //源库位
                from.setLocCode(invLotLocQtyBO.getLocCode());
                from.setLocId(invLotLocQtyBO.getLocId());
                //目标库位
                to.setLocId(warehouseLoc.getId());
                to.setLocCode(warehouseLoc.getLocCode());
                list.add(stockTransactionBO);
                break;
            default:
                break;
        }
        return list;
    }

    /**
     * 类型参考  【腾讯文档】库存数据逻辑梳理表
     * <a href="https://docs.qq.com/sheet/DRWpHck1FWWhZZFVJ?tab=BB08J2">...</a>  入库
     *
     * @return 返回类型
     */
    @Override
    public List<String> actionType() {
        return Lists.newArrayList(TransactionType.TRAN_RCV, TransactionType.TRAN_QC_RCV, TransactionType.TRAN_PA, TransactionType.TRAN_CR_PA, TransactionType.TRAN_OUT_PA, TransactionType.TRAN_CR_OUT_PA);
    }

    /**
     * 获取库存查询列表
     * 使用条件 商品名称、商品编码
     *
     * @param pageCondition
     * @return
     */
    public PageResult<LocListPageQuery> page(LocListPageCondition pageCondition) {
        if (pageCondition.getPageSize() == null || pageCondition.getPageNumber() == null) {
            pageCondition.setPageSize(10);
            pageCondition.setPageNumber(1);
        }
        WarehouseLocPageCondition condition = BeanUtil.copyProperties(pageCondition, WarehouseLocPageCondition.class);
        //组装参数
        switch (pageCondition.getLotSearchType()) {
            case SKU_CODE -> condition.setSkuCode(pageCondition.getKeyword());
            case SKU_NAME -> condition.setSkuName(pageCondition.getKeyword());
            default -> throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        }
        //查询主表信息
        return warehouseLocService.pageLocList(condition);
    }
}
