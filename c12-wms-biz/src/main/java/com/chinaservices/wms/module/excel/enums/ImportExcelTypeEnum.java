package com.chinaservices.wms.module.excel.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImportExcelTypeEnum {

    WAREHOUSE("1", "warehouse", "仓库"),
    WAREHOUSE_AREA("2", "warehouseArea", "区域"),
    WAREHOUSE_ZONE("3", "warehouseZone", "库区"),
    WAREHOUSE_SHELF("4", "warehouseShelf", "货架"),
    WAREHOUSE_LOC("5", "warehouseLoc", "库位"),
    OWNER("6", "owner", "货主"),
    CUSTOMER("7", "customer", "客户"),
    SUPPLIER("8", "supplier", "供应商"),
    CARRIER("9", "carrier", "承运商"),
    PACKAGE("10", "package", "包装"),
    SKU("11", "sku", "商品"),
    PALLET("12", "pallet", "容器号"),
    SN_NO("13", "snNo", "序列号"),
    RULE_CARRIER_DISTRIBUTION("14", "ruleCarrierDistribution", "承运商分配规则"),
    PASSIVE_TAG("15", "passiveTag", "标签导入模板.xlsx"),
    PURCHASE_ORDER("16", "purchaseOrder", "采购订单导入模板"),
    SALE_ORDER("17", "saleOrder", "销售订单导入模板"),
    QC_SN_NO("18", "qcSnNo", "质检序列号"),
    QC_SN("19", "qcSn", "质检序列号"),
    ARCHIVE_PASSIVE_TAG("20", "archivePassiveTag", "标签导入模板.xlsx"),
    PASSIVE_TAG_BIND("21", "passiveTagBind", "导入&绑定标签模板.xlsx"),
    ;

    private final String type;
    private final String code;
    private final String value;
}
