package com.chinaservices.wms.module.stock.common.exec;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.util.JsonUtil;
import com.chinaservices.util.StringPool;
import com.chinaservices.wms.common.constant.StatusConstant;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.domain.LotAttribute;
import com.chinaservices.wms.common.domain.LotDetailItem;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.common.exception.SoExcepitonEnum;
import com.chinaservices.wms.common.util.LotUtil;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.common.model.qty.bo.StockTransactionBO;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageQuery;
import com.chinaservices.wms.module.stock.multi.domain.InvLocAttByLotNumCondition;
import com.chinaservices.wms.module.stock.mutli.model.InvLotAtt;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.chinaservices.wms.module.stock.mutli.service.InvLotAttService;
import com.chinaservices.wms.module.warehouse.loc.domain.WarehouseLocPageCondition;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.chinaservices.wms.common.constant.IdRuleConstant.TRACE_ID;
import static com.chinaservices.wms.common.constant.TransactionType.TRAN_OUT_QA;
import static com.chinaservices.wms.common.constant.TransactionType.TRAN_S_PK;
import static com.chinaservices.wms.common.exception.InvExceptionEnum.LOTNUM_LOCID_PALLETNUM_NOT_NULL;
import static com.chinaservices.wms.common.exception.InvExceptionEnum.SKU_SN_IS_NULL;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class StockOutboundCommonService extends StockCommonBaseService{

    @Autowired
    private LotUtil lotUtil;

    @Override
    protected void checkInit(InvLotLocQtyBO invLotLocQty) {
        //发运默认走SORTATION 库位
        if(!TransactionType.TRAN_SP.equals(invLotLocQty.getTransactionType()) &&
                ObjectUtil.isAllEmpty(invLotLocQty.getLocId(),invLotLocQty.getLocCode())) {
            throw new ServiceException(LOTNUM_LOCID_PALLETNUM_NOT_NULL);
        }
        if(TransactionType.TRAN_PK.equals(invLotLocQty.getTransactionType()) || TransactionType.TRAN_SP.equals(invLotLocQty.getTransactionType())) {
            if (StatusConstant.YES.equals(invLotLocQty.getWhetherSerialController()) && CollUtil.isEmpty(invLotLocQty.getSkuSn())) {
                throw new ServiceException(SKU_SN_IS_NULL, invLotLocQty.getSkuCode());
            }
        }

    }

    @Override
    protected List<InvLotLoc> compileInvLotLoc(InvLotLocQtyBO invLotLocQtyBO) {
        List<InvLotLoc> invLotLocs = Lists.newArrayList();
        InvLotLoc invLotLoc;
        InvLotLoc sortationLotloc;
        WarehouseLoc warehouseLoc;
        switch (invLotLocQtyBO.getTransactionType()) {
            case TransactionType.TRAN_INA:  //分配
                invLotLoc = getInvLotloc(invLotLocQtyBO,false);
                //设置数量为空。只更新分配数
                invLotLoc.setQtyAlloc(invLotLocQtyBO.getUpdateNum());
                invLotLoc.setQty(BigDecimal.ZERO);
                invLotLocs.add(invLotLoc);
                break;
            case TransactionType.TRAN_CR_INA: //取消分配
                invLotLoc = getInvLotloc(invLotLocQtyBO,true);
                //设置数量为空。只更新分配数
                invLotLoc.setQty(BigDecimal.ZERO);
                invLotLoc.setQtyAlloc(invLotLocQtyBO.getUpdateNum().negate());
                invLotLocs.add(invLotLoc);
                break;
            case TransactionType.TRAN_PK: //拣货
                //减去总库存 和分配数 双减要考虑库存数的可用数问题
                invLotLoc = getInvLotloc(invLotLocQtyBO,true)
                        .setQtyPk(invLotLocQtyBO.getUpdateNum()).setQtyAlloc(invLotLocQtyBO.getUpdateNum().negate());
                //减去库位库存,可分配增加拣货数
                //增加 SORTATION库存
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), invLotLocQtyBO.getWarehouseId());
                sortationLotloc = getInvLotloc(invLotLocQtyBO,false)
                        .setLocId(warehouseLoc.getId()).setLocCode(warehouseLoc.getLocCode()).setPalletNum(invLotLocQtyBO.getToPallet());
                invLotLocs.add(invLotLoc);
                invLotLocs.add(sortationLotloc);
                break;
            case TransactionType.TRAN_SP: //发运
                //减去总库存 和分配数 双减要考虑库存数的可用数问题
                invLotLoc = getInvLotloc(invLotLocQtyBO,true);
                //减去拣货数
                invLotLoc.setQtyPk(invLotLocQtyBO.getUpdateNum().negate());
                invLotLoc.setQty(BigDecimal.ZERO);
                invLotLoc.setQtyQc(BigDecimal.ZERO);
                invLotLoc.setQtyHold(BigDecimal.ZERO);
                invLotLoc.setQtyAlloc(BigDecimal.ZERO);
                //减去 SORTATION库存  发运不强制容器号。
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), invLotLocQtyBO.getWarehouseId());
                sortationLotloc = getInvLotloc(invLotLocQtyBO,true).setLocId(warehouseLoc.getId()).setLocCode(warehouseLoc.getLocCode()).setPalletNum(invLotLocQtyBO.getToPallet());
                invLotLocs.add(sortationLotloc);
                invLotLocs.add(invLotLoc);
                break;
            //二次分拣
            case TRAN_S_PK:
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), invLotLocQtyBO.getWarehouseId());
                invLotLoc = getInvLotloc(invLotLocQtyBO,true);
                invLotLoc.setLocId(warehouseLoc.getId());
                invLotLoc.setLocCode(warehouseLoc.getLocCode());
                invLotLoc.setPalletNum(invLotLocQtyBO.getPalletNum());
                //减去库位库存,可分配增加拣货数
                //增加 SORTATION库存
                sortationLotloc = getInvLotloc(invLotLocQtyBO,false)
                        .setLocId(warehouseLoc.getId()).setLocCode(warehouseLoc.getLocCode()).setPalletNum(invLotLocQtyBO.getToPallet());
                invLotLocs.add(invLotLoc);
                invLotLocs.add(sortationLotloc);
                break;
            default:
                break;
        }
        return invLotLocs;
    }

    @Override
    protected void delete(InvLotLoc invLotLoc,String transactionType) {
        if(!WarehouseLocUseTypeEnum.TALLYING_STATION.getCode().equals(invLotLoc.getLocCode())){
            return;
        }
        //发货逻辑，如果全部拣货发运完，这边需要删除数据
        if(TransactionType.TRAN_SP.equals(transactionType)){
            InvLotLoc temp = super.invLotLoc(invLotLoc);
            log.info("清理SORTATION数据 ->{}", JsonUtil.toJson(temp));
            if (temp.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                invLotLocService.delete(temp.getId());
            }
        }
    }

    @Override
    protected List<StockTransactionBO> compileStockTransaction(InvLotLocQtyBO invLotLocQtyBO) {
        List<StockTransactionBO> list = Lists.newArrayList();
        StockTransactionBO stockTransactionBO = BeanUtil.copyProperties(invLotLocQtyBO, StockTransactionBO.class);
        WarehouseLoc warehouseLoc;
        InvLotLocQtyBO fm = getINvLotLocQtyFm(invLotLocQtyBO);
        InvLotLocQtyBO to = getINvLotLocQtyTo(invLotLocQtyBO);
        stockTransactionBO.setFmLocQty(fm);
        stockTransactionBO.setTotalQtyBO(to);
        switch (invLotLocQtyBO.getTransactionType()) {
            case TransactionType.TRAN_INA, TransactionType.TRAN_CR_INA:
                list.add(stockTransactionBO);
               break;
            case TransactionType.TRAN_PK:
                //源库位
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), invLotLocQtyBO.getWarehouseId());
                //目标库位 SORTATION
                stockTransactionBO.setTotalQtyBO(to.setLocId(warehouseLoc.getId())
                        .setLocCode(warehouseLoc.getLocCode())
                        .setPalletNum(invLotLocQtyBO.getPalletNum())
                        .setLotNum(invLotLocQtyBO.getLotNum())
                        .setPalletNum(invLotLocQtyBO.getToPallet()));
                list.add(stockTransactionBO);
                break;
            case TRAN_S_PK:
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), invLotLocQtyBO.getWarehouseId());
                fm.setLocId(warehouseLoc.getId());
                fm.setLocCode(warehouseLoc.getLocCode());
                fm.setPalletNum(invLotLocQtyBO.getPalletNum());
                to.setLocId(warehouseLoc.getId());
                to.setLocCode(warehouseLoc.getLocCode());
                to.setPalletNum(invLotLocQtyBO.getToPallet());
                list.add(stockTransactionBO);
                break;
            case TransactionType.TRAN_SP:
                stockTransactionBO.setFmLocQty(fm);
                to.setLocId(null);
                to.setLocCode(null);
                to.setOwnerName(null);
                to.setOwnerId(null);
                to.setWarehouseName(null);
                to.setWarehouseId(null);
                to.setPalletNum(TRACE_ID);
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), invLotLocQtyBO.getWarehouseId());
                fm.setLocId(warehouseLoc.getId());
                fm.setLocCode(warehouseLoc.getLocCode());
                fm.setPalletNum(TRACE_ID);
                //目标库位 为空
                list.add(stockTransactionBO);
                break;
            case TRAN_OUT_QA:
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), invLotLocQtyBO.getWarehouseId());
                fm.setLocId(warehouseLoc.getId());
                fm.setLocCode(warehouseLoc.getLocCode());
                to.setPalletNum(null);
                list.add(stockTransactionBO);
                break;
            default:
                break;
        }
        return list;
    }

    /**
     * 对应出库
     * @return
     */
    @Override
    public List<String> actionType() {
        return Lists.newArrayList(TransactionType.TRAN_INA,
                TransactionType.TRAN_CR_INA,
                TransactionType.TRAN_PK,
                TransactionType.TRAN_SP,
                TransactionType.TRAN_OUT_QA,
                TransactionType.TRAN_S_PK);
    }

    /**
     * 获取库存查询列表
     * 使用条件 商品名称、商品编码
     * @param pageCondition
     * @return
     */
    public PageResult<LocListPageQuery> page(LocListPageCondition pageCondition) {
        if (pageCondition.getPageSize() == null || pageCondition.getPageNumber() == null) {
            pageCondition.setPageSize(10);
            pageCondition.setPageNumber(1);
        }
        WarehouseLocPageCondition condition = BeanUtil.copyProperties(pageCondition, WarehouseLocPageCondition.class);
        String LOT_ATTR = "lotAtt01,lotAtt02,lotAtt03,lotAtt04,lotAtt05,lotAtt06,lotAtt07,lotAtt08,lotAtt09,lotAtt10,lotAtt11,lotAtt12";
        Arrays.stream(LOT_ATTR.split(StringPool.COMMA)).forEach(key -> {
            Object value = MapUtil.get(pageCondition.getAttMap(), key, Object.class);
            if (ObjUtil.isNotEmpty(value)) {
                ReflectUtil.setFieldValue(condition, key, value);
            }
        });
        //组装参数
        switch (pageCondition.getLotSearchType()) {
            case LOT_NUM -> condition.setLotNum(pageCondition.getKeyword());
            case LOC_NO -> condition.setLocCode(pageCondition.getKeyword());
            case PALLET_NUM -> condition.setPalletNum(pageCondition.getKeyword());
            case ZONE_NAME -> condition.setZoneName(pageCondition.getKeyword());
            default -> throw new ServiceException(SoExcepitonEnum.ALLOCATION_TYPE_ERROR);
        };
        //查询主表信息
        PageResult<LocListPageQuery> page = warehouseLocService.pageLocList(condition);
        page.getRows().forEach(e -> {
            LotAttribute lotAttribute = new LotAttribute();
            BeanUtil.copyProperties(e,lotAttribute);
            List<LotDetailItem> lotDetailItems = lotUtil.getLotDetailItemsByLotId(e.getLotId());
            e.setLotAtt(lotUtil.lotAttributeToStr(lotDetailItems,lotAttribute));
        });
        return page;
    }
}
