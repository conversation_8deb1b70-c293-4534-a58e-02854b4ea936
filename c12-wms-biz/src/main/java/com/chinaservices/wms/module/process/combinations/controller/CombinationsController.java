package com.chinaservices.wms.module.process.combinations.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.common.domain.IdCondition;
import com.chinaservices.wms.common.enums.process.ProcessCombinationsStatusEnum;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsItem;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsPageCondition;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsQuery;
import com.chinaservices.wms.module.process.combinations.service.CombinationsService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 组合件
 * <AUTHOR>
 * @Date 2025/4/15 9:26
 * @Description
 * @Version 1.0
 */
@RestController
@RequestMapping("/api/process/combinations")
public class CombinationsController {

    @Autowired
    private CombinationsService combinationsService;

    /**
     *  组合件分页查询
     *@Param condition
     *@Return * {@link ResponseData< PageResult< CombinationsQuery>> }
     *@Date 2025/4/15 10:25
     *<AUTHOR>
     **/
    @PostMapping("/page")
    @SaCheckPermission("combinations:combinationsList:headBtn:page")
    public ResponseData<PageResult<CombinationsQuery>> page(@RequestBody CombinationsPageCondition condition) {
        PageResult<CombinationsQuery> pageResult = combinationsService.page(condition);
        return ResponseData.success(pageResult);
    }

    /**
     *  组合件保存
     *@Param item
     *@Return * {@link ResponseData< Void> }
     *@Date 2025/4/15 11:08
     *<AUTHOR>
     **/
    @PostMapping("/saveEntity")
    @SaCheckPermission(value = {"combinations:combinationsList:headBtn:add"})
    public ResponseData<Void> page(@RequestBody @Valid CombinationsItem item) {
        combinationsService.saveEntity(item);
        return ResponseData.success();
    }

    /**
     *  组合件查看/编辑
     *@Param id
     *@Return * {@link ResponseData< CombinationsQuery> }
     *@Date 2025/4/15 11:50
     *<AUTHOR>
     **/
    @PostMapping("/getById/{id}")
    @SaCheckPermission(value = {"combinations:combinationsList:table:view", "combinations:combinationsList:table:edit"}, mode = SaMode.OR)
    public ResponseData<CombinationsQuery> page(@PathVariable Long id) {
        CombinationsQuery combinationsQuery = combinationsService.getById(id);
        return ResponseData.success(combinationsQuery);
    }

    /**
     *  根据主键id批量删除
     *@Param condition
     *@Return * {@link ResponseData< Void> }
     *@Date 2025/4/15 13:53
     *<AUTHOR>
     **/
    @PostMapping("/batchDel")
    @SaCheckPermission(value = {"combinations:combinationsList:headBtn:delete","combinations:combinationsList:table:delete"},mode = SaMode.OR)
    public ResponseData<Void> batchDel(@RequestBody @Valid IdCondition condition) {
        combinationsService.batchDel(condition);
        return ResponseData.success();
    }

    /**
     *  根据主键id批量启用
     *@Param condition
     *@Return * {@link ResponseData< Void> }
     *@Date 2025/4/15 14:16
     *<AUTHOR>
     **/
    @PostMapping("/batchEnable")
    @SaCheckPermission("combinations:combinationsList:headBtn:enable")
    public ResponseData<Void> batchEnable(@RequestBody @Valid IdCondition condition) {
        combinationsService.batchUpdateStatus(condition.getIdList(), ProcessCombinationsStatusEnum.NORMAL.getCode());
        return ResponseData.success();
    }

    /**
     *  根据主键id批量停用
     *@Param condition
     *@Return * {@link ResponseData< Void> }
     *@Date 2025/4/15 14:16
     *<AUTHOR>
     **/
    @PostMapping("/batchDisable")
    @SaCheckPermission("combinations:combinationsList:headBtn:disable")
    public ResponseData<Void> batchDisable(@RequestBody @Valid IdCondition condition) {
        combinationsService.batchUpdateStatus(condition.getIdList(), ProcessCombinationsStatusEnum.STOP.getCode());
        return ResponseData.success();
    }
}
