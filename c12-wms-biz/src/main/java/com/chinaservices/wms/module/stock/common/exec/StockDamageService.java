package com.chinaservices.wms.module.stock.common.exec;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.util.JsonUtil;
import com.chinaservices.wms.common.constant.StatusConstant;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.enums.stock.TraceBackBusinessTypeEnum;
import com.chinaservices.wms.module.stock.back.model.TraceBackRecordItem;
import com.chinaservices.wms.module.stock.back.service.TraceBackRecordService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.common.model.qty.bo.StockTransactionBO;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import static com.chinaservices.wms.common.constant.IdRuleConstant.TRACE_ID;
import static com.chinaservices.wms.common.constant.TransactionType.TRAN_CR_FREE;
import static com.chinaservices.wms.common.constant.TransactionType.TRAN_FREE;
import static com.chinaservices.wms.common.exception.InvExceptionEnum.LOTNUM_LOCID_PALLETNUM_NOT_NULL;
import static com.chinaservices.wms.common.exception.InvExceptionEnum.SKU_SN_IS_NULL;

/**
 * 报损单处理服务
 * 处理报损单的审核、取消审核、确认报损三个状态
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class StockDamageService extends StockCommonBaseService {

    @Autowired
    private TraceBackRecordService traceBackRecordService;

    @Override
    protected void checkInit(InvLotLocQtyBO invLotLocQty) {
        log.info("【库存操作】报损单相关逻辑处理...");

        switch (invLotLocQty.getTransactionType()) {
            case TransactionType.TRAN_DAMAGE_AUDIT:  // 报损单审核
            case TransactionType.TRAN_CR_DAMAGE_AUDIT:  // 报损单取消审核
            case TransactionType.TRAN_DAMAGE_CONFIRM:  // 报损单确认报损
                // 基本参数校验
                if (ObjectUtil.isAllEmpty(invLotLocQty.getLocId(), invLotLocQty.getLocCode())) {
                    throw new ServiceException(LOTNUM_LOCID_PALLETNUM_NOT_NULL);
                }
                // 序列号控制商品需要验证序列号
                if (StatusConstant.YES.equals(invLotLocQty.getWhetherSerialController()) && CollUtil.isEmpty(invLotLocQty.getSkuSn())) {
                    throw new ServiceException(SKU_SN_IS_NULL, invLotLocQty.getSkuCode());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected List<InvLotLoc> compileInvLotLoc(InvLotLocQtyBO invLotLocQtyBO) {
        List<InvLotLoc> invLotLocList = Lists.newArrayList();
        InvLotLoc invLotLoc;

        switch (invLotLocQtyBO.getTransactionType()) {
            case TransactionType.TRAN_DAMAGE_AUDIT:  // 报损单审核
                // 参考移动逻辑：审核时增加待报损数量，库存不变
                invLotLoc = getInvLotloc(invLotLocQtyBO, false);
                invLotLoc.setQty(BigDecimal.ZERO);
                invLotLoc.setQtyWaitDamage(invLotLocQtyBO.getUpdateNum());
                invLotLocList.add(invLotLoc);
                break;
            case TransactionType.TRAN_CR_DAMAGE_AUDIT:  // 报损单取消审核
                // 参考移动逻辑：取消审核时减去待报损数量，库存不变
                invLotLoc = getInvLotloc(invLotLocQtyBO, true);
                invLotLoc.setQty(BigDecimal.ZERO);
                invLotLoc.setQtyWaitDamage(invLotLocQtyBO.getUpdateNum().negate());
                invLotLocList.add(invLotLoc);
                break;
            case TransactionType.TRAN_DAMAGE_CONFIRM:  // 报损单确认报损
                // 参考移动逻辑：确认报损时减去待报损数量和实际库存
                invLotLoc = getInvLotloc(invLotLocQtyBO, true);
                // 减去待报损数量
                invLotLoc.setQtyWaitDamage(invLotLocQtyBO.getUpdateNum().negate());
                // 减去实际库存
                invLotLoc.setQty(invLotLocQtyBO.getUpdateNum().negate());
                invLotLocList.add(invLotLoc);
                break;
            default:
                break;
        }

        return invLotLocList;
    }

    @Override
    protected List<StockTransactionBO> compileStockTransaction(InvLotLocQtyBO invLotLocQtyBO) {
        List<StockTransactionBO> list = Lists.newArrayList();
        // 参考移动逻辑：只有确认报损才需要记录库存交易记录，审核和取消审核不记录
        if (TransactionType.TRAN_DAMAGE_CONFIRM.equals(invLotLocQtyBO.getTransactionType())) {
            StockTransactionBO stockTransactionBO = BeanUtil.copyProperties(invLotLocQtyBO, StockTransactionBO.class);
            InvLotLocQtyBO fm = getINvLotLocQtyFm(invLotLocQtyBO);
            InvLotLocQtyBO to = getINvLotLocQtyTo(invLotLocQtyBO);
            // 设置源库位信息
            stockTransactionBO.setFmLocQty(fm);
            // 设置目标为空（报损出库），参考移动逻辑
            to.setLocId(null);
            to.setLocCode(null);
            to.setOwnerName(null);
            to.setOwnerId(null);
            to.setWarehouseName(null);
            to.setWarehouseId(null);
            to.setPalletNum(TRACE_ID);
            stockTransactionBO.setTotalQtyBO(to);
            list.add(stockTransactionBO);
        }
        // 审核和取消审核不记录库存交易记录，参考移动逻辑
        //序列号追溯
        if(CollectionUtil.isNotEmpty(invLotLocQtyBO.getSkuSn())){
            traceBackRecordExecute(invLotLocQtyBO);
        }
        return list;
    }
    private void traceBackRecordExecute(InvLotLocQtyBO invLotLocQtyBO) {
        if(CollectionUtil.isNotEmpty(invLotLocQtyBO.getSkuSn())){
            TraceBackRecordItem recordItem = new TraceBackRecordItem();
            recordItem.setSnNoList(invLotLocQtyBO.getSkuSn());
            recordItem.setBusinessNo(invLotLocQtyBO.getOrderNo());
            recordItem.setSourceNo(invLotLocQtyBO.getOrderNo());
            recordItem.setWarehouseName(invLotLocQtyBO.getWarehouseName());
            recordItem.setOwnerName(invLotLocQtyBO.getOwnerName());
            if(TransactionType.TRAN_CR_DAMAGE_AUDIT.equals(invLotLocQtyBO.getTransactionType())){
                //取消审核
                recordItem.setBusinessType(TraceBackBusinessTypeEnum.REPORTING_LOSSES_EXAMINE);
                log.info("库存报损取消审核序列号库存回溯记录：{}", JSONUtil.toJsonStr(recordItem));
                traceBackRecordService.executeDel(recordItem);
            }else{
                //审核和确认报损
                switch (invLotLocQtyBO.getTransactionType()){
                    case TransactionType.TRAN_DAMAGE_AUDIT:  // 报损单审核
                        // 参考移动逻辑：审核时增加待报损数量，库存不变
                        recordItem.setBusinessType(TraceBackBusinessTypeEnum.REPORTING_LOSSES_EXAMINE);
                        break;
                    case TransactionType.TRAN_DAMAGE_CONFIRM:  // 报损单确认报损
                        recordItem.setBusinessType(TraceBackBusinessTypeEnum.CONFIRM_REPORTING_LOSSES);
                        break;
                    default:
                        break;
                }
                log.info("库存报损审核/确认报损序列号库存回溯记录：{}", JSONUtil.toJsonStr(recordItem));
                traceBackRecordService.execute(recordItem);
            }

        }
    }

    @Override
    public List<String> actionType() {
        return Lists.newArrayList(
                TransactionType.TRAN_DAMAGE_AUDIT,
                TransactionType.TRAN_CR_DAMAGE_AUDIT,
                TransactionType.TRAN_DAMAGE_CONFIRM
        );
    }
}
