package com.chinaservices.wms.module.process.combinations.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinaservices.auth.module.user.domain.UserForOuterCondition;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.auth.module.user.feign.RemoteUserService;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.domain.IdCondition;
import com.chinaservices.wms.common.enums.process.ProcessCombinationsStatusEnum;
import com.chinaservices.wms.common.exception.ProcessExceptionEnum;
import com.chinaservices.wms.module.process.combinations.dao.CombinationsDao;
import com.chinaservices.wms.module.process.combinations.domain.*;
import com.chinaservices.wms.module.process.combinations.model.Combinations;
import com.chinaservices.wms.module.process.combinations.model.CombinationsDetails;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName CombinationsService
 * <AUTHOR>
 * @Date 2025/4/15 9:22
 * @Description
 * @Version 1.0
 */
@Service
public class CombinationsService extends ModuleBaseServiceSupport<CombinationsDao, Combinations, Long> {

    @Resource
    private RemoteUserService remoteUserService;

    @Autowired
    private NumberGenerator numberGenerator;

    @Autowired
    private CombinationsDetailsService combinationsDetailsService;

    /**
     *@Description 组合件分页查询
     *@Param condition
     *@Return * {@link PageResult< CombinationsQuery> }
     *@Date 2025/4/15 10:26
     *<AUTHOR>
     **/
    public PageResult<CombinationsQuery> page(CombinationsPageCondition condition) {
        PageResult<CombinationsQuery> pageResult = dao.findPage(condition);

        if(CollectionUtil.isNotEmpty(pageResult.getRows())){
            Long[] userIds = pageResult.getRows().stream().map(item -> item.getCreator()).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList()).toArray(Long[]::new);
            Map<Long, UserForOuterQuery> userQueryMap = new HashMap<>();
            if(ObjectUtil.isNotEmpty(userIds)){
                UserForOuterCondition userForOuterCondition = new UserForOuterCondition();
                userForOuterCondition.setIds(userIds);
                ResponseData<List<UserForOuterQuery>> userQueryResponse = remoteUserService.getUserList(userForOuterCondition);
                if (userQueryResponse.getSuccess()) {
                    List<UserForOuterQuery> userQueries = userQueryResponse.getData();
                    if(CollectionUtil.isNotEmpty(userQueries)){
                        userQueryMap = userQueries.stream().collect(Collectors.toMap(UserForOuterQuery::getId, Function.identity(), (k1, k2) -> k1));
                    }
                }
            }
            Map<Long, UserForOuterQuery> finalUserQueryMap = userQueryMap;
            pageResult.getRows().forEach(item -> {
                UserForOuterQuery userForOuterQuery = finalUserQueryMap.get(item.getCreator());
                if(ObjectUtil.isNotEmpty(userForOuterQuery)){
                    item.setCreatorName(userForOuterQuery.getRealName());
                }
            });
        }
        return pageResult;
    }

    /**
     *@Description 组合件保存
     *@Param item
     *@Return Void
     *@Date 2025/4/15 11:09
     *<AUTHOR>
     **/
    public void saveEntity(CombinationsItem item) {
        //验证商品是否存在组合件
        validData(item);
        Combinations combinations  = new Combinations();
        BeanUtils.copyProperties(item,combinations);
        if(ObjectUtil.isNull(item.getId())){
            BeanUtils.copyProperties(item,combinations);
            combinations.setCombinationsNo(numberGenerator.nextValue(IdRuleConstant.COMBINATIONS_NO));
            combinations.setCombinationsStatus(ProcessCombinationsStatusEnum.NORMAL.getCode());
        }else{
            combinations = findById(item.getId());
            BeanUtils.copyProperties(item,combinations);
        }
        if(CollectionUtil.isNotEmpty(item.getCombinationsDetails())){
            List<Long> skuIdList  = item.getCombinationsDetails().stream().map(x -> x.getSkuId()).distinct().collect(Collectors.toList());
            if(skuIdList.contains(combinations.getSkuId())){
                throw new ServiceException(ProcessExceptionEnum.COMBINATIONS_DETAILS_SKU_ERROR);
            }
            Map<Long,List<CombinationsDetailsItem>> detailsMap = item.getCombinationsDetails().stream().collect(Collectors.groupingBy(CombinationsDetailsItem::getSkuId));
            detailsMap.forEach((key,value)->{
                if(value.size() > 1){
                    throw new ServiceException(ProcessExceptionEnum.COMBINATIONS_DETAILS_SKU_ERROR);
                }
            });
            //先根据编号进行删除
            combinationsDetailsService.deleteByCombinationsNoList(Arrays.asList(combinations.getCombinationsNo()));
            List<CombinationsDetails> detailsList = new ArrayList<>();
            for(CombinationsDetailsItem combinationsDetailsItem : item.getCombinationsDetails()){
                CombinationsDetails details = new CombinationsDetails();
                BeanUtils.copyProperties(combinationsDetailsItem,details);
                details.setId(null);
                details.setCombinationsNo(combinations.getCombinationsNo());
                combinationsDetailsService.preSave(details);
                detailsList.add(details);
            }
            combinationsDetailsService.batchInsert(detailsList);
        }
        dao.saveOrUpdate(combinations);
    }

    /**
     *@Description 验证组合件数据是否存在
     *@param item
     *@Return Void
     *@Date 2025/7/7 9:12
     *<AUTHOR>
     **/
    private void validData(CombinationsItem item) {
        //通过货主、仓库、商品查询组合件
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(Combinations::getOwnerId,item.getOwnerId());
        conditionRule.andEqual(Combinations::getWarehouseId,item.getWarehouseId());
        conditionRule.andEqual(Combinations::getSkuId,item.getSkuId());
        List<Combinations> combinationsList = dao.find(conditionRule);
        //是否有ID
        if(ObjectUtil.isNotNull(item.getId())){
            if(combinationsList.size() > 1
                    || (combinationsList.size() == 1 && !combinationsList.get(0).getId().equals(item.getId()))){
                throw new ServiceException(ProcessExceptionEnum.COMBINATIONS_EXIST);
            }
        }else {
            if(combinationsList.size() > 0){
                throw new ServiceException(ProcessExceptionEnum.COMBINATIONS_EXIST);
            }
        }
    }

    /**
     *@Description 组合件查看/编辑
     *@Param id
     *@Return * {@link CombinationsQuery }
     *@Date 2025/4/15 11:51
     *<AUTHOR>
     **/
    public CombinationsQuery getById(Long id) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(Combinations::getId,id);
        CombinationsQuery combinationsQuery = dao.findFirst(CombinationsQuery.class, conditionRule);
        //查询对应子件数据
        List<CombinationsDetailsQuery> detailsQueryList = combinationsDetailsService.getByCombinationNo(combinationsQuery.getCombinationsNo());
        combinationsQuery.setDetailsQueryList(detailsQueryList);
        return combinationsQuery;
    }

    /**
     *@Description 根据主键id批量删除
     *@Param condition
     *@Return Void
     *@Date 2025/4/15 13:53
     *<AUTHOR>
     **/
    public void batchDel(IdCondition condition) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(Combinations::getId,condition.getIdList());
        List<Combinations> combinationsList = dao.find(conditionRule);
        if(CollectionUtil.isNotEmpty(combinationsList)){
            //是否存在已启用状态的组合件
            boolean delFlag = combinationsList.stream().anyMatch(item -> ProcessCombinationsStatusEnum.NORMAL.getCode().equals(item.getCombinationsStatus()));
            List<String> noList = combinationsList.stream().filter(x -> ProcessCombinationsStatusEnum.NORMAL.getCode().equals(x.getCombinationsStatus())).map(item -> item.getCombinationsNo()).collect(Collectors.toList());
            if(delFlag){
                throw new ServiceException(ProcessExceptionEnum.DELETE_STATUS_ERROR,noList);
            }
            combinationsDetailsService.deleteByCombinationsNoList(combinationsList.stream().map(item -> item.getCombinationsNo()).collect(Collectors.toList()));
            dao.delete(conditionRule);
        }
    }

    /**
     *@Description 根据主键id批量启用/禁用
     *@Param condition
     *@Return Void
     *@Date 2025/4/15 14:16
     *<AUTHOR>
     **/
    public void batchUpdateStatus(List<Long> idList,String status) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(Combinations::getId,idList);
        List<Combinations> combinationsList = dao.find(conditionRule);
        if (CollectionUtil.isNotEmpty(combinationsList)){
            boolean enableFlag = combinationsList.stream().anyMatch(item -> status.equals(item.getCombinationsStatus()));
            List<String> noList = combinationsList.stream().filter(x -> status.equals(x.getCombinationsStatus())).map(item -> item.getCombinationsNo()).collect(Collectors.toList());
            if(enableFlag){
                if(ProcessCombinationsStatusEnum.NORMAL.getCode().equals(status)){
                    throw new ServiceException(ProcessExceptionEnum.ENABLE_STATUS_ERROR,noList);
                }else{
                    throw new ServiceException(ProcessExceptionEnum.DISABLE_STATUS_ERROR,noList);
                }

            }
            combinationsList.forEach(item -> {
                item.setCombinationsStatus(status);
                preSave(item);
            });
            dao.batchUpdate(combinationsList);
        }
    }

}
