package com.chinaservices.wms.module.performance.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.auth.module.user.domain.UserPageCondition;
import com.chinaservices.auth.module.user.feign.RemoteUserService;
import com.chinaservices.module.base.ModuleBaseController;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.fee.fee.domain.IdsItem;
import com.chinaservices.wms.module.performance.domain.*;
import com.chinaservices.wms.module.performance.service.KpiAllocationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 人员绩效指标分配相关接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/performance/kpi/allocation")
public class KpiAllocationController extends ModuleBaseController {

    @Autowired
    private KpiAllocationService kpiAllocationService;

    @Autowired
    private RemoteUserService userService;

    @PostMapping("/queryPickers")
    private ResponseData<PageResult<UserForOuterQuery>> getUserPage(@RequestBody UserPageCondition userPageCondition){
        ResponseData<PageResult<UserForOuterQuery>> userList = userService.getUserPage(userPageCondition);
        return userList;
    }

    /**
     * 人员绩效指标分配-列表分页
     * @param condition
     * @return
     */
    @PostMapping(value = "/page")
    @SaCheckPermission("employeePerformance:employeePerformanceList:headBtn:page")
    public ResponseData<PageResult<KpiHeaderQuery>> page(@RequestBody KpiHeaderPageCondition condition){
        return ResponseData.success(kpiAllocationService.page(condition));
    }

    /**
     * 人员绩效指标分配-详情
     * @param id
     * @return
     */
    @PostMapping(value = "/detail/{id}")
    @SaCheckPermission("employeePerformance:employeePerformanceList:headBtn:page")
    public ResponseData<KpiHeaderQuery> detailById(@PathVariable Long id){
        return ResponseData.success(kpiAllocationService.detailById(id));
    }

    /**
     * 人员绩效指标分配-新增
     * @param item
     * @return
     */
    @PostMapping(value = "/save")
    @SaCheckPermission("employeePerformance:employeePerformanceList:headBtn:add")
    public ResponseData<Boolean> saveHeader(@RequestBody KpiHeaderItem item){
        return ResponseData.success(kpiAllocationService.saveHeader(item));
    }

    /**
     * 人员绩效指标分配-删除
     * @param item
     * @return
     */
    @PostMapping(value = "/delete")
    @SaCheckPermission(value = {"employeePerformance:employeePerformanceList:headBtn:delete","employeePerformance:employeePerformanceList:table:delete"}, mode = SaMode.OR)
    public ResponseData<Boolean> deleteHeader(@RequestBody IdsItem item){
        return ResponseData.success(kpiAllocationService.deleteHeader(item));
    }

    /**
     * 人员绩效指标分配-编辑
     * @param item
     * @return
     */
    //@SaCheckPermission("employeePerformance:employeePerformanceList:headBtn:delete")
    @PostMapping(value = "/edit")
    @SaCheckPermission("employeePerformance:employeePerformanceList:table:edit")
    public ResponseData<Boolean> editHeader(@RequestBody KpiHeaderItem item){
        return ResponseData.success(kpiAllocationService.editHeader(item));
    }

    /**
     * 人员绩效指标分配-考核评分-手动评分
     * @param item
     * @return
     */
    @PostMapping(value = "/score/hand")
    @SaCheckPermission("employeePerformance:employeePerformanceList:table:score")
    public ResponseData<Boolean> scoreHand(@RequestBody List<KpiRecordItem> item){
        return ResponseData.success(kpiAllocationService.scoreHand(item));
    }

    /**
     * 人员绩效指标分配-考核记录
     * @param id
     * @return
     */
    @PostMapping(value = "/score/record/list")
    public ResponseData<List<KpiRecordItem>> scoreRecord(@RequestBody KpiRecordCondition condition){
        return ResponseData.success(kpiAllocationService.scoreRecord(condition));
    }

    /**
     * 人员绩效指标分配-审核
     * @param item
     * @return
     */
    @PostMapping(value = "/examine")
    @SaCheckPermission("employeePerformance:employeePerformanceList:headBtn:audit")
    public ResponseData<Boolean> examine(@RequestBody IdsItem item){
        return ResponseData.success(kpiAllocationService.examine(item));
    }

    /**
     * 人员绩效指标分配-拒绝审核
     * @param item
     * @return
     */
    @PostMapping(value = "/refuse/examine")
    @SaCheckPermission("employeePerformance:employeePerformanceList:headBtn:audit")
    public ResponseData<Boolean> refuseExamine(@RequestBody IdsItem item){
        return ResponseData.success(kpiAllocationService.refuseExamine(item));
    }

    /**
     * 人员绩效指标分配-设置考核周期
     * @param item
     * @return
     */
    @PostMapping(value = "/edit/cycle")
    @SaCheckPermission("employeePerformance:employeePerformanceList:headBtn:period")
    public ResponseData<Boolean> editCycle(@RequestBody KpiCycleItem item){
        return ResponseData.success(kpiAllocationService.editCycle(item));
    }

    /**
     * 人员绩效指标分配-查询考核周期
     * @return
     */
    @PostMapping(value = "/search/cycle")
    public ResponseData<KpiCycleItem> searchCycle(){
        return ResponseData.success(kpiAllocationService.searchCycle());
    }


    /**
     * 人员绩效指标分配-获取考核人指标分数记录
     */
    @PostMapping(value = "/score/record/list/{id}")
    @SaCheckPermission("employeePerformance:employeePerformanceList:table:record")
    public ResponseData<KpiRecordQuery> scoreRecordById(@PathVariable Long id){
        KpiRecordQuery query = kpiAllocationService.scoreRecordById(id);
        return ResponseData.success(query);
    }

    /**
     * 查询考核时间列表
     */
    @PostMapping(value = "/assessment/time/list")
    public ResponseData<List<KpiAssessmentTimeQuery>> assessmentTimeList(@RequestBody KpiRecordCondition condition){
        List<KpiAssessmentTimeQuery> list = kpiAllocationService.assessmentTimeList(condition);
        return ResponseData.success(list);
    }
}
