package com.chinaservices.wms.module.process.combinations.controller;


import com.chinaservices.wms.module.process.combinations.repository.AllocProcessOrderRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 加工分配控制器
 * <AUTHOR>
 * @Description
 * @Version 1.0
 */
@RestController
@RequestMapping(value = "/api/process/order/alloc")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AllocProcessOrderController {

    private final AllocProcessOrderRepository repository;


    /**
     * 左侧商品查询
     * 右侧分配明细查询
     * 手动分配
     * 自动分配
     * 分配明细移除
     * 一键分配
     * 取消分配
     */


}
