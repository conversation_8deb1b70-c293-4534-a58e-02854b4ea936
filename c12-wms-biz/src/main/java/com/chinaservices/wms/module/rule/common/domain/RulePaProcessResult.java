package com.chinaservices.wms.module.rule.common.domain;

import lombok.Data;

/**
 * 单个上架规则处理结果
 * 
 * <AUTHOR> Assistant
 */
@Data
public class RulePaProcessResult {
    
    /**
     * 收货明细ID
     */
    private Long asnReceiveId;
    
    /**
     * 商品编码
     */
    private String skuCode;
    
    /**
     * 处理是否成功
     */
    private boolean success;
    
    /**
     * 分配的库位ID
     */
    private Long locId;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 处理时间戳
     */
    private long processTime;
    
    /**
     * 创建成功结果
     */
    public static RulePaProcessResult success(Long asnReceiveId, String skuCode, Long locId) {
        RulePaProcessResult result = new RulePaProcessResult();
        result.setAsnReceiveId(asnReceiveId);
        result.setSkuCode(skuCode);
        result.setSuccess(true);
        result.setLocId(locId);
        result.setProcessTime(System.currentTimeMillis());
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static RulePaProcessResult failure(Long asnReceiveId, String skuCode, String errorMessage) {
        RulePaProcessResult result = new RulePaProcessResult();
        result.setAsnReceiveId(asnReceiveId);
        result.setSkuCode(skuCode);
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setProcessTime(System.currentTimeMillis());
        return result;
    }
    
    /**
     * 创建无规则结果（商品没有配置上架规则）
     */
    public static RulePaProcessResult noRule(Long asnReceiveId, String skuCode) {
        RulePaProcessResult result = new RulePaProcessResult();
        result.setAsnReceiveId(asnReceiveId);
        result.setSkuCode(skuCode);
        result.setSuccess(true);
        result.setLocId(null);
        result.setProcessTime(System.currentTimeMillis());
        return result;
    }
}
