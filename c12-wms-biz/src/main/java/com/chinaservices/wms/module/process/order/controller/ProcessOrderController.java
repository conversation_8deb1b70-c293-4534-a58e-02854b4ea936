package com.chinaservices.wms.module.process.order.controller;


import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.common.domain.IdCondition;
import com.chinaservices.wms.module.folder.logistics.domain.BatchDelDetailItem;
import com.chinaservices.wms.module.process.order.domain.*;
import com.chinaservices.wms.module.process.order.service.ProcessOrderService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 加工单/拆分单接口
 * <AUTHOR>
 * @Date 2025/6/26 20:38
 * @Description
 * @Version 1.0
 */
@RestController
@RequestMapping(value = "/api/process/order")
public class ProcessOrderController {

    @Autowired
    private ProcessOrderService processOrderService;


    /**
     * 加工单/拆分单分页查询
     * @param condition
     * @return
     */
    @PostMapping("/page")
    public ResponseData<PageResult<ProcessOrderQuery>> page(@RequestBody @Valid ProcessOrderPageCondition condition){
        PageResult<ProcessOrderQuery> pageResult = processOrderService.page(condition);
        return ResponseData.success(pageResult);
    }

    /**
     * 根据ID查询加工单信息
     *@Param id
     *@Return * {@link ResponseData< ProcessOrderQuery> }
     *@Date 2025/6/30 14:25
     *<AUTHOR>
     **/
    @PostMapping("/getById/{id}")
    public ResponseData<ProcessOrderQuery> getById(@PathVariable Long id){
        ProcessOrderQuery query = processOrderService.getQueryById(id);
        return ResponseData.success(query);
    }

    /**
     * 加工单保存
     *@Param item
     *@Return * {@link ResponseData< ProcessOrderQuery> }
     *@Date 2025/6/30 15:49
     *<AUTHOR>
     **/
    @PostMapping("/saveEntity")
    public ResponseData saveEntity(@RequestBody @Valid ProcessOrderItem item){
        processOrderService.saveEntity(item);
        return ResponseData.success();
    }

    /**
     * 根据ID批量删除加工单信息
     *@Param delDetailItem
     *@Return * {@link ResponseData }
     *@Date 2025/6/30 14:34
     *<AUTHOR>
     **/
    @PostMapping("/batchDel")
    public ResponseData batchDel(@RequestBody @Valid BatchDelDetailItem delDetailItem){
       processOrderService.batchDel(delDetailItem);
       return ResponseData.success();
    }

    /**
     * 加工单批量审核
     *@Param delDetailItem
     *@Return * {@link ResponseData }
     *@Date 2025/6/30 15:41
     *<AUTHOR>
     **/
    @PostMapping("/batchAudit")
    public ResponseData batchAudit(@RequestBody @Valid BatchDelDetailItem delDetailItem){
        processOrderService.batchAudit(delDetailItem);
        return ResponseData.success();
    }

    /**
     *批量取消审核
     *@Param delDetailItem
     *@Return * {@link ResponseData }
     *@Date 2025/6/30 15:44
     *<AUTHOR>
     **/
    @PostMapping("/batchCancelAudit")
    public ResponseData batchCancelAudit(@RequestBody @Valid BatchDelDetailItem delDetailItem){
        processOrderService.batchCancelAudit(delDetailItem);
        return ResponseData.success();
    }

    /**
     *  加工确认信息查询
     *@Param id
     *@Return * {@link ResponseData<ConfirmInfoQuery> }
     *@Date 2025/7/4 10:16
     *<AUTHOR>
     **/
    @PostMapping("/confirmInfo/{id}")
    public ResponseData<ConfirmInfoQuery> confirmInfo(@PathVariable Long id){
        ConfirmInfoQuery query = processOrderService.confirmInfo(id);
        return ResponseData.success(query);
    }

    /**
     *  批量加工确认
     *@Param delDetailItem
     *@Return * {@link ResponseData }
     *@Date 2025/6/30 20:45
     *<AUTHOR>
     **/
    @PostMapping("/batchConfirm")
    public ResponseData batchConfirm(@RequestBody @Valid ProcessOrderConfirmItem confirmItem){
        processOrderService.batchConfirm(confirmItem);
        return ResponseData.success();
    }

    /**
     * 批量取消
     *
     * @param condition 条件
     * @return {@link ResponseData }
     */
    @PostMapping("/batchCancel")
    public ResponseData batchCancel(@RequestBody @Valid IdCondition condition) {
        processOrderService.batchCancel(condition);
        return ResponseData.success();
    }

    /**
     * 批处理关闭订单
     *
     * @param condition 条件
     * @return {@link ResponseData }
     */
    @PostMapping("/batchClose")
    public ResponseData batchClose(@RequestBody @Valid IdCondition condition) {
        processOrderService.batchClose(condition);
        return ResponseData.success();
    }
}
