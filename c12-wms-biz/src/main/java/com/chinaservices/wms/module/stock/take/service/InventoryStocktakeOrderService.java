package com.chinaservices.wms.module.stock.take.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.auth.module.user.domain.UserPageCondition;
import com.chinaservices.auth.module.user.feign.RemoteUserService;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.util.JsonUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.domain.LotAttribute;
import com.chinaservices.wms.common.domain.LotDetailItem;
import com.chinaservices.wms.common.enums.adjust.AdjustModeEnum;
import com.chinaservices.wms.common.enums.adjust.AdjustTypeEnum;
import com.chinaservices.wms.common.enums.stock.*;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.common.exception.StockExcepitonEnum;
import com.chinaservices.wms.common.util.LotUtil;
import com.chinaservices.wms.common.util.MapObjectUtil;
import com.chinaservices.wms.module.asn.asn.service.AsnDetailSnService;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.excel.handler.ExportDataHandler;
import com.chinaservices.wms.module.passive.take.service.PassiveAutoInventoryNewService;
import com.chinaservices.wms.module.stock.adjust.domain.AdjustDetailItem;
import com.chinaservices.wms.module.stock.adjust.domain.AdjustHeaderItem;
import com.chinaservices.wms.module.stock.adjust.service.AdjustHeaderService;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.qty.query.InvLotLocForStockTakeOrderQuery;
import com.chinaservices.wms.module.stock.common.model.qty.query.InvLotLocForStockTakeOrderQueryCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocSnDetailCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocSnItem;
import com.chinaservices.wms.module.stock.loc.domain.LocSnItemQuery;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLocSn;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocSnService;
import com.chinaservices.wms.module.stock.take.dao.InventoryStocktakeOrderDao;
import com.chinaservices.wms.module.stock.take.domain.*;
import com.chinaservices.wms.module.stock.take.model.*;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import com.chinaservices.wms.module.warehouse.zone.model.WarehouseZone;
import com.chinaservices.wms.module.warehouse.zone.service.WarehouseZoneService;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName InventoryStocktakeOrderService
 * <AUTHOR>
 * @Date 2025/1/11 14:34
 * @Description
 * @Version 1.0
 */
@Service
public class InventoryStocktakeOrderService extends ModuleBaseServiceSupport<InventoryStocktakeOrderDao, InventoryStocktakeOrder, Long> {

    @Autowired
    private NumberGenerator numberGenerator;

    @Autowired
    private InventoryStocktakeConditionService stocktakeConditionService;

    @Autowired
    private InventoryStocktakeTaskService stocktakeTaskService;

    @Autowired
    private InventoryStocktakeTaskDetailsService detailsService;

    @Autowired
    private ExportDataHandler exportDataHandler;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private StockService stockService;

    @Autowired
    private WarehouseLocService warehouseLocService;

    @Autowired
    private AdjustHeaderService adjustHeaderService;

    @Autowired
    private SkuService skuService;

    @Autowired
    private PackageUnitService packageUnitService;

    @Autowired
    private InvLotLocSnService invLotLocSnService;

    @Autowired
    private InventoryStocktakeTaskDetailsSnService taskDetailsSnService;

    @Autowired
    @Lazy
    private PassiveAutoInventoryNewService passiveAutoInventoryNewService;
    /**
     *@Description 盘点单分页查询
     *@Param condition
     *@Return * {@link PageResult<  StocktakeOrderQuery > }
     *@Date 2025/1/11 17:53
     *<AUTHOR>
     **/
    public PageResult<StocktakeOrderQuery> page(StocktakeOrderPageCondition condition) {
        //如果是否过滤盘增盘减 为是 先查询盘点详情
        if(YesNoEnum.YES.getCode().equals(condition.getFilterAddOrderReduce())) {
            List<InventoryStocktakeTaskDetails> differentsList = detailsService.findDifferentsList(condition);
            List<String> noList = differentsList.stream().map(InventoryStocktakeTaskDetails::getStocktakeNo).distinct().collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(noList)){
                condition.setStocktakeNoList(noList);
            }
        }
        if(CollectionUtil.isEmpty(condition.getFilterStocktakeNoList())){
            condition.setFilterStocktakeNoList(null);
        }
        PageResult<StocktakeOrderQuery> pageResult = dao.page(condition);
        return pageResult;
    }

    /**
     *@Description 库存盘点保存
     *@Param item
     *@Return Void
     *@Date 2025/1/13 14:11
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void save(StocktakeOrderItem item) {
        paramValid(item);
        InventoryStocktakeOrder order = new InventoryStocktakeOrder();
        BeanUtil.copyProperties(item, order);
        order.setStocktakeNo(numberGenerator.nextValue(IdRuleConstant.STOCKTAKE_ORDER_CODE));
        order.setStocktakeStatus(StocktakeOrderStatusEnum.CREATE.getCode());
        preSave(order);
        dao.saveOrUpdate(order);
        List<InventoryStocktakeCondition> conditionList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(item.getConditionItem())){
                conditionList = item.getConditionItem().stream().filter(x -> ObjectUtil.isNotNull(x)).map(conditionItem -> {
                InventoryStocktakeCondition condition = new InventoryStocktakeCondition();
                BeanUtil.copyProperties(conditionItem, condition);
                condition.setId(null);
                condition.setStocktakeNo(order.getStocktakeNo());
                stocktakeConditionService.preSave(condition);
                return condition;
            }).collect(Collectors.toList());

            if(CollectionUtil.isNotEmpty(conditionList)){
                stocktakeConditionService.batchInsert(conditionList);
            }


        }
        List<InvLotLocForStockTakeOrderQuery> lotLocForStocktakeOrderQueryList = getStockData(order,conditionList);
        if(CollectionUtil.isEmpty(lotLocForStocktakeOrderQueryList)){
            throw new ServiceException(StockExcepitonEnum.LOT_LOC_NO_DATA);
        }
        //根据库存数据分组生成盘点任务及明细数据
        buildTaskAndDetailsData(order,lotLocForStocktakeOrderQueryList);

    }

    /**
     *@Description 组装库存查询条件并获取库存数据
     *@Param order
     *@param conditionList
     *@Return * {@link List< InvLotLocForStockTakeOrderQuery> }
     *@Date 2025/1/22 14:43
     *<AUTHOR>
     **/
    private List<InvLotLocForStockTakeOrderQuery> getStockData(InventoryStocktakeOrder order, List<InventoryStocktakeCondition> conditionList) {
        List<InvLotLocForStockTakeOrderQuery> lotLocForStocktakeOrderQueryList = new ArrayList<>();
        //调用库存服务查询库存数据
        InvLotLocForStockTakeOrderQueryCondition condition = buildStockQueryCondition(order,conditionList);
        Boolean flag = Boolean.TRUE;
        Integer pageNumber = 1;
        Integer pageSize = 100;
        condition.setPageSize(pageSize);
        while (flag){
            condition.setPageNumber(pageNumber);
            PageResult<InvLotLocForStockTakeOrderQuery> pageResult = stockService.getInvLotLocForStockTakeOrderQuery(condition);
            if(CollectionUtil.isNotEmpty(pageResult.getRows())){
                lotLocForStocktakeOrderQueryList.addAll(pageResult.getRows());
                if(pageResult.getRows().size() < pageSize){
                    flag = Boolean.FALSE;
                }else {
                    pageNumber++;
                }
            }else{
                flag = Boolean.FALSE;
            }
        }
        return lotLocForStocktakeOrderQueryList;
    }

    /**
     *@Description 构建库存查询条件
     *@Param order
     *@param conditionList
     *@Return * {@link InvLotLocForStockTakeOrderQueryCondition }
     *@Date 2025/1/21 19:04
     *<AUTHOR>
     **/
    private InvLotLocForStockTakeOrderQueryCondition buildStockQueryCondition(InventoryStocktakeOrder order, List<InventoryStocktakeCondition> conditionList) {
        InvLotLocForStockTakeOrderQueryCondition queryCondition = new InvLotLocForStockTakeOrderQueryCondition();
        queryCondition.setWarehouseId(order.getWarehouseId());
        if(CollectionUtil.isNotEmpty(conditionList)){
            List<Long> warehouseLocIdList = new ArrayList<>();
            //区域id集合
            List<Long> areaIdList = conditionList.stream().map(InventoryStocktakeCondition::getAreaId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(areaIdList)){
                //通过区域id查询库位信息
                List<WarehouseLoc> warehouseLocList = warehouseLocService.getListByAreaIdList(areaIdList);
                if(CollectionUtil.isNotEmpty(warehouseLocList)){
                    warehouseLocIdList.addAll(warehouseLocList.stream().map(WarehouseLoc::getId).collect(Collectors.toList()));
                }
            }
            //仓库库区id集合
            List<Long> warehouseZoneIdList = conditionList.stream().map(InventoryStocktakeCondition::getWarehouseZoneId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(warehouseZoneIdList)){
                //通过库区id查询库位信息
                List<WarehouseLoc> warehouseLocList = warehouseLocService.getListByWarehouseZoneIdList(warehouseZoneIdList);
                if(CollectionUtil.isNotEmpty(warehouseLocList)){
                    warehouseLocIdList.addAll(warehouseLocList.stream().map(WarehouseLoc::getId).collect(Collectors.toList()));
                }
            }
            //货主id集合
            List<Long> ownerIdList = conditionList.stream().map(InventoryStocktakeCondition::getOwnerId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
            if(CollectionUtil.isEmpty(ownerIdList)){
                ownerIdList = null;
            }
            queryCondition.setOwnerIdList(ownerIdList);
            //仓库库位id集合
            warehouseLocIdList.addAll(conditionList.stream().map(InventoryStocktakeCondition::getWarehouseLocId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList()));
            if(CollectionUtil.isEmpty(warehouseLocIdList)){
                warehouseLocIdList = null;
            }
            queryCondition.setWarehouseLocIdList(warehouseLocIdList);
            //容器号集合
            List<String> palletNumList = conditionList.stream().map(InventoryStocktakeCondition::getPalletCode).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
            if(CollectionUtil.isEmpty(palletNumList)){
                palletNumList = null;
            }
            queryCondition.setPalletNumList(palletNumList);
        }
        queryCondition.setOperatorTimeStart(order.getOperatorTimeStart());
        queryCondition.setOperatorTimeEnd(order.getOperatorTimeEnd());
        return queryCondition;
    }

    /**
     *@Description 生成任务及明细数据
     *@Param order
     *@param lotLocForStocktakeOrderQueryList
     *@Return Void
     *@Date 2025/1/21 16:24
     *<AUTHOR>
     **/
    private void buildTaskAndDetailsData(InventoryStocktakeOrder order, List<InvLotLocForStockTakeOrderQuery> lotLocForStocktakeOrderQueryList) {
        StocktakeTaskTypeEnum taskTypeEnum = EnumUtil.getBy(StocktakeTaskTypeEnum.class, e -> e.getCode().equals(order.getTaskType()));
        Map<Long,List<InvLotLocForStockTakeOrderQuery>> groupMap = new HashMap<>();
        switch (taskTypeEnum){
            //任务生成方式为直接生成 不用数据过滤，只成一条任务
            case DIRECT_GENERATION:
                groupMap.put(-1L,lotLocForStocktakeOrderQueryList);
                break;
            //任务生成方式为按区域生成，按区域过滤
//            case AREA_GENERATION:
//                //过滤掉区域Id为空的数据
//                lotLocForStocktakeOrderQueryList = lotLocForStocktakeOrderQueryList.stream().filter(e -> ObjectUtil.isNotNull(e.getAreaId())).collect(Collectors.toList());
//                groupMap = lotLocForStocktakeOrderQueryList.stream().collect(Collectors.groupingBy(InvLotLocForStockTakeOrderQuery::getAreaId));
//                break;
            //任务生成方式为按库区生成，按库区过滤
            case WAREHOUSE_GENERATION:
                List<String> locCodeList = Arrays.asList(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(),WarehouseLocUseTypeEnum.TALLYING_STATION.getCode());
                List<InvLotLocForStockTakeOrderQuery> hasWarehouseZoneIdList = lotLocForStocktakeOrderQueryList.stream().filter(e -> !locCodeList.contains(e.getLocCode())).collect(Collectors.toList());
                List<InvLotLocForStockTakeOrderQuery> noWarehouseZoneIdList = lotLocForStocktakeOrderQueryList.stream().filter(e -> locCodeList.contains(e.getLocCode())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(noWarehouseZoneIdList)){
                    groupMap.put(-2L,noWarehouseZoneIdList);
                }
                if(CollectionUtil.isNotEmpty(hasWarehouseZoneIdList)){
                    groupMap.putAll(hasWarehouseZoneIdList.stream().collect(Collectors.groupingBy(InvLotLocForStockTakeOrderQuery::getWarehouseZoneId)));
                }
                break;
            default:
                break;
            }
            //根据盘点单号查询当前任务表中任务编号最大值
            Long max = stocktakeTaskService.getMaxTaskNo(order.getStocktakeNo());
            List<InventoryStocktakeTask> taskList = new ArrayList<>();
            List<InventoryStocktakeTaskDetails> finalDetailsList = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(groupMap)){
                //for循环map
                for (Map.Entry<Long, List<InvLotLocForStockTakeOrderQuery>> entry : groupMap.entrySet()){
                    List<InventoryStocktakeTaskDetails> detailsList = new ArrayList<>();
                    max = max + 1;
                    InventoryStocktakeTask task = new InventoryStocktakeTask();
                    BeanUtil.copyProperties(order, task);
                    task.setTaskStatus(StocktakeTaskStatusEnum.CREATE.getCode());
                    task.setTaskAllocation(YesNoEnum.NO.getCode());
                    task.setTaskNo(order.getStocktakeNo() +"-"+max);
                    task.setId(null);
                    stocktakeTaskService.preSave(task);
                    taskList.add(task);
                    List<InvLotLocForStockTakeOrderQuery> currentList = entry.getValue();
                    currentList.forEach(x ->{
                        InventoryStocktakeTaskDetails details = new InventoryStocktakeTaskDetails();
                        BeanUtil.copyProperties(x, details);
                        if(entry.getKey() == -2L || entry.getKey() == -1L){
                            // 如果是按库区生成  且 库位为 STAGE 和 SORTATION  不赋值 库区id和名称
                            details.setWarehouseZoneId(null);
                            details.setWarehouseZoneName(null);
                        }
                        details.setStocktakeNo(order.getStocktakeNo());
                        details.setTaskNo(task.getTaskNo());
                        details.setInventoryCount(x.getQty().longValue());
                        details.setLocName(x.getLocCode());
                        details.setId(null);
                        details.setDataSource(YesNoEnum.NO.getCode());
                        details.setPdaMatchStatus(YesNoEnum.NO.getCode());
                        detailsService.preSave(details);
                        detailsList.add(details);
                    });
                    //将同仓库，同商品，同库位，同批次号的数据合并为一条
                    Map<String, List<InventoryStocktakeTaskDetails>>  map = detailsList.stream().collect(Collectors.groupingBy(e ->e.getWarehouseId() +"_"+ e.getSkuId()+"_"+ e.getLocId() + '_'+ e.getLotNum()));
                    map.forEach((k,v) ->{
                        List<InventoryStocktakeTaskDetails> list = v;
                        if(CollectionUtil.isNotEmpty(list)){
                            InventoryStocktakeTaskDetails details = new InventoryStocktakeTaskDetails();
                            //累加库存数
                            Long amount = list.stream().map(InventoryStocktakeTaskDetails::getInventoryCount).reduce(0L, Long::sum);
                            BeanUtil.copyProperties(list.get(0),details);
                            details.setInventoryCount(amount);
                            finalDetailsList.add(details);
                        }
                    });
                }
            }
            if(CollectionUtil.isNotEmpty(taskList)){
                stocktakeTaskService.batchInsert(taskList);
            }
            if (CollectionUtil.isNotEmpty(finalDetailsList)){
                detailsService.batchInsert(finalDetailsList);
            }

    }






    /**
     *@Description 盘点参数验证
     *@Param item
     *@Return Void
     *@Date 2025/1/20 11:37
     *<AUTHOR>
     **/
    private void paramValid(StocktakeOrderItem item) {
        //盘点范围验证
        StocktakeRangeEnum stocktakeRangeEnum = EnumUtil.getBy(StocktakeRangeEnum.class, e -> e.getCode().equals(item.getStocktakeRange()));
        switch (stocktakeRangeEnum){
            case PART_WAREHOUSE:
                //如果是部分盘点
                if(CollectionUtil.isEmpty(item.getConditionItem())){
                    if(StringUtils.isBlank(item.getStocktakeTargetType())){
                        throw new ServiceException(StockExcepitonEnum.STOCKTAKE_TARGET_ERROR);
                    }
                }else{
                    List<String> ownerCodeList = item.getConditionItem().stream().map(e -> e.getOwnerCode()).distinct().collect(Collectors.toList());
                    //货主、盘点目标不能同时为空
                    if(CollectionUtil.isEmpty(ownerCodeList) && StringUtils.isBlank(item.getStocktakeTargetType())){
                        throw new ServiceException(StockExcepitonEnum.STOCKTAKE_TARGET_ERROR);
                    }
                    if(StringUtils.isNotBlank(item.getStocktakeTargetType())){
                        //区域编码
                        List<String> areaCodeList = item.getConditionItem().stream().map(e -> e.getAreaCode()).distinct().collect(Collectors.toList());
                        //库区编码
                        List<String> warehouseZoneCodeList = item.getConditionItem().stream().map(e -> e.getWarehouseZoneCode()).distinct().collect(Collectors.toList());
                        //库位编码
                        List<String> warehouseLocCodeList = item.getConditionItem().stream().map(e -> e.getWarehouseLocCode()).distinct().collect(Collectors.toList());
                        if(CollectionUtil.isEmpty(areaCodeList) && CollectionUtil.isEmpty(warehouseZoneCodeList) && CollectionUtil.isEmpty(warehouseLocCodeList)){
                            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_RANGE_ERROR);
                        }
                    }
                }
                break;
            case MOVE_PART_WAREHOUSE:
                //动碰盘点不能选择盲盘
                if(StocktakeTypeEnum.BAMNPAN.getCode().equals(item.getStocktakeType())){
                    throw new ServiceException(StockExcepitonEnum.STOCKTAKE_TYPE_ERROR);
                }
                if(ObjectUtil.isNull(item.getOperatorTimeStart()) || ObjectUtil.isNull(item.getOperatorTimeEnd())){
                    throw new ServiceException(StockExcepitonEnum.STOCKTAKE_OPERATOR_TIME_ERROR);
                }
                break;
            case CONTAINER_PART_WAREHOUSE:
                //按容器盘点
                if(CollectionUtil.isEmpty(item.getConditionItem())){
                    throw new ServiceException(StockExcepitonEnum.STOCKTAKE_PALLET_ERROR);
                }else {
                    StocktakeConditionItem conditionItem = item.getConditionItem().get(0);
                    if (StringUtils.isBlank(conditionItem.getPalletCode())){
                        throw new ServiceException(StockExcepitonEnum.STOCKTAKE_PALLET_ERROR);
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     *@Description 根据盘点单id查询对应盘点单及盘点单对应盘点任务、商品信息
     *@Param id
     *@Return * {@link StocktakeOrderGetByIdQuery }
     *@Date 2025/1/13 15:13
     *<AUTHOR>
     **/
    public StocktakeOrderGetByIdQuery getById(Long id) {
        StocktakeOrderGetByIdQuery query = new StocktakeOrderGetByIdQuery();
        InventoryStocktakeOrder order = dao.findById(id);
        BeanUtil.copyProperties(order, query);
        //盘点条件
        query.setConditionQueryList(stocktakeConditionService.findItemByStocktakeNo(order.getStocktakeNo()));
        return query;
    }

    /**
     *@Description 批量取消盘点
     *@Param idList
     *@Return Void
     *@Date 2025/1/13 19:56
     *<AUTHOR>
     **/
    public void cancelByIdList(List<Long> idList) {
        List<InventoryStocktakeOrder> orderList = dao.findByIds(idList.toArray(new Long[idList.size()]));
        //状态为已取消或已关闭不可取消
        List<String> statusList = Arrays.asList(StocktakeOrderStatusEnum.CANCEL.getCode(),StocktakeOrderStatusEnum.CLOSE.getCode());
        List<String> noList = orderList.stream().filter( x -> statusList.contains(x.getStocktakeStatus())).map(InventoryStocktakeOrder::getStocktakeNo).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(noList)){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_ORDER_CANCEL_ERROR,noList);
        }
        //修改盘点单及盘点任务状态为已取消
        dao.updateStatusByIdList(StocktakeOrderStatusEnum.CANCEL.getCode(),idList);
    }

    /**
     *@Description 批量取消盘点任务
     *@Param idList
     *@Return Void
     *@Date 2025/1/13 20:48
     *<AUTHOR>
     **/
    public void cancelTaskByIdList(List<Long> idList) {
        List<InventoryStocktakeOrder> orderList = dao.findByIds(idList.toArray(new Long[idList.size()]));
        //状态为创建才可取消
        List<String> noList = orderList.stream().filter( x -> !StocktakeOrderStatusEnum.CREATE.getCode().equals(x.getStocktakeStatus())).map(InventoryStocktakeOrder::getStocktakeNo).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(noList)){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_ORDER_CANCEL_TASK_ERROR,noList);
        }
        //修改盘点单及盘点任务状态为已取消
        dao.updateStatusByIdList(StocktakeOrderStatusEnum.CANCEL.getCode(),idList);
    }

    /**
     *@Description 批量删除盘点单及盘点任务及任务明细
     *@Param idList
     *@Return Void
     *@Date 2025/1/13 20:53
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIdList(List<Long> idList) {
        List<InventoryStocktakeOrder> orderList = dao.findByIds(idList.toArray(new Long[idList.size()]));
        //状态为创建才可删除
        List<String> noList = orderList.stream().filter( x -> !StocktakeOrderStatusEnum.CREATE.getCode().equals(x.getStocktakeStatus())).map(InventoryStocktakeOrder::getStocktakeNo).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(noList)){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_ORDER_CANCEL_DEL_ERROR,noList);
        }
        noList = orderList.stream().map(InventoryStocktakeOrder::getStocktakeNo).collect(Collectors.toList());
        //删除盘点单及盘点任务及任务明细
        stocktakeConditionService.deleteByStocktakeNoList(noList);
        stocktakeTaskService.deleteByStocktakeNoList(noList);
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeOrder::getId,idList);
        dao.delete(conditionRule);
    }

    /**
     *@Description 批量关闭盘点
     *@Param idList
     *@Return Void
     *@Date 2025/1/13 21:04
     *<AUTHOR>
     **/
    public void closeByIdList(List<Long> idList) {
        List<InventoryStocktakeOrder> orderList = dao.findByIds(idList.toArray(new Long[idList.size()]));
        //盘点单状态为已取消、已关闭、未创建 不能关闭
        List<String> statusList = Arrays.asList(StocktakeOrderStatusEnum.CANCEL.getCode(),StocktakeOrderStatusEnum.CLOSE.getCode(),StocktakeOrderStatusEnum.CREATE.getCode());
        List<String> noList = orderList.stream().filter( x -> statusList.contains(x.getStocktakeStatus())).map(InventoryStocktakeOrder::getStocktakeNo).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(noList)){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_ORDER_CANCEL_CLOSE_ERROR,noList);
        }
        dao.updateStatusByIdList(StocktakeOrderStatusEnum.CLOSE.getCode(),idList);
        List<InventoryStocktakeTask> taskList = stocktakeTaskService.findByStocktakeNoList(noList);
        //对应无源盘点任务手工盘点数量更新
        passiveAutoInventoryNewService.modifyTaskCount(taskList.stream().map(InventoryStocktakeTask::getTaskNo).collect(Collectors.toList()));
    }

    /**
     *@Description 重新生成盘点单及盘点任务及盘点任务明细
     *@Param idList
     *@Return Void
     *@Date 2025/1/13 21:13
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void againByIdList(List<Long> idList) {
        List<InventoryStocktakeOrder> orderList = dao.findByIds(idList.toArray(new Long[idList.size()]));
        List<String> noList = orderList.stream().filter( x -> !StocktakeOrderStatusEnum.CLOSE.getCode().equals(x.getStocktakeStatus())).map(InventoryStocktakeOrder::getStocktakeNo).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(noList)){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_ORDER_CANCEL_AGAIN_ERROR,noList);
        }
        //查询对应配置数据
        List<String> orderNoList = orderList.stream().map(InventoryStocktakeOrder::getStocktakeNo).collect(Collectors.toList());
        List<InventoryStocktakeCondition> conditionList = stocktakeConditionService.findByStocktakeNoList(orderNoList);
        Map<String, List<InventoryStocktakeCondition>> groupMap = conditionList.stream().collect(Collectors.groupingBy(InventoryStocktakeCondition::getStocktakeNo));
        List<InventoryStocktakeOrder> againOrderList = new ArrayList<>();
        List<InventoryStocktakeCondition> againConditionList = new ArrayList<>();
        for (InventoryStocktakeOrder order : orderList) {
            InventoryStocktakeOrder againOrder = new InventoryStocktakeOrder();
            BeanUtil.copyProperties(order,againOrder);
            againOrder.setStocktakeStatus(StocktakeOrderStatusEnum.CREATE.getCode());
            againOrder.setStocktakeNo(numberGenerator.nextValue(IdRuleConstant.STOCKTAKE_ORDER_CODE));
            againOrder.setId(null);
            preSave(againOrder);
            List<InventoryStocktakeCondition> currentList = groupMap.get(order.getStocktakeNo());
            if(CollectionUtil.isNotEmpty(currentList)){
                currentList.forEach(condition -> {
                    condition.setId(null);
                    condition.setStocktakeNo(againOrder.getStocktakeNo());
                    stocktakeConditionService.preSave(condition);
                });
                againConditionList.addAll(currentList);
            }

            againOrderList.add(againOrder);
            //重新生成盘点单及盘点任务及盘点任务明细
            List<InvLotLocForStockTakeOrderQuery> lotLocForStocktakeOrderQueryList = getStockData(againOrder,currentList);
            if(CollectionUtil.isNotEmpty(lotLocForStocktakeOrderQueryList)){
                //根据库存数据分组生成盘点任务及明细数据
                buildTaskAndDetailsData(againOrder,lotLocForStocktakeOrderQueryList);
            }
            if(CollectionUtil.isNotEmpty(againOrderList)){
                dao.batchInsert(againOrderList);
            }
            if (CollectionUtil.isNotEmpty(againConditionList)){
                stocktakeConditionService.batchInsert(againConditionList);
            }

        }
    }

    /**
     *@Description 批量生成调整单
     *@Param idList
     *@Return List<AdjustDetailItem>
     *@Date 2025/1/13 21:14
     *<AUTHOR>
     **/
    public List<AdjustDetailItem> addAdjustByIdList(List<Long> idList,String isSave) {
        List<AdjustDetailItem> adjustDetailList = new ArrayList<>();
        //是否都为已关闭状态
        List<InventoryStocktakeOrder> orderList = dao.findByIds(idList.toArray(new Long[idList.size()]));
        List<String> failNoList = orderList.stream().filter( x -> !StocktakeOrderStatusEnum.CLOSE.getCode().equals(x.getStocktakeStatus())).map(InventoryStocktakeOrder::getStocktakeNo).collect(Collectors.toList());
        boolean flag  = orderList.stream().anyMatch( x -> !StocktakeOrderStatusEnum.CLOSE.getCode().equals(x.getStocktakeStatus()));
        if(flag){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_ORDER_ADJUST_ERROR,failNoList);
        }
        //根据单号查询所有盘点有差异的任务详情
        List<String> noList = orderList.stream().map(InventoryStocktakeOrder::getStocktakeNo).collect(Collectors.toList());
        List<GeneraAdjustItem> generaAdjustItemList = detailsService.findGeneraAdjustItemList(noList,Arrays.asList(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(),WarehouseLocUseTypeEnum.TALLYING_STATION.getCode()));
        if(CollectionUtil.isEmpty(generaAdjustItemList)){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_ORDER_ADJUST_DATA_ERROR,failNoList);
        }
        List<AdjustHeaderItem> insertList = generaAdjustData(generaAdjustItemList);


        if(CollectionUtil.isNotEmpty(insertList)){
            //批量生成调整单
            if(YesNoEnum.YES.getCode().equals(isSave)){
                adjustHeaderService.batchSaveAdjustHeader(insertList);
            }
            //返回insertList中所有的itemList
            insertList.forEach(x -> {
                adjustDetailList.addAll(x.getAdjustDetailList());
            });
        }
        return adjustDetailList;
    }

    /**
     *@Description 组装调整单数据
     *@Param generaAdjustItemList
     *@Return * {@link List< AdjustHeaderItem> }
     *@Date 2025/2/13 17:50
     *<AUTHOR>
     **/
    private List<AdjustHeaderItem> generaAdjustData(List<GeneraAdjustItem> generaAdjustItemList) {
        List<AdjustHeaderItem> resultList = new ArrayList<>();
        //抽取商品id
        List<Long> skuIdList = generaAdjustItemList.stream().map(GeneraAdjustItem::getSkuId).distinct().collect(Collectors.toList());
        //根据商品id查询对应商品信息
        List<Sku> skuList = skuService.findByIds(skuIdList.toArray(new Long[0]));
        //转map
        Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Function.identity()));
        //收集对应packageId
        List<Long> packageIdList = skuList.stream().map(Sku::getPackageId).distinct().collect(Collectors.toList());
        //通过packageId查询对应包装单位信息
        List<PackageUnit> packageUnitList = packageUnitService.listByPackageIdListAndDefault(packageIdList);
        //转map
        Map<Long, PackageUnit> packageUnitMap = packageUnitList.stream().collect(Collectors.toMap(PackageUnit::getPackageId, Function.identity()));
        //按照盘点单分组
        Map<String, List<GeneraAdjustItem>> groupMap = generaAdjustItemList.stream().collect(Collectors.groupingBy(GeneraAdjustItem::getStocktakeNo));
        //数据过滤 同个盘点单内只有仓库和货主一致的明细才生成到同一个调整单内
        //循环map
        for (Map.Entry<String, List<GeneraAdjustItem>> entry : groupMap.entrySet()) {
            List<GeneraAdjustItem> currentList = entry.getValue();
            //根据仓库id和货主id过滤收集到同一个集合
            Map<String, List<GeneraAdjustItem>> sameMap = currentList.stream().collect(Collectors.groupingBy(item -> item.getWarehouseId() +"_"+ item.getOwnerId()));
            //循环map组装数据
            for (Map.Entry<String, List<GeneraAdjustItem>> sameEntry : sameMap.entrySet()) {
                List<GeneraAdjustItem> sameList = sameEntry.getValue();
                if(CollectionUtil.isNotEmpty(sameList)){
                    //调整单头部信息
                    AdjustHeaderItem adjustHeaderItem = getAdjustHeaderItem(entry, sameList);
                    //调整单明细信息
                    List<AdjustDetailItem> adjustDetailItemList = getAdjustDetailItemList(sameList,skuMap,packageUnitMap);
                    adjustHeaderItem.setAdjustDetailList(adjustDetailItemList);
                    resultList.add(adjustHeaderItem);
                }
            }
        }
        return resultList;
    }

    /**
     *@Description 组装调整单明细数据
     *@Param sameList
     *@param skuMap
     *@param packageUnitMap
     *@Return * {@link List< AdjustDetailItem> }
     *@Date 2025/2/7 19:40
     *<AUTHOR>
     **/
    private List<AdjustDetailItem> getAdjustDetailItemList(List<GeneraAdjustItem> sameList, Map<Long, Sku> skuMap, Map<Long, PackageUnit> packageUnitMap) {
        List<AdjustDetailItem> adjustDetailItemList = new ArrayList<>();
        //查询盘点sn
        List<InventoryStocktakeTaskDetailsSn> locSnItemList = taskDetailsSnService.findByTaskDetailsId(sameList.stream().map(GeneraAdjustItem::getId).distinct().collect(Collectors.toList()));
        Map<Long, List<InventoryStocktakeTaskDetailsSn>> locSnItemMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(locSnItemList)){
            //按照盘点单号分组
            locSnItemMap = locSnItemList.stream().collect(Collectors.groupingBy(InventoryStocktakeTaskDetailsSn::getTaskDetailsId));
        }
        //商品id集合
        List<Long> skuIdList = sameList.stream().map(GeneraAdjustItem::getSkuId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
        //库位id集合
        List<Long> locIdList = sameList.stream().map(GeneraAdjustItem::getLocId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
        //批次号集合
        List<String> lotNumList = sameList.stream().map(GeneraAdjustItem::getLotNum).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        //容器号集合
        List<String> palletNumList = sameList.stream().map(GeneraAdjustItem::getPalletNum).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        //查询库序列号
        List<InvLotLocSn> locSnList = invLotLocSnService.getByLocIdListAndSkuIdListAndLotNumList(skuIdList, locIdList, lotNumList, null, YesNoEnum.NO.getCode());
        Map<String,List<InvLotLocSn>> groupLocSnMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(locSnList)){
            groupLocSnMap = locSnList.stream().collect(Collectors.groupingBy(x -> x.getSkuId()+"_"+x.getLocId()+"_"+x.getLotNum()));
        }
        for(GeneraAdjustItem item : sameList){
            AdjustDetailItem adjustDetailItem = new AdjustDetailItem();
            BeanUtil.copyProperties(item,adjustDetailItem);
            adjustDetailItem.setLocCode(item.getLocName());
            //盘点单号
            adjustDetailItem.setCountNo(item.getStocktakeNo());
            //调整方式：增加/减少
            Long result = item.getActualCount() - item.getInventoryCount();
            if(result > 0){
                //实盘数量比库存数量多
                adjustDetailItem.setAdjustMode(AdjustModeEnum.ADD.getCode());
            }else{
                //实盘数量比库存数量少
                adjustDetailItem.setAdjustMode(AdjustModeEnum.REDUCE.getCode());
            }
            //调整前ea 为 库存数量
            adjustDetailItem.setQtyAdjustBeforeEa(BigDecimal.valueOf(item.getInventoryCount()));
            //调整后ea 为库存数量 + 差值
            adjustDetailItem.setQtyAdjustAfterEa(BigDecimal.valueOf(item.getInventoryCount() + result));
            //调整数ea
            adjustDetailItem.setQtyAdjustEa(BigDecimal.valueOf(Math.abs(result)));
            Sku sku = skuMap.get(item.getSkuId());
            if(ObjectUtil.isNotNull(sku)){
                //包装id
                adjustDetailItem.setPackageId(sku.getPackageId());
                //包装名称
                adjustDetailItem.setPackageName(sku.getPackageName());
                PackageUnit packageUnit = packageUnitMap.get(sku.getPackageId());
                if(ObjectUtil.isNotNull(packageUnit)){
                    //包装单位id
                    adjustDetailItem.setPackageUomId(packageUnit.getId());
                    //包装单位名称
                    adjustDetailItem.setPackageUomName(packageUnit.getName());
                    //调整数 = 差异数/包装单位数量
                    adjustDetailItem.setQtyAdjustUom(BigDecimal.valueOf(Math.abs(result)/packageUnit.getMinQuantity()));
                }
            }
            adjustDetailItem.setAdjustType(AdjustTypeEnum.INVENTORY.getCode());

            //盘点序列号信息
            List<InventoryStocktakeTaskDetailsSn> currrentList = locSnItemMap.get(item.getId());
            List<InvLotLocSn> currentSnList = groupLocSnMap.get(item.getSkuId()+"_"+item.getLocId()+"_"+item.getLotNum());
            if(CollectionUtil.isNotEmpty(currrentList)){
                //当前库存序列号列表中存在盘点序列号中没有的序列号
                List<String> defferentSnCodeList = new ArrayList<>();
                if(CollectionUtil.isNotEmpty(currentSnList)){
                    List<String> snList = currrentList.stream().map(InventoryStocktakeTaskDetailsSn::getSnNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                    currentSnList.forEach(x -> {
                        if(!snList.contains(x.getSkuSn())){
                            defferentSnCodeList.add(x.getSkuSn());
                        }
                    });
                }
                if(CollectionUtil.isNotEmpty(defferentSnCodeList)){
                    //根据snList查询入库
                    LocSnDetailCondition condition = new LocSnDetailCondition();
                    condition.setSnList(defferentSnCodeList);
                    List<LocSnItem> locSnItems = invLotLocSnService.findListByKey(condition);
                    if(CollectionUtil.isNotEmpty(locSnItems)){
                        Map<String,List<String>> snMap = new HashMap<>();
                        //无箱码数据
                        List<String> noBoxCode = locSnItems.stream().filter(x -> StringUtils.isBlank(x.getBoxCode())).map(LocSnItem::getSkuSn).collect(Collectors.toList());
                        if(CollectionUtil.isNotEmpty(noBoxCode)){
                            snMap.put("无箱码",noBoxCode);
                        }
                        //有箱码数据
                        Map<String, List<String>> groupMap = locSnItems.stream().filter(x -> StringUtils.isNotBlank(x.getBoxCode()))
                                .collect(Collectors.groupingBy(LocSnItem::getBoxCode,
                                        Collectors.mapping(LocSnItem::getSkuSn, Collectors.toList())));
                        if(MapUtil.isNotEmpty(groupMap)){
                            snMap.putAll(groupMap);
                        }
                        if(MapUtil.isNotEmpty(snMap)){
                            adjustDetailItem.setSnStr(MapObjectUtil.mapToObject(snMap));
                            List<LocSnItemQuery> locSnItemQueryList = new ArrayList<>();
                            for (Map.Entry<String, List<String>> snEntry : snMap.entrySet()) {
                                LocSnItemQuery locSnItemQuery = new LocSnItemQuery();
                                locSnItemQuery.setKey(snEntry.getKey());
                                locSnItemQuery.setValue(snEntry.getValue());
                                locSnItemQueryList.add(locSnItemQuery);
                            }
                            adjustDetailItem.setSnMap(locSnItemQueryList);
                        }
                    }
                }
            }
            adjustDetailItemList.add(adjustDetailItem);
        }
        return adjustDetailItemList;
    }

    /**
     *@Description 组装调整单头部信息
     *@Param entry
     *@param sameList
     *@Return * {@link AdjustHeaderItem }
     *@Date 2025/2/7 19:39
     *<AUTHOR>
     **/
    @NotNull
    private static AdjustHeaderItem getAdjustHeaderItem(Map.Entry<String, List<GeneraAdjustItem>> entry, List<GeneraAdjustItem> sameList) {
        AdjustHeaderItem adjustHeaderItem = new AdjustHeaderItem();
        //盘点单号
        adjustHeaderItem.setCountNo(entry.getKey());
        //货主id
        adjustHeaderItem.setOwnerId(sameList.get(0).getOwnerId());
        //货主名称
        adjustHeaderItem.setOwnerName(sameList.get(0).getOwnerName());
        //仓库id
        adjustHeaderItem.setWarehouseId(sameList.get(0).getWarehouseId());
        //仓库名称
        adjustHeaderItem.setWarehouseName(sameList.get(0).getWarehouseName());
        //调整人
        adjustHeaderItem.setAdjustOp(SessionContext.getSessionUserInfo().getRealName());
        //调整时间
        adjustHeaderItem.setAdjustTime(new Date());
        return adjustHeaderItem;
    }


    /***
     *@Description 修改盘点状态为已关闭
     *@Param stocktakeNo
     *@param code
     *@Return Void
     *@Date 2025/1/14 11:12
     *<AUTHOR>
     **/
    public void updateByStocktakeNo(List<String> stocktakeNoList, String code) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeOrder::getStocktakeNo,stocktakeNoList);
        StocktakeOrderUpdateStatusItem item = new StocktakeOrderUpdateStatusItem();;
        item.setStocktakeStatus(code);
        dao.updateByCondition(item,conditionRule);
    }

    /**
     *@Description 根据编号进行查询
     *@Param stocktakeNo
     *@Return * {@link InventoryStocktakeOrder }
     *@Date 2025/1/14 16:54
     *<AUTHOR>
     **/
    public InventoryStocktakeOrder findByStocktakeNo(String stocktakeNo) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(InventoryStocktakeOrder::getStocktakeNo,stocktakeNo);
        return dao.findFirst(conditionRule);
    }

    /**
     *@Description 盘点单导出
     *@Param condition
     *@param httpServletResponse
     *@Return Void
     *@Date 2025/1/15 14:42
     *<AUTHOR>
     **/
    public void export(StocktakeOrderPageCondition condition, HttpServletResponse httpServletResponse) {
        List<InventoryStocktakeOrder> orderList;
        if(CollectionUtil.isNotEmpty(condition.getIdList())){
            //通过Id查询对应盘点单
            orderList = dao.findByIds(condition.getIdList().toArray(new Long[condition.getIdList().size()]));
        }else{
            orderList = dao.findListByPageCondition(condition);
        }

        //抽取盘点单号
        List<String> noList = orderList.stream().map(InventoryStocktakeOrder::getStocktakeNo).collect(Collectors.toList());
        //通过盘点单号集合查询对应盘点任务
        List<InventoryStocktakeTask> taskList = stocktakeTaskService.findByStocktakeNoList(noList);
        //抽取任务编号
        List<String> taskNoList = taskList.stream().map(InventoryStocktakeTask::getTaskNo).collect(Collectors.toList());
        //根据任务编号查询对应盘点任务明细
        List<StocktakeTaskDetailsQuery> detailsQueryList = detailsService.findQueryByTaskNoList(taskNoList);
        exportDataHandler.exportStocktakeOrder(orderList,detailsQueryList,httpServletResponse);
    }

    /**
     *@Description 选择盘点员
     *@Param condition
     *@Return * {@link List< UserForOuterQuery> }
     *@Date 2025/1/20 10:40
     *<AUTHOR>
     **/
    public ResponseData<PageResult<UserForOuterQuery>> getUserPage(UserPageCondition condition) {
        return remoteUserService.getUserPage(condition);
    }

    /**
     *@Description 根据传入的库存数据生成盘点单、盘点单条件、盘点任务、及盘点任务明细
     *@Param invLotLocList
     *@Return * {@link List< InventoryStocktakeTaskDetails> }
     *@Date 2025/7/22 20:00
     *<AUTHOR>
     **/
    public List<InventoryStocktakeTaskDetails> generateTaskDetails(List<InvLotLoc> invLotLocList) {
        //库存数据根据仓库分组
        Map<Long, List<InvLotLoc>> groupByWarehouseId = invLotLocList.stream().collect(Collectors.groupingBy(InvLotLoc::getWarehouseId));
        List<InventoryStocktakeOrder> orderList = new ArrayList<>();
        List<InventoryStocktakeTask> taskList = new ArrayList<>();
        List<InventoryStocktakeTaskDetails> taskDetailsList = new ArrayList<>();
        groupByWarehouseId.forEach((warehouseId, warehouseLocList1) -> {
            //根据库区分组
            //生成盘点单数据
            InventoryStocktakeOrder order = new InventoryStocktakeOrder();
            order.setWarehouseId(warehouseId);
            order.setWarehouseName(warehouseLocList1.get(0).getWarehouseName());
            order.setStocktakeStatus(StocktakeOrderStatusEnum.CREATE.getCode());
            order.setStocktakeType(StocktakeTypeEnum.MINGPAN.getCode());
            order.setStocktakeRange(StocktakeRangeEnum.ALL_WAREHOUSE.getCode());
            order.setTaskType(StocktakeTaskTypeEnum.DIRECT_GENERATION.getCode());
            order.setStocktakeNo(numberGenerator.nextValue(IdRuleConstant.STOCKTAKE_ORDER_CODE));
            preSave(order);
            orderList.add(order);

        });

        if(CollectionUtil.isNotEmpty(orderList)){
            Map<String,List<?>> result = generateTaskAndDetails(orderList,invLotLocList);
            if(CollectionUtil.isNotEmpty(result)){
                taskDetailsList = (List<InventoryStocktakeTaskDetails>) result.get("detailsList");
                taskList = (List<InventoryStocktakeTask>) result.get("taskList");
            }

        }
        if(CollectionUtil.isNotEmpty(orderList)){
            dao.batchInsert(orderList);
        }
        if(CollectionUtil.isNotEmpty(taskList)){
            stocktakeTaskService.batchInsert(taskList);
        }
        if(CollectionUtil.isNotEmpty(taskDetailsList)){
            detailsService.batchInsert(taskDetailsList);
        }
        return taskDetailsList;
    }

    /***
     *@Description 根据订单和库存数据生成盘点单、盘点任务及盘点任务明细
     *@Param order
     *@param allInvLotLocList
     *@Return * {@link List< InventoryStocktakeTaskDetails> }
     *@Date 2025/7/23 9:22
     *<AUTHOR>
     **/
    private Map<String,List<?>> generateTaskAndDetails(List<InventoryStocktakeOrder> orderList, List<InvLotLoc> allInvLotLocList) {
        Map<String,List<?>> resultMap = new HashMap<>();
        List<InventoryStocktakeTask> taskList = new ArrayList<>();
        List<InventoryStocktakeTaskDetails> finalDetailsList = new ArrayList<>();
        for(InventoryStocktakeOrder order : orderList){
            List<InvLotLoc> invLotLocList = allInvLotLocList.stream().filter(invLotLoc -> invLotLoc.getWarehouseId().equals(order.getWarehouseId())).collect(Collectors.toList());
            Map<Long,List<InvLotLoc>> groupMap = new HashMap<>();
            groupMap.put(-1L,invLotLocList);
            //根据盘点单号查询当前任务表中任务编号最大值
            Long max = stocktakeTaskService.getMaxTaskNo(order.getStocktakeNo());
            if(CollectionUtil.isNotEmpty(groupMap)){
                //for循环map
                for (Map.Entry<Long, List<InvLotLoc>> entry : groupMap.entrySet()){
                    List<InventoryStocktakeTaskDetails> detailsList = new ArrayList<>();
                    max = max + 1;
                    InventoryStocktakeTask task = new InventoryStocktakeTask();
                    BeanUtil.copyProperties(order, task);
                    task.setTaskStatus(StocktakeTaskStatusEnum.CREATE.getCode());
                    task.setTaskAllocation(YesNoEnum.NO.getCode());
                    task.setTaskNo(order.getStocktakeNo() +"-"+max);
                    task.setId(null);
                    stocktakeTaskService.preSave(task);
                    taskList.add(task);
                    List<InvLotLoc> currentList = entry.getValue();
                    currentList.forEach(x ->{
                        InventoryStocktakeTaskDetails details = new InventoryStocktakeTaskDetails();
                        BeanUtil.copyProperties(x, details);
                        details.setWarehouseZoneId(null);
                        details.setWarehouseZoneName(null);
                        details.setStocktakeNo(order.getStocktakeNo());
                        details.setTaskNo(task.getTaskNo());
                        details.setInventoryCount(x.getQty().longValue());
                        details.setLocName(x.getLocCode());
                        details.setId(null);
                        details.setDataSource(YesNoEnum.NO.getCode());
                        details.setPdaMatchStatus(YesNoEnum.NO.getCode());
                        detailsService.preSave(details);
                        detailsList.add(details);
                    });
                    //将同仓库，同商品，同库位，同批次号的数据合并为一条
                    Map<String, List<InventoryStocktakeTaskDetails>>  map = detailsList.stream().collect(Collectors.groupingBy(e ->e.getWarehouseId() +"_"+ e.getSkuId()+"_"+ e.getLocId() + '_'+ e.getLotNum()));
                    map.forEach((k,v) ->{
                        List<InventoryStocktakeTaskDetails> list = v;
                        if(CollectionUtil.isNotEmpty(list)){
                            InventoryStocktakeTaskDetails details = new InventoryStocktakeTaskDetails();
                            //累加库存数
                            Long amount = list.stream().map(InventoryStocktakeTaskDetails::getInventoryCount).reduce(0L, Long::sum);
                            BeanUtil.copyProperties(list.get(0),details);
                            details.setInventoryCount(amount);
                            finalDetailsList.add(details);
                        }
                    });
                }
            }
        }
        resultMap.put("taskList", taskList);
        resultMap.put("detailsList", finalDetailsList);
        return resultMap;
    }
}
