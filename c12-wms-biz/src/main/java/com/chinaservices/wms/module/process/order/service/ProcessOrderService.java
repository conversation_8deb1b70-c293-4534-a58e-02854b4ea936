package com.chinaservices.wms.module.process.order.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinaservices.auth.module.user.domain.UserForOuterCondition;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.auth.module.user.feign.RemoteUserService;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.domain.IdCondition;
import com.chinaservices.wms.common.enums.asn.PaSourceEnum;
import com.chinaservices.wms.common.enums.asn.PaStatusEnum;
import com.chinaservices.wms.common.enums.process.ProcessOrderAllocationStatusEnum;
import com.chinaservices.wms.common.enums.process.ProcessOrderPickingStatusEnum;
import com.chinaservices.wms.common.enums.process.ProcessOrderStatusEnum;
import com.chinaservices.wms.common.enums.process.ProcessOrderTypeEnum;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.common.exception.ProcessExceptionEnum;
import com.chinaservices.wms.module.asn.pa.model.CsPaTask;
import com.chinaservices.wms.module.asn.pa.service.CsPaTaskService;
import com.chinaservices.wms.module.asn.receive.model.AsnReceive;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.folder.logistics.domain.BatchDelDetailItem;
import com.chinaservices.wms.module.process.order.dao.ProcessOrderDao;
import com.chinaservices.wms.module.process.order.domain.*;
import com.chinaservices.wms.module.process.order.model.ProcessOrder;
import com.chinaservices.wms.module.process.order.model.ProcessTask;
import com.chinaservices.wms.module.process.order.model.ProcessTaskDetails;
import com.chinaservices.wms.module.rule.common.RulePaCommonService;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.att.query.InvLotAttQuery;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName ProcessOrderService
 * <AUTHOR>
 * @Date 2025/6/26 20:33
 * @Description
 * @Version 1.0
 */
@Service
public class ProcessOrderService extends ModuleBaseServiceSupport<ProcessOrderDao, ProcessOrder,Long> {

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private ProcessTaskService taskService;

    @Autowired
    private NumberGenerator numberGenerator;

    @Autowired
    private ProcessTaskDetailsService taskDetailsService;

    @Autowired
    private RulePaCommonService rulePaCommonService;

    @Autowired
    private WarehouseLocService warehouseLocService;

    @Autowired
    private PackageUnitService packageUnitService;

    @Autowired
    private CsPaTaskService paTaskService;

    @Autowired
    private StockService stockService;



    /**
     *@Description 加工单/拆分单分页查询
     *@Param condition
     *@Return * {@link PageResult< ProcessOrderQuery> }
     *@Date 2025/6/27 14:04
     *<AUTHOR>
     **/
    public PageResult<ProcessOrderQuery> page(ProcessOrderPageCondition condition) {
        PageResult<ProcessOrderQuery> pageResult = dao.findPage(condition);
        if(CollectionUtil.isNotEmpty(pageResult.getRows())){
            setTaskInfo(pageResult.getRows());
        }
        return pageResult;
    }

    /**
     *@Description 设置详细信息
     *@Param rows
     *@Return Void
     *@Date 2025/6/30 11:45
     *<AUTHOR>
     **/
    private void setTaskInfo(List<ProcessOrderQuery> rows) {
        //抽取创建人ID
        List<Long> creatorIdList = rows.stream().map(ProcessOrderQuery::getCreator).distinct().toList();
        Map<Long, UserForOuterQuery> userMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(creatorIdList)){
            UserForOuterCondition userForOuterCondition = new UserForOuterCondition();
            userForOuterCondition.setIds(creatorIdList.toArray(new Long[0]));
            ResponseData<List<UserForOuterQuery>> responseData =remoteUserService.getUserList(userForOuterCondition);
            if(responseData.getSuccess()){
                List<UserForOuterQuery> userList = responseData.getData();
                if(CollectionUtil.isNotEmpty(userList)){
                    userMap = userList.stream().collect(Collectors.toMap(UserForOuterQuery::getId, Function.identity(), (k1, k2) -> k1));
                }
            }
        }
        //抽取编号
        List<String> processOrderNoList = rows.stream().map(ProcessOrderQuery::getProcessOrderNo).distinct().toList();
        //查询对应任务
        List<ProcessTaskQuery> taskList = taskService.findQueryByProcessOrderNoList(processOrderNoList);
        Map<String, List<ProcessTaskQuery>> taskMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(taskList)){
            taskMap = taskList.stream().collect(Collectors.groupingBy(ProcessTaskQuery::getProcessOrderNo));
        }
        for (ProcessOrderQuery processOrderQuery:rows){
            if(ObjectUtil.isNotEmpty(userMap.get(processOrderQuery.getCreator()))) {
                processOrderQuery.setCreatorName(userMap.get(processOrderQuery.getCreator()).getRealName());
            }
            List<ProcessTaskQuery> currentTaskList = taskMap.get(processOrderQuery.getProcessOrderNo());
            if(CollectionUtil.isNotEmpty(currentTaskList)){
                processOrderQuery.setCombinationsNo(currentTaskList.get(0).getCombinationsNo());
                processOrderQuery.setCombinationsSkuName(currentTaskList.get(0).getCombinationsSkuName());
                processOrderQuery.setTaskQueryList(currentTaskList);
            }
        }
    }

    /**
     *@Description 根据ID查询加工单信息
     *@Param id
     *@Return * {@link ProcessOrderQuery }
     *@Date 2025/6/30 14:25
     *<AUTHOR>
     **/
    public ProcessOrderQuery getQueryById(Long id) {
        ProcessOrder processOrder = dao.findById(id);
        ProcessOrderQuery processOrderQuery = new ProcessOrderQuery();
        if (ObjectUtil.isNotNull(processOrder)){
            BeanUtil.copyProperties(processOrder, processOrderQuery);
            setTaskInfo(Arrays.asList(processOrderQuery));
        }
        return processOrderQuery;
    }

    /**
     *@Description 根据ID批量删除加工单信息
     *@Param delDetailItem
     *@Return Void
     *@Date 2025/6/30 14:34
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void batchDel(BatchDelDetailItem delDetailItem) {
        List<ProcessOrder> processOrderList =dao.findByIds(delDetailItem.getIdList().toArray(new Long[0]));
        //只有已创建状态数据才能删除
        boolean flag = processOrderList.stream().anyMatch(processOrder -> !ProcessOrderStatusEnum.CREATE.getCode().equals(processOrder.getProcessOrderStatus()));
        if (flag){
            throw new ServiceException(ProcessExceptionEnum.PROCESS_DEL_ONLY_CREATE);
        }
        dao.delete(delDetailItem.getIdList().toArray(new Long[0]));
        //删除对应任务
        List<String> processOrderNoList = processOrderList.stream().map(ProcessOrder::getProcessOrderNo).distinct().toList();
        taskService.batchDeleteByProcessOrderNoList(processOrderNoList);
    }

    /**
     *@Description 加工单批量审核
     *@Param delDetailItem
     *@Return Void
     *@Date 2025/6/30 15:41
     *<AUTHOR>
     **/
    public void batchAudit(BatchDelDetailItem delDetailItem) {
        List<ProcessOrder> processOrderList =dao.findByIds(delDetailItem.getIdList().toArray(new Long[0]));
        boolean flag = processOrderList.stream().anyMatch(processOrder -> !ProcessOrderStatusEnum.CREATE.getCode().equals(processOrder.getProcessOrderStatus()));
        if ( flag){
            throw new ServiceException(ProcessExceptionEnum.PROCESS_AUDIT_ONLY_CREATE);
        }
        processOrderList.forEach(processOrder -> {
            processOrder.setProcessOrderStatus(ProcessOrderStatusEnum.AUDIT.getCode());
            preSave(processOrder);
        });
        dao.batchUpdate(processOrderList);
    }

    /**
     *@Description 批量取消审核
     *@Param delDetailItem
     *@Return Void
     *@Date 2025/6/30 15:44
     *<AUTHOR>
     **/
    public void batchCancelAudit(BatchDelDetailItem delDetailItem) {
        List<ProcessOrder> processOrderList =dao.findByIds(delDetailItem.getIdList().toArray(new Long[0]));
        boolean flag = processOrderList.stream().anyMatch(processOrder -> !ProcessOrderStatusEnum.AUDIT.getCode().equals(processOrder.getProcessOrderStatus()));
        if ( flag){
            throw new ServiceException(ProcessExceptionEnum.PROCESS_CANCEL_AUDIT_ONLY_AUDIT);
        }
        processOrderList.forEach(processOrder -> {
            processOrder.setProcessOrderStatus(ProcessOrderStatusEnum.CREATE.getCode());
            preSave(processOrder);
        });
        dao.batchUpdate(processOrderList);
    }

    /**
     *@Description 保存/修改加工单信息
     *@Param item
     *@Return Void
     *@Date 2025/6/30 17:40
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveEntity(ProcessOrderItem item) {
        ProcessOrder processOrder = new ProcessOrder();
        if(ObjectUtil.isNotEmpty(item.getId())){
            processOrder = dao.findById(item.getId());
            //删除对应任务
            taskService.batchDeleteByProcessOrderNoList(Arrays.asList(processOrder.getProcessOrderNo()));
        }else {
            String processOrderNo;
            if(ProcessOrderTypeEnum.PROCESS_ORDER.getCode().equals(item.getOrderType())){
                processOrderNo = numberGenerator.nextValue(IdRuleConstant.PROCESS_ORDER_NO_PROCESS);
            }else {
                processOrderNo = numberGenerator.nextValue(IdRuleConstant.PROCESS_ORDER_NO_SPLIT);
            }
            processOrder.setProcessOrderStatus(ProcessOrderStatusEnum.CREATE.getCode());
            processOrder.setProcessOrderNo(processOrderNo);
            processOrder.setPickingStatus(ProcessOrderPickingStatusEnum.NOT_PICKED.getCode());
            processOrder.setAllocationStatus(ProcessOrderAllocationStatusEnum.UNALLOCATED.getCode());
        }
        BeanUtil.copyProperties(item, processOrder);
        dao.saveOrUpdate(processOrder);
        //保存对应任务
        ProcessOrder finalProcessOrder = processOrder;
        item.getTaskItemList().forEach(taskItem -> {
            taskItem.setProcessOrderNo(finalProcessOrder.getProcessOrderNo());
        });
        taskService.batchSaveEntity(item.getTaskItemList());
    }

    /**
     *@Description 批量加工确认数据组装
     *@Param delDetailItem
     *@Return Void
     *@Date 2025/6/30 20:50
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void batchConfirm(ProcessOrderConfirmItem confirmItem) {
        //获取加工任务信息
        ProcessTask processTask = taskService.findById(confirmItem.getTaskId());
        Long packageUnitId = processTask.getCombinationsPackageUnitId();
        //查询对应包装单位
        PackageUnit packageUnit = packageUnitService.findById(packageUnitId);
        //实际加工数 = 实际加工数 + 本次加工数
        processTask.setActualProcessQuantity(processTask.getActualProcessQuantity().add(confirmItem.getConfirmQuantity()));
        //实际加工数ea
        if(ObjectUtil.isNotNull(packageUnit)){
            //本次加工ea数是是否大于拣货数ea
            BigDecimal confirmQuantityEa = confirmItem.getConfirmQuantity().multiply(new BigDecimal(packageUnit.getMinQuantity()));
            if(processTask.getQtyPickingEa().compareTo(confirmQuantityEa) < 0){
                throw new ServiceException(ProcessExceptionEnum.CONFIRM_QUANTITY_EA_LARGE_THAN_PICKING_QUANTITY_EA,processTask.getCombinationsSkuName());
            }
            processTask.setActualProcessQuantityEa(processTask.getActualProcessQuantity().multiply(new BigDecimal(packageUnit.getMinQuantity())));
        }
        taskService.saveOrUpdate(processTask);
        //查询对应明细
        List<ProcessTaskDetails> processTaskDetails = taskDetailsService.findByTaskNo(processTask.getProcessTaskNo());
        List<Long> packageUnitIdList = processTaskDetails.stream().map(ProcessTaskDetails::getPackageUnitId).distinct().toList();
        List<PackageUnit> packageUnitList = packageUnitService.findByIds(packageUnitIdList.toArray(Long[]::new));
        Map<Long,PackageUnit> packageUnitMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(packageUnitList)){
            packageUnitMap = packageUnitList.stream().collect(Collectors.toMap(PackageUnit::getId, Function.identity(), (v1, v2) -> v1));
        }
        Map<Long, PackageUnit> finalPackageUnitMap = packageUnitMap;
        //查询对应加工单
        ProcessOrder processOrder = dao.findFirst(ConditionRule.getInstance().andEqual(ProcessOrder::getProcessOrderNo,processTask.getProcessOrderNo()));
        //如果 实际加工数 > 0 且 小于待加工数 加工单状态为 部分加工
        if (processTask.getActualProcessQuantity().compareTo(BigDecimal.ZERO) > 0
                && processTask.getActualProcessQuantity().compareTo(processTask.getProcessQuantity()) < 0){
            processOrder.setProcessOrderStatus(ProcessOrderStatusEnum.PARTIAL_PROCESS.getCode());
        }else if (processTask.getActualProcessQuantity().compareTo(BigDecimal.ZERO) > 0
                && processTask.getActualProcessQuantity().compareTo(processTask.getProcessQuantity()) == 0){
            processOrder.setProcessOrderStatus(ProcessOrderStatusEnum.COMPLETE_PROCESS.getCode());
            processOrder.setActualProcessingTime(DateUtil.date());
        }
        dao.saveOrUpdate(processOrder);
        if(ProcessOrderTypeEnum.PROCESS_ORDER.getCode().equals(processOrder.getOrderType())){
            processTaskDetails.forEach(details -> {
                PackageUnit currentPackageUnit = finalPackageUnitMap.get(details.getPackageUnitId());
                //本次加工数 = 源数量 * 确认加工数
                BigDecimal amount = details.getSkuQuantity().multiply(confirmItem.getConfirmQuantity());
                //实际加工数 = 实际加工数 + 本次加工数
                details.setDetailsActualProcessQuantity(details.getDetailsActualProcessQuantity().add(amount));
                //实际加工数ea
                if(ObjectUtil.isNotNull(currentPackageUnit)){
                    details.setDetailsActualProcessQuantityEa(details.getDetailsActualProcessQuantity().multiply(new BigDecimal(currentPackageUnit.getMinQuantity())));
                }
                taskDetailsService.preSave(details);
            });
            taskDetailsService.batchUpdate(processTaskDetails);
            //加工单生成加工上架任务
            generateTaskPickingTask(processTask,processOrder,confirmItem);
        }else{
            Map<Long,ConfirmDetailsItem> confirmDetailsItemMap = confirmItem.getConfirmDetailsItemList().stream().collect(Collectors.toMap(ConfirmDetailsItem::getId, Function.identity(), (key1, key2) -> key1));
            List<ProcessTaskDetails> updateList = new ArrayList<>();
            processTaskDetails.forEach(details -> {
                PackageUnit currentPackageUnit = finalPackageUnitMap.get(details.getPackageUnitId());
                //查询对应明细
                ConfirmDetailsItem confirmDetailsItem = confirmDetailsItemMap.get(details.getId());
                if (ObjectUtil.isNotNull(confirmDetailsItem)){
                    //实际加工数 = 实际加工数 + 确认加工数
                    BigDecimal amount = details.getDetailsActualProcessQuantity().add(confirmItem.getConfirmQuantity());
                    details.setDetailsProcessQuantity(amount);
                    //实际加工数ea
                    if(ObjectUtil.isNotNull(currentPackageUnit)){
                        //确认加工数是否比拣货数ea大
                        BigDecimal confirmQuantityEa = confirmDetailsItem.getConfirmQuantity().multiply(new BigDecimal(currentPackageUnit.getMinQuantity()));
                        if(details.getQtyPickingEa().compareTo(confirmQuantityEa) <0){
                            throw new ServiceException(ProcessExceptionEnum.CONFIRM_QUANTITY_EA_LARGE_THAN_PICKING_QUANTITY_EA,details.getSkuName());
                        }
                        details.setDetailsProcessQuantityEa(amount.multiply(new BigDecimal(currentPackageUnit.getMinQuantity())));
                    }
                    taskDetailsService.preSave(details);
                    updateList.add(details);
                }
            });
            if(CollectionUtil.isNotEmpty(updateList)){
                taskDetailsService.batchUpdate(updateList);
                //拆分单生成加工上架任务
                generateDetailsPickingTask(updateList,processOrder,confirmDetailsItemMap);
            }
        }

    }

    /**
     *@Description 拆分单生成加工上架任务
     *@Param updateList
     *@param processOrder
     *@param confirmDetailsItemMap
     *@Return Void
     *@Date 2025/7/4 14:06
     *<AUTHOR>
     **/
    private void generateDetailsPickingTask(List<ProcessTaskDetails> updateList, ProcessOrder processOrder, Map<Long,ConfirmDetailsItem> confirmDetailsItemMap) {
        List<WarehouseLoc> warehouseLocList = warehouseLocService
                .listByCodeAndWarehouseId(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(),processOrder.getWarehouseId());
        List<CsPaTask> csPaTaskList = new ArrayList<>();
        updateList.forEach(details -> {
            Long receiveId = rulePaCommonService.checkRulePaTaskAndGetLoc(new AsnReceive().setSkuCode(details.getSkuCode()));
            ConfirmDetailsItem confirmDetailsItem = confirmDetailsItemMap.get(details.getId());
            CsPaTask csPaTask = new CsPaTask();
            String paNo = numberGenerator.nextValue(IdRuleConstant.PA_CODE);
            csPaTask.setPaNo(paNo);
            csPaTask.setPaSource(PaSourceEnum.PRE.getCode());
            csPaTask.setOrderId(processOrder.getId());
            csPaTask.setOrderNo(processOrder.getProcessOrderNo());
            csPaTask.setStatus(PaStatusEnum.SHELVESING.getCode());
            csPaTask.setPackageUnitId(details.getPackageUnitId());
            csPaTask.setPackageUnitName(details.getPackageUnitName());
            csPaTask.setFmLocName(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode());
            if(CollectionUtil.isNotEmpty(warehouseLocList)){
                csPaTask.setFmLocId(warehouseLocList.get(0).getId());
            }
            if(ObjectUtil.isNotNull(confirmDetailsItem.getLocId())){
                csPaTask.setToLocId(confirmDetailsItem.getLocId());
                csPaTask.setToLocName(confirmDetailsItem.getLocName());
            }else{
                if (EmptyUtil.isNotEmpty(receiveId)){
                    WarehouseLoc warehouseLoc = warehouseLocService.findById(receiveId);
                    csPaTask.setToLocId(warehouseLoc.getId());
                    csPaTask.setToLocName(warehouseLoc.getLocName());
                }
            }

            PackageUnit packageUnit = packageUnitService.findById(csPaTask.getPackageUnitId());
            if(ObjectUtil.isNotEmpty(packageUnit)){
                csPaTask.setQtyPlanEa(confirmDetailsItem.getConfirmQuantity());
            }

            csPaTask.setPackageId(details.getPackageId());
            csPaTask.setPackageName(details.getPackageName());
            csPaTask.setSkuId(details.getSkuId());
            csPaTask.setSkuName(details.getSkuName());
            csPaTask.setOwnerId(processOrder.getOwnerId());
            csPaTask.setOwnerName(processOrder.getOwnerName());
            csPaTask.setWarehouseId(processOrder.getWarehouseId());
            csPaTask.setWarehouseName(processOrder.getWarehouseName());
            csPaTask.setPaTime(DateUtil.date());
            //获取批次号
            Map<String, Object> attMap = new HashMap<>();
            attMap.put("lotAtt01",confirmDetailsItem.getLotAtt01());
            attMap.put("lotAtt02",confirmDetailsItem.getLotAtt02());
            attMap.put("lotAtt03",confirmDetailsItem.getLotAtt03());
            String locNum =  stockService.getStockCommonBaseServices(new InvLotAttQuery().setAttMap(attMap)
                    .setOwnerId(processOrder.getOwnerId())
                    .setSkuId(details.getSkuId())
                    .setWarehouseId(processOrder.getWarehouseId()));
            csPaTask.setLotNum(locNum);
            paTaskService.preSave(csPaTask);
            csPaTaskList.add(csPaTask);
        });
        if(CollectionUtil.isNotEmpty(csPaTaskList)){
            paTaskService.batchInsert(csPaTaskList);
        }
    }

    /**
     *@Description 生成上架任务
     *@Param processTask
     *@param processOrder
     *@param confirmItem
     *@Return Void
     *@Date 2025/7/1 16:18
     *<AUTHOR>
     **/
    private void generateTaskPickingTask(ProcessTask processTask,ProcessOrder processOrder,ProcessOrderConfirmItem confirmItem) {
        Long receiveId = rulePaCommonService.checkRulePaTaskAndGetLoc(new AsnReceive().setSkuCode(processTask.getCombinationsSkuCode()));
        List<WarehouseLoc> warehouseLocList = warehouseLocService
                .listByCodeAndWarehouseId(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(),processOrder.getWarehouseId());
        //字段设置
        CsPaTask csPaTask = new CsPaTask();
        String paNo = numberGenerator.nextValue(IdRuleConstant.PA_CODE);
        csPaTask.setPaNo(paNo);
        csPaTask.setPaSource(PaSourceEnum.PFM.getCode());
        csPaTask.setOrderId(processOrder.getId());
        csPaTask.setOrderNo(processOrder.getProcessOrderNo());
        csPaTask.setStatus(PaStatusEnum.SHELVESING.getCode());
        csPaTask.setPackageUnitId(processTask.getCombinationsPackageUnitId());
        csPaTask.setPackageUnitName(processTask.getCombinationsPackageUnitName());
        csPaTask.setFmLocName(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode());
        if(CollectionUtil.isNotEmpty(warehouseLocList)){
            csPaTask.setFmLocId(warehouseLocList.get(0).getId());
        }
        if(ObjectUtil.isNotNull(confirmItem.getLocId())){
            csPaTask.setToLocId(confirmItem.getLocId());
            csPaTask.setToLocName(confirmItem.getLocName());
        }else{
            if (EmptyUtil.isNotEmpty(receiveId)){
                WarehouseLoc warehouseLoc = warehouseLocService.findById(receiveId);
                csPaTask.setToLocId(warehouseLoc.getId());
                csPaTask.setToLocName(warehouseLoc.getLocName());
            }
        }

        PackageUnit packageUnit = packageUnitService.findById(csPaTask.getPackageUnitId());
        if(ObjectUtil.isNotEmpty(packageUnit)){
            csPaTask.setQtyPlanEa(confirmItem.getConfirmQuantity().multiply(new BigDecimal(packageUnit.getMinQuantity())));
        }

        csPaTask.setPackageId(processTask.getCombinationsPackageId());
        csPaTask.setPackageName(processTask.getCombinationsPackageName());
        csPaTask.setSkuId(processTask.getCombinationsSkuId());
        csPaTask.setSkuName(processTask.getCombinationsSkuName());
        csPaTask.setOwnerId(processOrder.getOwnerId());
        csPaTask.setOwnerName(processOrder.getOwnerName());
        csPaTask.setWarehouseId(processOrder.getWarehouseId());
        csPaTask.setWarehouseName(processOrder.getWarehouseName());
        csPaTask.setPaTime(DateUtil.date());
        //获取批次号
        Map<String, Object> attMap = new HashMap<>();
        attMap.put("lotAtt01",confirmItem.getLotAtt01());
        attMap.put("lotAtt02",confirmItem.getLotAtt02());
        attMap.put("lotAtt03",confirmItem.getLotAtt03());
        String locNum =  stockService.getStockCommonBaseServices(new InvLotAttQuery().setAttMap(attMap)
                .setOwnerId(processOrder.getOwnerId())
                .setSkuId(processTask.getCombinationsSkuId())
                .setWarehouseId(processOrder.getWarehouseId()));
        csPaTask.setLotNum(locNum);
        paTaskService.saveOrUpdate(csPaTask);
    }

    /**
     *@Description 加工信息确认查询
     *@Param id
     *@Return * {@link ConfirmInfoQuery }
     *@Date 2025/7/4 10:36
     *<AUTHOR>
     **/
    public ConfirmInfoQuery confirmInfo(Long id) {
        //根据ID查询加工单
        ProcessOrder processOrder = dao.findById(id);
        //未拣货状态不能加工确认
        if (processOrder.getPickingStatus().equals(ProcessOrderPickingStatusEnum.NOT_PICKED.getCode())){
            throw new ServiceException(ProcessExceptionEnum.PROCESS_CONFIRM_ERROR_ON_PICKING);
        }
        //查询对应任务
        ProcessTask processTask = taskService.findFirst(ConditionRule.getInstance().andEqual(ProcessTask::getProcessOrderNo,processOrder.getProcessOrderNo()));
        ConfirmInfoQuery confirmInfoQuery = new ConfirmInfoQuery();
        BeanUtil.copyProperties(processTask, confirmInfoQuery);
        confirmInfoQuery.setOrderType(processOrder.getOrderType());
        confirmInfoQuery.setAllowProcessQuantity(confirmInfoQuery.getProcessQuantity().subtract(processTask.getActualProcessQuantity()));
        confirmInfoQuery.setAllowProcessQuantityEa(processTask.getProcessQuantityEa().subtract(processTask.getActualProcessQuantityEa()));
        if(ProcessOrderTypeEnum.SPLIT_ORDER.getCode().equals(processOrder.getOrderType())){
            //查询对应任务明细
            List<ProcessTaskDetails> processTaskDetailsList = taskDetailsService.findByTaskNo(processTask.getProcessTaskNo());
            if (CollectionUtil.isNotEmpty(processTaskDetailsList)){
                confirmInfoQuery.setDetailsQueryList(BeanUtil.copyToList(processTaskDetailsList, ConfirmInfoDetailsQuery.class));
            }
        }
        return confirmInfoQuery;
    }

    /**
     * 批量取消
     *
     * @param condition 条件
     */
    public void batchCancel(IdCondition condition) {
        // 查询加工单信息
        List<ProcessOrder> orderList = dao.findByIds(condition.getIdList().toArray(new Long[0]));
        List<ProcessOrder> list = orderList.stream().filter(order ->
                        !ProcessOrderStatusEnum.AUDIT.getCode().equals(order.getProcessOrderStatus())
                                && !ProcessOrderStatusEnum.CREATE.getCode().equals(order.getProcessOrderStatus()))
                .toList();
        // 判断非创建和审核状态的加工单
        if (EmptyUtil.isNotEmpty(list)) {
            throw new ServiceException(ProcessExceptionEnum.CANCEL_CREATE_OR_AUDITED);
        }

        // 更新加工单取消状态
        ProcessOrderStatusItem item = new ProcessOrderStatusItem();
        item.setProcessOrderStatus(ProcessOrderStatusEnum.CANCEL.getCode());
        preSave(item);
        dao.updateByIds(item, condition.getIdList().toArray(new Long[0]));
    }

    /**
     * 批处理关闭订单
     *
     * @param condition 条件
     */
    public void batchClose(IdCondition condition) {
        // 查询加工单信息
        List<ProcessOrder> orderList = dao.findByIds(condition.getIdList().toArray(new Long[0]));
        List<ProcessOrder> list = orderList.stream().filter(order ->
                        !ProcessOrderStatusEnum.COMPLETE_PROCESS.getCode().equals(order.getProcessOrderStatus()))
                .toList();
        // 判断非完全加工状态的加工单
        if (EmptyUtil.isNotEmpty(list)) {
            throw new ServiceException(ProcessExceptionEnum.CLOSE_COMPLETE_PROCESS);
        }

        // 更新加工单取消状态
        ProcessOrderStatusItem item = new ProcessOrderStatusItem();
        item.setProcessOrderStatus(ProcessOrderStatusEnum.FINISH.getCode());
        preSave(item);
        dao.updateByIds(item, condition.getIdList().toArray(new Long[0]));
    }
}
