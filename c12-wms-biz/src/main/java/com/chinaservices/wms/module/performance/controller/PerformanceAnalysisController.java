package com.chinaservices.wms.module.performance.controller;


import com.chinaservices.auth.module.position.domain.PositionCondition;
import com.chinaservices.auth.module.position.domain.PositionQuery;
import com.chinaservices.auth.module.position.feign.RemotePositionService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.performance.domain.*;
import com.chinaservices.wms.module.performance.service.PerformanceAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 作业绩效分析
 * <AUTHOR>
 * @Date 2025/4/17 9:12
 * @Description
 * @Version 1.0
 */
@RestController
@RequestMapping("/api/performanceAnalysis")
public class PerformanceAnalysisController {

    @Autowired
    private PerformanceAnalysisService performanceAnalysisService;

    @Autowired
    private RemotePositionService remotePositionService;

    /**
     *  盘点作业分析
     *@Param condition
     *@Return * {@link ResponseData< PageResult<StocktakeTaskPerformanceAnalysisQuery>> }
     *@Date 2025/4/17 9:15
     *<AUTHOR>
     **/
    @RequestMapping("/stocktakeTaskPerformanceAnalysis")
    public ResponseData<PageResult<StocktakeTaskPerformanceAnalysisQuery>> stocktakeTaskPerformanceAnalysis(@RequestBody PerformanceAnalysisPageCondition condition){
        PageResult<StocktakeTaskPerformanceAnalysisQuery> pageResult = performanceAnalysisService.stocktakeTaskPerformanceAnalysis(condition);
        return ResponseData.success(pageResult);
    }


    /**
     * 拣货任务绩效分析
     *
     * @param condition 条件
     * @return {@link PageResult }<{@link PickingTaskPerformanceAnalysisQuery }>
     */
    @PostMapping("/pickingTaskPerformanceAnalysis")
    public ResponseData<PageResult<PickingTaskPerformanceAnalysisQuery>> pickingTaskPerformanceAnalysis(@RequestBody PerformanceAnalysisPageCondition condition)
    {
        PageResult<PickingTaskPerformanceAnalysisQuery> pageResult = performanceAnalysisService.pickingTaskPerformanceAnalysis(condition);
        return ResponseData.success(pageResult);
    }

    /**
     * 上架作业分析
     * @param condition 条件
     * @return {@link PageResult }<{@link PutOnShelvesPageQuery }>
     */
    @PostMapping("/analysisOfShelfAssignments/search")
    @SaCheckPermission("taskPerformanceAnalysis:paTaskList:headBtn:page")
    public ResponseData<PageResult<PutOnShelvesPageQuery>> analysisOfShelfAssignments(@RequestBody PutOnShelvesSearchCondition condition){
        return ResponseData.success(performanceAnalysisService.analysisOfShelfAssignments(condition));
    }

    /**
     * 岗位列表
     *
     * @param condition 条件
     * @return {@link List }<{@link PositionQuery }>
     */
    @PostMapping("/position/list")
    public ResponseData<List<PositionQuery>> positionList(@RequestBody PositionCondition condition) {
        return remotePositionService.getList(condition);
    }

}
