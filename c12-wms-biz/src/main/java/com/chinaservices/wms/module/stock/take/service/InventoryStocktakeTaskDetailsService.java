package com.chinaservices.wms.module.stock.take.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.wms.module.asn.asn.domain.AsnDetailSnItem;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.asn.asn.service.AsnDetailSnService;
import com.chinaservices.wms.module.basic.sku.domain.FindBySkuCodeListQuery;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.stock.loc.domain.LocSnDetailItemQuery;
import com.chinaservices.wms.module.stock.loc.domain.LocSnDetailQuery;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLocSn;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocSnService;
import com.chinaservices.wms.module.stock.take.dao.InventoryStocktakeTaskDetailsDao;
import com.chinaservices.wms.module.stock.take.domain.GeneraAdjustItem;
import com.chinaservices.wms.module.stock.take.domain.StocktakeOrderPageCondition;
import com.chinaservices.wms.module.stock.take.domain.StocktakeTaskDetailsPdaQuery;
import com.chinaservices.wms.module.stock.take.domain.StocktakeTaskDetailsQuery;
import com.chinaservices.wms.module.stock.take.model.InventoryStocktakeTaskDetails;
import com.chinaservices.wms.module.stock.take.model.InventoryStocktakeTaskDetailsSn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName InventoryStocktakeTaskDetailsService
 * <AUTHOR>
 * @Date 2025/1/11 17:35
 * @Description
 * @Version 1.0
 */
@Service
public class InventoryStocktakeTaskDetailsService extends ModuleBaseServiceSupport<InventoryStocktakeTaskDetailsDao, InventoryStocktakeTaskDetails, Long> {

    @Autowired
    private SkuService skuService;

    @Autowired
    private InventoryStocktakeTaskDetailsSnService detailsSnService;

    @Autowired
    private InvLotLocSnService lotLocSnService;

    @Autowired
    private AsnDetailSnService asnDetailSnService;
    /**
     *@Description 根据任务号查询对应任务详情
     *@Param taskNoList
     *@Return * {@link List< StocktakeTaskDetailsQuery> }
     *@Date 2025/1/13 16:03
     *<AUTHOR>
     **/
    public List<StocktakeTaskDetailsQuery> findQueryByTaskNoList(List<String> taskNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeTaskDetails::getTaskNo,taskNoList);
        List<StocktakeTaskDetailsQuery> detailsQueryList = dao.find(StocktakeTaskDetailsQuery.class,conditionRule);
        if(CollectionUtil.isNotEmpty(detailsQueryList)){
            //抽取skuId集合
            List<Long> skuIdList = detailsQueryList.stream().map(StocktakeTaskDetailsQuery::getSkuId).distinct().collect(Collectors.toList());
            //抽取locId集合
            List<Long> locIdList = detailsQueryList.stream().map(StocktakeTaskDetailsQuery::getLocId).distinct().collect(Collectors.toList());
            //抽取批次号集合
            List<String> lotNumList = detailsQueryList.stream().map(StocktakeTaskDetailsQuery::getLotNum).distinct().collect(Collectors.toList());
            //抽取容器号
            List<String> palletNumList = detailsQueryList.stream().filter(x->StringUtils.isNotBlank(x.getPalletNum()))
                    .map(StocktakeTaskDetailsQuery::getPalletNum).distinct().collect(Collectors.toList());
            Map<String,List<InvLotLocSn>> locSnMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(skuIdList) && CollectionUtil.isNotEmpty(locIdList) && CollectionUtil.isNotEmpty(lotNumList)){
                //根据skuId和locId查询库存序列号明细
                List<InvLotLocSn> locSnList = lotLocSnService.getByLocIdListAndSkuIdListAndLotNumList(skuIdList, locIdList, lotNumList,null, YesNoEnum.NO.getCode());
                if(CollectionUtil.isNotEmpty(locSnList)){
                    locSnMap = locSnList.stream().collect(Collectors.groupingBy(x -> x.getSkuId() +"_"+x.getLocId()+"_"+x.getLotNum()));
                }
            }
            List<String> skuNoList = detailsQueryList.stream().map(StocktakeTaskDetailsQuery::getSkuNo).distinct().collect(Collectors.toList());
            Map<String,FindBySkuCodeListQuery> findBySkuCodeListQueryMap = new HashMap<>();
            //根据盘点任务号查询序列号明细
            List<InventoryStocktakeTaskDetailsSn> taskDetailsSnList = detailsSnService.findByTaskNoList(taskNoList);
            Map<Long, List<InventoryStocktakeTaskDetailsSn>> taskDetailsSnMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(taskDetailsSnList)){
                taskDetailsSnMap = taskDetailsSnList.stream().collect(Collectors.groupingBy(x -> x.getTaskDetailsId()));
            }
            if(CollectionUtil.isNotEmpty(skuNoList)){
                List<FindBySkuCodeListQuery> skuQueryList = skuService.findBySkuCodeList(skuNoList);
                if(CollectionUtil.isNotEmpty(skuQueryList)){
                    findBySkuCodeListQueryMap = skuQueryList.stream().collect(Collectors.toMap(FindBySkuCodeListQuery::getSkuCode,item->item));
                }
            }
            for (StocktakeTaskDetailsQuery stocktakeTaskDetailsQuery:detailsQueryList){
                if(ObjectUtil.isNotNull(stocktakeTaskDetailsQuery.getActualCount()) && ObjectUtil.isNotNull(stocktakeTaskDetailsQuery.getInventoryCount())){
                    stocktakeTaskDetailsQuery.setProfitLossCount(stocktakeTaskDetailsQuery.getActualCount() - stocktakeTaskDetailsQuery.getInventoryCount());
                }
                FindBySkuCodeListQuery findBySkuCodeListQuery = findBySkuCodeListQueryMap.get(stocktakeTaskDetailsQuery.getSkuNo());
                if(ObjectUtil.isNotNull(findBySkuCodeListQuery)){
                    stocktakeTaskDetailsQuery.setSkuName(findBySkuCodeListQuery.getSkuName());
                    stocktakeTaskDetailsQuery.setOwnerName(findBySkuCodeListQuery.getOwnerNameStr());
                    stocktakeTaskDetailsQuery.setLotNum(findBySkuCodeListQuery.getBatchNo());
                }
                List<InvLotLocSn> locSnList = locSnMap.get(stocktakeTaskDetailsQuery.getSkuId()+"_"+stocktakeTaskDetailsQuery.getLocId()+"_"+stocktakeTaskDetailsQuery.getLotNum());
                List<InventoryStocktakeTaskDetailsSn> currentList = taskDetailsSnMap.get(stocktakeTaskDetailsQuery.getId());
                if(CollectionUtil.isNotEmpty(locSnList)){
                    stocktakeTaskDetailsQuery.setShowLotLocSnBtn(Boolean.TRUE);
                    if(CollectionUtil.isNotEmpty(currentList)){
                        stocktakeTaskDetailsQuery.setShowStocktakeSnBtn(Boolean.TRUE);
                        List<String> locSnNoList= locSnList.stream().map(InvLotLocSn::getSkuSn).distinct().collect(Collectors.toList());
                        List<String> currentSnNoList = currentList.stream().map(InventoryStocktakeTaskDetailsSn::getSnNo).distinct().collect(Collectors.toList());
                        //两个集合中是否存在不交集的数据
                        List<String> detailNotContainsNo = new ArrayList<>();
                        List<String> locSnNotContainsNo = new ArrayList<>();
                        locSnNoList.forEach(x ->{
                            //盘点任务明细中不包含的序列号集合
                            if(!currentSnNoList.contains(x)){
                                locSnNotContainsNo.add(x);
                            }
                        });
                        currentSnNoList.forEach(x ->{
                            //序列号库存中不包含的序列号集合
                            if(!locSnNoList.contains(x)){
                                detailNotContainsNo.add(x);
                            }
                        });
                        if(CollectionUtil.isNotEmpty(detailNotContainsNo) && CollectionUtil.isNotEmpty(locSnNotContainsNo)){
                            //如果两个集合中都存在不交集的数据，且长度一致,则表示两个集合为类似 ABCD,EFGH 完全不相同
                            stocktakeTaskDetailsQuery.setTocktakeSnBtnRedPoint(Boolean.TRUE);
                            stocktakeTaskDetailsQuery.setLotLocSnBtnRedPoint(Boolean.TRUE);
                        }else if(CollectionUtil.isNotEmpty(detailNotContainsNo) && CollectionUtil.isEmpty(locSnNotContainsNo)){
                            stocktakeTaskDetailsQuery.setTocktakeSnBtnRedPoint(Boolean.TRUE);
                        }else if(CollectionUtil.isEmpty(detailNotContainsNo) && CollectionUtil.isNotEmpty(locSnNotContainsNo)){
                            stocktakeTaskDetailsQuery.setLotLocSnBtnRedPoint(Boolean.TRUE);
                        }
                        stocktakeTaskDetailsQuery.setSnNoList(currentSnNoList);
                    }


                }
            }

        }
        return detailsQueryList;
    }

    /**
     *@Description 根据盘点单号删除任务明细
     *@Param noList
     *@Return Void
     *@Date 2025/1/13 21:03
     *<AUTHOR>
     **/
    public void deleteByStocktakeNoList(List<String> noList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeTaskDetails::getStocktakeNo,noList);
        dao.delete(conditionRule);
    }

    /**
     *@Description 根据id集合查询
     *@Param ids
     *@Return List<InventoryStocktakeTaskDetails>
     *@Date 2025/1/14 15:57
     *<AUTHOR>
     **/
    public List<InventoryStocktakeTaskDetails> findByIds(List<Long> ids) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeTaskDetails::getId,ids);
        return dao.find(conditionRule);
    }

    /**
     *@Description 根据盘点单号查询盘点任务明细
     *@Param noList
     *@Return * {@link List< InventoryStocktakeTaskDetails> }
     *@Date 2025/1/15 15:09
     *<AUTHOR>
     **/
    public List<InventoryStocktakeTaskDetails> findByStocktakeNoList(List<String> noList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeTaskDetails::getStocktakeNo,noList);
        return dao.find(conditionRule);
    }

    /**
     *@Description 根据盘点单号查询可生成调整单数据
     *@Param noList       盘点单集合
     *@Return * {@link List< GeneraAdjustItem> }
     *@Date 2025/2/7 13:51
     *<AUTHOR>
     **/
    public List<GeneraAdjustItem> findGeneraAdjustItemList(List<String> noList,List<String> filterLocCodeList) {
        return dao.findGeneraAdjustItemList(noList,filterLocCodeList);
    }

    /**
     *@Description 查询盘点数量不一致的任务明细
     *@Param condition
     *@Return * {@link List< InventoryStocktakeTaskDetails> }
     *@Date 2025/2/8 14:43
     *<AUTHOR>
     **/
    public List<InventoryStocktakeTaskDetails> findDifferentsList(StocktakeOrderPageCondition condition) {
        return dao.findDifferentsList(condition);
    }

    /**
     *@Description 根据任务明细id查询序列号详情
     *@Param taskDetailsId
     *@Return * {@link LocSnDetailQuery }
     *@Date 2025/3/24 15:02
     *<AUTHOR>
     **/
    public LocSnDetailQuery getSnDetailByTaskDetailsId(Long taskDetailsId) {
        LocSnDetailQuery query = new LocSnDetailQuery();
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(InventoryStocktakeTaskDetailsSn::getTaskDetailsId,taskDetailsId);
        //根据任务明细id查询序列号详情
        List<InventoryStocktakeTaskDetailsSn> taskDetailsSnList = detailsSnService.findByTaskDetailsId(Arrays.asList(taskDetailsId));
        if(CollectionUtil.isNotEmpty(taskDetailsSnList)){
            //组装为map数据
            Map<String, List<InventoryStocktakeTaskDetailsSn>> detailsSnMap = getDetailSnMapBySnNoList(taskDetailsSnList);
            if(MapUtil.isNotEmpty(detailsSnMap)){
                List<LocSnDetailItemQuery> itemQueryList = new ArrayList<>();
                //组装返回数据
                detailsSnMap.forEach((key,asnDetailSnList)->{
                    //id不复制
                    CopyOptions copyOptions = CopyOptions.create().setIgnoreProperties("id");
                    List<AsnDetailSnItem> asnDetailSnItemList = BeanUtil.copyToList(asnDetailSnList,AsnDetailSnItem.class,copyOptions);
                    LocSnDetailItemQuery itemQuery = new LocSnDetailItemQuery();
                    itemQuery.setKey(key);
                    itemQuery.setValue(asnDetailSnItemList);
                    itemQueryList.add(itemQuery);
                });
                query.setItemQueryList(itemQueryList);
            }
        }
        if(CollectionUtil.isNotEmpty(taskDetailsSnList)){
            List<String> taskDetailsSnNoList = taskDetailsSnList.stream().map(InventoryStocktakeTaskDetailsSn::getSnNo).filter(x-> StringUtils.isNotBlank(x)).distinct().collect(Collectors.toList());
            InventoryStocktakeTaskDetails taskDetails = findById(taskDetailsId);
            //根据商品id库位id批次号查询序列号库存
            List<InvLotLocSn> invLotLocSnList = lotLocSnService
                    .getByLocIdListAndSkuIdListAndLotNumList(Arrays.asList(taskDetails.getSkuId()),Arrays.asList(taskDetails.getLocId()),Arrays.asList(taskDetails.getLotNum()),null,YesNoEnum.NO.getCode());
            if(CollectionUtil.isNotEmpty(invLotLocSnList)){
                List<String> snNoList = invLotLocSnList.stream().map(InvLotLocSn::getSkuSn).distinct().collect(Collectors.toList());
                List<String> differentSnCodeList = new ArrayList<>();
                //遍历序列号集合，如果集合中没有该序列号，则添加到differentSnCodeList集合中
                taskDetailsSnNoList.forEach(snNo->{
                    if(!snNoList.contains(snNo)){
                        differentSnCodeList.add(snNo);
                    }
                });
                if(CollectionUtil.isNotEmpty(differentSnCodeList)){
                    query.setColor("green");
                    query.setDifferentSnCodeList(differentSnCodeList);
                }
            }
        }
        return query;
    }

    /**
     *@Description 将序列号组装为map返回
     *@Param detailsSnList
     *@Return * {@link Map< String, List< InventoryStocktakeTaskDetailsSn>> }
     *@Date 2025/3/24 14:38
     *<AUTHOR>
     **/
    public Map<String, List<InventoryStocktakeTaskDetailsSn>> getDetailSnMapBySnNoList(List<InventoryStocktakeTaskDetailsSn> detailsSnList) {
        Map<String,List<InventoryStocktakeTaskDetailsSn>> map = new HashMap<>();
        // 根据SN码分组
        //过滤出没有箱号的数据
        List<InventoryStocktakeTaskDetailsSn> noBoxNoList = detailsSnList.stream().filter(asnDetailSn -> StringUtils.isBlank(asnDetailSn.getBoxCode())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(noBoxNoList)){
            map.put("无箱码", noBoxNoList) ;
        }
        //有箱号的数据
        List<InventoryStocktakeTaskDetailsSn> hasBoxNoList = detailsSnList.stream().filter(asnDetailSn -> StringUtils.isNotBlank(asnDetailSn.getBoxCode())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(hasBoxNoList)){
            map.putAll(hasBoxNoList.stream().collect(Collectors.groupingBy(InventoryStocktakeTaskDetailsSn::getBoxCode)));
        }
        return map;
    }

    /**
     *@Description 根据盘点任务编号集合查询对应任务明细
     *@Param taskNoList
     *@Return * {@link List< StocktakeTaskDetailsPdaQuery> }
     *@Date 2025/6/5 16:54
     *<AUTHOR>
     **/
    public List<StocktakeTaskDetailsPdaQuery> findPdaQueryByTaskNoList(List<String> taskNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeTaskDetails::getTaskNo,taskNoList);
        List<StocktakeTaskDetailsPdaQuery> pdaQueryList = dao.find(StocktakeTaskDetailsPdaQuery.class,conditionRule);
        if(CollectionUtil.isNotEmpty(pdaQueryList)){
            //查询对应已盘点sn信息
            List<Long> detailsIdList = pdaQueryList.stream().map(StocktakeTaskDetailsPdaQuery::getId).distinct().collect(Collectors.toList());
            List<InventoryStocktakeTaskDetailsSn> snDetailQueryList = detailsSnService.findByTaskDetailsId(detailsIdList);
            Map<Long, List<InventoryStocktakeTaskDetailsSn>> snDetailMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(snDetailQueryList)){
                snDetailMap = snDetailQueryList.stream().collect(Collectors.groupingBy(InventoryStocktakeTaskDetailsSn::getTaskDetailsId));
            }
            //抽取商品ID查询对应商品信息
            List<Long> skuIdList = pdaQueryList.stream().map(StocktakeTaskDetailsPdaQuery::getSkuId).distinct().collect(Collectors.toList());
            Map<Long, Sku> skuMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(skuIdList)){
                List<Sku> skuList = skuService.findByIds(skuIdList.toArray(new Long[0]));
                skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (v1, v2) -> v1));
            }
            List<Long> locIdList = pdaQueryList.stream().map(StocktakeTaskDetailsPdaQuery::getLocId).distinct().collect(Collectors.toList());
            List<String> lotNumList = pdaQueryList.stream().map(StocktakeTaskDetailsPdaQuery::getLotNum).distinct().collect(Collectors.toList());
            Map<String, Map<String,List<String>>> stockSnMap = new HashMap<>();
            //根据库位Id和商品Id、批次号询序列号库存
            List<InvLotLocSn> invLotLocSnList = lotLocSnService.getByLocIdListAndSkuIdListAndLotNumList(skuIdList,locIdList,lotNumList,null,YesNoEnum.NO.getCode());
            Map<String,List<InvLotLocSn>> groupLocSnMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(invLotLocSnList)){
                //抽取序列号
                List<String> snNoList = invLotLocSnList.stream().map(InvLotLocSn::getSkuSn).distinct().collect(Collectors.toList());
                List<AsnDetailSn> asnDetailSnList = new ArrayList<>();
                if(CollectionUtil.isNotEmpty(snNoList)){
                    //根据序列号查询对应序列号详情
                    asnDetailSnList = asnDetailSnService.listBySnNoList(snNoList);
                }
                groupLocSnMap = invLotLocSnList.stream()
                        .collect(Collectors.groupingBy(x -> x.getSkuId()+"_"+x.getLocId()+"_"+x.getLotNum()));
                List<AsnDetailSn> finalAsnDetailSnList = asnDetailSnList;
                groupLocSnMap.forEach((key, value)->{
                    Map<String,List<String>> currentMap = new HashMap<>();
                    List<String> currentSnNoList = value.stream().map(InvLotLocSn::getSkuSn).distinct().collect(Collectors.toList());
                    List<AsnDetailSn> currenAsnDetailSnList = finalAsnDetailSnList.stream()
                            .filter(asnDetailSn -> currentSnNoList.contains(asnDetailSn.getSnNo())).collect(Collectors.toList());
                    //无箱码数据
                    List<AsnDetailSn> notBoxNo = currenAsnDetailSnList.stream()
                            .filter(asnDetailSn -> StringUtils.isBlank(asnDetailSn.getBoxCode())).collect(Collectors.toList());
                    if(CollectionUtil.isNotEmpty(notBoxNo)){
                        currentMap.put("无箱码",  notBoxNo.stream().map(AsnDetailSn::getSnNo).distinct().collect(Collectors.toList()));
                    }
                    //有箱码数据
                    List<AsnDetailSn> hasBoxNo = currenAsnDetailSnList.stream()
                            .filter(asnDetailSn -> StringUtils.isNotBlank(asnDetailSn.getBoxCode())).collect(Collectors.toList());
                    if(CollectionUtil.isNotEmpty(hasBoxNo)){
                        currentMap.putAll(hasBoxNo.stream()
                                .collect(Collectors.groupingBy(
                                        AsnDetailSn::getBoxCode,
                                        Collectors.mapping(
                                                asnDetailSn -> {
                                                    // 返回序列号
                                                    return asnDetailSn.getSnNo();
                                                },
                                                Collectors.toList()
                                        )
                                )));
                    }
                    stockSnMap.put(key,currentMap);
                });
            }
            Map<Long, List<InventoryStocktakeTaskDetailsSn>> finalSnDetailMap = snDetailMap;
            Map<Long, Sku> finalSkuMap = skuMap;
            pdaQueryList.forEach(pdaQuery->{
                Sku sku = finalSkuMap.get(pdaQuery.getSkuId());
                if(ObjectUtil.isNotNull(sku)){
                    pdaQuery.setWhetherSerialController(sku.getWhetherSerialController());
                    pdaQuery.setSkuNo(sku.getSkuCode());
                }
                List<InventoryStocktakeTaskDetailsSn> snDetailList = finalSnDetailMap.get(pdaQuery.getId());
                if(CollectionUtil.isNotEmpty(snDetailList)){
                    pdaQuery.setSnNoList(snDetailList.stream().map(InventoryStocktakeTaskDetailsSn::getSnNo).collect(Collectors.toList()));
                }
                Map<String,List<String>> currentMap = stockSnMap.get(pdaQuery.getSkuId()+"_"+pdaQuery.getLocId()+"_"+pdaQuery.getLotNum());
                pdaQuery.setStockSnMap(currentMap);
            });
        }
        return pdaQueryList;
    }

    /**
     *@Description 根据盘点编号查询盘点任务明细
     *@Param taskNo
     *@Return * {@link List< InventoryStocktakeTaskDetails> }
     *@Date 2025/6/12 14:08
     *<AUTHOR>
     **/
    public List<InventoryStocktakeTaskDetails> findByTaskNo(String taskNo) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(InventoryStocktakeTaskDetails::getTaskNo,taskNo);
        return dao.find(conditionRule);
    }

    /**
     *@Description 根据盘点编号列表查询盘点任务明细
     *@Param findTaskNoList
     *@Return * {@link List< InventoryStocktakeTaskDetails> }
     *@Date 2025/6/12 14:08
     *<AUTHOR>
     **/
    public List<InventoryStocktakeTaskDetails> findByTaskNoList(List<String> findTaskNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeTaskDetails::getTaskNo,findTaskNoList);
        return dao.find(conditionRule);
    }
}
