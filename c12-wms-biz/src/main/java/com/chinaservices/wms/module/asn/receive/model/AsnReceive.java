package com.chinaservices.wms.module.asn.receive.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 入库收货明细 实体类
 */
@Entity
@Table(name = "cs_asn_receive")
@Data
public class AsnReceive extends ModuleBaseModel {

    /**
     * 收货明细编号
     */
    private String receiveNo;

    /**
     * 版本
     */
    private Long recVer;

    /**
     * 入库单号
     */
    private String asnCode;

    /**
     * 入库单明细ID
     */
    private Long asnDetailId;

    /**
     * 入库单主键
     */
    private Long asnId;


    /**
     * 收货明细行号
     */
    private String lineNo;

    /**
     * 收货状态
     */
    private String receiveStatus;

    /**
     * 商品名称
     */
    private String skuCode;

    /**
     * 滞库到期时间
     */
    private Date stockTime;

    /**
     * 包装编码
     */
    private Long packageId;

    /**
     * 包装单位主键
     */
    private Long packageUnitId;

    /**
     * 包装单位名称
     */
    private String packageUnitName;

    /**
     * 预收数EA
     */
    private BigDecimal qtyPlanEa;

    /**
     * 收货数EA
     */
    private BigDecimal qtyRcvEa;

    /**
     * 收货库位主键
     */
    private Long rcvLocId;

    /**
     * 收货库位名称
     */
    private String rcvLocName;

    /**
     * 收货容器主键
     */
    private Long rcvPalletId;

    /**
     * 收货容器号
     */
    private String rcvPallet;

    /**
     * 收货人
     */
    private String rvcPerson;

    /**
     * 收货时间
     */
    private Date rvcTime;

    /**
     * 上架员ID
     */
    private Long paPersonId;

    /**
     * 上架员名称
     */
    private String paPersonName;

    /**
     * 批次属性1
     */
    private String lotAtt01;

    /**
     * 批次属性2
     */
    private String lotAtt02;

    /**
     * 批次属性3
     */
    private String lotAtt03;

    /**
     * 批次属性4
     */
    private String lotAtt04;

    /**
     * 批次属性5
     */
    private String lotAtt05;

    /**
     * 批次属性6
     */
    private String lotAtt06;

    /**
     * 批次属性7
     */
    private String lotAtt07;

    /**
     * 批次属性8
     */
    private String lotAtt08;

    /**
     * 批次属性9
     */
    private String lotAtt09;

    /**
     * 批次属性10
     */
    private String lotAtt10;

    /**
     * 批次属性11
     */
    private String lotAtt11;

    /**
     * 批次属性12
     */
    private String lotAtt12;

    /**
     * 批次号
     */
    private String lotNum;

    /**
     * 上架任务号
     */
    private String paNo;

    /**
     * 是否上架（1：是，0：否）
     */
    private String whetherShelf;

    /**
     * 是否质检（1：是，0：否）
     */
    private String whetherQc;

    public static final String COL_NAME_CREATE_TIME = "create_time";

    /**
     * 识别标签号
     */
    private String tagNo;

}
