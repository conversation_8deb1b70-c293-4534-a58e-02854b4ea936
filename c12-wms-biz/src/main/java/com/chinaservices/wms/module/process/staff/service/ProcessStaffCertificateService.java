package com.chinaservices.wms.module.process.staff.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjUtil;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.wms.module.process.staff.dao.ProcessStaffCertificateDao;
import com.chinaservices.wms.module.process.staff.domain.ProcessStaffCertificateTableItem;
import com.chinaservices.wms.module.process.staff.model.ProcessStaffCertificate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: <PERSON>ran.<PERSON>
 * @Date: 2025/4/15
 * @Description: 加工人员资格证书服务层
 */
@Service
public class ProcessStaffCertificateService extends ModuleBaseServiceSupport<ProcessStaffCertificateDao, ProcessStaffCertificate, Long> {

    @Autowired
    private ProcessStaffCertificateFileService psCertificateFileService;

    /**
     * 新增资质证书
     * @param certificateTableList 入参
     * @param staffId 入参
     * @return Boolean
     */
    public Boolean save(List<ProcessStaffCertificateTableItem> certificateTableList, Long staffId) {
        if(CollectionUtil.isEmpty(certificateTableList)){
            return Boolean.TRUE;
        }
        // 1. 准备资质证书信息列表 用于批量插入
        List<ProcessStaffCertificate> psCertificateList = new ArrayList<>();
        for (ProcessStaffCertificateTableItem certificateItem : certificateTableList) {
            // 2. 将tableItem中的信息拷贝至实体类
            ProcessStaffCertificate psCertificate = new ProcessStaffCertificate();
            BeanUtil.copyProperties(certificateItem, psCertificate);
            // 3. 关联加工人员id
            psCertificate.setStaffId(staffId);
            preSave(psCertificate);
            psCertificateList.add(psCertificate);
        }
        Boolean insertResult = dao.batchInsert(psCertificateList) > 0;
        // 4. 按顺序获取所有插入后的id 便于后续关联businessId
        for(int i = 0; i < psCertificateList.size(); i++) {
            certificateTableList.get(i).setId(psCertificateList.get(i).getId());
        }
        Boolean fileResult = psCertificateFileService.saveOrUpdate(certificateTableList);
        return BooleanUtil.and(insertResult, fileResult);
    }

    /**
     * 更新资质证书
     * @param certificateTableList 入参
     * @param staffId 入参
     * @return Boolean
     */
    public Boolean edit(List<ProcessStaffCertificateTableItem> certificateTableList, Long staffId) {
        // 1. 获取该stuffId下所有的资质证书
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(ProcessStaffCertificate::getStaffId, staffId);
        List<ProcessStaffCertificate> certificateList = dao.find(conditionRule);
        // 2. 当资质证书为空时 删除所有数据库中的数据
        if (CollectionUtil.isEmpty(certificateTableList)) {
            if(CollectionUtil.isNotEmpty(certificateList)){
                List<Long> certificateIdList = certificateList.stream().map(ProcessStaffCertificate::getId).toList();
                return dao.delete(ArrayUtil.toArray(certificateIdList, Long.class)) > 0;
            }
            return Boolean.TRUE;
        }
        // 3. 如果没有记录就直接调用新增
        if (CollectionUtil.isEmpty(certificateList)) {
            return save(certificateTableList, staffId);
        }
        // 4. 存在记录的时 过滤出数据库中原有id与新的id
        List<Long> originIdList = certificateList.stream().map(ProcessStaffCertificate::getId).toList();
        List<Long> newIdList = certificateTableList.stream().map(ProcessStaffCertificateTableItem::getId).toList();
        // 5. 构建新增列表、更新列表与删除列表
        List<ProcessStaffCertificate> insertList = new ArrayList<>();
        List<ProcessStaffCertificate> updateList = new ArrayList<>();
        List<Long> deleteList = originIdList.stream().filter(id -> !newIdList.contains(id)).toList();
        // 5.1 构建新增与更新的tableItem列表
        List<ProcessStaffCertificateTableItem> insertTableList = new ArrayList<>();
        List<ProcessStaffCertificateTableItem> updatetableList = new ArrayList<>();
        // 6. 遍历所有的tableItem
        for (ProcessStaffCertificateTableItem certificateItem : certificateTableList) {
            // 6.1 新增/更新前操作
            ProcessStaffCertificate psCertificate = new ProcessStaffCertificate();
            // 7. 当不存在id时则意味着新增
            if (ObjUtil.isNull(certificateItem.getId())) {
                BeanUtil.copyProperties(certificateItem, psCertificate);
                psCertificate.setStaffId(staffId);
                preSave(psCertificate);
                insertList.add(psCertificate);
                insertTableList.add(certificateItem);
            }
            // 8. 当两者都存在该id时则意味着更新
            else if (BooleanUtil.and(
                    originIdList.contains(certificateItem.getId()),
                    newIdList.contains(certificateItem.getId())
            )) {
                psCertificate = dao.findById(certificateItem.getId());
                psCertificate.setCertificateType(certificateItem.getCertificateType());
                psCertificate.setValidityPeriodFm(certificateItem.getValidityPeriodFm());
                psCertificate.setValidityPeriodTo(certificateItem.getValidityPeriodTo());
                preSave(psCertificate);
                updateList.add(psCertificate);
                updatetableList.add(certificateItem);
            }
        }
        // 9. 进行数据操纵
        Long[] deleteIds = ArrayUtil.toArray(deleteList, Long.class);
        Boolean deleteResult = ArrayUtil.isNotEmpty(deleteIds) ? dao.delete(deleteIds) > 0 : Boolean.TRUE;
        Boolean insertResult = CollectionUtil.isNotEmpty(insertList) ? dao.batchInsert(insertList) > 0 : Boolean.TRUE;
        Boolean updateResult = CollectionUtil.isNotEmpty(updateList) ? dao.batchUpdate(updateList) > 0 : Boolean.TRUE;
        // 10. 添加附件所需要的id信息
        for (int i = 0; i < insertTableList.size(); i++) {
            insertTableList.get(i).setId(insertList.get(i).getId());
        }
        insertTableList.addAll(updatetableList);
        Boolean fileResult = psCertificateFileService.saveOrUpdate(insertTableList);
        return BooleanUtil.and(deleteResult, fileResult, insertResult, updateResult);
    }
}
