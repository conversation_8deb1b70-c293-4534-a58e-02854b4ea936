package com.chinaservices.wms.module.archive.archives.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.exception.BusinessException;
import com.chinaservices.session.SessionContext;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.enums.DocumentTypeEnum;
import com.chinaservices.wms.common.enums.folder.logistics.DetailFileTypeEnum;
import com.chinaservices.wms.common.enums.folder.logistics.FolderTypeEnum;

import com.chinaservices.wms.module.folder.logistics.domain.LogisticsFolderDetailLinkCondition;
import com.chinaservices.wms.module.folder.logistics.model.LogisticsFolderDetail;
import com.chinaservices.wms.module.folder.logistics.model.LogisticsFolderDetailFile;
import com.chinaservices.wms.module.folder.logistics.model.LogisticsFolderHeader;
import com.chinaservices.wms.module.folder.logistics.service.LogisticsFolderDetailFileService;
import com.chinaservices.wms.module.folder.logistics.service.LogisticsFolderDetailService;
import com.chinaservices.wms.module.folder.logistics.service.LogisticsFolderHeaderService;
import com.chinaservices.wms.module.warehouse.warehouse.dao.WarehouseDao;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> Tang
 * @version 1.0
 * @description TODO
 * @date 2025/6/20 13:58
 **/

@Service
public class LogisticsFileAutoGenerateService {

    @Autowired
    private LogisticsFolderHeaderService folderHeaderService;

    @Autowired
    private LogisticsFolderDetailService folderDetailService;
    @Autowired
    private WarehouseDao warehouseDao;
    @Autowired
    @Lazy
    private LogisticsFileAutoGenerateService logisticsFileAutoGenerateService;
    @Autowired
    private LogisticsFolderDetailFileService detailFileService;






    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    /**
     * 自动生成物流文件(公共方法)
     *
     * @param documentType 单据类型上级编号
     * @param linkConditions 单据和上级关系
     * @param companyId    企业ID
     * @param tenancy      租户ID
     * @param tenancy      仓库ID
     */

    public void batchGenerateLogisticsFiles(DocumentTypeEnum documentType,
                                            List<LogisticsFolderDetailLinkCondition> linkConditions,
                                            Long companyId,
                                            Long tenancy,
                                            Long warehouseId) {
        if (CollectionUtil.isEmpty(linkConditions)){
            return;
        }

        linkConditions.stream().forEach(linkCondition -> {
            if (CollectionUtil.isEmpty(linkCondition.getDocumentNos())) {
                return;
            }
            logger.error("父编号："+linkCondition.getUpDocumentNo()+"单据集合："+linkCondition.getDocumentNos());
            // 批量处理每个单据
            for (String documentNo : linkCondition.getDocumentNos()) {

                logisticsFileAutoGenerateService.generateSingleDocument(documentType,linkCondition.getUpDocumentNo(), documentNo, companyId, tenancy,warehouseId);
            }
        });

    }

    /**
     * 生成单个单据文件
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateSingleDocument(DocumentTypeEnum documentType,
                                        String upDocumentNo,
                                        String documentNo,
                                        Long companyId,
                                        Long tenancy,
                                        Long warehouseId) {
        try {
            // 1. 获取或创建文件夹
            Long folderId = getOrCreateDocumentFolder(documentType,upDocumentNo, documentNo, companyId, tenancy,warehouseId);

            // 2. 检查文件是否已存在
            if (isFileExists(documentNo, folderId)) {
                logger.error("物流文件已存在 - 单据类型: {}, 单据编号: {}", documentType, documentNo);
                return;
            }

            // 3. 创建文件记录
            createFileRecord(documentNo, folderId, companyId, tenancy);

        } catch (Exception e) {
            logger.error("生成物流文件失败 - 单据类型: {}, 单据编号: {}", documentType, documentNo, e);
            // 记录失败日志，但继续处理其他单据
        }
    }

    /**
     * 获取或创建单据文件夹
     */
    private Long getOrCreateDocumentFolder(DocumentTypeEnum documentType,
                                           String upDocumentNo,
                                           String documentNo,
                                           Long companyId,
                                           Long tenancy,
                                           Long warehouseId) {

        // 获取父文件夹ID
        Long parentId = getParentFolderId(documentType, upDocumentNo, companyId, tenancy);

        // 检查是否已存在该单据文件夹
        LogisticsFolderHeader folder = getDocumentFile(documentType, parentId, companyId, documentNo,tenancy,upDocumentNo);
        if (ObjUtil.isNotEmpty(folder)){
            return folder.getId();
        }

        // 创建新文件夹
        return createSubFoldersByType(documentNo,upDocumentNo,parentId, documentType,companyId,tenancy,warehouseId);

    }
    private LogisticsFolderHeader getDocumentFile(DocumentTypeEnum documentType,
                                              Long parentId,
                                              Long companyId,
                                              String documentNo,
                                              Long tenancy,
                                              String upDocumentNo){
        List<DocumentTypeEnum> documentTypeEnumList = getDocumentTypeEnum();
        List<DocumentTypeEnum> documentTypeEnumListBySpecial = getDocumentTypeEnumBySpecial();

        LogisticsFolderHeader folder = null;
        ConditionRule condition = ConditionRule.getInstance();

        condition.andEqual(LogisticsFolderHeader::getCompanyId, companyId);
        condition.andEqual(LogisticsFolderHeader::getTenancy, tenancy);

        if (documentTypeEnumList.contains(documentType)){
            condition.andEqual(LogisticsFolderHeader::getFolderType, documentType.getCode());
            condition.andEqual(LogisticsFolderHeader::getParentId, parentId);
             folder = folderHeaderService.findFirst(condition);

        } else if(documentTypeEnumListBySpecial.contains(documentType)){

              //没父级的质检单
              if(ObjUtil.isEmpty(upDocumentNo)){
                  condition.andIn(LogisticsFolderHeader::getFolderType, Arrays.asList("QC","ASN_QC"));
                  condition.andEqual(LogisticsFolderHeader::getFolderName, documentNo);
               }else{
                  condition.andEqual(LogisticsFolderHeader::getFolderType, documentType.getCode());
                  condition.andEqual(LogisticsFolderHeader::getParentId, parentId);
              }
            folder = folderHeaderService.findFirst(condition);

        }else{
            condition.andEqual(LogisticsFolderHeader::getFolderType, documentType.getCode());
            condition.andEqual(LogisticsFolderHeader::getFolderName, documentNo);
            folder = folderHeaderService.findFirst(condition);

        }
        return folder;
    }
    /**
     * 获取入库和出库的下级枚举集合
     */
    private List<DocumentTypeEnum> getDocumentTypeEnum(){
        List<DocumentTypeEnum> documentTypeEnumList = new ArrayList<>();
        documentTypeEnumList.add(DocumentTypeEnum.RECEIVING);
        documentTypeEnumList.add(DocumentTypeEnum.PUTAWAY);
        documentTypeEnumList.add(DocumentTypeEnum.PICKING);
        documentTypeEnumList.add(DocumentTypeEnum.WAVE);
        documentTypeEnumList.add(DocumentTypeEnum.CHECKING);
        documentTypeEnumList.add(DocumentTypeEnum.SHIPPING);
        documentTypeEnumList.add(DocumentTypeEnum.PACKING);
        documentTypeEnumList.add(DocumentTypeEnum.ASN_RECEIVING);
        documentTypeEnumList.add(DocumentTypeEnum.ASN_PUTAWAY);
        documentTypeEnumList.add(DocumentTypeEnum.SO_PICKING);
        documentTypeEnumList.add(DocumentTypeEnum.SO_WAVE);
        documentTypeEnumList.add(DocumentTypeEnum.SO_CHECKING);
        documentTypeEnumList.add(DocumentTypeEnum.SO_SHIPPING);
        documentTypeEnumList.add(DocumentTypeEnum.SO_PACKING);
        return documentTypeEnumList;
    }
    /**
     * 获取特殊枚举集合（目前是质检）
     */
    private List<DocumentTypeEnum> getDocumentTypeEnumBySpecial(){
        List<DocumentTypeEnum> documentTypeEnumList = new ArrayList<>();
        documentTypeEnumList.add(DocumentTypeEnum.QC);
        documentTypeEnumList.add(DocumentTypeEnum.ASN_QC);
        return documentTypeEnumList;
    }
    /**
     * 获取父文件夹ID
     */
    private Long getParentFolderId(DocumentTypeEnum documentType,
                                   String upDocumentNo,
                                   Long companyId,
                                   Long tenancy) {
        switch (documentType) {
            //采购单
            case PURCHASE_ORDER:
                return getRootFolderId(FolderTypeEnum.PURCHASE_ROOT.getCode(), companyId, tenancy);
            case ASN:
                // 入库单关联采购单
                return getOrderFolderId(DocumentTypeEnum.PURCHASE_ORDER, upDocumentNo, companyId, tenancy);
            case QC:
                return getDocumentFolderIdByQC(DocumentTypeEnum.ASN, upDocumentNo, companyId, tenancy);
            case RECEIVING:
            case PUTAWAY:
                // 质检单/收货单/上架任务关联入库单
                return getDocumentFolderId(DocumentTypeEnum.ASN, upDocumentNo, companyId, tenancy);

            //销售单
            case SALES_ORDER:
                return getRootFolderId(FolderTypeEnum.SALES_ROOT.getCode(), companyId, tenancy);
            case DN:
                // 出库单关联销售单
                return getOrderFolderId(DocumentTypeEnum.SALES_ORDER, upDocumentNo, companyId, tenancy);
            case PICKING:
            case WAVE:
            case CHECKING:
            case PACKING:
            case SHIPPING:
                // 拣货单/波次单/复合单/打包单/发运单等关联出库单
                return getDocumentFolderId(DocumentTypeEnum.DN, upDocumentNo, companyId, tenancy);

            //入库单
            case ASN_ORDER:
                return getRootFolderId(FolderTypeEnum.ASN_ROOT.getCode(), companyId, tenancy);
            case ASN_QC:
                return getDocumentFolderIdByQC(DocumentTypeEnum.ASN_ORDER, upDocumentNo, companyId, tenancy);
            case ASN_RECEIVING:
            case ASN_PUTAWAY:
                // 质检单/收货单/上架任务关联入库单
                return getDocumentFolderId(DocumentTypeEnum.ASN_ORDER, upDocumentNo, companyId, tenancy);

            //出库单
            case SO_ORDER:
                return getRootFolderId(FolderTypeEnum.SO_ROOT.getCode(), companyId, tenancy);
            case SO_PICKING:
            case SO_WAVE:
            case SO_CHECKING:
            case SO_PACKING:
            case SO_SHIPPING:
                // 拣货单/波次单/复合单/打包单/发运单等关联出库单
                return getDocumentFolderId(DocumentTypeEnum.SO_ORDER, upDocumentNo, companyId, tenancy);
            default:
                return getRootFolderId(FolderTypeEnum.DEFAULT.getCode(), companyId, tenancy);
        }
    }

    /**
     * 获取根文件夹ID
     */
    private Long getRootFolderId(String folderType, Long companyId, Long tenancy) {
        ConditionRule condition = ConditionRule.getInstance();
        condition.andEqual(LogisticsFolderHeader::getFolderType, folderType);
        condition.andEqual(LogisticsFolderHeader::getCompanyId, companyId);
        condition.andEqual(LogisticsFolderHeader::getTenancy, tenancy);

        LogisticsFolderHeader folder = folderHeaderService.findFirst(condition);
        if (folder == null) {
            throw new BusinessException("根文件夹不存在: " + folderType);
        }
        return folder.getId();
    }

    /**
     * 获取订单文件夹ID
     */
    private Long getOrderFolderId(DocumentTypeEnum orderType,
                                  String orderNo,
                                  Long companyId,
                                  Long tenancy) {

        if(StrUtil.isBlank(orderNo)){
            throw new BusinessException("父编号为空: " + orderType.getName());
        }
        ConditionRule condition = ConditionRule.getInstance();
        condition.andEqual(LogisticsFolderHeader::getFolderType, orderType.getCode());
        condition.andEqual(LogisticsFolderHeader::getFolderName, orderNo);
        condition.andEqual(LogisticsFolderHeader::getCompanyId, companyId);
        condition.andEqual(LogisticsFolderHeader::getTenancy, tenancy);

        LogisticsFolderHeader folder = folderHeaderService.findFirst(condition);
        if (folder == null) {
            throw new BusinessException("订单文件夹不存在: " + orderNo);
        }
        return folder.getId();
    }

    /**
     * 获取单据文件夹ID
     */
    private Long getDocumentFolderId(DocumentTypeEnum documentType,
                                     String documentNo,
                                     Long companyId,
                                     Long tenancy) {
        ConditionRule condition = ConditionRule.getInstance();
        condition.andEqual(LogisticsFolderHeader::getFolderType, documentType.getCode());
        condition.andEqual(LogisticsFolderHeader::getFolderName, documentNo);
        condition.andEqual(LogisticsFolderHeader::getCompanyId, companyId);
        condition.andEqual(LogisticsFolderHeader::getTenancy, tenancy);

        LogisticsFolderHeader folder = folderHeaderService.findFirst(condition);
        if (folder == null) {
            throw new BusinessException("父单据文件夹不存在: " + documentNo);
        }
        return folder.getId();
    }
    /**
     * 获取单据文件夹ID（质检单特殊处理）
     */
    private Long getDocumentFolderIdByQC(DocumentTypeEnum documentType,
                                     String documentNo,
                                     Long companyId,
                                     Long tenancy) {

        LogisticsFolderHeader folder = null;
        if(StrUtil.isBlank(documentNo)){
            ConditionRule condition = ConditionRule.getInstance();
            condition.andEqual(LogisticsFolderHeader::getFolderType, FolderTypeEnum.QC_ROOT.getCode());
            condition.andEqual(LogisticsFolderHeader::getCompanyId, companyId);
            condition.andEqual(LogisticsFolderHeader::getTenancy, tenancy);
            folder = folderHeaderService.findFirst(condition);
        }else{
            ConditionRule condition = ConditionRule.getInstance();
            condition.andEqual(LogisticsFolderHeader::getFolderType, documentType.getCode());
            condition.andEqual(LogisticsFolderHeader::getFolderName, documentNo);
            condition.andEqual(LogisticsFolderHeader::getCompanyId, companyId);
            condition.andEqual(LogisticsFolderHeader::getTenancy, tenancy);
            folder = folderHeaderService.findFirst(condition);
        }

        if (folder == null) {
            throw new BusinessException("父单据文件夹不存在: " + documentNo);
        }
        return folder.getId();
    }

    /**
     * 创建下级标准文件夹
     */
    private Long createSubFoldersByType(String documentNo,
                                        String upDocumentNo,
                                        Long parentId,
                                        DocumentTypeEnum documentType,
                                        Long companyId,
                                        Long tenancy,
                                        Long warehouseId) {

        String parentIds = "";
        // 设置父级路径
        if (parentId != null) {
            LogisticsFolderHeader parent = folderHeaderService.findById(parentId);
            if (ObjUtil.isNotEmpty(parent) && ObjUtil.isNotEmpty(parent.getParentIds())){
                parentIds = parent.getParentIds() + "," + parentId;
            }else{
                parentIds = String.valueOf(parentId);
            }

        }

        List<DocumentTypeEnum> documentTypeEnum = getDocumentTypeEnum();
        List<DocumentTypeEnum> documentTypeEnumBySpecial = getDocumentTypeEnumBySpecial();
        LogisticsFolderHeader newFolder = new LogisticsFolderHeader();
        if (documentTypeEnum.contains(documentType)){
            newFolder.setFolderName(documentType.getName());

        }else if(documentTypeEnumBySpecial.contains(documentType)){
            //是否有传父级编号
            if(StrUtil.isBlank(upDocumentNo)){
                newFolder.setFolderName(documentNo);
            }else{
                newFolder.setFolderName(documentType.getName());
            }

        }else{
            newFolder.setFolderName(documentNo);
        }
        Warehouse warehouse = warehouseDao.findById(warehouseId);

        newFolder.setSort(1);
        newFolder.setIsRecycle(Integer.valueOf(YesNoEnum.NO.getCode()));
        newFolder.setIsDefault(Integer.valueOf(YesNoEnum.YES.getCode()));
        newFolder.setWarehouseId(warehouseId);
        newFolder.setWarehouseName(warehouse.getWarehouseName());
        newFolder.setParentId(parentId);
        newFolder.setFolderType(documentType.getCode());
        newFolder.setParentIds(parentIds);
        newFolder.setCompanyId(companyId);
        newFolder.setTenancy(tenancy);
        folderHeaderService.preSave(newFolder);
        folderHeaderService.insert(newFolder);
        return newFolder.getId();
    }
    /**
     * 根据单据类型获取子文件夹名称数组
     */
    private String[] getSubFolderNamesByDocumentType(DocumentTypeEnum documentType) {
        switch (documentType) {
            case ASN:
            case ASN_ORDER:
                return new String[]{"质检单", "收货单", "上架任务"};
            case DN:
            case SO_ORDER:
                return new String[]{"拣货单", "波次单", "复核单", "打包单", "发运单"};
            default:
                return null;
        }
    }

    /**
     * 获取子文件夹的类型
     */
    private String getSubFolderType(DocumentTypeEnum documentType, String folderName) {

        if (documentType == DocumentTypeEnum.ASN) {
            switch (folderName) {
                case "质检单": return DocumentTypeEnum.QC.getCode();
                case "收货单": return DocumentTypeEnum.RECEIVING.getCode();
                case "上架任务": return DocumentTypeEnum.PUTAWAY.getCode();
            }
        }
        if (documentType == DocumentTypeEnum.ASN_ORDER){
            switch (folderName) {
                case "质检单": return DocumentTypeEnum.ASN_QC.getCode();
                case "收货单": return DocumentTypeEnum.ASN_RECEIVING.getCode();
                case "上架任务": return DocumentTypeEnum.ASN_PUTAWAY.getCode();
            }
        }

        if (documentType == DocumentTypeEnum.DN) {
            switch (folderName) {
                case "拣货单": return DocumentTypeEnum.PICKING.getCode();
                case "波次单": return DocumentTypeEnum.WAVE.getCode();
                case "复核单": return DocumentTypeEnum.CHECKING.getCode();
                case "打包单": return DocumentTypeEnum.PACKING.getCode();
                case "发运单": return DocumentTypeEnum.SHIPPING.getCode();
            }
        }
        if( documentType == DocumentTypeEnum.SO_ORDER){
            switch (folderName) {
                case "拣货单": return DocumentTypeEnum.SO_PICKING.getCode();
                case "波次单": return DocumentTypeEnum.SO_WAVE.getCode();
                case "复核单": return DocumentTypeEnum.SO_CHECKING.getCode();
                case "打包单": return DocumentTypeEnum.SO_PACKING.getCode();
                case "发运单": return DocumentTypeEnum.SO_SHIPPING.getCode();
            }
        }
        return documentType.getCode();
    }
    /**
     * 检查文件是否已存在
     */
    private boolean isFileExists(String documentNo, Long folderId) {
        ConditionRule condition = ConditionRule.getInstance();
        condition.andEqual(LogisticsFolderDetail::getFileName, documentNo);
        condition.andEqual(LogisticsFolderDetail::getFolderId, folderId);
        return folderDetailService.count(condition) > 0;
    }

    /**
     * 创建文件记录
     */
    private void createFileRecord(String documentNo, Long folderId, Long companyId,
                                  Long tenancy) {
        // 创建文件记录
        LogisticsFolderDetail detail = new LogisticsFolderDetail();
        detail.setFileCode(documentNo);
        detail.setFileName(documentNo);
        detail.setFileType(DetailFileTypeEnum.LOGISTICS.getCode());
        detail.setFolderId(folderId);
        detail.setCompanyId(companyId);
        detail.setTenancy(tenancy);
        detail.setCurrentVersion(1);
        detail.setTimeOut(Integer.valueOf(YesNoEnum.NO.getCode()));
        detail.setIsRecycle(Integer.valueOf(YesNoEnum.NO.getCode()));
        detail.setCreatorName(SessionContext.getSessionUserInfo().getRealName());
        detail.setSort(Integer.parseInt(YesNoEnum.NO.getCode()));
        folderDetailService.preSave(detail);

        // 创建附件记录
//        LogisticsFolderDetailFile file = new LogisticsFolderDetailFile();
//        file.setBusinessId(detail.getFileCode());
//        file.setFileUrl(ossUrl);
//        file.setFileName(fileName);
//        file.setFileType("pdf");
//        file.setOssDeleted(YesNoEnum.NO.getCode());

         //批量保存
//        BatchSaveDetailItem saveItem = new BatchSaveDetailItem();
//        LogisticsFolderDetailItem detailItem = new LogisticsFolderDetailItem();
//        BeanUtil.copyProperties(detail, detailItem);
//        detailItem.setFileItemList(Collections.singletonList(
//                new LogisticsFolderDetailFileItem(file)
//        ));
//        saveItem.setItemList(Collections.singletonList(detailItem));

        folderDetailService.saveOrUpdate(detail);
    }
}
