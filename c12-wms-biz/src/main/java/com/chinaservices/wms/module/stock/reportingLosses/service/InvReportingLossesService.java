package com.chinaservices.wms.module.stock.reportingLosses.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.util.JsonUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.enums.stock.InvReportingLossesStatusEnum;
import com.chinaservices.wms.common.exception.StockExcepitonEnum;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.folder.logistics.domain.BatchDelDetailItem;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.multi.domain.SnSkuPageCondition;
import com.chinaservices.wms.module.stock.multi.domain.SnSkuPageQuery;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLocSn;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocSnService;
import com.chinaservices.wms.module.stock.reportingLosses.dao.InvReportingLossesDao;
import com.chinaservices.wms.module.stock.reportingLosses.domain.*;
import com.chinaservices.wms.module.stock.reportingLosses.model.InvLossesDetails;
import com.chinaservices.wms.module.stock.reportingLosses.model.InvReportingLosses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.security.Key;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName InvReportingLossesService
 * <AUTHOR>
 * @Date 2025/7/11 16:33
 * @Description
 * @Version 1.0
 */
@Service
public class InvReportingLossesService extends ModuleBaseServiceSupport<InvReportingLossesDao, InvReportingLosses, Long> {

    @Autowired
    private InvLossesDetailsService invLossesDetailsService;

    @Autowired
    private NumberGenerator numberGenerator;

    @Autowired
    private InvLotLocSnService invLotLocSnService;

    @Autowired
    private SkuService skuService;

    @Autowired
    private PackageUnitService packageUnitService;

    @Autowired
    private StockService stockService;

    /**
     *@Description 分页查询
     *@Param queryCondition
     *@Return * {@link PageResult< InvReportingLossesQuery> }
     *@Date 2025/7/11 16:44
     *<AUTHOR>
     **/
    public PageResult<InvReportingLossesQuery> getPage(InvReportingLossesPageCondition queryCondition) {
        PageResult<InvReportingLossesQuery> pageResult = dao.getPage(queryCondition);
        return pageResult;
    }

    /**
     *@Description 根据id查询
     *@Param id
     *@Return * {@link InvReportingLossesQuery }
     *@Date 2025/7/11 17:07
     *<AUTHOR>
     **/
    public InvReportingLossesQuery getQueryById(Long id) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(InvReportingLosses::getId,id);
        InvReportingLossesQuery invReportingLossesQuery = dao.findFirst(InvReportingLossesQuery.class,conditionRule);
        //查询对应明细
        List<InvLossesDetailsQuery> detailsQueryList = invLossesDetailsService.findByLossesNoList(Arrays.asList(invReportingLossesQuery.getLossesNo()));
        //抽取包装单位ID
        List<Long> packageUnitIdList = detailsQueryList.stream().map(InvLossesDetailsQuery::getPackageUnitId).distinct().collect(Collectors.toList());
        //查询包装单位
        List<PackageUnit> packageUnitList = packageUnitService.findByIds(packageUnitIdList.toArray(Long[]::new));
        Map<Long,PackageUnit> packageUnitMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(packageUnitList)){
            packageUnitMap = packageUnitList.stream().collect(Collectors.toMap(PackageUnit::getId, Function.identity(), (v1, v2) -> v1));
        }
        Map<Long, PackageUnit> finalPackageUnitMap = packageUnitMap;
        detailsQueryList.forEach(detailsQuery -> {
            PackageUnit packageUnit = finalPackageUnitMap.get(detailsQuery.getPackageUnitId());
            if (ObjectUtil.isNotNull(packageUnit)){
                detailsQuery.setMinQuantity(packageUnit.getMinQuantity());
            }
        });
        invReportingLossesQuery.setDetailsQueryList(detailsQueryList);
        return invReportingLossesQuery;
    }

    /**
     *@Description 批量删除
     *@Param item
     *@Return Void
     *@Date 2025/7/11 17:34
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void batchDel(BatchDelDetailItem item) {
        //根据id查询
        List<InvReportingLosses> invReportingLossesList = dao.findByIds(item.getIdList().toArray(new Long[0]));
        //是否全是已创建状态
        boolean flag = invReportingLossesList.stream().allMatch(invReportingLosses -> InvReportingLossesStatusEnum.CREATE.getCode().equals(invReportingLosses.getLossesStatus()));
        List< String> failNoList = invReportingLossesList.stream().filter(invReportingLosses -> !InvReportingLossesStatusEnum.CREATE.getCode().equals(invReportingLosses.getLossesStatus())).map(InvReportingLosses::getLossesNo).distinct().collect(Collectors.toList());
        if(!flag){
            throw new ServiceException(StockExcepitonEnum.DELETE_ONLY_CREATE,failNoList);
        }
        dao.delete(item.getIdList().toArray(new Long[0]));
        List<String> lossesNoList = invReportingLossesList.stream().map(InvReportingLosses::getLossesNo).distinct().collect(Collectors.toList());
        //删除对应明细和序列号
        invLossesDetailsService.batchDel(lossesNoList);
    }

    /**
     *@Description 批量审核
     *@Param item
     *@Return Void
     *@Date 2025/7/11 17:54
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void batchAudit(BatchDelDetailItem item) {
        List<InvReportingLosses> invReportingLossesList = dao.findByIds(item.getIdList().toArray(new Long[0]));
        //是否都是已创建状态
        boolean flag = invReportingLossesList.stream().allMatch(invReportingLosses -> InvReportingLossesStatusEnum.CREATE.getCode().equals(invReportingLosses.getLossesStatus()));
        List< String> failNoList = invReportingLossesList.stream().filter(invReportingLosses -> !InvReportingLossesStatusEnum.CREATE.getCode().equals(invReportingLosses.getLossesStatus())).map(InvReportingLosses::getLossesNo).distinct().collect(Collectors.toList());
        if(!flag){
            throw new ServiceException(StockExcepitonEnum.DELETE_ONLY_CREATE,failNoList);
        }
        invReportingLossesList.forEach(invReportingLosses -> {
            invReportingLosses.setLossesStatus(InvReportingLossesStatusEnum.AUDIT.getCode());
            preSave(invReportingLosses);
        });
        dao.batchUpdate(invReportingLossesList);
        //查询对应明细
        List<InvLossesDetailsQuery> invLossesDetailsList = invLossesDetailsService.findByLossesNoList(invReportingLossesList.stream().map(InvReportingLosses::getLossesNo).collect(Collectors.toList()));
        //库存报损审核操作
        executorLoc(TransactionType.TRAN_DAMAGE_AUDIT,invReportingLossesList,invLossesDetailsList);
    }

    /**
     *@Description 执行库存操作
     *@Param invLossesDetailsList
     *@Return Void
     *@Date 2025/7/16 15:39
     *<AUTHOR>
     **/
    private void executorLoc(String type,List<InvReportingLosses> invReportingLossesList,List<InvLossesDetailsQuery> invLossesDetailsList) {
        //invLossesDetailsList抽取所有序列号
        List<String> snList = invLossesDetailsList.stream()
                .filter(invLossesDetailsQuery -> CollectionUtil.isNotEmpty(invLossesDetailsQuery.getSnNoList()))
                .map(InvLossesDetailsQuery::getSnNoList)
                .flatMap(map -> map.values().stream()).flatMap(list -> list.stream()).collect(Collectors.toList());
        Map<Long,List<InvLotLocSn>> invLotLocSnMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(snList)){
            List<InvLotLocSn> invLotLocSnList = invLotLocSnService.findBySnList(snList);
            if(CollectionUtil.isNotEmpty(invLotLocSnList)){
                //按照商品id分组
                invLotLocSnMap = invLotLocSnList.stream().collect(Collectors.groupingBy(invLotLocSn -> invLotLocSn.getSkuId()));
            }
        }
        //转Map
        Map<String,InvReportingLosses> invReportingLossesMap = invReportingLossesList.stream().collect(Collectors.toMap(InvReportingLosses::getLossesNo, Function.identity(), (old, newItem)->old));
        Map<Long, List<InvLotLocSn>> finalInvLotLocSnMap = invLotLocSnMap;
        invLossesDetailsList.forEach(item -> {
            if(CollectionUtil.isNotEmpty(item.getSnNoList())){
                List<InvLotLocSn> currentLocSnList = finalInvLotLocSnMap.get(item.getSkuId());
                if(CollectionUtil.isNotEmpty(currentLocSnList)){
                    //根据库位ID、批次号、容器号分组
                    Map<String,List<InvLotLocSn>> locSnMap = currentLocSnList.stream().collect(Collectors.groupingBy(x -> x.getLocId() +"_"+x.getLotNum()+"_"+x.getPalletNum()));
                    if(CollectionUtil.isNotEmpty(locSnMap)){
                        locSnMap.forEach((key,value) -> {
                            if(CollectionUtil.isNotEmpty(value)){
                                String[] keyArr = key.split("_");
                                //抽取序列号
                                List<String> lotNumList = value.stream().map(InvLotLocSn::getLotNum).distinct().collect(Collectors.toList());
                                //根据序列号分组
                                Map<String,List<InvLotLocSn>> lotNumMap = value.stream().collect(Collectors.groupingBy(InvLotLocSn::getLotNum));
                                lotNumList.forEach(lotNum -> {
                                    List<InvLotLocSn> currentLotNumList = lotNumMap.get(lotNum);
                                    InvLotLocQtyBO bo = new  InvLotLocQtyBO();
                                    bo.setOrderNo(item.getLossesNo());
                                    bo.setWarehouseId(invReportingLossesMap.get(item.getLossesNo()).getWarehouseId());
                                    bo.setWarehouseName(invReportingLossesMap.get(item.getLossesNo()).getWarehouseName());
                                    bo.setOwnerId(invReportingLossesMap.get(item.getLossesNo()).getOwnerId());
                                    bo.setOwnerName(invReportingLossesMap.get(item.getLossesNo()).getOwnerName());
                                    bo.setLocId(Long.valueOf(keyArr[0]));
                                    bo.setSkuId(item.getSkuId());
                                    bo.setLotNum(keyArr[1]);
                                    bo.setPalletNum(keyArr[2]);
                                    if(CollectionUtil.isNotEmpty(currentLotNumList)){
                                        bo.setSkuSn(currentLotNumList.stream().map(InvLotLocSn::getSkuSn).collect(Collectors.toList()));
                                        bo.setUpdateNum(BigDecimal.valueOf(currentLotNumList.size()));
                                    }
                                    bo.setTransactionType(type);
                                    //报损
                                    logger.info("库存序列号报损：{}", JsonUtil.toJson(bo));
                                    stockService.exec(bo);
                                });
                            }
                        });

                    }
                }
            }else{
                BigDecimal count = item.getLossesQuantityEa();
                InvLotLocQtyBO bo = new  InvLotLocQtyBO();
                bo.setOrderNo(item.getLossesNo());
                bo.setWarehouseId(invReportingLossesMap.get(item.getLossesNo()).getWarehouseId());
                bo.setWarehouseName(invReportingLossesMap.get(item.getLossesNo()).getWarehouseName());
                bo.setOwnerId(invReportingLossesMap.get(item.getLossesNo()).getOwnerId());
                bo.setOwnerName(invReportingLossesMap.get(item.getLossesNo()).getOwnerName());
                bo.setLocId(item.getLocId());
                bo.setSkuId(item.getSkuId());
                bo.setLotNum(item.getLotNum());
                bo.setPalletNum(item.getPalletNum());
                bo.setUpdateNum(count);
                bo.setTransactionType(type);
                //报损
                logger.info("库存报损：{}", JsonUtil.toJson(bo));
                stockService.exec(bo);
            }
        });

    }

    /**
     *@Description 批量取消审核
     *@Param item
     *@Return Void
     *@Date 2025/7/11 17:54
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void batchCancelAudit(BatchDelDetailItem item) {
        List<InvReportingLosses> invReportingLossesList = dao.findByIds(item.getIdList().toArray(new Long[0]));
        //是否都是已审核状态
        boolean flag = invReportingLossesList.stream().allMatch(invReportingLosses -> InvReportingLossesStatusEnum.AUDIT.getCode().equals(invReportingLosses.getLossesStatus()));
        List< String> failNoList = invReportingLossesList.stream().filter(invReportingLosses -> !InvReportingLossesStatusEnum.AUDIT.getCode().equals(invReportingLosses.getLossesStatus())).map(InvReportingLosses::getLossesNo).distinct().collect(Collectors.toList());
        if(!flag){
            throw new ServiceException(StockExcepitonEnum.CANCEL_ONLY_AUDIT,failNoList);
        }
        invReportingLossesList.forEach(invReportingLosses -> {
            invReportingLosses.setLossesStatus(InvReportingLossesStatusEnum.CREATE.getCode());
            preSave(invReportingLosses);
        });
        dao.batchUpdate(invReportingLossesList);
        //查询对应明细
        List<InvLossesDetailsQuery> invLossesDetailsList = invLossesDetailsService.findByLossesNoList(invReportingLossesList.stream().map(InvReportingLosses::getLossesNo).collect(Collectors.toList()));
        //库存取消报损审核操作
        executorLoc(TransactionType.TRAN_CR_DAMAGE_AUDIT,invReportingLossesList,invLossesDetailsList);
    }

    /**
     *@Description 保存/修改
     *@Param item
     *@Return Void
     *@Date 2025/7/14 9:56
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveEntity(ReportingLossesSaveItem item) {
        InvReportingLosses invReportingLosses = new InvReportingLosses();
        if(ObjectUtil.isNull(item.getId())){
            invReportingLosses.setLossesNo(numberGenerator.nextValue(IdRuleConstant.REPORTING_LOSSES_NO));
            invReportingLosses.setLossesStatus(InvReportingLossesStatusEnum.CREATE.getCode());
        }else {
            invReportingLosses = dao.findById(item.getId());
            //删除对应明细和序列号
            invLossesDetailsService.batchDel(Arrays.asList(invReportingLosses.getLossesNo()));
        }
        BeanUtil.copyProperties(item,invReportingLosses);
        dao.saveOrUpdate(invReportingLosses);
        List<InvLossesDetailsSaveItem> detailsSaveItemList = item.getDetailsSaveItemList();
        InvReportingLosses finalInvReportingLosses = invReportingLosses;
        List<InvLossesDetails> detailsList = new ArrayList<>();
        detailsSaveItemList.forEach(detailsSaveItem -> {
            InvLossesDetails invLossesDetails = new InvLossesDetails();
            BeanUtil.copyProperties(detailsSaveItem,invLossesDetails);
            invLossesDetails.setLossesNo(finalInvReportingLosses.getLossesNo());
            invLossesDetails.setId(null);
            invLossesDetailsService.preSave(invLossesDetails);
            detailsList.add(invLossesDetails);
        });
        if(CollectionUtil.isNotEmpty(detailsList)){
            invLossesDetailsService.batchSaveItem(detailsList,detailsSaveItemList);
        }

    }

    /**
     *@Description 确认报损
     *@Param item
     *@Return Void
     *@Date 2025/7/14 16:34
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void batchConfirm(BatchDelDetailItem item) {
        List<InvReportingLosses> invReportingLossesList = dao.findByIds(item.getIdList().toArray(new Long[0]));
        //是否都是已审核状态
        boolean flag = invReportingLossesList.stream().allMatch(invReportingLosses -> InvReportingLossesStatusEnum.AUDIT.getCode().equals(invReportingLosses.getLossesStatus()));
        List< String> failNoList = invReportingLossesList.stream().filter(invReportingLosses -> !InvReportingLossesStatusEnum.AUDIT.getCode().equals(invReportingLosses.getLossesStatus())).map(InvReportingLosses::getLossesNo).distinct().collect(Collectors.toList());
        if(!flag){
            throw new ServiceException(StockExcepitonEnum.CANCEL_ONLY_AUDIT,failNoList);
        }
        invReportingLossesList.forEach(invReportingLosses -> {
            invReportingLosses.setLossesStatus(InvReportingLossesStatusEnum.LOSS.getCode());
            preSave(invReportingLosses);
        });
        dao.batchUpdate(invReportingLossesList);
        //查询对应明细
        List<InvLossesDetailsQuery> invLossesDetailsList = invLossesDetailsService.findByLossesNoList(invReportingLossesList.stream().map(InvReportingLosses::getLossesNo).collect(Collectors.toList()));
        //库存确认报损操作
        executorLoc(TransactionType.TRAN_DAMAGE_CONFIRM,invReportingLossesList,invLossesDetailsList);
    }

    /**
     *@Description 序列号商品分页查询
     *@Param condition
     *@Return Void
     *@Date 2025/7/15 14:37
     *<AUTHOR>
     **/
    public PageResult<SnSkuPageQuery> getSnSkuPage(SnSkuPageCondition condition) {
        PageResult<SnSkuPageQuery> pageResult = invLotLocSnService.getSnSkuPage(condition);
        if(CollectionUtil.isNotEmpty(pageResult.getRows())){
            //抽取商品id
            List<Long> skuIds = pageResult.getRows().stream().map(SnSkuPageQuery::getSkuId).distinct().collect(Collectors.toList());
            //通过商品id查询对应商品信息
            List<Sku> skuList = skuService.findByIds(skuIds.toArray(new Long[0]));
            //根据商品ID转map
            Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (old, newValue) -> old));
            //抽取包装规格ID
            List<Long> packageIdList = skuList.stream().map(Sku::getPackageId).distinct().collect(Collectors.toList());
            //通过包装ID查询包装单位信息
            List<PackageUnit> packageList = packageUnitService.listByPackageIds(packageIdList);
            //根据包装ID分组
            Map<Long, List<PackageUnit>> packageMap = packageList.stream().collect(Collectors.groupingBy(PackageUnit::getPackageId));
            pageResult.getRows().forEach(snSkuPageQuery -> {
                Sku sku = skuMap.get(snSkuPageQuery.getSkuId());
                if(ObjectUtil.isNotNull(sku)){
                    snSkuPageQuery.setPackageId(sku.getPackageId());
                    snSkuPageQuery.setPackageName(sku.getPackageName());
                    List<PackageUnit> currentPackageList = packageMap.get(sku.getPackageId());
                    if(CollectionUtil.isNotEmpty(currentPackageList)){
                        List<PackageUnit> eaList = currentPackageList.stream().filter(packageUnit -> "EA".equals(packageUnit.getCode())).collect(Collectors.toList());
                        if(CollectionUtil.isNotEmpty(eaList)){
                            PackageUnit eaPackage = eaList.get(0);
                            snSkuPageQuery.setPackageUnitId(eaPackage.getId());
                            snSkuPageQuery.setPackageUnitName(eaPackage.getName());
                            snSkuPageQuery.setMinQuantity(eaPackage.getMinQuantity());
                        }
                    }
                }
            });
        }
        return pageResult;
    }

    /**
     *@Description 根据商品ID，仓库ID，货主ID，查询对应序列号
     *@Param condition
     *@Return Void
     *@Date 2025/7/15 16:00
     *<AUTHOR>
     **/
    public Map<String, List<String>> getSnMap(SnSkuPageCondition condition) {
        Map<String, List<String>> snMap = invLotLocSnService.getSnMap(condition);
        return snMap;
    }
}
