package com.chinaservices.wms.module.stock.expirywarning.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.stock.expirywarning.domain.ExpiryWarningSettingPageCondition;
import com.chinaservices.wms.module.stock.expirywarning.domain.ExpiryWarningSettingPageQuery;
import com.chinaservices.wms.module.stock.expirywarning.domain.ExpiryWarningSettingTableItem;
import com.chinaservices.wms.module.stock.expirywarning.service.ExpiryWarningSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 预警设置控制器
 * <AUTHOR>
 * @date 2025/6/16
 */
@RestController
@RequestMapping("/api/expiry/warning/setting")
public class ExpiryWarningSettingController {

    @Autowired
    private ExpiryWarningSettingService expiryWarningSettingService;

    /**
     * 分页查询效期设置
     * @param condition 分页查询条件
     * @return ResponseData<PageResult<ExpiryWarningSettingPageQuery>>
     */
    @PostMapping("/page")
    @SaCheckPermission("expiryWarningSetting:expiryWarningSettingList:headBtn:page")
    public ResponseData<PageResult<ExpiryWarningSettingPageQuery>> page(@RequestBody ExpiryWarningSettingPageCondition condition) {
        return ResponseData.success(expiryWarningSettingService.page(condition));
    }

    /**
     * 通过id获取效期设置
     * @param id 主键
     * @return ResponseData<ExpiryWarningSettingPageQuery>
     */
    @PostMapping("/getById/{id}")
    public ResponseData<ExpiryWarningSettingPageQuery> getById(@PathVariable Long id) {
        return ResponseData.success(expiryWarningSettingService.getById(id));
    }

    /**
     * 新增编辑效期设置
     * @param tableItem 预警设置更新实体类
     * @return ResponseData<ExpiryWarningSettingPageQuery>
     */
    @PostMapping("/saveOrUpdate")
    @SaCheckPermission(value = {
        "expiryWarningSetting:expiryWarningSettingList:headBtn:add",
        "expiryWarningSetting:expiryWarningSettingList:table:edit"
    }, mode = SaMode.OR)
    public ResponseData<Boolean> saveOrUpdate(@RequestBody ExpiryWarningSettingTableItem tableItem) {
        return ResponseData.success(expiryWarningSettingService.saveOrUpdate(tableItem));
    }

    /**
     * 批量删除预警设置
     * @param ids 主键数组
     * @return ResponseData<Boolean>
     */
    @PostMapping("/batchDelete")
    @SaCheckPermission(value = {
        "expiryWarningSetting:expiryWarningSettingList:headBtn:delete",
        "expiryWarningSetting:expiryWarningSettingList:table:delete"
    }, mode = SaMode.OR)
    public ResponseData<Boolean> batchDelete(@RequestBody Long[] ids){
        return ResponseData.success(expiryWarningSettingService.batchDelete(ids));
    }
}
