package com.chinaservices.wms.module.stock.reportingLosses.model;


import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 库存报损明细序列号表
 * cs_inv_losses_details_sn
 */
@Data
@Entity
@Table(name = "cs_inv_losses_details_sn")
public class InvLossesDetailsSn extends ModuleBaseModel {

    /**
     * 报损明细id
     */
    private Long detailsId;

    /**
     * 报损编号
     */
    private String lossesNo;

    /**
     * 箱码
     */
    private String boxCode;

    /**
     * 序列号
     */
    private String snNo;

}