package com.chinaservices.wms.module.performance.dao;


import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.sqlid.performance.PerformanceSqlId;
import com.chinaservices.wms.module.performance.domain.KpiHeaderPageCondition;
import com.chinaservices.wms.module.performance.domain.KpiHeaderQuery;
import com.chinaservices.wms.module.performance.domain.KpiHeaderRankingQuery;
import com.chinaservices.wms.module.performance.model.KpiHeader;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class KpiHeaderDao extends ModuleBaseDaoSupport<KpiHeader, Long> {

    public PageResult<KpiHeaderQuery> page(KpiHeaderPageCondition condition) {
        return sqlExecutor.page(KpiHeaderQuery.class, PerformanceSqlId.KPI_HEADER_PAGE, condition);
    }

    /**
     * 绩效分页排名
     *
     * @param condition 条件
     * @return {@link PageResult }<{@link KpiHeaderRankingQuery }>
     */
    public PageResult<KpiHeaderRankingQuery> pageRanking(KpiHeaderPageCondition condition) {
        return sqlExecutor.page(KpiHeaderRankingQuery.class, PerformanceSqlId.KPI_HEADER_PAGE_RANKING, condition);
    }
}
