package com.chinaservices.wms.module.stock.transfer.dao;

import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.wms.common.constant.WmsSqlId;
import com.chinaservices.wms.module.stock.transfer.domain.TransferHeaderCondition;
import com.chinaservices.wms.module.stock.transfer.domain.TransferHeaderInfo;
import com.chinaservices.wms.module.stock.transfer.domain.TransferHeaderPageCondition;
import com.chinaservices.wms.module.stock.transfer.domain.TransferHeaderQuery;
import com.chinaservices.wms.module.stock.transfer.model.TransferHeader;
import com.chinaservices.wms.module.print.bean.TransferPrintHeader;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class TransferHeaderDao extends ModuleBaseDaoSupport<TransferHeader, Long> {

    /**
     * 条件获取查询转移单
     */
    public PageResult<TransferHeaderQuery> page(TransferHeaderPageCondition condition) {
        return sqlExecutor.page(TransferHeaderQuery.class, WmsSqlId.TRANSFER_HEADER_QUERY_GET_PAGE_LIST, condition);
    }

    public void updateStatusById(TransferHeaderCondition condition) {
        sqlExecutor.update(WmsSqlId.TRANSFER_HEADER_UPDATE_UPDATE_STATUS, condition);
    }

    public void updateStatusTransferHeader(Map<String, Object> params) {
        commonJpaRepository.update(TransferHeader.class, params);
    }

    public TransferPrintHeader getPrintHeaderById(TransferHeaderCondition transferHeaderCondition) {
        return sqlExecutor.findFirst(TransferPrintHeader.class, WmsSqlId.TRANSFER_HEADER_QUERY_GET_HEADER_PRINT,transferHeaderCondition);
    }

    /**
     * 根据Id获取转移单
     */
    public TransferHeaderInfo findInfoById(Long id) {
        return sqlExecutor.findFirst(TransferHeaderInfo.class , WmsSqlId.TRANSFER_HEADER_QUERY_LIST, "id", id);
    }

    /**
     * 更新
     */
    public int update(String sqlId, TransferHeaderCondition condition) {
        return sqlExecutor.update(sqlId, condition);
    }

}
