package com.chinaservices.wms.module.performance.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.fee.fee.domain.IdsItem;
import com.chinaservices.wms.module.performance.domain.KpiIndexIntervalItem;
import com.chinaservices.wms.module.performance.domain.KpiIndexItem;
import com.chinaservices.wms.module.performance.domain.KpiIndexPageCondition;
import com.chinaservices.wms.module.performance.domain.KpiIndexQuery;
import com.chinaservices.wms.module.performance.service.KpiIndexService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 绩效指标管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/performance/kpi/index")
public class KpiIndexController {

    @Autowired
    private KpiIndexService kpiIndexService;


    /**
     * 绩效指标分页查询
     * @param condition
     * @return
     */
    @PostMapping(value = "/page")
    @SaCheckPermission("performanceMetrics:performanceMetricsList:headBtn:page")
    public ResponseData<PageResult<KpiIndexQuery>> page(@RequestBody KpiIndexPageCondition condition){
        return ResponseData.success(kpiIndexService.page(condition));
    }

    /**
     * 搜索所有指标
     * @param condition
     * @return
     */
    @PostMapping(value = "/search")
    public ResponseData<List<KpiIndexQuery>> search(@RequestBody KpiIndexPageCondition condition){
        return ResponseData.success(kpiIndexService.search(condition));
    }

    /**
     * 绩效指标新增
     * @param item
     * @return
     */
    @PostMapping(value = "/add")
    @SaCheckPermission("performanceMetrics:performanceMetricsList:headBtn:add")
    public ResponseData<Boolean> add(@RequestBody KpiIndexItem item){
        return ResponseData.success(kpiIndexService.add(item));
    }

    /**
     * 绩效指标编辑
     * @param item
     * @return
     */
    @PostMapping(value = "/edit")
    @SaCheckPermission("performanceMetrics:performanceMetricsList:table:edit")
    public ResponseData<Boolean> edit(@RequestBody KpiIndexItem item){
        return ResponseData.success(kpiIndexService.edit(item));
    }

    /**
     * 绩效指标批量删除
     * @param item
     * @return
     */
    @PostMapping(value = "/delete")
    @SaCheckPermission(value = {"performanceMetrics:performanceMetricsList:headBtn:delete","performanceMetrics:performanceMetricsList:table:delete"}, mode = SaMode.OR)
    public ResponseData<Boolean> delete(@RequestBody IdsItem item){
        return ResponseData.success(kpiIndexService.delete(item));
    }

    /**
     * 绩效指标启用
     * @param item
     * @return
     */
    @PostMapping(value = "/enable")
    @SaCheckPermission(value = {"performanceMetrics:performanceMetricsList:headBtn:enable","performanceMetrics:performanceMetricsList:table:enable"}, mode = SaMode.OR)
    public ResponseData<Boolean> enable(@RequestBody IdsItem item){
        return ResponseData.success(kpiIndexService.enable(item, YesNoEnum.YES));
    }

    /**
     * 绩效指标停用
     * @param item
     * @return
     */
    @PostMapping(value = "/deactivate")
    @SaCheckPermission(value = {"performanceMetrics:performanceMetricsList:headBtn:disable","performanceMetrics:performanceMetricsList:table:disable"}, mode = SaMode.OR)
    public ResponseData<Boolean> deactivate(@RequestBody IdsItem item){
        return ResponseData.success(kpiIndexService.enable(item, YesNoEnum.NO));
    }


    /**
     * 根据指标ID查询指标区间范围
     */
    @PostMapping(value = "/interval/byNo/{indexNo}")
    public ResponseData<List<KpiIndexIntervalItem>> intervalById(@PathVariable String indexNo){
        return ResponseData.success(kpiIndexService.intervalById(indexNo));
    }
}
