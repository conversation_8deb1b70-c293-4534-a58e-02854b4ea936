package com.chinaservices.wms.module.asn.pa.dao;

import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.sqlid.asn.pa.PaTaskSqlId;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskDetailPageCondition;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskDetailQuery;
import com.chinaservices.wms.module.asn.pa.model.CsPaTaskDetail;
import com.chinaservices.wms.module.performance.domain.PutOnShelvesPageQuery;
import com.chinaservices.wms.module.performance.domain.PutOnShelvesSearchCondition;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @Description: 上架任务明细 Dao
 */
@Repository
public class CsPaTaskDetailDao extends ModuleBaseDaoSupport<CsPaTaskDetail, Long> {

    public PageResult<PaTaskDetailQuery> page(PaTaskDetailPageCondition condition){
        return sqlExecutor.page(PaTaskDetailQuery.class, PaTaskSqlId.PATASKDETAIL_QUERY_ASN_LIST, condition);
    }

    public PageResult<PutOnShelvesPageQuery> analysisOfShelfAssignments(PutOnShelvesSearchCondition condition) {
        return sqlExecutor.page(PutOnShelvesPageQuery.class, PaTaskSqlId.PUT_ON_SHELVES_PAGE, condition);
    }
}
