package com.chinaservices.wms.module.message.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.auth.module.message.domain.MessageGetReceiveUsersCondition;
import com.chinaservices.auth.module.message.domain.MessageSendItem;
import com.chinaservices.auth.module.message.feign.RemoteMessageReceiveUserService;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.util.JsonUtil;
import com.chinaservices.util.MapUtil;
import com.chinaservices.wms.common.enums.message.MessageSendTemplateEnum;
import com.chinaservices.wms.common.enums.passive.PassiveTagStatusEnum;
import com.chinaservices.wms.module.archive.archive.domain.ArchiverCondition;
import com.chinaservices.wms.module.archive.archiveCabinet.modal.ArchiveCabinet;
import com.chinaservices.wms.module.archive.archiveError.domain.ArchiveErrorTableItem;
import com.chinaservices.wms.module.archive.archiveborrow.model.ArchiveBorrowRecord;
import com.chinaservices.wms.module.archive.archives.model.Archive;
import com.chinaservices.wms.module.message.eventbus.MessageSendWarningPush;
import com.chinaservices.wms.module.passive.tag.model.PassiveTag;
import com.chinaservices.wms.module.stock.expirywarning.model.ExpiryWarning;
import com.chinaservices.wms.module.stock.warning.model.StockWarning;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warning.InvWarningQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SendMessageService {

    @Autowired
    private RemoteMessageReceiveUserService remoteMessageReceiveUserService;
    @Autowired
    private MessageSendWarningPush messageSendWarningPush;

    /**
     * 构建站内信发送模板
     * @param userIds 用户列表
     * @param messageCode 消息模板
     * @param businessName 业务名称
     * @param modeData 消息内容
     * @return MessageSendItem
     */
    private MessageSendItem buildMessageSendItem(List<Long> userIds, String businessName, Map<String, String> modeData, String messageCode, Long... warehouseId) {
        MessageSendItem messageSendItem = new MessageSendItem();
        // 不存在用户特殊处理时 发送配置用户信息
        if (CollectionUtil.isEmpty(userIds)) {
            MessageGetReceiveUsersCondition condition = new MessageGetReceiveUsersCondition();
            condition.setMessageCode(messageCode);
            if (warehouseId.length > 0){
                condition.setWarehouseId(warehouseId[0]);
            }
            ResponseData<List<Long>> responseData = remoteMessageReceiveUserService.getByWarehouseIdAndMsgCode(condition);
            userIds = responseData.getSuccess() ? responseData.getData() : userIds;
        }
        // 封装站内信发送实体
        messageSendItem.setUserIds(userIds);
        messageSendItem.setMessageCode(messageCode);
        messageSendItem.setBusinessName(businessName);
        messageSendItem.setModeData(modeData);
        return messageSendItem;
    }

    /**
     * 发送站内信
     * @param messageSendItemList 站内信发送实体类
     */
    private void sendMessageList(List<MessageSendItem> messageSendItemList) {
        messageSendWarningPush.sendMessageList(messageSendItemList);
    }

    /*  以下为消息模板——站内信发送方法 **/

    /**
     * 发送即将滞库/滞库预警站内信
     * @param stockWarningByAsnReceive 滞库预警列表
     * @param messageCode 消息模板
     * @param nearWarningDays 即将滞库天数
     */
    public void sendMessageByStockWarning(List<StockWarning> stockWarningByAsnReceive, String messageCode, Integer... nearWarningDays) {
        Map<String, StockWarning> collect = stockWarningByAsnReceive.stream().collect(Collectors.toMap(StockWarning::getLotNum, Function.identity(), (k1, k2) -> k1));
        if (CollectionUtil.isEmpty(collect)){
            return;
        }
        List<MessageSendItem> list = new ArrayList<>();
        collect.forEach((lotNum, stockWarning) -> {
            log.info("发送即将滞库/滞库预警站内信 StockWarningList:{}, messageCode:{}", stockWarningByAsnReceive, messageCode);
            Map<String, String> modeData = MapUtil.newHashMap();
            modeData.put("warehouseName", stockWarning.getWarehouseName());
            modeData.put("skuName", stockWarning.getSkuName());
            modeData.put("lotNum", lotNum);
            if(StrUtil.equals(MessageSendTemplateEnum.STOCK_DAYS_ONE.getCode() ,messageCode)){
                if(nearWarningDays.length > 0){
                    modeData.put("nearWarningDays", String.valueOf(nearWarningDays[0]));
                }
            }
            MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), stockWarning.getSkuName(), modeData, messageCode, stockWarning.getWarehouseId());
            list.add(messageSendItem);
        });
        log.info("发送即将滞库/滞库预警站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送文件分享/临期/过期站内信
     * @param userIdList 用户id列表
     * @param messageCode 消息模板
     * @param fileCodeList 文件编码列表
     */
    public void sendMessageByFileStatus(List<Long> userIdList, String messageCode, List<String> fileCodeList) {
        List<MessageSendItem> list = new ArrayList<>();
        log.info("文件分享/临期/过期站内信 fileCodeList:{}, messageCode:{}", fileCodeList, messageCode);
        Map<String, String> modeData = MapUtil.newHashMap();
        modeData.put("userName", SessionContext.getSessionUserInfo().getRealName());
        modeData.put("fileCodeList", JsonUtil.toJson(fileCodeList));
        MessageSendItem messageSendItem = this.buildMessageSendItem(userIdList, StrUtil.EMPTY, modeData, messageCode);
        list.add(messageSendItem);
        log.info("文件分享/临期/过期站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送新增档案站内信
     * @param archive 档案信息
     */
    public void sendMessageByAddArchive(Archive archive, ArchiveCabinet archiveCabinet) {
        List<MessageSendItem> list = new ArrayList<>();
        log.info("新增档案站内信 archive:{}", archive);
        Map<String,String> modeData = new HashMap<>();
        modeData.put("archiveCabinetName", archiveCabinet.getArchiveCabinetName());
        modeData.put("archiveName", archive.getArchiveName());
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), archive.getArchiveName(), modeData, MessageSendTemplateEnum.SAVE_ARCHIVE.getCode());
        list.add(messageSendItem);
        log.info("新增档案站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送档案借阅异常站内信
     * @param tableItem 档案异常更新实体类
     * @param expected 预期档案位置
     * @param actual 实际档案位置
     */
    public void sendMessageByArchiveBorrowError(ArchiveErrorTableItem tableItem, String expected, String actual) {
        List<MessageSendItem> list = new ArrayList<>();
        log.info("档案借阅异常站内信 ArchiveErrorTableItem:{}", tableItem);
        Map<String,String> modeData = new HashMap<>();
        modeData.put("errorCreatorName", tableItem.getErrorCreatorName());
        modeData.put("shouldBorrow", expected);
        modeData.put("realBorrow", actual);
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), tableItem.getReason(), modeData, MessageSendTemplateEnum.BORROW_ERROR_CODE.getCode());
        list.add(messageSendItem);
        log.info("档案借阅异常站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送档案归还异常站内信
     * @param tableItem 档案异常更新实体类
     * @param realCabinetName 目前档案柜名称
     * @param originCabinetName 原档案柜名称
     * @param currentPos 目前档案位置
     * @param originalPos 应归还档案位置
     */
    public void sendMessageByArchiveReturnError(ArchiveErrorTableItem tableItem, String archiveName, String realCabinetName, String originCabinetName, String currentPos, String originalPos) {
        List<MessageSendItem> list = new ArrayList<>();
        log.info("档案归还异常站内信 ArchiveErrorTableItem:{}", tableItem);
        Map<String,String> modeData = new HashMap<>();
        modeData.put("archiveName", archiveName);
        modeData.put("realCabinetName", realCabinetName);
        modeData.put("originCabinetName", originCabinetName);
        modeData.put("realPos", currentPos);
        modeData.put("originPos", originalPos);
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), archiveName, modeData, MessageSendTemplateEnum.RETURN_ERROR_CODE.getCode());
        list.add(messageSendItem);
        log.info("档案归还异常站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送档案归还提醒站内信
     * @param archive 档案实体
     * @param condition 档案更新实体
     * @param record 档案借阅记录
     */
    public void sendMessageByArchiveReminder(Archive archive, ArchiveCabinet archiveCabinet, ArchiverCondition condition, ArchiveBorrowRecord record) {
        List<MessageSendItem> list = new ArrayList<>();
        log.info("档案归还提醒站内信 Archive:{}, ArchiverCondition:{}", archive, condition);
        Map<String,String> modeData = new HashMap<>();
        modeData.put("archiveName", archive.getArchiveName());
        modeData.put("archiveCabinetName", archiveCabinet.getArchiveCabinetName());
        modeData.put("tier", String.valueOf(condition.getTier()));
        modeData.put("column", String.valueOf(condition.getColumn()));
        modeData.put("grid", String.valueOf(condition.getGrid()));
        MessageSendItem messageSendItem = this.buildMessageSendItem(Collections.singletonList(record.getBorrowCreator()), archive.getArchiveName(), modeData, MessageSendTemplateEnum.ARCHIVE_REMINDER_CODE.getCode());
        list.add(messageSendItem);
        log.info("档案归还提醒站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送库存预警站内信
     * @param invWarningQueryList 库存预警
     */
    public void sendMessageByInv(List<InvWarningQuery> invWarningQueryList) {
        List<MessageSendItem> list = new ArrayList<>();
        log.info("库存预警站内信 invWarningQueryList:{}", invWarningQueryList);
        invWarningQueryList.forEach(query -> {
            Map<String, String> modeData = MapUtil.newHashMap();
            modeData.put("warehouseName", query.getWarehouseName());
            modeData.put("ownerName", query.getOwnerName());
            modeData.put("skuName", query.getSkuName());
            modeData.put("qty", String.valueOf(query.getQty().setScale(0, RoundingMode.DOWN)));
            MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), query.getSkuName(), modeData, MessageSendTemplateEnum.INV_WARNING.getCode(), query.getWarehouseId());
            list.add(messageSendItem);
        });
        log.info("库存预警站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送效期预警站内信
     * @param expiryWarning 效期预警
     * @param skuName 商品名称
     */
    public void sendMessageByExpiryWarning(ExpiryWarning expiryWarning, Warehouse warehouse, String skuName){
        List<MessageSendItem> list = new ArrayList<>();
        log.info("效期预警站内信 expiryWarning:{}", expiryWarning);
        Map<String, String> modeData = MapUtil.newHashMap();
        modeData.put("warehouseName", warehouse.getWarehouseName());
        modeData.put("skuName", skuName);
        modeData.put("lotNum", expiryWarning.getLotNum());
        modeData.put("expiryDate", DateUtil.format(expiryWarning.getExpiryDate(), DatePattern.CHINESE_DATE_FORMAT));
        modeData.put("nearExpiryDays", StrUtil.toString(expiryWarning.getNearExpiryDays()));
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), skuName, modeData, MessageSendTemplateEnum.NEAR_EXPIRY_WARNING.getCode(), expiryWarning.getWarehouseId());
        list.add(messageSendItem);
        log.info("效期预警站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送标签已失效/已拆包/已损坏站内信
     * @param passiveTag 无源标签
     */
    public void sendMessageByTagStatusIllegal(PassiveTag passiveTag){
        List<MessageSendItem> list = new ArrayList<>();
        log.info("标签已失效/已拆包/已损坏站内信 passiveTag:{}", passiveTag);
        Map<String, String> modeData = MapUtil.newHashMap();
        modeData.put("tagNo", passiveTag.getTagNo());
        MessageSendItem messageSendItem;
        if (ObjUtil.equals(PassiveTagStatusEnum.DAMAGED.getCode(), passiveTag.getStatus()) && ObjUtil.isNull(passiveTag.getWarehouseId())) {
            messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), passiveTag.getTagNo(), modeData, MessageSendTemplateEnum.TAG_STATUS_ILLEGAL.getCode());
        } else {
            messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), passiveTag.getTagNo(), modeData, MessageSendTemplateEnum.TAG_STATUS_ILLEGAL.getCode(), passiveTag.getWarehouseId());
        }
        list.add(messageSendItem);
        log.info("标签已失效/已拆包/已损坏站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送无法判断标签作业类型站内信
     * @param passiveTag 无源标签
     */
    public void sendMessageByTagWorkTypeJudgeFail(PassiveTag passiveTag){
        List<MessageSendItem> list = new ArrayList<>();
        log.info("无法判断标签作业类型站内信 passiveTag:{}", passiveTag);
        Map<String, String> modeData = MapUtil.newHashMap();
        modeData.put("tagNo", passiveTag.getTagNo());
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), passiveTag.getTagNo(), modeData, MessageSendTemplateEnum.TAG_WORK_TYPE_JUDGE_FAIL.getCode(), passiveTag.getWarehouseId());
        list.add(messageSendItem);
        log.info("无法判断标签作业类型站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送标签绑定入库订单未审核站内信
     * @param passiveTag 无源标签
     */
    public void sendMessageByTagBindAsnUninspected(PassiveTag passiveTag){
        List<MessageSendItem> list = new ArrayList<>();
        log.info("标签绑定入库订单未审核站内信 passiveTag:{}", passiveTag);
        Map<String, String> modeData = MapUtil.newHashMap();
        modeData.put("tagNo", passiveTag.getTagNo());
        modeData.put("asnNo", passiveTag.getAsnNo());
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), passiveTag.getTagNo(), modeData, MessageSendTemplateEnum.TAG_BIND_ASN_UNINSPECTED.getCode(), passiveTag.getWarehouseId());
        list.add(messageSendItem);
        log.info("标签绑定入库订单未审核站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送标签与入库订单商品不一致站内信
     * @param passiveTag 无源标签
     */
    public void sendMessageByTagAsnSkuMismatched(PassiveTag passiveTag){
        List<MessageSendItem> list = new ArrayList<>();
        log.info("标签与入库订单商品不一致站内信 passiveTag:{}", passiveTag);
        Map<String, String> modeData = MapUtil.newHashMap();
        modeData.put("tagNo", passiveTag.getTagNo());
        modeData.put("asnNo", passiveTag.getAsnNo());
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), passiveTag.getTagNo(), modeData, MessageSendTemplateEnum.TAG_ASN_SKU_MISMATCHED.getCode(), passiveTag.getWarehouseId());
        list.add(messageSendItem);
        log.info("标签与入库订单商品不一致站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送标批次属性未填写无法自动收货站内信
     * @param passiveTag 无源标签
     */
    public void sendMessageByTagLotAttUnfilled(PassiveTag passiveTag){
        List<MessageSendItem> list = new ArrayList<>();
        log.info("标批次属性未填写无法自动收货站内信 passiveTag:{}", passiveTag);
        Map<String, String> modeData = MapUtil.newHashMap();
        modeData.put("tagNo", passiveTag.getTagNo());
        modeData.put("asnNo", passiveTag.getAsnNo());
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), passiveTag.getTagNo(), modeData, MessageSendTemplateEnum.TAG_LOT_ATT_UNFILLED.getCode(), passiveTag.getWarehouseId());
        list.add(messageSendItem);
        log.info("标批次属性未填写无法自动收货站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送出库识别标签反查入库信息查不到站内信
     * @param passiveTag 无源标签
     */
    public void sendMessageByTagDispatchNoError(PassiveTag passiveTag){
        List<MessageSendItem> list = new ArrayList<>();
        log.info("出库识别标签反查入库信息查不到站内信 passiveTag:{}", passiveTag);
        Map<String, String> modeData = MapUtil.newHashMap();
        modeData.put("tagNo", passiveTag.getTagNo());
        modeData.put("dispatchNo", passiveTag.getDispatchNo());
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), passiveTag.getTagNo(), modeData, MessageSendTemplateEnum.TAG_DISPATCH_NO_ERROR.getCode(), passiveTag.getWarehouseId());
        list.add(messageSendItem);
        log.info("出库识别标签反查入库信息查不到站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送识别商品与发运单商品不一致站内信
     * @param passiveTag 无源标签
     */
    public void sendMessageByTagDispatchSkuMismatched(PassiveTag passiveTag){
        List<MessageSendItem> list = new ArrayList<>();
        log.info("识别商品与发运单商品不一致站内信 passiveTag:{}", passiveTag);
        Map<String, String> modeData = MapUtil.newHashMap();
        modeData.put("tagNo", passiveTag.getTagNo());
        modeData.put("skuName", passiveTag.getSkuName());
        modeData.put("dispatchNo", passiveTag.getDispatchNo());
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), passiveTag.getTagNo(), modeData, MessageSendTemplateEnum.TAG_DISPATCH_SKU_MISMATCHED.getCode(), passiveTag.getWarehouseId());
        list.add(messageSendItem);
        log.info("识别商品与发运单商品不一致站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送识别数量与发运单数量不一致站内信
     * @param passiveTagList 无源标签列表
     */
    public void sendMessageByTagDispatchEaMismatched(List<PassiveTag> passiveTagList){
        List<MessageSendItem> list = new ArrayList<>();
        log.info("识别数量与发运单数量不一致站内信 passiveTagList:{}", passiveTagList);
        Map<String, String> modeData = MapUtil.newHashMap();
        String tagNoList = passiveTagList.stream().map(PassiveTag::getTagNo).filter(StrUtil::isNotBlank).collect(Collectors.joining(StrPool.COMMA));
        modeData.put("tagNoList", tagNoList);
        modeData.put("dispatchNo", passiveTagList.getFirst().getDispatchNo());
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), passiveTagList.getFirst().getDispatchNo(), modeData, MessageSendTemplateEnum.TAG_DISPATCH_EA_MISMATCHED.getCode(), passiveTagList.getFirst().getWarehouseId());
        list.add(messageSendItem);
        log.info("识别数量与发运单数量不一致站内信 list:{}", list);
        this.sendMessageList(list);
    }

    /**
     * 发送标签已收货站内信
     * @param passiveTag 无源标签
     */
    public void sendMessageByTagAlreadyReceived(PassiveTag passiveTag){
        List<MessageSendItem> list = new ArrayList<>();
        log.info("标签已收货站内信 passiveTag:{}", passiveTag);
        Map<String, String> modeData = MapUtil.newHashMap();
        modeData.put("tagNo", passiveTag.getTagNo());
        MessageSendItem messageSendItem = this.buildMessageSendItem(CollectionUtil.newArrayList(), passiveTag.getTagNo(), modeData, MessageSendTemplateEnum.TAG_ALREADY_RECEIVED.getCode(), passiveTag.getWarehouseId());
        list.add(messageSendItem);
        log.info("标签已收货站内信 list:{}", list);
        this.sendMessageList(list);
    }

}
