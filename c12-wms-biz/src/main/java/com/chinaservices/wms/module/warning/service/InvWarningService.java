package com.chinaservices.wms.module.warning.service;

import cn.hutool.core.util.ObjUtil;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.session.SessionContext;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.common.exception.WarningExceptionEnum;
import com.chinaservices.wms.module.excel.item.stock.InvLotLocLocExcelItem;
import com.chinaservices.wms.module.message.service.SendMessageService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.multi.domain.InvLotLocItem;
import com.chinaservices.wms.module.stock.multi.domain.InvLotLocPageCondition;
import com.chinaservices.wms.module.stock.mutli.dao.InvLotLocDao;
import com.chinaservices.wms.module.warning.InvWarningItem;
import com.chinaservices.wms.module.warning.InvWarningPageCondition;
import com.chinaservices.wms.module.warning.InvWarningPageQuery;
import com.chinaservices.wms.module.warning.InvWarningQuery;
import com.chinaservices.wms.module.warning.dao.InvWarningDao;
import com.chinaservices.wms.module.warning.model.InvWarning;
import com.chinaservices.wms.module.warning.model.InvWarningMessage;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

import static com.chinaservices.wms.common.constant.TransactionType.*;

/**
 * 库存预警 qmy
 *
 * <AUTHOR>
 */
@Service
public class InvWarningService extends ModuleBaseServiceSupport<InvWarningDao, InvWarning, Long> {

    @Autowired
    private InvLotLocDao invLotLocDao;
    @Autowired
    private NumberGenerator numberGenerator;
    @Autowired
    private SendMessageService sendMessageService;
    // 改为静态常量，避免每次调用重复创建
    private static final List<String> WARNING_STATUSES = Arrays.asList(
            TRAN_CR_PA, TRAN_CR_OUT_PA,
            //库内质检 新增/编辑, 出库质检-新增/编辑,
            TRAN_QC_CRT_NF, TRAN_QC_EDT_ADD_NF, TRAN_QC_OUT_CRT, TRAN_QC_OUT_EDT_SAVE,
            //分配,    冻结,      调整,     取消调整,  待移动,    越库
            TRAN_INA, TRAN_FREE, TRAN_AD, TRAN_CAD, TRAN_W_MV, TRAN_ROSS
    );


    /**
     * 库存预警消息分页查询
     *
     * @param condition
     * @return
     */
    public PageResult<InvWarningPageQuery> messagePage(InvWarningPageCondition condition) {
        return dao.messagePage(condition);
    }

    /**
     * 库存预警消息分页查询
     *
     * @param condition
     * @return
     */
    public PageResult<InvWarningPageQuery> page(InvWarningPageCondition condition) {
        return dao.page(condition);
    }

    public void deleteByIdList(Long[] idList) {
        dao.delete(idList);
    }

    public void save(InvWarningItem item) {
        InvWarning invWarning = new InvWarning();
        if (!Objects.isNull(item.getId())) {
            invWarning = dao.findById(item.getId());
            if (Objects.isNull(invWarning)) {
                throw new ServiceException(WarningExceptionEnum.WARNING_INV_NOT_EXIST);
            }
        } else {
            InvWarning existWarning = this.getInvWarningFist(item.getSkuId(), item.getWarehouseId(), item.getOwnerId());
            // 根据条件查询是否存在商品,仓库,或组合的预警信息
            if (ObjUtil.isNotNull(existWarning)) {
                throw new ServiceException(WarningExceptionEnum.WARNING_INV_EXIST);
            }
            BeanUtil.copyProperties(item, invWarning);
        }
        invWarning.setMinInv(item.getMinInv());
        invWarning.setMinInvEa(item.getMinInvEa());
        invWarning.setPackageUnitId(item.getPackageUnitId());
        invWarning.setPackageUnitName(item.getPackageUnitName());
        invWarning.setMinQuantity(item.getMinQuantity());
        invWarning.setOp(SessionContext.getSessionUserInfo().getRealName());
        // 新增预警设置
        dao.saveOrUpdate(invWarning);
    }

    public void messageSave(InvLotLocQtyBO invLotLocQty) {
        logger.info("库存预警开始:{}", invLotLocQty);
        // 过滤非预警事务类型
        if (!WARNING_STATUSES.contains(invLotLocQty.getTransactionType())) {
            return;
        }
        // 执行调整 -- 调减 || 取消调整 -- 调增
        if (Lists.newArrayList(TRAN_AD, TRAN_CAD).contains(invLotLocQty.getTransactionType())) {
            invLotLocQty.getList().stream()
                    .filter(e -> ObjUtil.isNotNull(e.getUpdateNum()))
                    .filter(e -> (e.getTransactionType().equals(TRAN_AD) && e.getUpdateNum().compareTo(BigDecimal.ZERO) < 0) ||
                            (e.getTransactionType().equals(TRAN_CAD) && e.getUpdateNum().compareTo(BigDecimal.ZERO) > 0))
                    .forEach(e -> this.sendMessage(e.getSkuId(),e.getWarehouseId(),e.getOwnerId(),e.getUpdateNum()));
            return;
        } else if (Lists.newArrayList(TRAN_FREE).contains(invLotLocQty.getTransactionType())) {
            invLotLocQty.getInvLotLocList().forEach(e -> this.sendMessage(e.getSkuId(),e.getWarehouseId(),e.getOwnerId(),e.getQtyHold()));
            return;
        }
        sendMessage(invLotLocQty.getSkuId(),invLotLocQty.getWarehouseId(),invLotLocQty.getOwnerId(),invLotLocQty.getUpdateNum());
    }

    private void sendMessage(Long skuId,Long warehouseId,Long ownerId,BigDecimal updateNum) {
        InvWarning invWarning = this.getInvWarningFist(skuId, warehouseId, ownerId);
        logger.info("查询库存预警设置:" + invWarning);
        if (Objects.isNull(invWarning)) {
            return;
        }

        InvWarningQuery query = new InvWarningQuery();
        BeanUtil.copyProperties(invWarning, query);
        query.setUpdateNum(updateNum.abs());
        getInvQty(query);
        if (Objects.isNull(query.getQty())) {
            return;
        }
        saveInvWarningMessage(query);
        sendMessageService.sendMessageByInv(Collections.singletonList(query));
    }

    public void getInvQty(InvWarningQuery query) {
        logger.info("库存预警 -- 修改数updateNum:" + query.getUpdateNum());
        InvLotLocPageCondition invLotLocPageCondition = new InvLotLocPageCondition();
        invLotLocPageCondition.setSkuId(String.valueOf(query.getSkuId()));
        invLotLocPageCondition.setWarehouseId(String.valueOf(query.getWarehouseId()));
        invLotLocPageCondition.setOwnerId(String.valueOf(query.getOwnerId()));
        invLotLocPageCondition.setHideCode(Collections.singletonList(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode()));
        InvLotLocItem bySku = invLotLocDao.getBySku(invLotLocPageCondition);
        BigDecimal qty = BigDecimal.ZERO;
        if (Objects.nonNull(bySku) && Objects.nonNull(bySku.getQty())){
            qty = bySku.getQty();
        }
        // 原总库存数 = |调整数| + 现有可用库存数
        BigDecimal multiply = query.getUpdateNum().add(qty);
        // 如果最低预警数 小于等于 现有可用库存数 说明不需要预警
        if (query.getMinInvEa().compareTo(qty) <= 0) {
            logger.info("最低预警数query.getMinInvEa(){}小于现有可用库存数qty{},无需预警", query.getMinInvEa(), qty);
            return;
        }
        // 如果 原总库存数 小于 最低预警数  说明已经发送过预警
        if (multiply.compareTo(query.getMinInvEa()) < 0) {
            logger.info("原总库存数multiply{}小于最低预警数query.getMinInvEa(){},已发送过预警", multiply, query.getMinInvEa());
            return;
        }
        logger.info("库存预警现可用库存数:" + qty);
        query.setQty(qty);
    }

    public void saveInvWarningMessage(InvWarningQuery query) {
        logger.info("保存库存预警WMS消息");
        InvWarningMessage invWarningMessage = new InvWarningMessage();
        invWarningMessage.setInvWarningId(query.getId());
        invWarningMessage.setPackageId(query.getPackageId());
        invWarningMessage.setPackageUnitId(query.getPackageUnitId());
        invWarningMessage.setPackageUnitName(query.getPackageUnitName());
        invWarningMessage.setQty(query.getQty());
        invWarningMessage.setWarningCode(numberGenerator.nextValue(IdRuleConstant.INV_WARNING));
        invWarningMessage.setMinInv(query.getMinInv());
        invWarningMessage.setMinInvEa(query.getMinInvEa());
        invWarningMessage.setWarningTime(new Date());
        dao.saveOrUpdate(invWarningMessage);
    }

    /**
     * 根据条件查询数据
     */
    public InvWarning getInvWarningFist(Long skuId, Long warehouseId, Long ownerId) {
        ConditionRule conditionRule = new ConditionRule()
                .andEqual(InvWarning::getSkuId, skuId)
                .andEqual(InvWarning::getWarehouseId, warehouseId)
                .andEqual(InvWarning::getOwnerId, ownerId);
        return dao.findFirst(conditionRule);
    }


}
