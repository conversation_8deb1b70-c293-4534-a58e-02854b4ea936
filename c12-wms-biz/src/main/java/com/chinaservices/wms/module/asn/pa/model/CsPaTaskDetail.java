package com.chinaservices.wms.module.asn.pa.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 上架任务 实体类
 */
@Entity
@Table(name = "cs_pa_task_detail")
@Data
public class CsPaTaskDetail extends ModuleBaseModel {

    /**
     * 上架任务id
     */
    private Long paId;

    /**
     * 上架任务号
     */
    private String paNo;

    /**
     * 上架任务明细号
     */
    private String paDetailNo;

    /**
     * 仓库代码
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 订单类型:ASN入库单,SO出库单
     */
    private String paSource;


    /**
     * 出入库订单id
     */
    private Long orderId;

    /**
     * 业务单据号
     */
    private String orderNo;

    /**
     * 收货明细号
     */
    private String receiveNo;

    /**
     * 行状态
     */
    private String status;

    /**
     * 货主代码
     */
    private Long ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 商品代码
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 批次號
     */
    private String lotNum;

    /**
     * 包装编码
     */
    private Long packageId;

    /**
     * 包装名称
     */
    private String packageName;

    /**
     * 包装单位
     */
    private Long packageUnitId;

    /**
     * 包装单位名称
     */
    private String packageUnitName;

    /**
     * 源库位主键
     */
    private Long fmLocId;

    /**
     * 源库位名称
     */
    private String fmLocName;

    /**
     * 源容器号
     */
    private Long fmPalletId;

    /**
     * 源容器号
     */
    private String fmPalletNo;

    /**
     * 目标库位主键
     */
    private Long toLocId;

    /**
     * 目标库位名称
     */
    private String toLocName;

    /**
     * 目标容器号
     */
    private Long toPalletId;

    /**
     * 目标容器号
     */
    private String toPalletNo;

    /**
     * 待上架数EA
     */
    private BigDecimal qtyPlanEa;

    /**
     * 上架数EA
     */
    private BigDecimal qtyPaEa;

    /**
     * 上架时间
     */
    private Date paTime;

    /**
     * 上架人
     */
    private Long paPersonId;

    /**
     * 上架人
     */
    private String paPersonName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本
     */
    private Long recVer;

    /**
     * 标签号
     */
    private String tagNo;
}
