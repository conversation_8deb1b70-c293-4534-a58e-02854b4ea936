package com.chinaservices.wms.module.warehouse.warehouse.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.chinaservices.annotation.DataPermission;
import com.chinaservices.module.base.ModuleBaseController;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.excel.handler.basic.ImportDataHandler;
import com.chinaservices.wms.module.warehouse.warehouse.domain.*;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 仓库管理
 *
 * <AUTHOR>
 * @description 仓库Controller
 */
@RestController
@RequestMapping("/api/warehouse/warehouse")
public class WarehouseController extends ModuleBaseController {

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private ImportDataHandler importDataHandler;

    /**
     * 分页查询仓库
     *
     * @param condition
     * @return PageResult<WarehouseQuery>
     */
    @DataPermission(warehouseField = "id",user = false)
    @PostMapping("/page")
    @SaCheckPermission("warehouse:warehouseList:headBtn:page")
    public ResponseData<PageResult<WarehouseQuery>> page(@RequestBody WarehousePageCondition condition) {
        return ResponseData.success(warehouseService.page(condition));
    }

    /**
     * 无仓库权限查询
     * @param condition
     * @return
     */
    @PostMapping("/pageNew")
    public ResponseData<PageResult<WarehouseQuery>> pageNew(@RequestBody WarehousePageCondition condition) {
        return ResponseData.success(warehouseService.page(condition));
    }

    /**
     * 根据ID查询仓库
     *
     * @param id
     * @return Warehouse
     */
    @PostMapping("/getById/{id}")
    @SaCheckPermission(value = {"warehouse:warehouseList:table:view", "warehouse:warehouseList:table:edit"}, mode = SaMode.OR)
    public ResponseData<Warehouse> getById(@PathVariable(value = "id") Long id) {
        return ResponseData.success(warehouseService.getById(id));
    }

    /**
     * 新增或编辑仓库
     *
     * @param item
     * @return Boolean
     */
    @PostMapping("/save")
    @SaCheckPermission(value = {"warehouse:warehouseList:headBtn:add", "warehouse:warehouseList:table:edit"}, mode = SaMode.OR)
    public ResponseData<Boolean> save(@Valid @RequestBody WarehouseItem item) {
        return ResponseData.success(warehouseService.save(item));
    }

    /**
     * 根据IDS批量删除仓库
     *
     * @param ids
     * @return Boolean
     */
    @PostMapping("/deleteByIds")
    @SaCheckPermission(value = {"warehouse:warehouseList:headBtn:delete", "warehouse:warehouseList:table:delete"}, mode = SaMode.OR)
    public ResponseData<Boolean> deleteByIds(@RequestBody Object[] ids) {
        return ResponseData.success(warehouseService.deleteByIds(ids));
    }

    /**
     * wms对接仓库接收(wms对接)
     *
     * @param item
     * @return Boolean
     */
    @PostMapping("/reception")
    public ResponseData<Boolean> reception( @RequestBody WarehouseItem item) {
        return ResponseData.success(warehouseService.crzSave(item));
    }
    /**
     * 根据IDS批量删除仓库(wms对接)
     *
     * @param codes
     * @return Boolean
     */
    @PostMapping("/crzDeleteByCodes")
    public ResponseData<Boolean> crzDeleteByCodes(@RequestBody Object[] codes) {
        return ResponseData.success(warehouseService.crzDeleteByCodes(codes));
    }
    /**
     * 根据IDS批量启用仓库(wms对接)
     *
     * @param ids
     * @return Boolean
     */
    @PostMapping("/crzEnableByIds")
    public ResponseData<Boolean> crzEnableByIds(@RequestBody Object[] ids) {
        return ResponseData.success(warehouseService.crzEnableByIds(ids));
    }

    /**
     * 根据IDS批量停用仓库(wms对接)
     *
     * @param codes
     * @return Boolean
     */
    @PostMapping("/crzDisableByCodes")
    public ResponseData<Boolean> crzDisableByIds(@RequestBody Object[] codes) {
        return ResponseData.success(warehouseService.crzDisableByIds(codes));
    }
    /**
     * 根据IDS批量启用仓库
     *
     * @param ids
     * @return Boolean
     */
    @PostMapping("/enableByIds")
    @SaCheckPermission("warehouse:warehouseList:headBtn:enable")
    public ResponseData<Boolean> enableByIds(@RequestBody Object[] ids) {
        return ResponseData.success(warehouseService.enableByIds(ids));
    }

    /**
     * 根据IDS批量停用仓库
     *
     * @param ids
     * @return Boolean
     */
    @PostMapping("/disableByIds")
    @SaCheckPermission("warehouse:warehouseList:headBtn:disable")
    public ResponseData<Boolean> disableByIds(@RequestBody Object[] ids) {
        return ResponseData.success(warehouseService.disableByIds(ids));
    }

    /**
     * 全部仓库查询
     *
     * @param condition 条件
     * @return List<Warehouse>
     */
    @SaCheckPermission("report:stockTurnoverWarehouseList:headBtn:page")
    @PostMapping("/all")
    public ResponseData<List<WarehouseNameQuery>> all(@RequestBody WarehouseCondition condition) {
        return ResponseData.success(warehouseService.allByName(condition.getWarehouseName()));
    }

    /**
     * feign接口 获取所有仓库信息
     * @return ResponseData<List<WarehouseQuery>>
     */
    @PostMapping("/list")
    public ResponseData<List<WarehouseQuery>> list() {
        return ResponseData.success(warehouseService.list());
    }
}
