package com.chinaservices.wms.module.process.order.model;

import java.util.Date;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 加工拆分单表
 * cs_process_order
 */
@Data
@Entity
@Table(name = "cs_process_order")
public class ProcessOrder extends ModuleBaseModel {


    /**
     * 单号
     */
    private String processOrderNo;

    /**
     * 所属仓库ID
     */
    private Long warehouseId;

    /**
     * 所属仓库名称
     */
    private String warehouseName;

    /**
     * 所属货主ID
     */
    private Long ownerId;

    /**
     * 所属货主名称
     */
    private String ownerName;

    /**
     * 工作台ID(库位id)
     */
    private Long locId;

    /**
     * 工作台名称(库位名称)
     */
    private String locName;

    /**
     * 加工人员id
     */
    private Long processStaffId;

    /**
     * 加工人员名称
     */
    private String processStaffName;

    /**
     * 加工单类型：1-加工单；2-拆分单
     */
    private String orderType;

    /**
     * 加工类型-加工单：1-多商品组合;加工类型-拆分单：1-商品拆分加工
     */
    private String processType;

    /**
     * 加工单状态:1-创建;2-已审核;3-部分加工;4-完全加工;5-已完结;6-已取消
     */
    private String processOrderStatus;

    /**
     * 加工单分配状态:1-未分配;2-部分分配;3-完全分配
     */
    private String allocationStatus;

    /**
     * 加工单拣货状态:1-未拣货;2-部分拣货;3-完全拣货
     */
    private String pickingStatus;

    /**
     * 预计加工完成时间
     */
    private Date expectedProcessingTime;

    /**
     * 加工完成时间
     */
    private Date actualProcessingTime;

}