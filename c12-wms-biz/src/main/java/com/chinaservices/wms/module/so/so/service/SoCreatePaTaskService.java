package com.chinaservices.wms.module.so.so.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseModel;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.util.DateUtil;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.enums.AllocationTypeEnum;
import com.chinaservices.wms.common.enums.asn.PaSourceEnum;
import com.chinaservices.wms.common.enums.asn.PaStatusEnum;
import com.chinaservices.wms.common.enums.so.*;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskSoCreateItem;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskSoCreateRestItem;
import com.chinaservices.wms.module.asn.pa.service.CsPaTaskAssociationService;
import com.chinaservices.wms.module.asn.pa.service.CsPaTaskService;
import com.chinaservices.wms.module.basic.cspackage.model.Package;
import com.chinaservices.wms.module.basic.cspackage.service.PackageService;
import com.chinaservices.wms.module.basic.owner.dao.OwnerDao;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.pallet.dao.PalletDao;
import com.chinaservices.wms.module.basic.pallet.model.Pallet;
import com.chinaservices.wms.module.basic.sku.dao.SkuDao;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.so.check.dao.SoRecheckDetailDao;
import com.chinaservices.wms.module.so.check.dao.SoRecheckHeaderDao;
import com.chinaservices.wms.module.so.check.model.SoRecheckDetail;
import com.chinaservices.wms.module.so.check.model.SoRecheckHeader;
import com.chinaservices.wms.module.so.pack.dao.PackHeaderDao;
import com.chinaservices.wms.module.so.pack.model.PackHeader;
import com.chinaservices.wms.module.so.pack.service.PackHeaderService;
import com.chinaservices.wms.module.so.picking.dao.SoPickingDao;
import com.chinaservices.wms.module.so.picking.lock.SoPickingLock;
import com.chinaservices.wms.module.so.picking.model.SoPicking;
import com.chinaservices.wms.module.so.so.dao.SoAllocationPalletDao;
import com.chinaservices.wms.module.so.so.dao.SoDetailDao;
import com.chinaservices.wms.module.so.so.dao.SoHeaderDao;
import com.chinaservices.wms.module.so.so.domain.AllocUpdateTaskStatusItem;
import com.chinaservices.wms.module.so.so.domain.CancalSaveItem;
import com.chinaservices.wms.module.so.so.domain.OrderForCreatePaTaskItem;
import com.chinaservices.wms.module.so.so.model.SoAllocation;
import com.chinaservices.wms.module.so.so.model.SoAllocationPallet;
import com.chinaservices.wms.module.so.so.model.SoDetail;
import com.chinaservices.wms.module.so.so.model.SoHeader;
import com.chinaservices.wms.module.warehouse.loc.dao.WarehouseLocDao;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.warehouse.dao.WarehouseDao;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.google.common.collect.Lists;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.sf.jsqlparser.parser.feature.Feature.first;

@Component
@RequiredArgsConstructor
@Slf4j
public class SoCreatePaTaskService {

    private final SoAllocationPickingService soAllocationPickingService;
    private final SoRecheckHeaderDao soRecheckHeaderDao;
    private final SoRecheckDetailDao soRecheckDetailDao;
    private final CsPaTaskService csPaTaskService;
    private final WarehouseDao warehouseDao;
    private final WarehouseLocDao warehouseLocDao;
    private final PalletDao palletDao;
    private final PackageService packageService;
    private final SkuDao skuDao;
    private final OwnerDao ownerDao;
    private final PackHeaderService packHeaderService;
    private final NumberGenerator numberGenerator;
    private final CsPaTaskAssociationService csPaTaskAssociationService;
    private final SoPickingDao soPickingDao;
    private final SoAllocationPalletDao soAllocationPalletDao;

    /**
     * 执行订单部分发运业务逻辑主方法-不需要生成上架任务
     * @param soHeader
     * @param soDetails
     * @return
     */
    @Transactional
    public OrderForCreatePaTaskItem executePartialShipmentServiceNotCreate(SoHeader soHeader, List<SoDetail> soDetails) {
        //1. 订单状态更新为订单完结；
        //2. 订单不允许再进行操作（分配库位）；
        //3. 校验订单所有未发货商品是否存在拣货任务，
        List<SoDetail> notOrderForDetailList = soDetails.stream().filter(f -> f.getQtyShippedEa().compareTo(f.getQtyAllocationEa()) < 0).toList();
        Map<String, SoDetail> soDetailMap = notOrderForDetailList.stream().collect(Collectors.toMap(SoDetail::getSoDetailNo, Function.identity(), (a, b) -> a));
        List<SoAllocation> allocationList = soAllocationPickingService.find(new ConditionRule().andIn(SoAllocation::getSoDetailNo, soDetailMap.keySet())
                .andIn(SoAllocation::getPickingStatus, SoPickingStatusEnum.UNPICKED.getCode(), SoPickingStatusEnum.NOT_GENERATE.getCode()));
        if (EmptyUtil.isEmpty(allocationList)) {
            List<SoPicking> pickings = soPickingDao.find(new ConditionRule().andEqual(SoPicking::getSoNo, soHeader.getSoNo()));
            if (EmptyUtil.isNotEmpty(pickings)) {
                //3.1. 否，流程结束；
                pickings.forEach(p -> p.setStatus(SoPickingStatusEnum.UNPICKED.getCode()));
                soPickingDao.batchUpdate(pickings);
            }
            return new OrderForCreatePaTaskItem().setType(0);
        }else{
            //3.2. 是，取消拣货任务，并将任务状态更新为已完结，继续进行拣货单校验；
            //将未拣货任务 和未生成数据区分开
            //未生成
            List<SoAllocation> notGenerateStatusList = allocationList.stream().filter(f -> SoPickingStatusEnum.NOT_GENERATE.getCode().equals(f.getPickingStatus())).toList();
            if (EmptyUtil.isNotEmpty(notGenerateStatusList)) {
                //执行取消分配
                //区分波次和出库
                List<SoAllocation> wvList = notGenerateStatusList.stream().filter(f -> StrUtil.isNotEmpty(f.getPkWvNo())).toList();
                if (EmptyUtil.isNotEmpty(wvList)) {
                    CancalSaveItem cancalSaveItem = new CancalSaveItem();
                    cancalSaveItem.setCancelPicking(true);
                    cancalSaveItem.setIdList(wvList.stream().map(ModuleBaseModel::getId).toList());
                    cancalSaveItem.setAllocationType(AllocationTypeEnum.WAVE_TIMES.getCode());
                    soAllocationPickingService.cancelSave(cancalSaveItem);
                }
                List<SoAllocation> soList = notGenerateStatusList.stream().filter(f -> StrUtil.isEmpty(f.getPkWvNo())).toList();
                if (EmptyUtil.isNotEmpty(soList)) {
                    CancalSaveItem cancalSaveItem = new CancalSaveItem();
                    cancalSaveItem.setCancelPicking(true);
                    cancalSaveItem.setIdList(soList.stream().map(ModuleBaseModel::getId).toList());
                    cancalSaveItem.setAllocationType(AllocationTypeEnum.OUTBOUND.getCode());
                    soAllocationPickingService.cancelSave(cancalSaveItem);
                }
            }
            //未拣货
            List<SoAllocation> unpickedStatusList = allocationList.stream().filter(f -> SoPickingStatusEnum.UNPICKED.getCode().equals(f.getPickingStatus())).toList();
            if (EmptyUtil.isNotEmpty(unpickedStatusList)) {
                soAllocationPickingService.cancelPickingTask(unpickedStatusList.stream().map(ModuleBaseModel::getId).toArray(Long[]::new));
            }
            //更新任务状态 已完结
            allocationList.forEach(allocation -> {
                allocation.setTaskStatus(TaskStatusEnum.COMPLETED.getCode());
            });
            soAllocationPickingService.batchUpdate(allocationList);
            //验证拣货单是否可以取消拣货单和二次分拣数据
            List<String> pickingNoList = allocationList.stream().map(SoAllocation::getPickingNo).distinct().toList();
            List<SoAllocation> soAllocationList = soAllocationPickingService.find(new ConditionRule().andIn(SoAllocation::getPickingNo, pickingNoList));
            Map<String, List<SoAllocation>> listMap = soAllocationList.stream().collect(Collectors.groupingBy(SoAllocation::getPickingNo));
            List<String> updatePickingNoList = listMap.entrySet().stream().filter(entry -> {
                boolean unpicked = entry.getValue().stream().allMatch(f -> SoPickingStatusEnum.UNPICKED.getCode().equals(f.getPickingStatus()));
                boolean notGenerate = entry.getValue().stream().allMatch(f -> SoPickingStatusEnum.NOT_GENERATE.getCode().equals(f.getPickingStatus()));
                return unpicked || notGenerate;
            }).map(Map.Entry::getKey).toList();
            if (EmptyUtil.isNotEmpty(updatePickingNoList)) {
                List<SoPicking> soPickingList = soPickingDao.find(new ConditionRule().andIn(SoPicking::getPickingNo, updatePickingNoList));
                soPickingList.forEach(soPicking -> soPicking.setStatus(SoPickingStatusEnum.CANCELLED.getCode()));
                soPickingDao.batchUpdate(soPickingList);
            }
            return new OrderForCreatePaTaskItem().setType(0);
        }
    }

    /**
     * 执行订单部分发运业务逻辑主方法-需要生成上架任务
     * @param soHeader
     * @param soDetails
     * @return
     */
    public OrderForCreatePaTaskItem executePartialShipmentServiceCreate(SoHeader soHeader, List<SoDetail> soDetails) {
        //1. 订单状态更新为订单完结；
        //2. 订单不允许再进行操作（分配库位）；
        //3. 根据已拣货未发运商品信息生成出库上架任务；给出提示“订单已生成上架任务，是否进行查看”，点击确认跳转到入库上架页面，点击取消，关闭当前页面；
        List<String> soDetailNoList = soDetails.stream().map(SoDetail::getSoDetailNo).toList();
        List<SoAllocation> allocations = soAllocationPickingService.find(new ConditionRule().andIn(SoAllocation::getSoDetailNo, soDetailNoList));
        if (EmptyUtil.isEmpty(allocations)) {
            //没有任何拣货单
            return new OrderForCreatePaTaskItem().setType(0);
        }


        // 1. 获取已拣货或部分拣货的分配数据
        List<SoAllocation> pickedAllocations = allocations.stream()
                .filter(a -> a.getPickingStatus().equals(SoPickingStatusEnum.COMPLETE_PICKING.getCode()) ||
                        a.getPickingStatus().equals(SoPickingStatusEnum.PARTIAL_PICKING.getCode()))
                .toList();

        // 2. 按拣货任务号分组(一个拣货任务对应一个上架任务)
        Map<String, List<SoAllocation>> allocationsByTask = pickedAllocations.stream()
                .collect(Collectors.groupingBy(SoAllocation::getPickingTaskNo));

        // 3. 准备存储最终结果的集合
        List<PaTaskSoCreateRestItem> paTaskItems = new ArrayList<>();

        // 4. 获取所有相关的复核明细(一次性查询提高效率)
        List<String> allPickingTaskNos = new ArrayList<>(allocationsByTask.keySet());
        List<SoRecheckDetail> allRecheckDetails = soRecheckDetailDao.find(
                new ConditionRule().andIn(SoRecheckDetail::getPickingTaskNo, allPickingTaskNos));

        // 5. 获取所有相关的复核头(一次性查询)
        List<String> recheckNos = allRecheckDetails.stream()
                .map(SoRecheckDetail::getRecheckNo)
                .distinct()
                .toList();
        Map<String, SoRecheckHeader> recheckHeaderMap = EmptyUtil.isEmpty(recheckNos) ?
                new HashMap<>() :
                soRecheckHeaderDao.find(new ConditionRule().andIn(SoRecheckHeader::getRecheckNo, recheckNos))
                        .stream()
                        .collect(Collectors.toMap(SoRecheckHeader::getRecheckNo, Function.identity()));

        // 6. 获取所有相关的已发运打包单(一次性查询)
        List<String> allPackNos = recheckHeaderMap.values().stream()
                .filter(h -> YesNoEnum.YES.getCode().equals(h.getIsGeneratePackTask()))
                .map(SoRecheckHeader::getPackNo)
                .distinct()
                .toList();
        Set<String> shippedPackNos = EmptyUtil.isEmpty(allPackNos) ?
                new HashSet<>() :
                packHeaderService.find(new ConditionRule()
                                .andIn(PackHeader::getPackNo, allPackNos)
                                .andIsNotNull(PackHeader::getShippingStatus)
                                .andIn(PackHeader::getShippingStatus, Lists.newArrayList(PackShippingStatusEnum.IN_TRANSIT.getCode(),PackShippingStatusEnum.SHIPMENT_COMPLETE.getCode())))
                        .stream()
                        .map(PackHeader::getPackNo)
                        .collect(Collectors.toSet());

        List<SoAllocationPallet> allocationPalletList = soAllocationPalletDao.find(new ConditionRule().andIn(SoAllocationPallet::getPickingTaskNo, allPickingTaskNos));
        Map<String, List<SoAllocationPallet>> listMap = CollUtil.isNotEmpty(allocationPalletList) ?
                allocationPalletList.stream().collect(Collectors.groupingBy(SoAllocationPallet::getPickingTaskNo)) :
                new HashMap<>() ;
        // 7. 处理每个拣货任务
        for (Map.Entry<String, List<SoAllocation>> entry : allocationsByTask.entrySet()) {
            String pickingTaskNo = entry.getKey();
            List<SoAllocation> taskAllocations = entry.getValue();
            SoAllocation sampleAlloc = taskAllocations.get(0); // 取第一个作为样本
            List<SoAllocationPallet> palletList = listMap.get(pickingTaskNo);
            String paNo = numberGenerator.nextValue(IdRuleConstant.PA_CODE);
            if (EmptyUtil.isEmpty(palletList)) {
                // 7.1 检查是否已发运
                boolean isShipped = allRecheckDetails.stream()
                        .filter(d -> d.getPickingTaskNo().equals(pickingTaskNo)) // 当前拣货任务的复核明细
                        .anyMatch(d -> {
                            SoRecheckHeader header = recheckHeaderMap.get(d.getRecheckNo());
                            return header != null &&
                                    YesNoEnum.YES.getCode().equals(header.getIsGeneratePackTask()) &&
                                    shippedPackNos.contains(header.getPackNo());
                        });

                if (isShipped) {
                    continue; // 已发运则跳过
                }

                // 7.2 构建关联关系对象
                PaTaskSoCreateRestItem item = new PaTaskSoCreateRestItem();
                item.setPaNo(paNo);
                item.setSoNo(soHeader.getSoNo());
                item.setPickingNo(sampleAlloc.getPickingNo());
                item.setSoDetailNo(sampleAlloc.getSoDetailNo());
                item.setPickingTaskNo(pickingTaskNo);

                // 处理复核和打包关联信息
                Optional<SoRecheckDetail> recheckDetail = allRecheckDetails.stream()
                        .filter(d -> d.getPickingTaskNo().equals(pickingTaskNo))
                        .findFirst();

                if (recheckDetail.isPresent()) {
                    item.setRecheckNo(recheckDetail.get().getRecheckNo());
                    SoRecheckHeader header = recheckHeaderMap.get(recheckDetail.get().getRecheckNo());
                    if (header != null && YesNoEnum.YES.getCode().equals(header.getIsGeneratePackTask())) {
                        item.setPackNo(header.getPackNo());
                        item.setIsPack("1");
                    } else {
                        item.setIsPack("0");
                    }
                } else {
                    item.setIsPack("0");
                }
                item.setToPalletNum("*");
                item.setPickingEa(sampleAlloc.getPickingEa());
                paTaskItems.add(item);
            }else {
                palletList.forEach(item -> {
                    if (StrUtil.isEmpty(item.getToPalletNum())){
                        item.setToPalletNum("*");
                    }
                });
                Map<String, List<SoAllocationPallet>> collected = palletList.stream().collect(Collectors.groupingBy(SoAllocationPallet::getToPalletNum));
                for (Map.Entry<String, List<SoAllocationPallet>> pallet : collected.entrySet()) {
                    // 7.1 检查是否已发运
                    boolean isShipped = allRecheckDetails.stream()
                            .filter(d -> d.getPickingTaskNo().equals(pickingTaskNo)) // 当前拣货任务的复核明细
                            .anyMatch(d -> {
                                SoRecheckHeader header = recheckHeaderMap.get(d.getRecheckNo());
                                return header != null &&
                                        YesNoEnum.YES.getCode().equals(header.getIsGeneratePackTask()) &&
                                        shippedPackNos.contains(header.getPackNo());
                            });

                    if (isShipped) {
                        continue; // 已发运则跳过
                    }

                    // 7.2 构建关联关系对象
                    PaTaskSoCreateRestItem item = new PaTaskSoCreateRestItem();
                    item.setPaNo(paNo);
                    item.setSoNo(soHeader.getSoNo());
                    item.setPickingNo(sampleAlloc.getPickingNo());
                    item.setSoDetailNo(sampleAlloc.getSoDetailNo());
                    item.setPickingTaskNo(pickingTaskNo);

                    // 处理复核和打包关联信息
                    Optional<SoRecheckDetail> recheckDetail = allRecheckDetails.stream()
                            .filter(d -> d.getPickingTaskNo().equals(pickingTaskNo))
                            .findFirst();

                    if (recheckDetail.isPresent()) {
                        item.setRecheckNo(recheckDetail.get().getRecheckNo());
                        SoRecheckHeader header = recheckHeaderMap.get(recheckDetail.get().getRecheckNo());
                        if (header != null && YesNoEnum.YES.getCode().equals(header.getIsGeneratePackTask())) {
                            item.setPackNo(header.getPackNo());
                            item.setIsPack("1");
                        } else {
                            item.setIsPack("0");
                        }
                    } else {
                        item.setIsPack("0");
                    }
                    item.setToPalletNum(pallet.getKey());
                    item.setPickingEa(pallet.getValue().stream().map(SoAllocationPallet::getPickingEa).reduce(BigDecimal.ZERO,BigDecimal::add));
                    paTaskItems.add(item);
                }
            }
            // 7.3 实际创建上架任务的逻辑
            // createPutAwayTask(item.getPaNo(), taskAllocations);
        }

        Map<String, SoAllocation> allocationMap = pickedAllocations.stream().collect(Collectors.toMap(SoAllocation::getPickingTaskNo, Function.identity(), (a, b) -> a));
        //查询库位信息
        Set<Long> toLocIdList = pickedAllocations.stream().map(SoAllocation::getToLocId).collect(Collectors.toSet());
        List<WarehouseLoc> warehouseLocList = warehouseLocDao.find(new ConditionRule().andIn(WarehouseLoc::getId, toLocIdList));
        //将库位转换成MAP
        Map<Long, WarehouseLoc> warehouseLocMap = warehouseLocList.stream().collect(Collectors.toMap(ModuleBaseModel::getId, Function.identity()));
        Map<Long, SoDetail> detailMap = soDetails.stream().collect(Collectors.toMap(SoDetail::getId, Function.identity()));
        Set<Long> packageIdList = detailMap.values().stream().map(SoDetail::getPackageId).collect(Collectors.toSet());
        List<Package> packages = packageService.find(new ConditionRule().andIn(Package::getId, packageIdList));
        Map<Long, Package> packageMap = packages.stream().collect(Collectors.toMap(Package::getId, Function.identity(), (a, b) -> a));
        if (EmptyUtil.isEmpty(paTaskItems)) {
            //无未发运商品，将已上架数据生成出库上架任务
            return new OrderForCreatePaTaskItem().setType(0);
        }else{
            Warehouse warehouse = warehouseDao.findById(soHeader.getWarehouseId());
            //生成上架任务
            PaTaskSoCreateItem createItem = new PaTaskSoCreateItem();
            List<PaTaskSoCreateItem.CreateItemInfo> list = new ArrayList<>();
            Map<String, List<PaTaskSoCreateRestItem>> collect = paTaskItems.stream().collect(Collectors.groupingBy(PaTaskSoCreateRestItem::getPaNo));
            //取消分配数据-任务状态已完结
            List<SoAllocation> notGenerateStatusList = new ArrayList<>();
            //取消拣货任务数据-任务状态已取消
            List<SoAllocation> unpickedStatusList = new ArrayList<>();
            //任务状态已完结
            List<SoAllocation> all = new ArrayList<>();
            //部分收货任务
            List<SoAllocation> partialList = new ArrayList<>();
            collect.forEach((s, items) -> {
                PaTaskSoCreateRestItem first = items.getFirst();
                SoAllocation soAllocation = allocationMap.get(first.getPickingTaskNo());
                if (SoPickingStatusEnum.UNPICKED.getCode().equals(soAllocation.getPickingStatus())) {
                    unpickedStatusList.add(soAllocation);
                }else if (SoPickingStatusEnum.NOT_GENERATE.getCode().equals(soAllocation.getPickingStatus())) {
                    notGenerateStatusList.add(soAllocation);
                }else if(SoPickingStatusEnum.PARTIAL_PICKING.getCode().equals(soAllocation.getPickingStatus())) {
                    SoAllocation allocation = new SoAllocation();
                    BeanUtil.copyProperties(soAllocation, allocation);
                    //变更要回退的qtyEa数 逻辑 qtyEa - pickingEa = 回退的分配EA
                    allocation.setQtyEa(allocation.getQtyEa().subtract(allocation.getPickingEa()));
                    partialList.add(allocation);
                }
                all.add(soAllocation);
                PaTaskSoCreateItem.CreateItemInfo info = new PaTaskSoCreateItem.CreateItemInfo();
                info.setPaNo(first.getPaNo());
                info.setWarehouseId(warehouse.getId());
                info.setWarehouseName(warehouse.getWarehouseName());
                info.setPaSource(PaSourceEnum.SO.getCode());
                info.setOrderId(soHeader.getId());
                info.setOrderNo(soHeader.getSoNo());
                info.setStatus(PaStatusEnum.SHELVESING.getCode());
                info.setLotNum(soAllocation.getLotNum());
                WarehouseLoc warehouseLoc = warehouseLocMap.get(soAllocation.getToLocId());
                info.setFmLocId(warehouseLoc.getId());
                info.setFmLocName(warehouseLoc.getLocName());
                BigDecimal reduce = items.stream().map(PaTaskSoCreateRestItem::getPickingEa).reduce(BigDecimal.ZERO, BigDecimal::add);
                info.setQtyPlanEa(reduce);
                SoDetail soDetail = detailMap.get(soAllocation.getSoDetailId());
                //TODO
                Package packageInfo = packageMap.get(soDetail.getPackageId());
                info.setPackageId(packageInfo.getId());
                info.setPackageName(packageInfo.getName());
                info.setPackageUnitId(soDetail.getPackageUnitId());
                info.setPackageUnitName(soDetail.getPackageUnitName());
                //TODO 没有上架人
                Sku sku = skuDao.findById(soDetail.getSkuId());
                info.setSkuId(sku.getId());
                info.setSkuName(sku.getSkuName());
                Owner owner = ownerDao.findById(soAllocation.getOwnerId());
                if (ObjectUtil.isNotEmpty(owner)){
                    info.setOwnerId(owner.getId());
                    info.setOwnerName(owner.getOwnerName());
                }
                info.setPaTime(DateUtil.date());
                list.add(info);
            });
            //未生成
            if (EmptyUtil.isNotEmpty(notGenerateStatusList)) {
                //执行取消分配
                //区分波次和出库
                List<SoAllocation> wvList = notGenerateStatusList.stream().filter(f -> StrUtil.isNotEmpty(f.getPkWvNo())).toList();
                if (EmptyUtil.isNotEmpty(wvList)) {
                    CancalSaveItem cancalSaveItem = new CancalSaveItem();
                    cancalSaveItem.setCancelPicking(false);
                    cancalSaveItem.setIdList(wvList.stream().map(ModuleBaseModel::getId).toList());
                    cancalSaveItem.setAllocationType(AllocationTypeEnum.WAVE_TIMES.getCode());
                    soAllocationPickingService.cancelSave(cancalSaveItem);
                }
                List<SoAllocation> soList = notGenerateStatusList.stream().filter(f -> StrUtil.isEmpty(f.getPkWvNo())).toList();
                if (EmptyUtil.isNotEmpty(soList)) {
                    CancalSaveItem cancalSaveItem = new CancalSaveItem();
                    cancalSaveItem.setCancelPicking(false);
                    cancalSaveItem.setIdList(soList.stream().map(ModuleBaseModel::getId).toList());
                    cancalSaveItem.setAllocationType(AllocationTypeEnum.OUTBOUND.getCode());
                    soAllocationPickingService.cancelSave(cancalSaveItem);
                }
            }
            //未拣货
            if (EmptyUtil.isNotEmpty(unpickedStatusList)) {
                soAllocationPickingService.cancelPickingTask(unpickedStatusList.stream().map(ModuleBaseModel::getId).toArray(Long[]::new));
            }
            AllocUpdateTaskStatusItem updateTaskStatusItem = new AllocUpdateTaskStatusItem();
            updateTaskStatusItem.setTaskStatus(TaskStatusEnum.COMPLETED.getCode());
            soAllocationPickingService.updateByCondition(updateTaskStatusItem,new ConditionRule().andIn(SoAllocation::getId,all.stream().map(ModuleBaseModel::getId).toList()));
            createItem.setCreateItemInfoList(list);

            log.info("出库上架-> 生成上架任务前置完成，数据集合：{}", JSONUtil.toJsonStr(createItem));
            csPaTaskService.createSoPaTask(createItem);
            //保存关联关系
            log.info("出库上架-> 生成关联关系表前置完成，数据集合：{}", JSONUtil.toJsonStr(paTaskItems));
            csPaTaskAssociationService.batchSoCreateAssociation(paTaskItems);
            //回退部分拣货中未拣货的EA数
            log.info("出库上架-> 回退部分拣货中未拣货的EA数，数据集合：{}", JSONUtil.toJsonStr(partialList));
            soAllocationPickingService.changeInvLotLoc(partialList);
            List<String> packNoList = paTaskItems.stream().map(PaTaskSoCreateRestItem::getPackNo).filter(Objects::nonNull).toList();
            //验证是否存在二次分拣，存在 则更新二次分拣的任务状态为已完结
            List<SoAllocation> secondarySortingList = all.stream().filter(f -> StrUtil.isNotEmpty(f.getSecondarySortingStatus())).toList();
            if (EmptyUtil.isNotEmpty(secondarySortingList)) {
                //更新二次分拣任务状态为已完结
                secondarySortingList.stream().forEach(item -> item.setSortingTaskStatus(TaskStatusEnum.COMPLETED.getCode()));
                soAllocationPickingService.batchUpdate(secondarySortingList);
            }
            //7. 校验是否生成打包任务；
            if (CollUtil.isNotEmpty(packNoList)){
                //  是，打包任务关联上架任务信息，校验打包任务是否装箱；
                List<PackHeader> headers = packHeaderService.find(new ConditionRule().andIn(PackHeader::getPackNo, packNoList));
                Map<String, PackHeader> headerMap = headers.stream().collect(Collectors.toMap(PackHeader::getPackNo, Function.identity()));
                List<Long> ydbIds = new ArrayList<>();
                List<Long> wdbIds = new ArrayList<>();
                for (String packNo : packNoList) {
                    PackHeader packHeader = headerMap.get(packNo);
                    //验证是否装箱 unpack pack close 打包任务生成运单关联上架任务，直接取消运单，如果推送到TMS，更新TMS运单状态，流程结束 TODO 暂时无最新代码
                    if (packHeader.getPackStatus().equals("pack")) {
                        if (StrUtil.isNotEmpty(packHeader.getShippingStatus())){
                            packHeaderService.cancel(packHeader.getId());
                        }
                        ydbIds.add(packHeader.getId());
                    }else{
                        //未打包
                        wdbIds.add(packHeader.getId());
                    }
                }
                if (EmptyUtil.isNotEmpty(ydbIds)){
                    //以打包先变更为未打包
                    packHeaderService.deletePack(ydbIds.toArray(Long[]::new));
                    //在执行取消
                    packHeaderService.cancel(ydbIds.toArray(Long[]::new));
                }else{
                    packHeaderService.cancel(wdbIds.toArray(Long[]::new));
                }
            }
            return new OrderForCreatePaTaskItem().setType(1);
        }
    }

    /**
     * 执行订单未发运业务逻辑主方法-需要生成上架任务
     * @param soHeader
     * @param soDetails
     * @return
     */
    public OrderForCreatePaTaskItem executeAllShipmentServiceCreate(SoHeader soHeader, List<SoDetail> soDetails) {
        //1.检验所有商品是否存在已拣货数
        List<SoDetail> czyjhsspList = soDetails.stream().filter(f -> BigDecimal.ZERO.compareTo(f.getQtyPickedEa()) < 0).toList();
        if (CollUtil.isNotEmpty(czyjhsspList)){
            //是，根据已拣货商品生成上架任务，并且更新相关任务状态关联对应上架信息，根据生成上架任务业务场景逻辑进行校验
            return executePartialShipmentServiceCreate(soHeader,soDetails);
        }
        //2.校验所有商品是否存在拣货单
        List<SoPicking> pickings = soPickingDao.find(new ConditionRule().andEqual(SoPicking::getSoNo, soHeader.getSoNo()));
        if (EmptyUtil.isNotEmpty(pickings)){
            //3. 校验所有商品是否存在拣货任务
            List<String> list = pickings.stream().map(SoPicking::getPickingNo).toList();
            List<SoAllocation> allocations = soAllocationPickingService.find(new ConditionRule().andIn(SoAllocation::getPickingNo, list));
            if (EmptyUtil.isNotEmpty(allocations)){
                //  2. 是，更新拣货任务拣货状态（未拣货更新为已取消，其他状态不更新），任务状态更新为已完结，进行拣货单校验
                List<SoAllocation> allocationList = allocations.stream().filter(f -> SoPickingStatusEnum.UNPICKED.getCode().equals(f.getPickingStatus())).toList();
                if (EmptyUtil.isNotEmpty(allocationList)){
                    Long[] ids = allocationList.stream().map(ModuleBaseModel::getId).toArray(Long[]::new);
                    soAllocationPickingService.cancelPickingTask(ids);
                    AllocUpdateTaskStatusItem item = new AllocUpdateTaskStatusItem();
                    item.setTaskStatus(TaskStatusEnum.COMPLETED.getCode());
                    soAllocationPickingService.updateByIds(item, ids);
                }
                //对未生成的数据执行取消
                List<SoAllocation> allocationListNotGenerate = allocations.stream().filter(f -> SoPickingStatusEnum.NOT_GENERATE.getCode().equals(f.getPickingStatus())).toList();
                if (EmptyUtil.isNotEmpty(allocationListNotGenerate)){
                    List<SoAllocation> wvList = allocationListNotGenerate.stream().filter(f -> StrUtil.isNotEmpty(f.getPkWvNo())).toList();
                    if (EmptyUtil.isNotEmpty(wvList)) {
                        CancalSaveItem cancalSaveItem = new CancalSaveItem();
                        cancalSaveItem.setCancelPicking(false);
                        cancalSaveItem.setIdList(wvList.stream().map(ModuleBaseModel::getId).toList());
                        cancalSaveItem.setAllocationType(AllocationTypeEnum.WAVE_TIMES.getCode());
                        soAllocationPickingService.cancelSave(cancalSaveItem);
                    }
                    List<SoAllocation> soList = allocationListNotGenerate.stream().filter(f -> StrUtil.isEmpty(f.getPkWvNo())).toList();
                    if (EmptyUtil.isNotEmpty(soList)) {
                        CancalSaveItem cancalSaveItem = new CancalSaveItem();
                        cancalSaveItem.setCancelPicking(false);
                        cancalSaveItem.setIdList(soList.stream().map(ModuleBaseModel::getId).toList());
                        cancalSaveItem.setAllocationType(AllocationTypeEnum.OUTBOUND.getCode());
                        soAllocationPickingService.cancelSave(cancalSaveItem);
                    }
                }
            }
        }
        return new OrderForCreatePaTaskItem().setType(2);
    }

    /**
     * 根据分配明细更新拣货单状态
     */
    public void updatePickingStatus(SoPicking picking) {

        // 如果是已取消状态，不再更新
        if (SoPickingStatusEnum.CANCELLED.getCode().equals(picking.getStatus())) {
            return;
        }

        List<SoAllocation> allocations = soAllocationPickingService.find(
                new ConditionRule().andEqual(SoAllocation::getPickingNo, picking.getPickingNo()));

        // 2. 分析分配明细状态
        boolean allNotPicked = allocations.stream()
                .allMatch(a -> SoPickingStatusEnum.UNPICKED.getCode().equals(a.getPickingStatus()));

        boolean allComplete = allocations.stream()
                .allMatch(a -> SoPickingStatusEnum.COMPLETE_PICKING.getCode().equals(a.getPickingStatus()));

        boolean anyPartial = allocations.stream()
                .anyMatch(a -> SoPickingStatusEnum.PARTIAL_PICKING.getCode().equals(a.getPickingStatus()));

        // 3. 根据规则更新拣货单状态
        String newStatus;
        if (allComplete) {
            picking.setStatus(SoPickingStatusEnum.COMPLETE_PICKING.getCode());
        } else if (anyPartial || (!allNotPicked && !allComplete)) {
            picking.setStatus(SoPickingStatusEnum.PARTIAL_PICKING.getCode());
        } else if (allNotPicked) {
            picking.setStatus(SoPickingStatusEnum.UNPICKED.getCode());
        }
    }
}
