package com.chinaservices.wms.module.asn.receive.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.auth.module.dict.domain.DictDataCondition;
import com.chinaservices.auth.module.dict.domain.DictDataQuery;
import com.chinaservices.auth.module.dict.feign.RemoteDictDataService;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.utils.id.IdWorker;
import com.chinaservices.auth.module.dict.domain.DictDataCondition;
import com.chinaservices.auth.module.dict.domain.DictDataQuery;
import com.chinaservices.auth.module.dict.feign.RemoteDictDataService;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.edi.module.asn.domain.CrzReceiveItem;
import com.chinaservices.edi.module.asn.domain.ReceiveUpdateIncInvItem;
import com.chinaservices.edi.module.asn.feign.RemoteAsnStatusPushToCrzFegin;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.jpa.support.rule.OrderRule;
import com.chinaservices.module.base.ModuleBaseModel;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.util.StringPool;
import com.chinaservices.wms.common.constant.*;
import com.chinaservices.wms.common.constants.DictDataTypeConstants;
import com.chinaservices.wms.common.enums.asn.AsnStatusEnum;
import com.chinaservices.wms.common.constants.DictDataTypeConstants;
import com.chinaservices.wms.common.enums.DocumentTypeEnum;
import com.chinaservices.wms.common.enums.asn.PaSourceEnum;
import com.chinaservices.wms.common.enums.asn.PaStatusEnum;
import com.chinaservices.wms.common.enums.order.OrderStatusEnum;
import com.chinaservices.wms.common.enums.so.ReceiveStatusEnum;
import com.chinaservices.wms.common.enums.so.SoOrderStatusEnum;
import com.chinaservices.wms.common.enums.stock.ExpiryWarningSaveStageEnum;
import com.chinaservices.wms.common.enums.stock.TraceBackBusinessTypeEnum;
import com.chinaservices.wms.common.enums.stock.ExpiryWarningSaveStageEnum;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.common.exception.AsnExceptionEnum;
import com.chinaservices.wms.common.exception.InvExceptionEnum;
import com.chinaservices.wms.common.properties.ExecutorThreadProperties;
import com.chinaservices.wms.common.util.SpringUtil;
import com.chinaservices.wms.module.archive.archives.service.LogisticsFileAutoGenerateService;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailDao;
import com.chinaservices.wms.module.asn.asn.dao.AsnHeaderDao;
import com.chinaservices.wms.module.asn.asn.domain.*;
import com.chinaservices.wms.module.asn.asn.model.AsnDetail;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.asn.service.AsnDetailSnService;
import com.chinaservices.wms.module.asn.merge.dao.AsnMergeReceiveDao;
import com.chinaservices.wms.module.asn.merge.model.AsnMergeReceive;
import com.chinaservices.wms.module.asn.pa.model.CsPaTask;
import com.chinaservices.wms.module.asn.pa.service.CsPaTaskService;
import com.chinaservices.wms.module.asn.receive.dao.AsnReceiveDao;
import com.chinaservices.wms.module.asn.receive.dao.AsnReceiveFileDao;
import com.chinaservices.wms.module.asn.receive.domain.*;
import com.chinaservices.wms.module.asn.receive.eventbus.UpdateReceiveMergeEventBusPush;
import com.chinaservices.wms.module.asn.receive.model.AsnReceive;
import com.chinaservices.wms.module.asn.receive.model.AsnReceiveFile;
import com.chinaservices.wms.module.basic.cspackage.model.Package;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageService;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.basic.lot.dao.WarehouseLotHeaderDao;
import com.chinaservices.wms.module.basic.lot.model.WarehouseLotDetail;
import com.chinaservices.wms.module.basic.lot.model.WarehouseLotHeader;
import com.chinaservices.wms.module.basic.lot.service.WarehouseLotDetailService;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.basic.supplier.model.Supplier;
import com.chinaservices.wms.module.basic.supplier.service.SupplierService;
import com.chinaservices.wms.module.common.InboundCommonService;
import com.chinaservices.wms.module.common.InventoryCommonService;
import com.chinaservices.wms.module.folder.logistics.domain.LogisticsFolderDetailLinkCondition;
import com.chinaservices.wms.module.order.model.Order;
import com.chinaservices.wms.module.order.model.OrderRelation;
import com.chinaservices.wms.module.order.model.OrderSku;
import com.chinaservices.wms.module.order.service.OrderRelationService;
import com.chinaservices.wms.module.order.service.OrderService;
import com.chinaservices.wms.module.order.service.OrderSkuService;
import com.chinaservices.wms.module.passive.tag.dao.PassiveTagDao;
import com.chinaservices.wms.module.passive.tag.model.PassiveTag;
import com.chinaservices.wms.module.stock.back.model.TraceBackRecordItem;
import com.chinaservices.wms.module.stock.back.service.TraceBackRecordService;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.att.query.InvLotAttQuery;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.expirywarning.service.ExpiryWarningService;
import com.chinaservices.wms.module.stock.mutli.model.InvLotAtt;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLocQty;
import com.chinaservices.wms.module.stock.mutli.service.InvLotAttService;
import com.chinaservices.wms.module.stock.warning.service.StockWarningService;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import com.chinaservices.wms.module.warehouse.shelf.service.WarehouseShelfService;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description: 入库收货明细 Service
 */
@Service
@Slf4j
public class AsnReceiveService extends ModuleBaseServiceSupport<AsnReceiveDao, AsnReceive, Long> {

    @Autowired
    private AsnDetailDao asnDetailDao;

    @Autowired
    private SkuService skuService;

    @Autowired
    private InboundUpdateStatusService inboundUpdateStatusService;

    @Autowired
    private InventoryCommonService inventoryCommonService;

    @Autowired
    private InboundCommonService inboundCommonService;

    @Autowired
    private AsnHeaderDao asnHeaderDao;
    @Autowired
    private NumberGenerator numberGenerator;
    @Autowired
    private InvLotAttService invLotAttService;
    @Autowired
    private WarehouseLotHeaderDao warehouseLotHeaderDao;
    @Autowired
    private WarehouseLocService warehouseLocService;

    @Autowired
    private StockService stockService;
    @Autowired
    private OwnerService ownerService;
    @Autowired
    private PackageUnitService packageUnitService;
    @Autowired
    private WarehouseLotDetailService warehouseLotDetailService;
    @Autowired
    private AsnDetailSnService asnDetailSnService;
    @Autowired
    private WarehouseShelfService warehouseShelfService;
    @Autowired
    private AsnReceiveFileDao asnReceiveFileDao;
    @Autowired
    private CsPaTaskService csPaTaskService;
    @Autowired
    private SupplierService supplierService;

    @Autowired
    private StockWarningService stockWarningService;
    @Autowired
    private ExpiryWarningService expiryWarningService;

    @Autowired
    private RemoteAsnStatusPushToCrzFegin remoteAsnStatusPushToCrzFegin;
    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private OrderRelationService orderRelationService;

    @Autowired
    private OrderSkuService orderSkuService;

    @Autowired
    private OrderService orderService;
    @Autowired
    private TaskExecutor taskExecutor;
    @Autowired
    private LogisticsFileAutoGenerateService logisticsFileAutoGenerateService;

    @Autowired
    private UpdateReceiveMergeEventBusPush  updateReceiveMergeEventBusPush;

    @Autowired
    @Qualifier(value = ExecutorThreadProperties.TRANSACTION_THREAD_EXECUTOR)
    private ExecutorService executorService;

    @Autowired
    private TraceBackRecordService traceBackRecordService;
    @Autowired
    private AsnMergeReceiveDao asnMergeReceiveDao;

    @Autowired
    private RemoteDictDataService remoteDictDataService;

    @Autowired
    public PackageService packageService;
    @Autowired
    public PassiveTagDao  passiveTagDao;


    /**
     * 入库收货明细的商品信息分页查询
     * @param condition
     * @return
     */
    public PageResult<AsnReceiveQuery> page(AsnReceivePageCondition condition){
        return dao.page(condition);
    }

    /**
     * 在收货管理获取入库计划信息列表
     * @param condition
     * @return
     */
    public PageResult<AsnHeaderQuery> pages(AsnHeaderPageCondition condition){
        //时间查询
        if (EmptyUtil.isNotEmpty(condition.getOrderTimeFrom())) {
            condition.setOrderTimeFrom(condition.getOrderTimeFrom() + " 00:00:00");
        }
        if (EmptyUtil.isNotEmpty(condition.getOrderTimeTo())) {
            condition.setOrderTimeTo(condition.getOrderTimeTo() + " 23:59:59");
        }
        PageResult<AsnHeaderQuery> result = dao.pages(condition);
        //后置处理
        if (CollUtil.isEmpty(result.getRows())){
            return result;
        }
        Map<String, AsnHeaderQuery> queryMap = result.getRows().stream().collect(Collectors.toMap(AsnHeaderQuery::getAsnNo, item -> item, (one, two) -> one));
        //通过asnNoList去批量查询detail
        AsnDetailByReceiveICondition cond = new AsnDetailByReceiveICondition();
        cond.setAsnNos(queryMap.keySet());
        List<AsnDetailByReceiveItem> detailList = asnDetailDao.selectListByAsnNo(cond);//.find(AsnDetailByReceiveItem.class,new ConditionRule().andIn(AsnDetailItem.COL_NAME_ASN_ID, queryMap.keySet()));
        if (CollUtil.isEmpty(detailList)){
            return result;
        }
        Map<String, List<AsnDetailByReceiveItem>> groupList = detailList.stream().collect(Collectors.groupingBy(AsnDetailByReceiveItem::getAsnNo));
        List<Long> detailIdList = detailList.stream().map(AsnDetailByReceiveItem::getId).toList();
        List<AsnReceive> list = dao.find(new ConditionRule().andIn(AsnReceive::getAsnDetailId, detailIdList));
        Map<Long,String> lotNumMap = new HashMap<>();
        if (CollUtil.isNotEmpty(list)){
            Map<Long, List<AsnReceive>> listMap = list.stream().collect(Collectors.groupingBy(ModuleBaseModel::getId));
            listMap.keySet().forEach(item -> {
                lotNumMap.put(item, listMap.get(item).getFirst().getLotNum());
            });
        }
        List<PassiveTag> passiveTags = passiveTagDao.find(new ConditionRule().andIn(PassiveTag::getAsnNo, queryMap.keySet()));
        Map<String,PassiveTag> passiveTagMap = new HashMap<>();
        if (CollUtil.isNotEmpty(passiveTags)){
            passiveTagMap = passiveTags.stream().collect(Collectors.toMap(m -> m.getAsnNo() + m.getSkuId() , Function.identity(), (one,two) -> one));
        }
        Map<String, PassiveTag> finalPassiveTagMap = passiveTagMap;
        queryMap.keySet().forEach(item -> {
            List<AsnDetailByReceiveItem> details = groupList.get(item);
            if (CollUtil.isNotEmpty(details)){
                AsnHeaderQuery headerQuery = queryMap.get(item);
                for (AsnDetailByReceiveItem detail : details) {
                    String lotNum = lotNumMap.get(detail.getId());
                    if (StrUtil.isNotEmpty(lotNum)){
                        detail.setLotNum(lotNum);
                    }
                    PassiveTag passiveTag = finalPassiveTagMap.get(headerQuery.getAsnNo() + detail.getSkuId());
                    if (EmptyUtil.isNotEmpty(passiveTag)){
                        detail.setTagNo(passiveTag.getTagNo());
                    }
                }
                headerQuery.setDetails(details);
            }else{
                AsnHeaderQuery headerQuery = queryMap.get(item);
                headerQuery.setDetails(new ArrayList<>());
            }
        });
        return result;
    }

    /**
     * 根据Id查询收货信息
     * @param id
     * @return
     */
    public AsnReceive findById(long id) {
        return dao.findById(id);
    }

    /**
     * 查询收货商品由条件
     *
     * @param condition 条件
     * @return {@link List }<{@link AsnReceive }>
     */
    public List<AsnReceive> findByCondition(AsnReceiveCondition condition)
    {
        return dao.findByCondition(condition);
    }

    /**
     * 完全收货-收货明细批量收货
     * @param ids 收货明细ids集合
     * @return
     */
    public ResponseData<AsnReceive> totalReceiveaByIds(Object[] ids) {
        // 查询勾选的收货明细
        Long[] idArray = Arrays.stream(ids).map(Convert::toLong).toArray(Long[]::new);
        AsnReceiveCondition asnReceiveCondition = new AsnReceiveCondition();
        asnReceiveCondition.setIds(idArray);
        List<AsnReceive> receives = dao.getToTalByIds(asnReceiveCondition);
        StringBuilder error = new StringBuilder();
        for (int i = 0; i < receives.size(); i++) {
            AsnReceive asnReceive = receives.get(i);
            //实际收货数如果为空，则默认按计划收货EA进行收货
            BigDecimal qtyRcvEa = asnReceive.getQtyRcvEa()==null?BigDecimal.ZERO:asnReceive.getQtyRcvEa();
            if(qtyRcvEa.compareTo(BigDecimal.ZERO)<=0){
                asnReceive.setQtyRcvEa(asnReceive.getQtyPlanEa());
            }
            ResponseData<AsnReceive> msg = this.receiveConfirm(asnReceive);
            if (!msg.getSuccess()){
                error.append(msg.getMsg());
            }
        }
        if (error.length() > 0) {
            return ResponseData.error(error.toString());
        }
        return ResponseData.success();
    }

    /**
     * 完全收货--订单列表批量收货
     * @param ids 预收货订单ids集合
     * @return
     */
    public ResponseData<AsnReceive> totalReceiveByAsnIds(Object[] ids) {
        // 查询勾选订单的收货明细
        Long[] idArray = Arrays.stream(ids).map(Convert::toLong).toArray(Long[]::new);
        AsnReceiveCondition asnReceiveCondition = new AsnReceiveCondition();
        asnReceiveCondition.setAsnIds(idArray);
        List<AsnReceive> receives = dao.getToTalByIds(asnReceiveCondition);
        StringBuilder error = new StringBuilder();
        for (AsnReceive asnReceive:receives) {
            //实际收货数如果为空，则默认按计划收货EA进行收货
            BigDecimal qtyRcvEa = asnReceive.getQtyRcvEa() == null ? BigDecimal.ZERO : asnReceive.getQtyRcvEa();
            if (qtyRcvEa.compareTo(BigDecimal.ZERO) <= 0) {
                asnReceive.setQtyRcvEa(asnReceive.getQtyPlanEa());
            }
            ResponseData<AsnReceive> msg = this.receiveConfirm(asnReceive);
            if (!msg.getSuccess()){
                error.append(msg.getMsg());
            }
        }
        if (error.length() > 0) {
            return ResponseData.error(error.toString());
        }
        return ResponseData.success();
    }



    /**
     * 根据Ids批量删除入库收货明细信息
     * @param ids
     * @return
     */
    public boolean deleteByIds(Object[] ids) {
        Long[] idArray = Arrays.stream(ids).map(Convert::toLong).toArray(Long[]::new);
        return dao.delete(idArray) > 0;
    }

    /**
     * 收货明细单条收货确认
     * @param asnReceive
     * @return
     */
    public ResponseData<AsnReceive> receiveConfirm(AsnReceive asnReceive) {
//        try {
//            //收货前校验start，包括订单状态、质检//构造参数
//            ResponseData<InvLotLocQty> msg = this.checkBeforeReceiving(asnReceive);
//            if(!msg.getSuccess()){
//                return ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, asnReceive.getAsnNo(), asnReceive.getLineNo(), msg.getMsg());
//            }
//            //收货容器号
//            String toPalletNum;
//            Long asnId = asnReceive.getAsnId();
//            // 更新库存
//            msg = this.getInvLotLocQty(asnReceive, ActionCode.RECEIVING);
//            if(!msg.getSuccess()){
//                return ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, asnReceive.getAsnNo(), asnReceive.getLineNo(), msg.getMsg());
//            }
//            InvLotLocQty invLotLocQty = msg.getData();
//            msg = updateInventoryQtyService.updateInventory(invLotLocQty);
//
//            if(!msg.getSuccess()){
//               return ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, asnReceive.getAsnNo(), asnReceive.getLineNo(), msg.getMsg());
//            }
//            invLotLocQty = msg.getData();
//            asnReceive.setLotNum(invLotLocQty.getLotNum());
//            //部分收货：预收数>实收数，拆分收货明细。
//            this.splitAsnReceive(asnReceive);
//            //库位为忽略ID时，更新完库存，容器号置*
//            toPalletNum = invLotLocQty.getPalletNum();
//            asnReceive.setRcvPallet(toPalletNum);
//            //更新当前收货明细状态,回填ASN明细的收货数
//            AsnReceiveInfo asnReceiveInfo = new AsnReceiveInfo();
//            BeanUtil.copyPropertiesIsNotNull(asnReceive, asnReceiveInfo);
//            asnReceiveInfo.setCurrentQtyRcvEa(asnReceive.getQtyRcvEa());
//            asnReceiveInfo = inboundUpdateStatusService.updateReceivingStatus(asnReceiveInfo);
//            //更新ASN单状态
//            inboundUpdateStatusService.updateAsnStatus(asnId);
//            //收货容器号变更为占用状态
//            String pallet = asnReceiveInfo.getRcvPallet();
//            if (EmptyUtil.isNotEmpty(pallet)&&!IdRuleConstant.TRACE_ID.equals(pallet)) {
//                palletService.updateIsUsed(pallet,asnReceive.getWarehouseId(), SystemStatus.YES);
//            }
//            //判断是否生成上架任务
//            WarehouseLoc warehouseLoc = warehouseLocService.findById(asnReceiveInfo.getRcvLocId().longValue());//查询收货库位信息
//            //如果计划收货库位的库位使用类型为过渡库位
//            if(warehouseLoc!=null&& LocUseType.LOC_USE_ST.equals(warehouseLoc.getLocUseType())){
//                //上架任务
//                PaTaskInfo paTaskInfo = new PaTaskInfo();
//                BeanUtil.copyPropertiesIsNotNull(asnReceiveInfo, paTaskInfo);
//                paTaskInfo.setId(null);
//                paTaskInfo.setFmLocId(asnReceiveInfo.getRcvLocId());
//                paTaskInfo.setFmLocCode(asnReceiveInfo.getRcvLocName());
//                paTaskInfo.setFmPallet(asnReceiveInfo.getRcvPallet());
//                paTaskInfo.setQtyPlanEa(asnReceive.getQtyRcvEa());
//                paTaskInfo.setQtyPaEa(BigDecimal.ZERO);
//                paTaskInfo.setPaNoRcv(asnReceiveInfo.getPaNo());
//                paTaskInfo.setRcvId(asnReceiveInfo.getId());
//                paTaskInfo.setOrderNo(asnReceiveInfo.getAsnNo());
//                paTaskInfo.setOrderType(OrderType.ASN.getCode());
//                try {
//                    ResponseData<CsPaTask> createMsg = csPaTaskService.inboundCreatePaTask(paTaskInfo);
//                    if (createMsg.getSuccess()) {
//                        CsPaTask csPaTask = createMsg.getData();
//                        String paNo = csPaTask.getPaNo();
//                        //容器号为“*”，每个收货明细生成一条上架任务
//                        if(IdRuleConstant.TRACE_ID.equals(asnReceiveInfo.getRcvPallet())){
//                            asnReceiveInfo.setPaNo(paNo);
//                            inboundCommonService.saveAsnDetailReceiveEntity(asnReceiveInfo);
//                        }else{
//                            //更新相同容器号的收货明细行的PaNo
//                            AsnReceiveCondition asnReceiveCondition = new AsnReceiveCondition();
//                            asnReceiveCondition.setAsnId(asnReceiveInfo.getAsnId());
//                            asnReceiveCondition.setRcvPallet(asnReceiveInfo.getRcvPallet());
//                            asnReceiveCondition.setStatus(AsnStatus.ASN_FULL_RECEIVING);
//                            asnReceiveCondition.setPaNo(paNo);
//                            dao.updatePaNo(asnReceiveCondition);
//                        }
//                    }
//                }catch (Exception e){
//                    //异常不做处理
//                }
//            }else{
//                try {
//                    //完全收货状态，并且不存在未完成的上架任务，是否自动关闭ASN
//                    inboundCommonService.checkRcvAutoCloseAsn(asnReceive.getAsnId(), asnReceive.getWarehouseId());
//                } catch (Exception e) {
//                    logger.error(e.getMessage());
//                }
//            }
//        } catch (Exception e) {
//            logger.error(e.getMessage());
//            return ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO, asnReceive.getAsnNo(), asnReceive.getLineNo(), e.getMessage());
//        }
        return ResponseData.success();
    }

    /**
     * 收货明细-收货前校验
     * @param asnReceive
     * @return
     */
    private ResponseData<InvLotLocQty> checkBeforeReceiving(AsnReceive asnReceive) {
        //收货前校验start，包括订单状态、质检//构造参数
//        Receiving receivingEntity = new Receiving();
//        receivingEntity.setActionCode(ActionCode.RECEIVING);
//        //收货明细
//        AsnReceiveInfo csAsnReceiveInfo = new AsnReceiveInfo();
//        BeanUtil.copyProperties(asnReceive, csAsnReceiveInfo);
//        receivingEntity.setAsnReceiveInfo(csAsnReceiveInfo);
//        Sku skuModel = skuService.getByOwnerAndSkuId(asnReceive.getOwnerId(), asnReceive.getSkuId(), asnReceive.getWarehouseId());
//        if (skuModel != null) {
//            try {
//                ResponseData<AsnReceiveInfo> msg = inboundReceivingService.checkBeforeReceiving(receivingEntity, skuModel);
//                return ResponseData.success(msg.getData());
//            } catch (Exception e) {
//                return ResponseData.error(e.getMessage());
//            }
//        }else{
//            return ResponseData.error(MsgConstant.ASN_SKU_DATE_NULL);
//        }
        return ResponseData.success();
    }

    /**
     * 部分收货时，拆分收货明细
     * @param asnReceive
     */
    public void splitAsnReceive(AsnReceive asnReceive){
        BigDecimal currentRcvQty = asnReceive.getQtyRcvEa();//当前实收数
        BigDecimal planQty = asnReceive.getQtyPlanEa();
        //如果收货数<预收数，部分收货时，拆明细
        if(currentRcvQty.compareTo(BigDecimal.ZERO)!=0 &&currentRcvQty.compareTo(planQty)<0){
            //赋值
            AsnReceive newReceiveModel = new AsnReceive();
            BeanUtil.copyPropertiesIsNotNull(asnReceive,newReceiveModel);
            newReceiveModel.setId(null);
            newReceiveModel.setLotNum(null);//未收货明细，批次号为空
            newReceiveModel.setRcvPallet(null);//清空容器号
            newReceiveModel.setQtyPlanEa(planQty.subtract(currentRcvQty));
            newReceiveModel.setQtyRcvEa(BigDecimal.ZERO);
            if (EmptyUtil.isEmpty(asnReceive.getRcvPallet())) {
                String rcvPallet = IdRuleConstant.TRACE_ID;
                asnReceive.setRcvPallet(rcvPallet);
            }
            //新增保存
            inboundCommonService.saveAsnReceive(newReceiveModel);
        }

    }

    /**
     * 构建更新库存类
     * @param asnReceive
     * @param action
     * @return
     */
    private ResponseData<InvLotLocQty> getInvLotLocQty(AsnReceive asnReceive, String action) {
        InvLotLocQty invLotLocQtyItem = new InvLotLocQty();
        invLotLocQtyItem.setAction(action);
        invLotLocQtyItem.setUpdateNum(asnReceive.getQtyRcvEa());
        //仓库代码
//        invLotLocQtyItem.setWarehouseId(asnReceive.getWarehouseId());
//        //仓库名称
//        invLotLocQtyItem.setWarehouseName(asnReceive.getWarehouseName());
//        //货主代码
//        invLotLocQtyItem.setOwnerId(asnReceive.getOwnerId());
//        //货主名称
//        invLotLocQtyItem.setOwnerName(asnReceive.getOwnerName());
//        //商品代码
//        invLotLocQtyItem.setSkuId(asnReceive.getSkuId());
        //商品名称
//        invLotLocQtyItem.setSkuName(asnReceive.getSkuName());
        //源库位编码
        invLotLocQtyItem.setLocId(asnReceive.getRcvLocId());
        //源库位编码
//        invLotLocQtyItem.setLocCode(asnReceive.getRcvLocName());
//        invLotLocQtyItem.setOrderNo(asnReceive.getAsnCode());
//        invLotLocQtyItem.setLineNo(asnReceive.getLineNo());
//        invLotLocQtyItem.setPackageId(asnReceive.getPackageId());
//        invLotLocQtyItem.setPackageName(asnReceive.getPackageUnitName());
//        invLotLocQtyItem.setPackageUomId(asnReceive.getPackageUnitId());
//        invLotLocQtyItem.setPackageUomName(asnReceive.getPackageUomName());
//        invLotLocQtyItem.setClientId(asnReceive.getClientId());
//        invLotLocQtyItem.setClientName(asnReceive.getClientName());
        //源容器号
        String rcvPallet = asnReceive.getRcvPallet();
        if (EmptyUtil.isEmpty(rcvPallet)) {
            rcvPallet = "*";
        }
        invLotLocQtyItem.setPalletNum(rcvPallet);
        //创建人
        invLotLocQtyItem.setCreator(asnReceive.getModifier());

        Map<String, Object> lotAttMaps = new HashMap<>();
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt01())) {
            lotAttMaps.put("lotAtt01", asnReceive.getLotAtt01());
        }
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt02())) {
            lotAttMaps.put("lotAtt02", asnReceive.getLotAtt02());
        }
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt03())) {
            lotAttMaps.put("lotAtt03", asnReceive.getLotAtt03());
        }
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt04())) {
            lotAttMaps.put("lotAtt04", asnReceive.getLotAtt04());
        }
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt05())) {
            lotAttMaps.put("lotAtt05", asnReceive.getLotAtt05());
        }
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt06())) {
            lotAttMaps.put("lotAtt06", asnReceive.getLotAtt06());
        }
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt07())) {
            lotAttMaps.put("lotAtt07", asnReceive.getLotAtt07());
        }
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt08())) {
            lotAttMaps.put("lotAtt08", asnReceive.getLotAtt08());
        }
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt09())) {
            lotAttMaps.put("lotAtt09", asnReceive.getLotAtt09());
        }
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt10())) {
            lotAttMaps.put("lotAtt10", asnReceive.getLotAtt10());
        }
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt11())) {
            lotAttMaps.put("lotAtt11", asnReceive.getLotAtt11());
        }
        if (EmptyUtil.isNotEmpty(asnReceive.getLotAtt12())) {
            lotAttMaps.put("lotAtt12", asnReceive.getLotAtt12());
        }
        invLotLocQtyItem.setLotAttMaps(lotAttMaps);
        if(ActionCode.RECEIVING.equals(action)){
            ResponseData<String> msg = this.getLotNum(invLotLocQtyItem);
            if (!msg.getSuccess()){
                return ResponseData.error(msg.getMsg());
            }
            String lotNum = msg.getData();
            invLotLocQtyItem.setLotNum(lotNum);
        }else if(ActionCode.CANCEL_RECEIVING.equals(action)){
            invLotLocQtyItem.setLotNum(asnReceive.getLotNum());
        }
        return ResponseData.success(invLotLocQtyItem);
    }

    /**
     * 获取批次号
     * @param invLotLocQty
     * @return
     */
    private ResponseData<String> getLotNum(InvLotLocQty invLotLocQty) {
        Map<String, Object> lotAttMaps = invLotLocQty.getLotAttMaps();
        InvLotAtt invLotAtt = BeanUtil.mapToBean(lotAttMaps, InvLotAtt.class);
        invLotAtt.setOwnerId(invLotLocQty.getOwnerId());
        invLotAtt.setOwnerName(invLotLocQty.getOwnerName());
        invLotAtt.setClientId(invLotLocQty.getClientId());
        invLotAtt.setClientName(invLotLocQty.getClientName());
        invLotAtt.setSkuId(invLotLocQty.getSkuId());
        invLotAtt.setSkuName(invLotLocQty.getSkuName());
        invLotAtt.setWarehouseId(invLotLocQty.getWarehouseId());
        invLotAtt.setWarehouseName(invLotLocQty.getWarehouseName());

        ResponseData<String> msg = inventoryCommonService.createInvLotNum(invLotAtt);
        if (!msg.getSuccess()){
            return ResponseData.error(msg.getMsg());
        }
        return ResponseData.success(msg.getData());
    }


    /**
     * 批量取消收货-按收货明细
     * @param ids
     * @return
     */
    public ResponseData<AsnReceive> inboundBatchCancelReceiving(Object[] ids) {
        ResponseData<AsnReceive> responseData = new ResponseData<>();
        StringBuilder errorMsg = new StringBuilder();
        // 查询勾选订单的收货明细
        Long[] idArray = Arrays.stream(ids).map(Convert::toLong).toArray(Long[]::new);
        AsnReceiveCondition asnReceiveCondition = new AsnReceiveCondition();
        asnReceiveCondition.setIds(idArray);
        List<AsnReceive> asnReceives = dao.findAllByIds(asnReceiveCondition);
        for (int i = 0; i < asnReceives.size(); i++) {
            AsnReceive asnReceive = asnReceives.get(i);

            ResponseData<AsnReceive> msg = this.inboundCancelReceiving(asnReceive);
            if (!msg.getSuccess()){
                errorMsg.append(msg.getMsg());
            }
        }
        if(EmptyUtil.isNotEmpty(errorMsg.toString())){
            responseData.setSuccess(false);
            responseData.setMsg(errorMsg.toString());
        }else{
            responseData.setSuccess(true);
        }
        return responseData;
    }

    /**
     * 收货明细取消收货
     * @param asnReceive 收货明细model
     * @return
     */
    public ResponseData<AsnReceive> inboundCancelReceiving(AsnReceive asnReceive) {
        //取消收货前校验start
        AsnReceiveInfo asnReceiveInfo = new AsnReceiveInfo();
        BeanUtil.copyProperties(asnReceive, asnReceiveInfo);
        try {
            ResponseData<AsnReceiveInfo> msg = this.checkBeforeCancelReceiving(asnReceiveInfo);
            if(!msg.getSuccess()){
                return ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, asnReceiveInfo.getAsnNo(), asnReceiveInfo.getLineNo(), msg.getMsg() );
            }
            //更新库存
            ResponseData<InvLotLocQty> msgInvLotLocQty = this.getInvLotLocQty(asnReceive, ActionCode.CANCEL_RECEIVING);
            if(!msgInvLotLocQty.getSuccess()){
                return ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, asnReceiveInfo.getAsnNo(), asnReceiveInfo.getLineNo(), msg.getMsg() );
            }
            InvLotLocQty invLotLocQty = msgInvLotLocQty.getData();
            invLotLocQty.setAction(ActionCode.CANCEL_RECEIVING);
            invLotLocQty.setLotNum(asnReceive.getLotNum());
            InvLotLocQty invLotLocItem = new InvLotLocQty();
            BeanUtil.copyProperties(invLotLocQty, invLotLocItem);
//            ResponseData<InvLotLocQty> msgInvLotLocQtyItem = updateInventoryQtyService.updateInventory(invLotLocItem);


//            if(!msgInvLotLocQtyItem.getSuccess()){
//                return ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, asnReceiveInfo.getAsnNo(), asnReceiveInfo.getLineNo(), msg.getMsg() );
//            }
            //取消收货明细，合并相同明细行，相同PlanID的明细行。(合并明细后，会改变收货明细的收货数，需要对取消数，重新赋值)
            //当前收货明细行，需要取消的收货数。
            BigDecimal cancelQtyEa = asnReceive.getQtyRcvEa();
            asnReceiveInfo = this.cancelRcvUnionRcvDetail(asnReceiveInfo);
            //更新收货明细状态及ASN的收货数
            asnReceiveInfo.setCurrentQtyRcvEa(cancelQtyEa);
            asnReceiveInfo = this.updateCancelReceivingStatus(asnReceiveInfo);
            //更新ASN单单状态
            inboundUpdateStatusService.updateAsnStatus(asnReceive.getAsnId());
            //释放收货容器号
            String pallet = asnReceive.getRcvPallet();
            if (EmptyUtil.isNotEmpty(pallet)&&!IdRuleConstant.TRACE_ID.equals(pallet)) {
                //palletService.updateIsUsed(pallet,asnReceive.getWarehouseId(), SystemStatus.NO);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            return ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, asnReceiveInfo.getAsnNo(), asnReceiveInfo.getLineNo(), e.getMessage() );
        }
        return ResponseData.success();
    }

    /**
     *  取消收货前校验
     * @param receiving
     */
    protected ResponseData<AsnReceiveInfo> checkBeforeCancelReceiving(AsnReceiveInfo receiving) {
        //校验数据是否过期
        AsnReceive querySku = inboundCommonService.getByAsnIdAndLineNo(receiving.getAsnId(), receiving.getLineNo());
        if (null == querySku) {
            return ResponseData.error(MsgConstant.ASN_RECEIVE_DATE_NULL);
        }
        String status = querySku.getReceiveStatus();
        //收货明细为创建状态
        if ("00".equals(status)) {
            return ResponseData.error(MsgConstant.ASN_RECEIVE_STATUS_UN_RECEIVE);
        }
        // 入库单状态为90或99时不能操作
        if ("90".equals(status)) {
            return ResponseData.error(MsgConstant.ASN_RECEIVE_STATUS_CANCEL);
        }
        //收货后上架前质检，需要删除质检单，才能取消收货。
        //收货前质检，生成质检单后，未完成质检，不能收货。完成质检后，可收货，可取消收货。
//        if (EmptyUtil.isNotEmpty(querySku.getQcStatus()) && "PA".equals(querySku.getQcPhase())) {
//            return ResponseData.error(MsgConstant.ASN_RECEIVE_QC_STATUS);
//        }
        //订单状态的校验
        ResponseData<AsnHeader> msg = inboundCommonService.checkAsnIsOperateStatus(receiving.getAsnId());
        if (!msg.getSuccess()) {
            return ResponseData.error(msg.getMsg());
        }
//        //存在创建状态的上架任务，不能取消收货
        return ResponseData.success();
    }



    /**
     *  取消收货明细时，查询来自同一个ASN明细行，且planID相同的创建收货明细，删除并将为收货数合并到取消行。
     *  如果存在计划上架库位的明细，不合并。
     *  收货前质检，质检确认后，拆分收货明细生成质检收货明细号。取消收货时，相同质检收货明细号，合并。
     *  如果是质检后的取消收货，容器号，收货库位，批次4，取质检结果。
     *  生成越库任务，标记CR_RCV_ID
     * @param asnReceiveInfo
     * @return
     */
    public AsnReceiveInfo cancelRcvUnionRcvDetail(AsnReceiveInfo asnReceiveInfo) {
        //ASN SKU明细
        AsnDetail wmAsnDetailModel = inboundCommonService.getByAsnIdAndLineNos(asnReceiveInfo.getAsnId().toString(), asnReceiveInfo.getAsnLineNo());
        //取消时的收货数=计划数。
        BigDecimal qtyRcvEa = asnReceiveInfo.getQtyPlanEa();

        //查询出同一个planId的创建状态的收货明细
        AsnReceiveCondition asnReceiveCondition = new AsnReceiveCondition();
        asnReceiveCondition.setAsnLineNo(asnReceiveInfo.getAsnLineNo());
        asnReceiveCondition.setId(asnReceiveInfo.getAsnId());
        asnReceiveCondition.setPlanPalletNum(asnReceiveInfo.getPlanPalletNum());
        asnReceiveCondition.setStatus("00");
        List<AsnReceive> records = dao.findByCancleReceive(asnReceiveCondition);

        String[] asnReceiveIds = new String[records.size()];
        int i = 0;
        for (int s = 0; s < records.size(); s++) {
            AsnReceive asnReceive = records.get(s);
            asnReceiveIds[i++] = asnReceive.getId().toString();
            qtyRcvEa = qtyRcvEa.add(asnReceive.getQtyPlanEa());
        }
        //取消收货的明细，恢复到原来的值
        //如果是质检后，取消收货的，还原到质检确认时的信息
        asnReceiveInfo.setRcvPallet(asnReceiveInfo.getPlanPalletNum());
        asnReceiveInfo.setRcvLocId(wmAsnDetailModel.getPlanToLocId());
        asnReceiveInfo.setRcvLocName(wmAsnDetailModel.getPlanToLocName());
        asnReceiveInfo.setLotAtt04(wmAsnDetailModel.getLotAtt04());
        asnReceiveInfo.setQtyPlanEa(qtyRcvEa);
        asnReceiveInfo.setQtyPlanUom(qtyRcvEa);
        asnReceiveInfo.setQtyRcvEa(BigDecimal.ZERO);
        asnReceiveInfo.setLotNum(null);
        //清空计划上架库位
        asnReceiveInfo.setLotAtt01(wmAsnDetailModel.getLotAtt01());
        asnReceiveInfo.setLotAtt02(wmAsnDetailModel.getLotAtt02());
        asnReceiveInfo.setLotAtt03(wmAsnDetailModel.getLotAtt03());
        asnReceiveInfo.setLotAtt04(wmAsnDetailModel.getLotAtt04());
        asnReceiveInfo.setLotAtt05(wmAsnDetailModel.getLotAtt05());
        asnReceiveInfo.setLotAtt06(wmAsnDetailModel.getLotAtt06());
        asnReceiveInfo.setLotAtt07(wmAsnDetailModel.getLotAtt07());
        asnReceiveInfo.setLotAtt08(wmAsnDetailModel.getLotAtt08());
        asnReceiveInfo.setLotAtt09(wmAsnDetailModel.getLotAtt09());
        asnReceiveInfo.setLotAtt10(wmAsnDetailModel.getLotAtt10());
        asnReceiveInfo.setLotAtt11(wmAsnDetailModel.getLotAtt11());
        asnReceiveInfo.setLotAtt12(wmAsnDetailModel.getLotAtt12());

        //清空PAID
        asnReceiveInfo.setRvcTime(null);
        //更新取消行，删除创建行
        if(asnReceiveIds.length > 0){
            this.deleteByIds(asnReceiveIds);
        }
        return inboundCommonService.saveAsnDetailReceiveEntity(asnReceiveInfo);
    }

    /**
     * 取消收货时，更新状态，ASN的收货数
     * @param csAsnReceiveInfo
     * @return
     */
    public AsnReceiveInfo updateCancelReceivingStatus(AsnReceiveInfo csAsnReceiveInfo) {
        //需取消的收货数
        BigDecimal cancelQytRcvEa = csAsnReceiveInfo.getCurrentQtyRcvEa();
        //更新收货明细
        csAsnReceiveInfo.setQtyRcvEa(BigDecimal.ZERO);
        csAsnReceiveInfo.setStatus(AsnStatus.ASN_NEW);
        csAsnReceiveInfo = inboundCommonService.saveAsnDetailReceiveEntity(csAsnReceiveInfo);
        //更新ASN明细
        String status = "";
        Long asnId = csAsnReceiveInfo.getAsnId();
        String asnLineNo = csAsnReceiveInfo.getAsnLineNo();
        AsnDetail csAsnDetail = inboundCommonService.getByAsnIdAndLineNos(asnId.toString(), asnLineNo);
        BigDecimal receivedEaAsn = csAsnDetail.getQtyRcvEa();//已收数
        BigDecimal expectedEaAsn = csAsnDetail.getQtyPlanEa();//明细行预收数
        //已收货=取消收货数
        if (receivedEaAsn.subtract(expectedEaAsn).compareTo(BigDecimal.ZERO) == 0) {
            status = AsnStatus.ASN_NEW;//创建
        } else if (expectedEaAsn.compareTo(receivedEaAsn.subtract(cancelQytRcvEa)) < 0) {//预收数<已收货-取消收货数
            status = AsnStatus.ASN_FULL_RECEIVING;//完全收货
        } else if (receivedEaAsn.compareTo(cancelQytRcvEa) > 0) {//已收数>取消收货数
            status = AsnStatus.ASN_PART_RECEIVING;//部分收货
        }
        //更新ASN明细的收货数
        csAsnDetail.setQtyRcvEa(receivedEaAsn.subtract(cancelQytRcvEa));
        csAsnDetail.setStatus(status);
        asnDetailDao.update(csAsnDetail);
        return csAsnReceiveInfo;
    }

    /**
     * 根据批量ansId删除入库收货明细
     * @param ids ID集合
     */
    public void deleteByAsnIds(Long[] ids) {
        if (EmptyUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.DELETE_FAILED);
        }
        AsnReceiveCondition condition = new AsnReceiveCondition();
        condition.setAsnIdArray(ids);
        dao.updateByAsnId(condition);
    }

    /**
     * 批量收货确认
     * @param confirmationItem
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean receiptConfirmationBatch(ReceiptConfirmationItem confirmationItem) {
        //前置校验
        if (CollUtil.isEmpty(confirmationItem.getReceiptDetails())){
            throw new ServiceException(AsnExceptionEnum.ASN_NO_NOT_EMPTY);
        }
        //验证必填项批次属性是否存在，是否传值
        validateBatchRequiredFields(confirmationItem.getReceiptDetails());
        //提前定义好需要收货确认的商品集合列表
        List<AsnDetailByReceiveItem> skuReceiveList = new ArrayList<>();
        //根据入库单拿到入库明细
        for (ReceiptConfirmationItem.ReceiptDetail detail : confirmationItem.getReceiptDetails()) {
            //先判断状态是否是完全收货
            AsnHeader asnHeader = asnHeaderDao.findFirst(new ConditionRule().andEqual(AsnDetail::getAsnNo, detail.getAsnNo()));
            if (SoOrderStatusEnum.CANCEL.getCode().equals(asnHeader.getStatus())
                    || SoOrderStatusEnum.CLOSE.getCode().equals(asnHeader.getStatus())){
                throw new ServiceException(AsnExceptionEnum.ORDER_STATUS_CANCEL, detail.getAsnNo());
            }
            if (ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode().equals(asnHeader.getReceiveStatus())) {
                //完全收货，不允许再次自动确认收货
                throw new ServiceException(AsnExceptionEnum.RECEIVE_STATUS_ALL_RECEIVED, detail.getAsnNo());
            }
            List<AsnDetailByReceiveItem> asnDetailByReceiveItems = asnDetailDao.find(AsnDetailByReceiveItem.class, new ConditionRule().andEqual(AsnDetail::getAsnNo, detail.getAsnNo()));
            if (CollUtil.isEmpty(asnDetailByReceiveItems)){
                throw new ServiceException(AsnExceptionEnum.ASN_NO_NOT_SUCCESS);
            }
            //判断订单状态是否是取消状态
            if (SoOrderStatusEnum.CANCEL.getCode().equals(asnHeader.getStatus())){
                throw new ServiceException(AsnExceptionEnum.ORDER_STATUS_CANCEL, detail.getAsnNo());
            }
            //需要收货的商品
            List<AsnDetailByReceiveItem> receiveList = new ArrayList<>();
            for (AsnDetailByReceiveItem item : asnDetailByReceiveItems) {
                if(ObjUtil.isNotNull(item.getQtyRcvEa())){
                    if (item.getQtyPlanEa().compareTo(item.getQtyRcvEa()) > 0) {
                        receiveList.add(item);
                        //将商品添加到集合，外部进行收货确认时使用
                        skuReceiveList.add(item);
                    }
                }else{
                    receiveList.add(item);
                    skuReceiveList.add(item);
                }
            }
            if (CollUtil.isEmpty(receiveList)){
                throw new ServiceException(AsnExceptionEnum.RECEIVE_STATUS_ALL_RECEIVED, detail.getAsnNo());
            }
        }
        //对每个商品以 asnId 进行分组，并使用线程进行并行执行，以减少阻塞时间
        Map<String, List<AsnDetailByReceiveItem>> groupReceiveMap = skuReceiveList.stream().collect(Collectors.groupingBy(AsnDetailByReceiveItem::getAsnNo));
        SessionUserInfo userInfo = SessionContext.getSessionUserInfo();
        List<CompletableFuture<Boolean>> futures = new ArrayList<>();
        for (String id : groupReceiveMap.keySet()) {
            List<AsnDetailByReceiveItem> detail = groupReceiveMap.get(id);
            //异步收货 使用线程执行每一条数据并在循环外去提交任务，并等待线程执行完成
            //通过Bean的方式调用异步线程，保持同类中不同方法的线程可用性
            log.info("批量收货： asnNo:{}, detailList:{}",id,JSONUtil.toJsonStr(detail));
            futures.add(CompletableFuture.supplyAsync(() -> asynchronousReceive(detail,id,userInfo),executorService));
            //加入到列表
        }
        // 等待所有任务完成
        StringBuilder bui = new StringBuilder();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        for (CompletableFuture<Boolean> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof ServiceException serviceException){
                    bui.append(cause.getMessage()).append(",");
                }else{
                    log.error("线程执行失败,系统异常",e);
                    throw new ServiceException(AsnExceptionEnum.RECEIVE_BATCH_ERROR);
                }
            }
        }
        //验证是否存在错误信息，如果存在则直接提示报错信息
        if (EmptyUtil.isNotEmpty(bui.toString())){
            String msg = bui.deleteCharAt(bui.length() - 1).toString();
            throw new ServiceException(msg);
        }
        //验证执行结果
        List<String> asnNoList = confirmationItem.getReceiptDetails().stream().map(ReceiptConfirmationItem.ReceiptDetail::getAsnNo).toList();
        List<AsnHeader> headerList = asnHeaderDao.find(new ConditionRule().andIn(AsnHeader::getAsnNo, asnNoList));
        AsnHeader first = headerList.getFirst();
        if (StrUtil.isNotEmpty(first.getMergeNo())){
            BigDecimal rcv = BigDecimal.ZERO;
            for (AsnDetailByReceiveItem asnDetailByReceiveItem : skuReceiveList) {
                if (ObjUtil.isNull(asnDetailByReceiveItem.getQtyRcvEa())){
                    asnDetailByReceiveItem.setQtyRcvEa(BigDecimal.ZERO);
                }
                rcv = rcv.add(asnDetailByReceiveItem.getQtyRcvEa());
            }
            AsnMergeReceive receive = asnMergeReceiveDao.findById(first.getMergeId());
            receive.setStatus(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
            receive.setQtyRcvEa(receive.getQtyRcvEa().add(rcv));
            asnMergeReceiveDao.update(receive);
        }
        return Boolean.TRUE;
    }

    /**
     * 验证必填批次属性
     * @param list
     */
    private void validateBatchRequiredFields(List<ReceiptConfirmationItem.ReceiptDetail> list) {
        List<String> asnNoList = list.stream().map(ReceiptConfirmationItem.ReceiptDetail::getAsnNo).toList();
        List<AsnDetail> asnDetails = asnDetailDao.find(new ConditionRule().andIn(AsnDetail::getAsnNo, asnNoList));
        Map<String, List<AsnDetail>> asnNoGroupMap = asnDetails.stream().collect(Collectors.groupingBy(AsnDetail::getAsnNo));
        Set<Long> skuIdArray = asnNoGroupMap.values().stream()
                .flatMap(List::stream)
                .filter(Objects::nonNull)  // 过滤 null 对象
                .map(AsnDetail::getSkuId)
                .filter(Objects::nonNull)  // 过滤 null skuId
                .collect(Collectors.toSet());
        List<Sku> skus = skuService.find(new ConditionRule().andIn(Sku::getId, skuIdArray));
        List<Long> lotIdLisdt = skus.stream().map(Sku::getLotId).filter(Objects::nonNull).toList();
        List<WarehouseLotDetail> detailList = warehouseLotDetailService.find(new ConditionRule().andIn(WarehouseLotDetail::getLotId, lotIdLisdt));
        List<WarehouseLotDetail> details = detailList.stream().filter(f -> YesNoEnum.YES.getCode().equals(f.getInputControl())).toList();
        if (CollUtil.isEmpty(details)) {
            return;
        }
        Map<Long, List<WarehouseLotDetail>> lotIdGroupMap = details.stream().collect(Collectors.groupingBy(WarehouseLotDetail::getLotId));
        Map<Long, Sku> skuMap = skus.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (one, two) -> one));
        StringBuffer sbError = new StringBuffer();
        for (String key : asnNoGroupMap.keySet()) {
            //验证是否存在标签号
            Long count = passiveTagDao.count(new ConditionRule().andEqual(PassiveTag::getAsnNo, key));
            if (count > 0){
                throw new ServiceException(AsnExceptionEnum.ORDER_BY_PASSIVES_NOT_RECEIVE);
            }
            for (AsnDetail asnDetail : asnNoGroupMap.get(key)) {
                Sku sku = skuMap.get(asnDetail.getSkuId());
                List<WarehouseLotDetail> lotDetailList = lotIdGroupMap.get(sku.getLotId());
                if (CollUtil.isEmpty(lotDetailList)) {
                    continue;
                }
                boolean lotValid = Boolean.TRUE;
                for (WarehouseLotDetail warehouseLotDetail : lotDetailList) {
                    String attNumber = warehouseLotDetail.getLotAtt().substring(warehouseLotDetail.getLotAtt().length() - 2);
                    Object invoke = ReflectUtil.invoke(asnDetail, "getLotAtt" + attNumber);
                    if (ObjUtil.isEmpty(invoke)) {
                        lotValid = Boolean.FALSE;
                        break;
                    }
                }
                if (!lotValid) {
                    //存在必填项为空的数据
                    sbError.append(asnDetail.getAsnNo()).append("、");
                    break;
                }
            }
        }
        if (!sbError.isEmpty()) {
            throw new ServiceException(AsnExceptionEnum.ASN_RCV_ERROR_BY_NOT_REQUIRED_ATT,sbError.deleteCharAt(sbError.length() - 1).toString());
        }
    }


    private String capitalizeFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    /**
     * 异步收货
     * @param receiveList
     */
    public Boolean asynchronousReceive(List<AsnDetailByReceiveItem> receiveList,String asnNo,SessionUserInfo userInfo){
        try {
            //将用户加入到上下文中
            SessionContext.put(userInfo);
            AsnHeader asnHeader = asnHeaderDao.findFirst(new ConditionRule().andEqual(AsnHeader::getAsnNo, asnNo));
            WarehouseLoc warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), asnHeader.getWarehouseId());
            Warehouse warehouse = warehouseService.getById(asnHeader.getWarehouseId());
            Owner owner = ownerService.findById(asnHeader.getOwnerId());
            //针对单个商品进行收货确认,这里不使用线程
            List<AsnDetailSn> snList = new ArrayList<>();
            BigDecimal headerRcvEa = BigDecimal.ZERO;
            for (AsnDetailByReceiveItem item : receiveList) {
                //增加收货明细
                AsnReceive asnReceive = new AsnReceive();
                //查询入库单明细
                AsnDetail detail = asnDetailDao.findFirst(new ConditionRule().andEqual(AsnDetail::getId,item.getId()));
                asnReceive.setAsnDetailId(detail.getId());
                asnReceive.setReceiveNo(numberGenerator.nextValue(IdRuleConstant.RECEIVE_NO));
                asnReceive.setAsnCode(detail.getAsnNo());
                asnReceive.setLineNo(detail.getLineNo());
                asnReceive.setReceiveStatus(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
                asnReceive.setSkuCode(item.getSkuCode());
                //根据code 查询商品信息
                Sku sku = skuService.findFirst(new ConditionRule().andEqual(Sku::getSkuCode, item.getSkuCode()));
                asnReceive.setPackageId(detail.getPackageId());
                asnReceive.setPackageUnitId(detail.getPackageUnitId());
                asnReceive.setPackageUnitName(detail.getPackageUnitName());
                if (ObjUtil.isNotNull(detail.getQtyPlanEa())){
                    if (ObjUtil.isNull(detail.getQtyRcvEa())){
                        detail.setQtyRcvEa(BigDecimal.ZERO);
                    }
                    asnReceive.setQtyRcvEa(detail.getQtyPlanEa().subtract(detail.getQtyRcvEa()));
                }else{
                    asnReceive.setQtyRcvEa(detail.getQtyPlanEa());
                }
                asnReceive.setQtyPlanEa(detail.getQtyPlanEa());

                asnReceive.setRcvLocId(detail.getPlanToLocId());
                asnReceive.setRcvLocName(detail.getPlanToLocName());
                //asnReceive.setRcvPalletId(detail.getPlanToPalletId());
                //asnReceive.setRcvPallet(detail.getPlanToPallet());
                asnReceive.setRvcTime(new Date());
                //查询批次属性
                WarehouseLotHeader warehouseLot = warehouseLotHeaderDao.findById(sku.getLotId());

                //InvLotAtt lotAtt = invLotAttService.getByLotNum(warehouseLot.getLotCode(), warehouseLot.getId());
                asnReceive.setLotAtt01(detail.getLotAtt01());
                asnReceive.setLotAtt02(detail.getLotAtt02());
                asnReceive.setLotAtt03(detail.getLotAtt03());
                asnReceive.setLotAtt04(detail.getLotAtt04());
                asnReceive.setLotAtt05(detail.getLotAtt05());
                asnReceive.setLotAtt06(detail.getLotAtt06());
                asnReceive.setLotAtt07(detail.getLotAtt07());
                asnReceive.setLotAtt08(detail.getLotAtt08());
                asnReceive.setLotAtt09(detail.getLotAtt09());
                asnReceive.setLotAtt10(detail.getLotAtt10());
                asnReceive.setLotAtt11(detail.getLotAtt11());
                asnReceive.setLotAtt12(detail.getLotAtt12());
                asnReceive.setLotNum(warehouseLot.getLotCode());
                asnReceive.setWhetherShelf(YesNoEnum.NO.getCode());
                asnReceive.setWhetherQc(YesNoEnum.NO.getCode());
                asnReceive.setPaPersonName(detail.getPaPersonName());
                asnReceive.setPaPersonId(detail.getPaPersonId());
                preSave(asnReceive);
                //WarehouseLoc warehouseLoc = warehouseLocService.findById(detail.getPlanToLocId());
                asnReceive.setAsnId(asnHeader.getId());
                Map<String, Object> attMap = getAttMap(asnReceive);
                //获取批次号
                String locNum =  stockService.getStockCommonBaseServices(new InvLotAttQuery().setAttMap(attMap)
                        .setOwnerId(sku.getOwnerId())
                        .setSkuId(sku.getId())
                        .setWarehouseId(asnHeader.getWarehouseId()));
                asnReceive.setLotNum(locNum);
                asnReceive.setRcvLocId(warehouseLoc.getId());
                asnReceive.setRcvLocName(warehouseLoc.getLocName());
                dao.insert(asnReceive);
                List<AsnDetailSn> detailSns = asnDetailSnService.find(new ConditionRule().andEqual(AsnDetailSn::getAsnDetailId, detail.getId()).andNotEqual(AsnDetailSn::getReceiveStatus, ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode()));
                if (CollUtil.isNotEmpty(detailSns)){
                    detailSns.forEach(detailSn -> {
                        detailSn.setReceiveStatus(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
                        detailSn.setReceiveCode(asnReceive.getReceiveNo());
                        detailSn.setAsnDetailId(detail.getId());
                    });
                    log.info("批量收货异步方法：存储序列号商品 detailId：{}，商品SN集合：{}"
                            ,detail.getId(),JSONUtil.toJsonStr(detailSns.stream().map(AsnDetailSn::getSnNo).toList()));
                    snList.addAll(detailSns);
                    //记录日志
                    traceBackRecordService.execute(new TraceBackRecordItem()
                            .setBusinessNo(asnHeader.getAsnNo())
                            .setBusinessType(TraceBackBusinessTypeEnum.RECEIVING)
                            .setSourceNo(asnReceive.getAsnCode())
                            .setWarehouseName(warehouse.getWarehouseName())
                            .setOwnerName(owner.getOwnerName())
                            .setSnNoList(detailSns.stream().map(AsnDetailSn::getSnNo).toList()));
                }
                log.info("批量收货异步方法：执行库存 detailId：{}，orderNo:{},仓库ID：{}，商品code：{}，更新条数：{}，批次号：{}，货主ID：{}，商品SN集合：{}"
                        ,detail.getId(),detail.getAsnNo(),asnHeader.getWarehouseId(),sku.getSkuCode(),asnReceive.getQtyRcvEa(),locNum
                    ,sku.getOwnerId(),CollUtil.isNotEmpty(detailSns) ? JSONUtil.toJsonStr(detailSns.stream().map(AsnDetailSn::getSnNo).toList()) : null);
                //更新库存
                stockService.exec(new InvLotLocQtyBO()
                        .setOrderNo(detail.getAsnNo())
                        .setWarehouseId(asnHeader.getWarehouseId())
                        .setSkuId(sku.getId())
                        .setUpdateNum(asnReceive.getQtyRcvEa())
                        .setLotNum(locNum)
                        .setOwnerId(sku.getOwnerId())
                        .setSkuSn(CollUtil.isNotEmpty(detailSns) ? detailSns.stream().map(AsnDetailSn::getSnNo).toList() : null)
                        .setTransactionType(TransactionType.TRAN_RCV));

                //修改入库单明细收货数明细
                detail.setQtyRcvEa(detail.getQtyPlanEa());
                detail.setStatus(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
                asnDetailDao.update(detail);
                item.setQtyRcvEa(asnReceive.getQtyRcvEa());
                headerRcvEa = headerRcvEa.add(asnReceive.getQtyRcvEa());
                expiryWarningService.addExpiryWarning(ExpiryWarningSaveStageEnum.INBOUND.getCode(), locNum, sku.getOwnerId(), asnHeader.getWarehouseId(), sku.getId(), asnReceive.getAsnCode());
                stockWarningService.stockSave(locNum,asnReceive.getLotAtt01(), sku.getOwnerId(), asnHeader.getWarehouseId(), sku.getId());
                modifyRelationOrderStatus(Arrays.asList(asnHeader));
                pushTransportationDoc(asnReceive,asnHeader.getWarehouseId());
                if (StrUtil.isNotEmpty(asnHeader.getLogisticNo())){
                    //推送数据
                    ReceiveUpdateIncInvItem invItem = new ReceiveUpdateIncInvItem();
                    invItem.setAction(ActionCode.RECEIVING);
                    invItem.setWarehouseCode(warehouse.getUpWarehouseCode());
                    invItem.setOwnerCode(owner.getUpstreamOwnerCode());
                    invItem.setAsnNo(asnHeader.getLogisticNo());
                    invItem.setSkuCode(sku.getUpSkuCode());
                    invItem.setLocCode(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode());
                    CrzReceiveItem crzReceiveItem = new CrzReceiveItem();
                    BeanUtil.copyProperties(asnReceive,crzReceiveItem);
                    invItem.setAsnReceive(crzReceiveItem);
                    remoteAsnStatusPushToCrzFegin.updateTotalInventory(invItem);
                }
            }
            //更新收货状态
            asnHeader.setReceiveStatus(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
            asnHeaderDao.update(asnHeader);
            asnDetailSnService.batchUpdate(snList);
            modifyRelationOrderStatus(Arrays.asList(asnHeader));
            return true;
        } catch (ServiceException e) {
            log.error("ASN {} 异步收货失败", asnNo, e);
            // 可以记录失败状态或发送通知
            throw e;
        } catch (Exception e) {
            log.error(StrUtil.format("ASN {} 异步收货失败", asnNo), e);
            // 可以记录失败状态或发送通知
            throw new ServiceException(GlobalExceptionEnum.SYSTEM_ERROR);
        }
    }

    private void pushTransportationDoc(AsnReceive asnReceive,Long warehouseId) {
        taskExecutor.execute(() -> {
            //收货单集合
            List<String> paNoList = List.of(asnReceive.getReceiveNo());
            List<LogisticsFolderDetailLinkCondition> linkConditions = new ArrayList<>();
            linkConditions.add(new LogisticsFolderDetailLinkCondition().setDocumentNos(paNoList).setUpDocumentNo(asnReceive.getAsnCode()));
            //这⾥第三个参数，为上级的单号，这⾥需替换成⼊库单号号码
            logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                    DocumentTypeEnum.RECEIVING,linkConditions,
                    SessionContext.getSessionUserInfo().getCompanyId(),
                    SessionContext.getSessionUserInfo().getTenancy(),
                    warehouseId);
            //这⾥第三个参数，为上级的单号，这⾥需替换成⼊库单号号码
            logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                    DocumentTypeEnum.ASN_RECEIVING,linkConditions,
                    SessionContext.getSessionUserInfo().getCompanyId(),
                    SessionContext.getSessionUserInfo().getTenancy(),
                    warehouseId);
        });
    }

    /**
     * 通过id集合更新是否上架
     * @Date 10:05 2025/1/13
     * @Param [ids, whetherShelf]
     * @return void
     **/
    public void updateWhetherShelfByIds(List<Long> ids, String whetherShelf) {
        AsnReceiveWhetherShelfItem asnReceiveWhetherShelfItem = new AsnReceiveWhetherShelfItem();
        asnReceiveWhetherShelfItem.setWhetherShelf(whetherShelf);
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(AsnReceive::getId, ids);
        dao.updateByCondition(asnReceiveWhetherShelfItem, conditionRule);
    }

    /**
     * 根据入库单号和商品code查询收货明细列表
     */
    public List<AsnReceiveDetailQuery> queryListByAsnNoAndSkuCode(AsnReceiveDetailCondition detailCondition) {
        List<AsnReceive> list;
        if (StrUtil.isEmpty(detailCondition.getIsMerge()) || YesNoEnum.NO.getCode().equals(detailCondition.getIsMerge())){
            list = dao.find(new ConditionRule().andEqual(AsnReceive::getAsnDetailId,detailCondition.getAsnDetailId()),
                    new OrderRule().addDescOrder(AsnReceive.COL_NAME_CREATE_TIME));
        }else{
            list = dao.find(new ConditionRule().andIn(AsnReceive::getAsnDetailId,detailCondition.getAsnDetailIds()),
                    new OrderRule().addDescOrder(AsnReceive.COL_NAME_CREATE_TIME));
        }
        List<AsnReceiveDetailQuery> result = new ArrayList<>();
        //后置处理
        if (CollUtil.isNotEmpty(list)){
            Set<String> gysIdList = Stream.concat(
                            list.stream().map(AsnReceive::getLotAtt04),
                            list.stream().map(AsnReceive::getLotAtt02)
                    )
                    .filter(item -> StrUtil.isNotEmpty(item) && NumberUtil.isNumber(item))
                    .collect(Collectors.toSet());
            Map<String, Supplier> supplierMap = new HashMap<>();
            if (CollUtil.isNotEmpty(gysIdList)){
                List<Long> byIds = new ArrayList<>();
                gysIdList.forEach(item -> {
                    byIds.add(Convert.toLong(item));
                });
                List<Supplier> supplierList = supplierService.find(new ConditionRule().andIn(Supplier::getId, byIds));
                if (CollUtil.isNotEmpty(supplierList)){
                    supplierMap = supplierList.stream().collect(Collectors.toMap(item -> Convert.toStr(item.getId()), item -> item));
                }
            }
            AsnHeader asnHeader = asnHeaderDao.findFirst(new ConditionRule().andEqual(AsnHeader::getAsnNo, list.getFirst().getAsnCode()));
            Map<String, Supplier> finalSupplierMap = supplierMap;
            list.stream().forEach(item -> {
                AsnReceiveDetailQuery receiveItem = new AsnReceiveDetailQuery();
                BeanUtil.copyProperties(item, receiveItem);
                Supplier supplier = finalSupplierMap.get(item.getLotAtt04());
                if (ObjUtil.isEmpty(supplier)){
                    supplier = finalSupplierMap.get(item.getLotAtt02());
                }
                if (ObjUtil.isNotNull(supplier)){
                    receiveItem.setSupplierName(supplier.getSupplierName());
                }
                result.add(receiveItem);
            });
            Map<String, List<AsnReceiveDetailQuery>> map = result.stream().collect(Collectors.groupingBy(AsnReceiveDetailQuery::getSkuCode));
            List<Sku> skus = skuService.find(new ConditionRule().andIn(Sku::getSkuCode, map.keySet()));
            if (CollUtil.isNotEmpty(skus)){
                Map<String,Sku> skuCodes = skus.stream().collect(Collectors.toMap(Sku::getSkuCode, item-> item,(one,two) -> one));
                skuCodes.forEach((key,value) -> {
                    List<AsnReceiveDetailQuery> receives = map.get(key);
                    if (CollUtil.isNotEmpty(receives)){
                        receives.forEach(receive -> {
                            receive.setSkuName(value.getSkuName());
                            receive.setSkuId(value.getId());
                        });
                    }
                });
            }
            result.forEach(item -> {
                item.setReceiverOp(asnHeader.getReceiverOp());
                if (ObjUtil.isNotNull(item.getRcvLocId())){
                    WarehouseLoc warehouseLoc = warehouseLocService.findById(item.getRcvLocId());
                    item.setShelfName(warehouseLoc.getShelfName());
                    item.setZoneName(warehouseLoc.getZoneName());
                }
            });
            // 批次号条形码文件
            List<String> lotNumList = result.stream().map(AsnReceiveDetailQuery::getLotNum)
                    .filter(EmptyUtil::isNotEmpty).distinct().toList();
            List<AsnReceiveFile> fileList =
                    asnReceiveFileDao.find(new ConditionRule().andIn(AsnReceiveFile::getBusinessId, lotNumList));
            if (CollUtil.isNotEmpty(fileList)){
                Map<String, String> lotNumFileIdMap = fileList.stream().collect(Collectors.toMap(AsnReceiveFile::getBusinessId, AsnReceiveFile::getFileId));
                result.forEach(item -> {
                    String fileId = lotNumFileIdMap.get(item.getLotNum());
                    item.setFileId(fileId);
                });
            }
            //取出已上架任务
            List<AsnReceiveDetailQuery> whetherShelfReceiveList = result.stream().filter(item -> YesNoEnum.YES.getCode().equals(item.getWhetherShelf())).toList();
            if (CollUtil.isNotEmpty(whetherShelfReceiveList)){
                List<String> receiveNoList = whetherShelfReceiveList.stream().map(AsnReceiveDetailQuery::getReceiveNo).toList();
                if (CollUtil.isNotEmpty(receiveNoList)){
                    List<CsPaTask> csPaTasks = csPaTaskService.find(new ConditionRule().andIn(CsPaTask::getReceiveNo, receiveNoList));
                    Map<String, CsPaTask> taskMap = csPaTasks.stream().collect(Collectors.toMap(CsPaTask::getReceiveNo, Function.identity(), (one, two) -> one));
                    whetherShelfReceiveList.forEach(item -> {
                        CsPaTask csPaTask = taskMap.get(item.getReceiveNo());
                        if (PaStatusEnum.SHELVESED.getCode().equals(csPaTask.getStatus())) {
                            item.setWhetherShelfComplete(YesNoEnum.YES.getCode());
                        }
                    });
                }
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean receiptConfirmation(AsnReceiptDetailItem reveipt) {
        //增加收货明细
        AsnReceive asnReceive = new AsnReceive();
        //查询入库单明细
        AsnDetail detail = asnDetailDao.findById(reveipt.getId());
        //判断订单状态是否是取消状态
        AsnHeader headerDaoFirst = asnHeaderDao.findFirst(new ConditionRule().andEqual(AsnHeader::getAsnNo, detail.getAsnNo()));
        if (SoOrderStatusEnum.CANCEL.getCode().equals(headerDaoFirst.getStatus())
            || SoOrderStatusEnum.CLOSE.getCode().equals(headerDaoFirst.getStatus())){
            throw new ServiceException(AsnExceptionEnum.ORDER_STATUS_CANCEL, detail.getAsnNo());
        }
        if (StrUtil.isEmpty(reveipt.getTagNo())){
            //验证是否存在标签号
            Long count = passiveTagDao.count(new ConditionRule().andEqual(PassiveTag::getAsnNo, headerDaoFirst.getAsnNo()));
            if (count > 0){
                throw new ServiceException(AsnExceptionEnum.ORDER_BY_PASSIVES_NOT_RECEIVE);
            }
        }
        asnReceive.setAsnCode(reveipt.getAsnNo());
        asnReceive.setLineNo(detail.getLineNo());
        //验证是否是完全收货
        List<AsnReceive> list = dao.find(new ConditionRule().andEqual(AsnReceive::getAsnDetailId,detail.getId()));
        if (CollUtil.isEmpty(list)){
            if (reveipt.getQtyRcvEa().compareTo(reveipt.getQtyPlanEa()) >= 0) {
                //完全收货
                asnReceive.setReceiveStatus(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
            }else {
                //部分收货
                asnReceive.setReceiveStatus(ReceiveStatusEnum.PART_RECEIVED_GOODS.getCode());
            }
        }else{
            if (StrUtil.isNotEmpty(reveipt.getReceiveNo())){
                list =
                        list.stream().filter(item -> !reveipt.getReceiveNo().equals(item.getReceiveNo())).collect(Collectors.toList());
            }
            BigDecimal sum = list.stream().map(AsnReceive::getQtyRcvEa).reduce(BigDecimal.ZERO, BigDecimal::add);
            sum = sum.add(reveipt.getQtyRcvEa());
            //验证 sum 是否大于 reveipt中的getQtyPlanEa字段
            if (sum.compareTo(reveipt.getQtyPlanEa()) >= 0) {
                //完全收货
                asnReceive.setReceiveStatus(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
            }else {
                //部分收货
                asnReceive.setReceiveStatus(ReceiveStatusEnum.PART_RECEIVED_GOODS.getCode());
            }
        }

        if (StrUtil.isNotEmpty(reveipt.getReceiveNo())){
            //编辑
            asnReceive.setReceiveNo(reveipt.getReceiveNo());
            asnReceive.setId(reveipt.getId());
        }else{
            asnReceive.setReceiveNo(numberGenerator.nextValue(IdRuleConstant.RECEIVE_NO));
        }
        reveipt.setReceiveNo(asnReceive.getReceiveNo());
        detail.setStatus(asnReceive.getReceiveStatus());
        asnReceive.setSkuCode(reveipt.getSkuCode());
        //根据code 查询商品信息
        Sku sku = skuService.findFirst(new ConditionRule().andEqual(Sku::getSkuCode, reveipt.getSkuCode()));
        asnReceive.setPackageId(detail.getPackageId());
        asnReceive.setPackageUnitId(detail.getPackageUnitId());
        asnReceive.setPackageUnitName(detail.getPackageUnitName());
        asnReceive.setQtyPlanEa(reveipt.getQtyPlanEa());
        asnReceive.setQtyRcvEa(reveipt.getQtyRcvEa());
        asnReceive.setAsnDetailId(detail.getId());
        asnReceive.setRvcPerson(SessionContext.getSessionUserInfo().getRealName());
        asnReceive.setRcvLocId(reveipt.getRcvLocId());
        asnReceive.setRcvLocName(reveipt.getRcvLocName());
        asnReceive.setRcvPalletId(reveipt.getRcvPalletId());
        asnReceive.setRcvPallet(reveipt.getRcvPallet());
        asnReceive.setRvcTime(new Date());
        //查询批次属性
        asnReceive.setLotAtt01(reveipt.getLotAtt01());
        asnReceive.setLotAtt02(reveipt.getLotAtt02());
        asnReceive.setLotAtt03(reveipt.getLotAtt03());
        asnReceive.setLotAtt04(reveipt.getLotAtt04());
        asnReceive.setLotAtt05(reveipt.getLotAtt05());
        asnReceive.setLotAtt06(reveipt.getLotAtt06());
        asnReceive.setLotAtt07(reveipt.getLotAtt07());
        asnReceive.setLotAtt08(reveipt.getLotAtt08());
        asnReceive.setLotAtt09(reveipt.getLotAtt09());
        asnReceive.setLotAtt10(reveipt.getLotAtt10());
        asnReceive.setLotAtt11(reveipt.getLotAtt11());
        asnReceive.setLotAtt12(reveipt.getLotAtt12());
        asnReceive.setPaPersonName(detail.getPaPersonName());
        asnReceive.setPaPersonId(detail.getPaPersonId());
        asnReceive.setWhetherShelf(YesNoEnum.NO.getCode());
        asnReceive.setWhetherQc(YesNoEnum.NO.getCode());
        asnReceive.setTagNo(reveipt.getTagNo());
        preSave(asnReceive);
        //更新总预售数
        if (CollUtil.isNotEmpty(list)){
            BigDecimal sum = list.stream().map(AsnReceive::getQtyRcvEa).reduce(BigDecimal.ZERO, BigDecimal::add);
            detail.setQtyRcvEa(sum.add(reveipt.getQtyRcvEa()));
        }else{
            detail.setQtyRcvEa(asnReceive.getQtyRcvEa());
        }
        Map<String, Object> attMap = getAttMap(asnReceive);
        //TODO 该位置暂时使用上游库位ID
//        WarehouseLoc warehouseLoc = warehouseLocService.findById(receiveId);
        AsnHeader asnHeader = asnHeaderDao.findFirst(new ConditionRule().andEqual(AsnHeader::getAsnNo, asnReceive.getAsnCode()));
        //获取批次号
        String locNum =  stockService.getStockCommonBaseServices(new InvLotAttQuery().setAttMap(attMap)
                .setOwnerId(sku.getOwnerId())
                .setSkuId(sku.getId())
                .setWarehouseId(asnHeader.getWarehouseId()));
        asnReceive.setLotNum(locNum);
        asnReceive.setAsnId(asnHeader.getId());
        WarehouseLoc warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), asnHeader.getWarehouseId());
        Warehouse warehouse = warehouseService.findById(asnHeader.getWarehouseId());
        asnReceive.setRcvLocId(warehouseLoc.getId());
        asnReceive.setRcvLocName(warehouseLoc.getLocName());
        if (StrUtil.isNotEmpty(reveipt.getReceiveNo())){
            //编辑
            dao.update(asnReceive);
        }else{
            dao.insert(asnReceive);
        }
        if (CollUtil.isNotEmpty(reveipt.getSkuSn())){
            reveipt.setSkuSn(reveipt.getSkuSn().stream().filter(StrUtil::isNotEmpty).toList());
            List<AsnDetailSn> detailSns = asnDetailSnService.find(new ConditionRule().andIn(AsnDetailSn::getSnNo, reveipt.getSkuSn()));
            detailSns.forEach(item -> {
                item.setReceiveStatus(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
                item.setReceiveCode(asnReceive.getReceiveNo());
                item.setAsnDetailId(detail.getId());
            });
            asnDetailSnService.batchUpdate(detailSns);
            //记录日志
            Owner owner = ownerService.findById(asnHeader.getOwnerId());

            traceBackRecordService.execute(new TraceBackRecordItem()
                    .setBusinessNo(asnReceive.getAsnCode())
                    .setBusinessType(TraceBackBusinessTypeEnum.RECEIVING)
                    .setSourceNo(asnReceive.getAsnCode())
                    .setOwnerName(owner.getOwnerName())
                    .setWarehouseName(warehouse.getWarehouseName())
                    .setSnNoList(reveipt.getSkuSn()));
        }
        //更新库存
        stockService.exec(new InvLotLocQtyBO()
                        .setOrderNo(detail.getAsnNo())
                .setWarehouseId(asnHeader.getWarehouseId())
                .setSkuId(sku.getId())
                .setUpdateNum(reveipt.getQtyRcvEa())
                .setPalletNum(reveipt.getRcvPallet())
                .setLotNum(locNum)
                .setOwnerId(sku.getOwnerId())
                .setSkuSn(reveipt.getSkuSn())
                .setTransactionType(TransactionType.TRAN_RCV));
        asnDetailDao.update(detail);
        List<AsnReceive> receives = dao.find(new ConditionRule().andEqual(AsnReceive::getAsnCode, asnReceive.getAsnCode()));
        List<AsnDetail> details = asnDetailDao.find(new ConditionRule().andEqual(AsnDetail::getAsnNo, detail.getAsnNo()));
        //receives 包含所有商品
        if (CollUtil.isNotEmpty(receives)){
            List<String> statusList = details.stream()
                    .map(AsnDetail::getStatus)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (statusList.isEmpty()) {
                asnHeader.setReceiveStatus(ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode());
            } else if (statusList.stream().allMatch(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode()::equals)) {
                asnHeader.setReceiveStatus(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
            } else {
                asnHeader.setReceiveStatus(ReceiveStatusEnum.PART_RECEIVED_GOODS.getCode());
            }
            asnHeaderDao.update(asnHeader);
            //效期预警
            expiryWarningService.addExpiryWarning(ExpiryWarningSaveStageEnum.INBOUND.getCode(), asnReceive.getLotNum(), sku.getOwnerId(), asnHeader.getWarehouseId(), sku.getId());
            // 滞库预警
            stockWarningService.stockSave(locNum,asnReceive.getLotAtt01(), sku.getOwnerId(), asnHeader.getWarehouseId(), sku.getId());
        }
        handleUpdateReceive(reveipt);
        modifyRelationOrderStatus(Arrays.asList(asnHeader));
        pushTransportationDoc(asnReceive,asnHeader.getWarehouseId());
        //验证是否需要推送数据到超然子,根据是否存在上游单号确定
        if (StrUtil.isNotEmpty(asnHeader.getLogisticNo())){
            //推送数据
            ReceiveUpdateIncInvItem item = new ReceiveUpdateIncInvItem();
            item.setAction(ActionCode.RECEIVING);

            Owner owner = ownerService.findById(asnHeader.getOwnerId());

            item.setWarehouseCode(warehouse.getUpWarehouseCode());
            item.setOwnerCode(owner.getUpstreamOwnerCode());
            item.setAsnNo(asnHeader.getLogisticNo());
            item.setSkuCode(sku.getUpSkuCode());
            item.setLocCode(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode());
            CrzReceiveItem crzReceiveItem = new CrzReceiveItem();
            BeanUtil.copyProperties(asnReceive,crzReceiveItem);
            item.setAsnReceive(crzReceiveItem);
            remoteAsnStatusPushToCrzFegin.updateTotalInventory(item);
        }
        return Boolean.TRUE;
    }

    /**
     *@Description 更新关联订单状态
     *@Param ids
     *@Return Void
     *@Date 2025/5/16 15:13
     *<AUTHOR>
     **/
    public void modifyRelationOrderStatus(List<AsnHeader> asnHeaderList) {
        List<AsnHeader> relationAsnHeaderList = asnHeaderList.stream().filter(x -> YesNoEnum.YES.getCode().equals(x.getRelationOrder())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(relationAsnHeaderList)){
            List<String> businessNoList = relationAsnHeaderList.stream().map(AsnHeader::getAsnNo).distinct().collect(Collectors.toList());
            // 根据入库单号查询关联信息
            List<OrderRelation> orderRelationList = orderRelationService.findByBusinessNo(businessNoList);
            if(CollectionUtil.isNotEmpty(orderRelationList)){
                //抽取订单号
                List<String> orderNoList = orderRelationList.stream().map(OrderRelation::getOrderNo).distinct().collect(Collectors.toList());
                //根据订单号集合查询订单对应的所有的关联数据
                List<OrderRelation> allOrderRelationList = orderRelationService.findByOrderNoList(orderNoList);
                //转map
                Map<String,List<OrderRelation>> allOrderRelationMap = allOrderRelationList.stream().collect(Collectors.groupingBy(OrderRelation::getOrderNo));
                //抽取入库单号
                List<String> asnNoList = allOrderRelationList.stream().map(OrderRelation::getBusinessNo).distinct().collect(Collectors.toList());
                //查询入库单
                List<AsnHeader> allAsnHeaderList = asnHeaderDao.find(ConditionRule.getInstance().andIn(AsnHeader::getAsnNo,asnNoList));
                Map<String,AsnHeader> allSnHeadMap = allAsnHeaderList.stream().collect(Collectors.toMap(AsnHeader::getAsnNo,Function.identity(),(v1, v2) -> v2));
                //查询订单
                List<Order> allOrderList = orderService.findByOrderNoList(orderNoList);
                Map<String,Order> allOrderMap = allOrderList.stream().collect(Collectors.toMap(Order::getOrderNo,Function.identity(),(v1, v2) -> v2));
                //查询订单关联商品
                List<OrderSku> orderSkuList = orderSkuService.listByOrderNoList(orderNoList);
                Map<String,List<OrderSku>> orderSkuMap = orderSkuList.stream().collect(Collectors.groupingBy(OrderSku::getOrderNo));
                List<Order> updateList = new ArrayList<>();
                allOrderRelationMap.forEach((orderNo,currentOrderRelationList) ->{
                    List<String> currentAsnNoList = currentOrderRelationList.stream().map(OrderRelation::getBusinessNo).distinct().collect(Collectors.toList());
                    List<AsnHeader> currentAsnHeaderList = allSnHeadMap.values().stream().filter(x -> currentAsnNoList.contains(x.getAsnNo())).distinct().collect(Collectors.toList());
                    boolean asnFlag = false;
                    //入库单是否全部收货且订单完结
                    if(CollectionUtil.isNotEmpty(currentAsnHeaderList)){
                        asnFlag = currentAsnHeaderList.stream().allMatch(x -> ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode().equals(x.getReceiveStatus()));
                        if(!asnFlag){
                            //获取未完全收货的入库单明细
                            List<AsnHeader> partAsnHeaderList = currentAsnHeaderList.stream().filter(x -> !ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode().equals(x.getReceiveStatus())).collect(Collectors.toList());
                            //是否是已完结的入库单
                            boolean allAsnFlag = partAsnHeaderList.stream().allMatch(x -> AsnStatusEnum.ASN_CLOSE.getCode().equals(x.getStatus()));
                            if(allAsnFlag){
                                asnFlag = true;
                            }
                        }
                    }
                    List<OrderSku> currentOrderSkuList = orderSkuMap.get(orderNo);
                    boolean osFlag = false;
                    //采购单对应商品的剩余量是否都为0
                    if(CollectionUtil.isNotEmpty(currentOrderSkuList)){
                        osFlag = currentOrderSkuList.stream().allMatch(x -> x.getRemainCount().compareTo(BigDecimal.ZERO) == 0);
                    }
                    Order order = allOrderMap.get(orderNo);
                    if(asnFlag && osFlag){
                        //将采购单状态修改为已完成
                        order.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
                        orderService.preSave(order);
                        updateList.add(order);
                    }
                });
                if(CollectionUtil.isNotEmpty(updateList)){
                    orderService.batchUpdate(updateList);
                }
            }
        }
    }

    private @NotNull Map<String, Object> getAttMap(AsnReceive reveipt) {
        Map<String,Object> attMap = new HashMap<>();
        attMap.put("lotAtt01", reveipt.getLotAtt01());
        attMap.put("lotAtt02", reveipt.getLotAtt02());
        attMap.put("lotAtt03", reveipt.getLotAtt03());
        attMap.put("lotAtt04", reveipt.getLotAtt04());
        attMap.put("lotAtt05", reveipt.getLotAtt05());
        attMap.put("lotAtt06", reveipt.getLotAtt06());
        attMap.put("lotAtt07", reveipt.getLotAtt07());
        attMap.put("lotAtt08", reveipt.getLotAtt08());
        attMap.put("lotAtt09", reveipt.getLotAtt09());
        attMap.put("lotAtt10", reveipt.getLotAtt10());
        attMap.put("lotAtt11", reveipt.getLotAtt11());
        attMap.put("lotAtt12", reveipt.getLotAtt12());
        return attMap;
    }


    /**
     * 验证是否存在收货信息
     * @param asnDetailInfo
     */
    public void verificationExistenceReceive(AsnDetailInfo asnDetailInfo){
        List<AsnReceive> list = dao.find(new ConditionRule().andEqual(AsnReceive::getAsnCode, asnDetailInfo.getAsnNo())
                .andEqual(AsnReceive::getSkuCode, asnDetailInfo.getSkuCode()));
        asnDetailInfo.setHasChildren(CollUtil.isNotEmpty(list));
    }


    public void updateWhetherShelfByReceiveNos(Set<String> receiveNoSet, String whetherShelf) {
        AsnReceiveWhetherShelfItem asnReceiveWhetherShelfItem = new AsnReceiveWhetherShelfItem();
        asnReceiveWhetherShelfItem.setWhetherShelf(whetherShelf);
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(AsnReceive::getReceiveNo, receiveNoSet);
        preSave(asnReceiveWhetherShelfItem);
        dao.updateByCondition(asnReceiveWhetherShelfItem, conditionRule);
    }

    public List<AsnReceive> findByReceiveNos(List<String> receiveNoList){
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(AsnReceive::getReceiveNo, receiveNoList);
        return this.find(conditionRule);
    }

    /**
     * APP收货页面查询
     * @param condition
     * @return
     */
    public PageResult<AsnHeaderAppQuery> appAsnHeaderPage(AsnHeaderAppPageCondition condition) {
        //增加用户身份判断，超级管理员身份可以看到所有数据，不增加用户ID查询
        SessionUserInfo userInfo = SessionContext.get();
        if (!userInfo.isSuperAdmin()){
            condition.setUserId(userInfo.getUserId());
        }
        PageResult<AsnHeaderAppQuery> result = dao.pages(condition);
        if (CollUtil.isNotEmpty(result.getRows())){
            Map<String, AsnHeaderAppQuery> asnRestMap = result.getRows().stream().collect(Collectors.toMap(AsnHeaderAppQuery::getAsnNo, item -> item, (one, two) -> one));
            List<AsnDetail> detailList = asnDetailDao.find(new ConditionRule().andIn(AsnDetail::getAsnNo, asnRestMap.keySet()));
            if (CollUtil.isNotEmpty(detailList)){
                Map<String, List<AsnDetail>> map = detailList.stream().collect(Collectors.groupingBy(AsnDetail::getAsnNo));
                map.keySet().forEach(item -> {
                    AsnHeaderAppQuery appQuery = asnRestMap.get(item);
                    //对 map中 asnDetail的qtyPlanEa字段进行add 并对null进行判断
                    appQuery.setSkuCount(map.get(item).size());
                    appQuery.setQtyPlanEa(map.get(item).stream().map(AsnDetail::getQtyPlanEa).reduce(BigDecimal.ZERO, BigDecimal::add));
                });
            }
        }
        return result;
    }

    /**
     * 查询入库单明细信息
     * @param condition
     * @return
     */
    public AsnDetailAppQuery appAsnHeaderDetail(AsnHeaderAppPageCondition condition) {
        AsnDetailAppQuery detailAppQuery = new AsnDetailAppQuery();
        //组装外部数据
        if (StrUtil.isEmpty(condition.getKeyword())){
            throw new ServiceException(AsnExceptionEnum.ASN_NO_NOT_EMPTY);
        }
        detailAppQuery.setAsnNo(condition.getKeyword());
        AsnHeader asnHeader = asnHeaderDao.findFirst(new ConditionRule().andEqual(AsnHeader::getAsnNo, condition.getKeyword()));
        detailAppQuery.setCreateTime(asnHeader.getCreateTime());
        Owner owner = ownerService.findFirst(new ConditionRule().andEqual(Owner::getId, asnHeader.getOwnerId()));
        detailAppQuery.setOwnerName(owner.getOwnerName());
        detailAppQuery.setReceiveStatus(asnHeader.getReceiveStatus());
        List<AsnDetail> detailList = asnDetailDao.find(new ConditionRule().andEqual(AsnDetail::getAsnNo, condition.getKeyword()));
        detailAppQuery.setSkuCount(detailList.size());
        BigDecimal qtyPlanEa = detailList.stream().map(AsnDetail::getQtyPlanEa).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailAppQuery.setQtyPlanEa(qtyPlanEa);
        List<AsnDetailAppQuery.Detail> restDetailList = new ArrayList<>();
        detailAppQuery.setWarehouseId(asnHeader.getWarehouseId());
        detailAppQuery.setAsnId(asnHeader.getId());
        Set<String> skuCodeSet = detailList.stream().map(AsnDetail::getSkuCode).collect(Collectors.toSet());

        List<Sku> skus = skuService.find(new ConditionRule().andIn(Sku::getSkuCode, skuCodeSet));
        Map<String, Sku> skuCodeMap = skus.stream().collect(Collectors.toMap(Sku::getSkuCode, item -> item, (one, two) -> one));
        //组装明细信息
        detailList.stream().forEach(item -> {
            AsnDetailAppQuery.Detail detail = new AsnDetailAppQuery.Detail();
            BeanUtil.copyProperties(item, detail);
            Sku sku = skuCodeMap.get(item.getSkuCode());
            detail.setSkuId(sku.getId());
            detail.setSkuCode(sku.getSkuCode());
            detail.setSkuName(sku.getSkuName());

            Long count = asnDetailSnService.count(new ConditionRule().andEqual(AsnDetailSn::getAsnDetailId, item.getId()));
            if (count != null && count > 0){
                detail.setIsExistenceSn(Boolean.TRUE);
            }
            PackageUnit packageUnit = packageUnitService.findById(item.getPackageUnitId());
            List<PackageUnit> packageUnits = packageUnitService.find(new ConditionRule().andEqual(PackageUnit::getPackageId, item.getPackageId())
                            .andEqual(PackageUnit::getStatus,YesNoEnum.YES.getCode())
                            .andLessEqual(PackageUnit::getSequencesNo, packageUnit.getSequencesNo())
                    , new OrderRule().addDescOrder("sequences_no"));
            for (int i = 0; i < packageUnits.size(); i++) {
                if (i > 0){
                    PackageUnit packageUnitUp = packageUnits.get(i - 1);
                    PackageUnit packageUnitCurrent = packageUnits.get(i);
                    packageUnitCurrent.setQuantity(packageUnitUp.getQuantity() * packageUnitCurrent.getQuantity());
                }else{
                    //最大单位保持为1
                    PackageUnit packageUnitCurrent = packageUnits.get(i);
                    packageUnitCurrent.setQuantity(1);
                }
            }
            String specifications = packageUnits.stream().map(unit -> unit.getQuantity() + unit.getName()).collect(Collectors.joining("/"));
            detail.setPackingSpecifications(specifications);
            List<PackageUnit> list = packageUnits.stream().filter(f -> f.getId().equals(item.getPackageUnitId())).toList();
            detail.setPackageUnitName(list.getFirst().getName());
            detail.setMinQuantity(list.getFirst().getMinQuantity());
            List<AsnDetailAppQuery.LotDetail> lotDetailList = new ArrayList<>();
            //组合批次属性信息
            List<WarehouseLotDetail> lotDetails = warehouseLotDetailService.find(new ConditionRule().andEqual(WarehouseLotDetail::getLotId, sku.getLotId())
                    , new OrderRule().addAscOrder("lot_seq"));
            if (CollUtil.isNotEmpty(lotDetails)){
                lotDetails.stream().forEach(lot -> {
                    AsnDetailAppQuery.LotDetail lotDetail = new AsnDetailAppQuery.LotDetail();
                    BeanUtil.copyProperties(lot, lotDetail);
                    lotDetailList.add(lotDetail);
                });
            }
            detail.setLotDetailList(lotDetailList);
            restDetailList.add(detail);
        });
        detailAppQuery.setDetailList(restDetailList);
        return detailAppQuery;
    }

    /**
     * APP收货确认
     * @param asnAppReceiptDetailItem
     * @return
     */
    @Transactional
    public Boolean appReceiveSave(AsnAppReceiptDetailItem asnAppReceiptDetailItem) {
        AsnAppSnNoItem asnAppSnNoItem = new AsnAppSnNoItem();
        asnAppSnNoItem.setBoxNo(asnAppReceiptDetailItem.getBoxNo());
        asnAppSnNoItem.setSnNoList(asnAppReceiptDetailItem.getSnNoList());
        if (appVerificationReceipt(asnAppSnNoItem) && CollUtil.isNotEmpty(asnAppReceiptDetailItem.getSnNoList())) {
            throw new ServiceException(AsnExceptionEnum.SN_NO_NOT_REPEAT_RECEIVE);
        }
        AsnReceiptDetailItem detailItem = new AsnReceiptDetailItem();
        detailItem.setId(asnAppReceiptDetailItem.getDetailId());
        detailItem.setSkuSn(asnAppReceiptDetailItem.getSnNoList());
        BeanUtil.copyProperties(asnAppReceiptDetailItem, detailItem);
        receiptConfirmation(detailItem);
        return Boolean.FALSE;
    }

    /**
     * 验证SN码是否收货 TRUE = 以收货，FLASE = 未收货
     * @param asnAppSnNoItem
     * @return
     */
    public Boolean appVerificationReceipt(AsnAppSnNoItem asnAppSnNoItem) {
        List<AsnDetailSn> detailSns = asnDetailSnService.find(new ConditionRule().andEqual(AsnDetailSn::getBoxCode, asnAppSnNoItem.getBoxNo())
                .andIn(AsnDetailSn::getSnNo, asnAppSnNoItem.getSnNoList()));
        long count = detailSns.stream().filter(item -> !ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(item.getReceiveStatus())).count();
        if (count > 0){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 验证商品SN码是否存在当前的入库单中
     * @param asnSnVerificationItem 验证条件
     * @return 所在入库单明细ID
     */
    public Long appVerificationSnExistence(AsnSnVerificationItem asnSnVerificationItem) {
        List<AsnDetail> details = asnDetailDao.find(new ConditionRule().andEqual(AsnDetail::getAsnNo, asnSnVerificationItem.getAsnNo()));
        List<Long> detailIdList = details.stream().map(ModuleBaseModel::getId).toList();
        List<AsnDetailSn> snList = asnDetailSnService.find(new ConditionRule().andIn(AsnDetailSn::getAsnDetailId, detailIdList)
                .andEqual(AsnDetailSn::getSnNo, asnSnVerificationItem.getSnNo()).orEqual(AsnDetailSn::getBoxCode,asnSnVerificationItem.getSnNo()));
        if (CollUtil.isEmpty(snList)){
            throw new ServiceException(AsnExceptionEnum.SN_CODE_INCORRECT);
        }
        return snList.getFirst().getAsnDetailId();
    }

    /**
     * 分配上架员
     */
    @Transactional
    public Boolean distributionPa(DistributionPaItem paItem) {
        List<AsnDetail> asnDetails = asnDetailDao.find(new ConditionRule().andIn(AsnDetail::getId, paItem.getIds()));
        if (CollUtil.isEmpty(asnDetails)){
            throw new ServiceException(AsnExceptionEnum.ASN_NO_NOT_SUCCESS);
        }
        asnDetails.forEach(item -> {
            item.setPaPersonId(paItem.getPaPersonId());
            item.setPaPersonName(paItem.getPaPersonName());
        });
        //更新 收货明细信息 要求：未上架状态并且上架员为空的数据
        List<Long> idList = asnDetails.stream().map(AsnDetail::getId).toList();
        List<AsnReceive> list = find(new ConditionRule().andIn(AsnReceive::getAsnDetailId, idList));
        if (CollUtil.isNotEmpty(list)){
            List<String> receiveNoList = list.stream().map(AsnReceive::getReceiveNo).toList();
            List<CsPaTask> paTasks = csPaTaskService.find(new ConditionRule().andIn(CsPaTask::getReceiveNo, receiveNoList));
            if (CollUtil.isNotEmpty(paTasks)){
                List<CsPaTask> csPaTasks = paTasks.stream().filter(item -> {
                    if (ObjUtil.isNull(item.getQtyPaEa())){
                        item.setQtyPaEa(BigDecimal.ZERO);
                    }
                    return BigDecimal.ZERO.compareTo(item.getQtyPlanEa().subtract(item.getQtyPaEa())) != 0;
                }).toList();
                if (CollUtil.isNotEmpty(csPaTasks)){
                    Set<String> set = csPaTasks.stream().map(CsPaTask::getReceiveNo).collect(Collectors.toSet());
                    list = list.stream().filter(item -> StrUtil.isEmpty(item.getPaPersonName()) || set.contains(item.getReceiveNo())).toList();
                    csPaTasks.forEach(item -> {
                        item.setPaPersonId(paItem.getPaPersonId());
                        item.setPaPersonName(paItem.getPaPersonName());
                    });
                    csPaTaskService.batchUpdate(csPaTasks);
                }
            }
            list.forEach(item -> item.setPaPersonName(paItem.getPaPersonName()).setPaPersonId(paItem.getPaPersonId()));
            dao.batchUpdate(list);
        }
        asnDetailDao.batchUpdate(asnDetails);
        return Boolean.TRUE;
    }

    /**
     *@Description 根据批次号查询收货信息
     *@Param lotNumList
     *@Return * {@link List< AsnReceive> }
     *@Date 2025/3/13 11:35
     *<AUTHOR>
     **/
    public List<AsnReceive> findByLotNumListForAgeReport(List<String> lotNumList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(AsnReceive::getLotNum,lotNumList);
        return dao.find(conditionRule);
    }

    /**
     * 根据收获任务号查询收货单信息-单据预览数据查询
     * @param asnNo
     */
    public AsnHeaderPreviewQuery getAsnHeaderPreviewQueryByAsnNo(String receiveNo) {
        AsnHeaderPreviewQuery result = new AsnHeaderPreviewQuery();
        result.setReceiveNo(receiveNo);
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(AsnReceive::getReceiveNo, receiveNo);
        AsnReceive asnReceive = dao.findFirst(conditionRule);
        result.setAsnNo(asnReceive.getAsnCode());
        result.setPackageUnitName(asnReceive.getPackageUnitName());
        // 组装商品名称
        Sku sku = skuService.findFirst(new ConditionRule().andIn(Sku::getSkuCode, asnReceive.getSkuCode()));
        result.setSkuName(EmptyUtil.isNotEmpty(sku) ? sku.getSkuName() : null);
        //组装包装信息
        if(EmptyUtil.isNotEmpty(asnReceive.getPackageId())){
            Package pack = packageService.getById(asnReceive.getPackageId());
            result.setPackageName(EmptyUtil.isNotEmpty(pack) ? pack.getName() : null);
        }
        AsnReceivePreviewQuery receivePreviewQuery = new AsnReceivePreviewQuery();
        BeanUtil.copyProperties(asnReceive, receivePreviewQuery);
        // 组装收货员
        AsnHeader asnHeader = asnHeaderDao.findFirst(new ConditionRule().andEqual(AsnHeader::getAsnNo, asnReceive.getAsnCode()));
        receivePreviewQuery.setReceiverOp(EmptyUtil.isNotEmpty(asnHeader) ? asnHeader.getReceiverOp() : null);
        // 组装库区
        if (ObjUtil.isNotNull(receivePreviewQuery.getRcvLocId())){
            WarehouseLoc warehouseLoc = warehouseLocService.findById(receivePreviewQuery.getRcvLocId());
            receivePreviewQuery.setShelfName(warehouseLoc.getShelfName());
            receivePreviewQuery.setZoneName(warehouseLoc.getZoneName());
        }
        result.setDetailList(List.of(receivePreviewQuery));
        return result;
    }

    public void handleUpdateReceive(AsnReceiptDetailItem item) {
        //根据单号查询出当前单时候是合并收货
        AsnHeader asnHeader = asnHeaderDao.findFirst(new ConditionRule().andEqual(AsnHeader::getAsnNo, item.getAsnNo()));
        if (asnHeader == null || YesNoEnum.NO.getCode().equals(asnHeader.getIsMerge())) {
            return;
        }
        List<AsnHeader> mergeAsnHeaderList = asnHeaderDao.find(new ConditionRule().andEqual(AsnHeader::getMergeNo, asnHeader.getMergeNo()));
        /*List<String> asnNoList = mergeAsnHeaderList.stream().map(AsnHeader::getAsnNo).toList();
        List<AsnDetail> asnDetails = asnDetailService.find(new ConditionRule().andIn(AsnDetail::getAsnNo, asnNoList));
        BigDecimal qtyPlanEa = asnDetails.stream().map(AsnDetail::getQtyPlanEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal qtyRcvEa = asnDetails.stream().map(AsnDetail::getQtyRcvEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        Long mergeId = asnHeader.getMergeId();*/
        AsnMergeReceive receive = asnMergeReceiveDao.findById(asnHeader.getMergeId());
        log.info("【单体-更新合并数据收获状态和EA数监听器】 原来数据集：{}", JSONUtil.toJsonStr(receive));
        //验证整体收货状态
        List<String> statusList = mergeAsnHeaderList.stream()
                .map(AsnHeader::getReceiveStatus)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.info("合并单 {} 的状态列表: {}", asnHeader.getMergeNo(), statusList);

        if (statusList.isEmpty()) {
            receive.setStatus(com.chinaservices.wms.common.enums.ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode());
        } else if (statusList.stream().allMatch(com.chinaservices.wms.common.enums.ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode()::equals)) {
            receive.setStatus(com.chinaservices.wms.common.enums.ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
        } else {
            receive.setStatus(com.chinaservices.wms.common.enums.ReceiveStatusEnum.PART_RECEIVED_GOODS.getCode());
        }
        if (ObjUtil.isNull(receive.getQtyRcvEa())) {
            receive.setQtyRcvEa(BigDecimal.ZERO);
        }
        // 6. 更新合并收货记录
        receive.setQtyRcvEa(receive.getQtyRcvEa().add(item.getQtyRcvEa()));
        log.info("【单体-更新合并数据收获状态和EA数监听器】 更新数据集：{}", JSONUtil.toJsonStr(receive));
        asnMergeReceiveDao.update(receive);
        log.info("【单体-更新合并数据收获状态和EA数监听器】 更新后数据集：{}", JSONUtil.toJsonStr(receive));
    }

    /**
     * 根据收货任务号获取入库单号
     * @param receiveNo
     * @return
     */
    public String getAsnCodeByReceiveNo(String receiveNo){
        String result = StringPool.EMPTY;
        if(EmptyUtil.isEmpty(receiveNo)){
            return result;
        }
        AsnReceive asnReceive = this.getByReceiveNo(receiveNo);
        if(EmptyUtil.isNotEmpty(asnReceive)){
            result = asnReceive.getAsnCode();
        }
        return result;
    }

    public AsnReceive getByReceiveNo(String receiveNo) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(AsnReceive::getReceiveNo, receiveNo);
        return dao.findFirst(conditionRule);
    }
}
