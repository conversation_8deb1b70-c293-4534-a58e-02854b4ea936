package com.chinaservices.wms.module.process.staff.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.common.enums.process.ProcessStaffStatusEnum;
import com.chinaservices.wms.module.process.staff.domain.ProcessStaffPageCondition;
import com.chinaservices.wms.module.process.staff.domain.ProcessStaffPageQuery;
import com.chinaservices.wms.module.process.staff.domain.ProcessStaffTableItem;
import com.chinaservices.wms.module.process.staff.service.ProcessStaffService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 加工人员管理
 * @author: <PERSON><PERSON>.<PERSON>
 * @Date: 2025/4/14
 */
@RestController
@RequestMapping("/api/process/staff")
public class ProcessStaffController {

    @Autowired
    private ProcessStaffService processStaffService;

    /**
     *  分页查询加工人员
     * @param pageCondition 查询条件
     * @return PageResult<ProcessStaffPageQuery>
     */
    @PostMapping("/page")
    @SaCheckPermission("processStaff:processStaffList:headBtn:page")
    public ResponseData<PageResult<ProcessStaffPageQuery>> page(@RequestBody ProcessStaffPageCondition pageCondition) {
        return ResponseData.success(processStaffService.page(pageCondition));
    }

    /**
     * 根据id查询加工人员
     * @param id 入参
     * @return ResponseData
     */
    @PostMapping("/getById/{id}")
    @SaCheckPermission("processStaff:processStaffList:table:view")
    public ResponseData<ProcessStaffPageQuery> getById(@PathVariable("id") Long id) {
        ProcessStaffPageQuery pageQuery = processStaffService.getById(id);
        return ResponseData.success(pageQuery);
    }

    /**
     * 新增或编辑加工人员
     * @param tableItem 新增与更新入参
     * @return ResponseData
     */
    @PostMapping("/saveOrUpdate")
    @SaCheckPermission(value = {
            "processStaff:processStaffList:headBtn:add",
            "processStaff:processStaffList:table:edit"
    }, mode = SaMode.OR)
    public ResponseData saveOrUpdate(@Valid @RequestBody ProcessStaffTableItem tableItem) {
        return ResponseData.success(processStaffService.saveOrUpdate(tableItem));
    }

    /**
     * 批量删除加工人员
     * @param ids 入参
     * @return ResponseData
     */
    @PostMapping("/batchDelete")
    @SaCheckPermission(value = {
            "processStaff:processStaffList:headBtn:delete",
            "processStaff:processStaffList:table:delete"
    }, mode = SaMode.OR)
    public ResponseData batchDelete(@RequestBody Long[] ids) {
        return ResponseData.success(processStaffService.batchDelete(ids));
    }

    /**
     * 批量启用加工人员
     * @param ids 入参
     * @return ResponseData
     */
    @PostMapping("/batchEnable")
    @SaCheckPermission("processStaff:processStaffList:headBtn:enable")
    public ResponseData batchEnable(@RequestBody Long[] ids) {
        return ResponseData.success(processStaffService.updateStatusByIds(ids, ProcessStaffStatusEnum.ENABLED.getCode()));
    }

    /**
     * 批量禁用加工人员
     * @param ids 入参
     * @return ResponseData
     */
    @PostMapping("/batchDisable")
    @SaCheckPermission("processStaff:processStaffList:headBtn:disable")
    public ResponseData batchDisable(@RequestBody Long[] ids) {
        return ResponseData.success(processStaffService.updateStatusByIds(ids, ProcessStaffStatusEnum.DISABLED.getCode()));
    }
}
