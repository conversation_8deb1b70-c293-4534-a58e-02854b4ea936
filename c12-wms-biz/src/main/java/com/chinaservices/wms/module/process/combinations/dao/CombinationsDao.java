package com.chinaservices.wms.module.process.combinations.dao;

import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.sqlid.process.combinations.ProcessCombinationsSqlId;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsPageCondition;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsQuery;
import com.chinaservices.wms.module.process.combinations.model.Combinations;
import org.springframework.stereotype.Repository;

@Repository
public class CombinationsDao extends ModuleBaseDaoSupport<Combinations, Long> {

    /**
     *@Description 组合件分页查询
     *@Param condition
     *@Return * {@link PageResult< CombinationsQuery> }
     *@Date 2025/4/15 10:44
     *<AUTHOR>
     **/
    public PageResult<CombinationsQuery> findPage(CombinationsPageCondition condition) {
        return sqlExecutor.page(CombinationsQuery.class, ProcessCombinationsSqlId.PROCESS_COMBINATIONS_QUERY_PAGE_LIST,condition);
    }
}