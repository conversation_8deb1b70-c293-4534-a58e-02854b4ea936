package com.chinaservices.wms.module.process.combinations.dao;

import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.sqlid.process.combinations.ProcessCombinationsSqlId;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsDetailsPageCondition;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsDetailsQuery;
import com.chinaservices.wms.module.process.combinations.model.CombinationsDetails;
import org.springframework.stereotype.Repository;

@Repository
public class CombinationsDetailsDao extends ModuleBaseDaoSupport<CombinationsDetails, Long> {

    /**
     *@Description 子件分页查询
     *@Param condition
     *@Return * {@link PageResult< CombinationsDetailsQuery> }
     *@Date 2025/4/15 14:47
     *<AUTHOR>
     **/
    public PageResult<CombinationsDetailsQuery> findPage(CombinationsDetailsPageCondition condition) {
        return sqlExecutor.page(CombinationsDetailsQuery.class, ProcessCombinationsSqlId.PROCESS_COMBINATIONS_DETAILS_QUERY_PAGE_LIST,condition);
    }
}