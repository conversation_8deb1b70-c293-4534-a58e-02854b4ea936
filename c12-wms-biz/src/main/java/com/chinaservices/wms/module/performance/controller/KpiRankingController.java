package com.chinaservices.wms.module.performance.controller;

import com.chinaservices.module.base.ModuleBaseController;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.performance.domain.KpiHeaderPageCondition;
import com.chinaservices.wms.module.performance.domain.KpiHeaderRankingQuery;
import com.chinaservices.wms.module.performance.service.KpiHeaderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 绩效排名控制器
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@RestController
@RequestMapping("/api/performance/kpi/ranking")
public class KpiRankingController extends ModuleBaseController
{

    @Autowired
    private KpiHeaderService kpiHeaderService;


    /**
     * 分页绩效排名
     *
     * @param condition 条件
     * @return {@link ResponseData }<{@link PageResult }<{@link KpiHeaderRankingQuery }>>
     */
    @PostMapping(value = "/page")
    public ResponseData<PageResult<KpiHeaderRankingQuery>> pageRanking(@RequestBody KpiHeaderPageCondition condition)
    {
        return ResponseData.success(kpiHeaderService.pageRanking(condition));
    }
}
