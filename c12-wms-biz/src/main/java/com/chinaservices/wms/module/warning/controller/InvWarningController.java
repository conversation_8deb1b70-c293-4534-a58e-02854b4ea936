package com.chinaservices.wms.module.warning.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.warning.InvWarningItem;
import com.chinaservices.wms.module.warning.InvWarningPageCondition;
import com.chinaservices.wms.module.warning.InvWarningPageQuery;
import com.chinaservices.wms.module.warning.service.InvWarningService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 预警管理 -- 库存预警
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/invWarning")
public class InvWarningController {

    @Autowired
    private InvWarningService invWarningService;

    /**
     * 库存预警  - 消息分页查询
     *
     * @param condition
     * @return
     */
    @PostMapping("/messagePage")
    @SaCheckPermission("inventoryWarning:inventoryWarningList:headBtn:page")
    public ResponseData<InvWarningPageQuery> messagePage(@RequestBody InvWarningPageCondition condition) {
        // todo 设置数据权限
        PageResult<InvWarningPageQuery> pageResult = invWarningService.messagePage(condition);
        return ResponseData.success(pageResult);
    }

    /**
     * 库存预警设置--分页查询
     *
     * @param condition
     * @return
     */
    @PostMapping("/page")
    @SaCheckPermission("inventoryWarningSetting:inventoryWarningSettingList:headBtn:page")
    public ResponseData<InvWarningPageQuery> page(@RequestBody InvWarningPageCondition condition) {
        // todo 设置数据权限
        PageResult<InvWarningPageQuery> pageResult = invWarningService.page(condition);
        return ResponseData.success(pageResult);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @PostMapping("/deleteByIds")
    @SaCheckPermission(value = {"inventoryWarning:inventoryWarningList:headBtn:delete"})
    public ResponseData deleteByIdList(@RequestBody Long[] ids) {
        invWarningService.deleteByIdList(ids);
        return ResponseData.success();
    }

    @PostMapping("/save")
    @SaCheckPermission(value = {"inventoryWarning:inventoryWarningList:headBtn:add","inventoryWarning:inventoryWarningList:table:edit"}, mode = SaMode.OR)
    public ResponseData save(@Valid @RequestBody InvWarningItem item){
        invWarningService.save(item);
        return ResponseData.success();
    }
}
