package com.chinaservices.wms.module.passive.take.dto;

import com.chinaservices.core.third.porch.domain.PorchRecordResponse;
import com.chinaservices.sdk.pojo.PageCondition;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
* <AUTHOR> generate
* @description task
* @date  2025-07-23
**/
@Data
public class PassiveTaskMockCondition  {
    /**
     * id
     *
     */
    private Long id;
    /**
    * 任务id
     *
    */
    private String taskId;
    /**
     * 类型
     *
     */
    private String type;
    /**
     * 标签号
     *
     */
    private List<PorchRecordResponse> epcList;

}
