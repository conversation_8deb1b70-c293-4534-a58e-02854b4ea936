package com.chinaservices.wms.module.stock.common.exec;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.wms.common.constant.StatusConstant;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.common.model.qty.bo.StockTransactionBO;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

import static com.chinaservices.wms.common.constant.IdRuleConstant.TRACE_ID;
import static com.chinaservices.wms.common.exception.InvExceptionEnum.*;

/**
 * 拆分相关
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class StockSplitCommonService extends StockCommonBaseService {

    @Override
    protected void checkInit(InvLotLocQtyBO invLotLocQtyBO) {
        log.info("【库存操作】拆分相关逻辑处理...");
        switch (invLotLocQtyBO.getTransactionType()) {
            case TransactionType.TRAN_SPLIT_ALLOC:  // 拆分分配
                if (ObjectUtil.isAllEmpty(invLotLocQtyBO.getLocId(), invLotLocQtyBO.getLocCode())) {
                    throw new ServiceException(LOTNUM_LOCID_PALLETNUM_NOT_NULL);
                }
                break;
            case TransactionType.TRAN_CR_SPLIT_ALLOC:  // 取消拆分分配
                if (ObjectUtil.isAllEmpty(invLotLocQtyBO.getLocId(), invLotLocQtyBO.getLocCode())) {
                    throw new ServiceException(LOTNUM_LOCID_PALLETNUM_NOT_NULL);
                }
                break;
            case TransactionType.TRAN_SPLIT_PICK:  // 拆分拣货
                if (ObjectUtil.isAllEmpty(invLotLocQtyBO.getLocId(), invLotLocQtyBO.getLocCode())) {
                    throw new ServiceException(LOTNUM_LOCID_PALLETNUM_NOT_NULL);
                }
                if (StatusConstant.YES.equals(invLotLocQtyBO.getWhetherSerialController()) && CollUtil.isEmpty(invLotLocQtyBO.getSkuSn())) {
                    throw new ServiceException(SKU_SN_IS_NULL, invLotLocQtyBO.getSkuCode());
                }
                break;
            case TransactionType.TRAN_SPLIT_IN:  // 拆分入库
                if (StrUtil.isBlank(invLotLocQtyBO.getLotNum())) {
                    throw new ServiceException(LOC_NUM_IS_NULL);
                }
                if (ObjectUtil.isNull(invLotLocQtyBO.getLocId()) || StrUtil.isBlank(invLotLocQtyBO.getLocCode())) {
                    WarehouseLoc warehouse = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), invLotLocQtyBO.getWarehouseId());
                    invLotLocQtyBO.setLocId(warehouse.getId());
                    invLotLocQtyBO.setLocCode(warehouse.getLocCode());
                }
                invLotLocQtyBO.setToPallet(invLotLocQtyBO.getPalletNum());
                break;
            case TransactionType.TRAN_SPLIT_OUT:  // 拆分出库
                if (ObjectUtil.isAllEmpty(invLotLocQtyBO.getLocId(), invLotLocQtyBO.getLocCode())) {
                    throw new ServiceException(LOTNUM_LOCID_PALLETNUM_NOT_NULL);
                }
                if (StatusConstant.YES.equals(invLotLocQtyBO.getWhetherSerialController()) && CollUtil.isEmpty(invLotLocQtyBO.getSkuSn())) {
                    throw new ServiceException(SKU_SN_IS_NULL, invLotLocQtyBO.getSkuCode());
                }
                break;
            case TransactionType.TRAN_SPLIT_PA:  // 拆分上架
                if (ObjectUtil.isNull(invLotLocQtyBO.getToLocId())) {
                    throw new ServiceException(TO_LOC_IS_NULL_ID);
                }
                if (StatusConstant.YES.equals(invLotLocQtyBO.getWhetherSerialController()) && CollUtil.isEmpty(invLotLocQtyBO.getSkuSn())) {
                    throw new ServiceException(SKU_SN_IS_NULL, invLotLocQtyBO.getSkuCode());
                }
                super.checkInvMixSkuMixLot(new InvLotLocQtyBO().setLocId(invLotLocQtyBO.getToLocId()).setLocCode(invLotLocQtyBO.getToLocCode()).setWarehouseId(invLotLocQtyBO.getWarehouseId()).setSkuId(invLotLocQtyBO.getSkuId()).setLotNum(invLotLocQtyBO.getLotNum()));
                break;
            case TransactionType.TRAN_SPLIT_RETURN:  // 拆分退料
                if (ObjectUtil.isAllEmpty(invLotLocQtyBO.getLocId(), invLotLocQtyBO.getLocCode())) {
                    throw new ServiceException(LOTNUM_LOCID_PALLETNUM_NOT_NULL);
                }
                if (StatusConstant.YES.equals(invLotLocQtyBO.getWhetherSerialController()) && CollUtil.isEmpty(invLotLocQtyBO.getSkuSn())) {
                    throw new ServiceException(SKU_SN_IS_NULL, invLotLocQtyBO.getSkuCode());
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected List<InvLotLoc> compileInvLotLoc(InvLotLocQtyBO invLotLocQtyBO) {
        List<InvLotLoc> invLotLocs = Lists.newArrayList();
        InvLotLoc invLotLoc;
        InvLotLoc processingLotloc;
        WarehouseLoc warehouseLoc;
        
        switch (invLotLocQtyBO.getTransactionType()) {
            case TransactionType.TRAN_SPLIT_ALLOC:  // 拆分分配
                invLotLoc = getInvLotloc(invLotLocQtyBO, false);
                // 设置数量为空，只更新分配数
                invLotLoc.setQtyAlloc(invLotLocQtyBO.getUpdateNum());
                invLotLoc.setQty(BigDecimal.ZERO);
                invLotLocs.add(invLotLoc);
                break;
            case TransactionType.TRAN_CR_SPLIT_ALLOC:  // 取消拆分分配
                invLotLoc = getInvLotloc(invLotLocQtyBO, true);
                // 设置数量为空，只更新分配数
                invLotLoc.setQty(BigDecimal.ZERO);
                invLotLoc.setQtyAlloc(invLotLocQtyBO.getUpdateNum().negate());
                invLotLocs.add(invLotLoc);
                break;
            case TransactionType.TRAN_SPLIT_PICK:  // 拆分拣货
                // 减去总库存和分配数
                invLotLoc = getInvLotloc(invLotLocQtyBO, true)
                        .setQtyPk(invLotLocQtyBO.getUpdateNum()).setQtyAlloc(invLotLocQtyBO.getUpdateNum().negate());
                // 增加加工台库存
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.PROCESSING_PLATFORM.getCode(), invLotLocQtyBO.getWarehouseId());
                processingLotloc = getInvLotloc(invLotLocQtyBO, false)
                        .setLocId(warehouseLoc.getId()).setLocCode(warehouseLoc.getLocCode()).setPalletNum(invLotLocQtyBO.getToPallet());
                invLotLocs.add(invLotLoc);
                invLotLocs.add(processingLotloc);
                break;
            case TransactionType.TRAN_SPLIT_IN:  // 拆分入库
                // 增加库存数
                invLotLoc = getInvLotloc(invLotLocQtyBO, false);
                invLotLoc.setQty(invLotLocQtyBO.getUpdateNum());
                invLotLocs.add(invLotLoc);
                break;
            case TransactionType.TRAN_SPLIT_OUT:  // 拆分出库
                // 减去加工台库存
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.PROCESSING_PLATFORM.getCode(), invLotLocQtyBO.getWarehouseId());
                invLotLoc = getInvLotloc(invLotLocQtyBO, true);
                invLotLoc.setLocId(warehouseLoc.getId());
                invLotLoc.setLocCode(warehouseLoc.getLocCode());
                invLotLoc.setQtyPk(invLotLocQtyBO.getUpdateNum().negate());
                invLotLoc.setQty(BigDecimal.ZERO);
                invLotLoc.setQtyQc(BigDecimal.ZERO);
                invLotLoc.setQtyHold(BigDecimal.ZERO);
                invLotLoc.setQtyAlloc(BigDecimal.ZERO);
                invLotLocs.add(invLotLoc);
                break;
            case TransactionType.TRAN_SPLIT_PA:  // 拆分上架
                // 减去过渡库位库存
                invLotLoc = getInvLotloc(invLotLocQtyBO, true);
                // 增加目标库位库存
                InvLotLoc toInvLotLoc = getInvLotloc(invLotLocQtyBO, false);
                toInvLotLoc.setLocId(invLotLocQtyBO.getToLocId());
                toInvLotLoc.setLocCode(invLotLocQtyBO.getToLocCode());
                toInvLotLoc.setPalletNum(invLotLocQtyBO.getToPallet());
                invLotLocs.add(invLotLoc);
                invLotLocs.add(toInvLotLoc);
                break;
            case TransactionType.TRAN_SPLIT_RETURN:  // 拆分退料
                // 减去过渡库位库存
                invLotLoc = getInvLotloc(invLotLocQtyBO, true);
                // 增加目标库位库存
                toInvLotLoc = getInvLotloc(invLotLocQtyBO, false);
                toInvLotLoc.setLocId(invLotLocQtyBO.getToLocId());
                toInvLotLoc.setLocCode(invLotLocQtyBO.getToLocCode());
                toInvLotLoc.setPalletNum(invLotLocQtyBO.getToPallet());
                invLotLocs.add(invLotLoc);
                invLotLocs.add(toInvLotLoc);
                break;
            default:
                break;
        }
        return invLotLocs;
    }

    @Override
    protected List<StockTransactionBO> compileStockTransaction(InvLotLocQtyBO invLotLocQtyBO) {
        List<StockTransactionBO> list = Lists.newArrayList();
        StockTransactionBO stockTransactionBO = BeanUtil.copyProperties(invLotLocQtyBO, StockTransactionBO.class);
        WarehouseLoc warehouseLoc;
        InvLotLocQtyBO fm = getINvLotLocQtyFm(invLotLocQtyBO);
        InvLotLocQtyBO to = getINvLotLocQtyTo(invLotLocQtyBO);
        stockTransactionBO.setFmLocQty(fm);
        stockTransactionBO.setTotalQtyBO(to);
        
        switch (invLotLocQtyBO.getTransactionType()) {
            case TransactionType.TRAN_SPLIT_ALLOC, TransactionType.TRAN_CR_SPLIT_ALLOC:
                list.add(stockTransactionBO);
                break;
            case TransactionType.TRAN_SPLIT_PICK:
                // 源库位
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.PROCESSING_PLATFORM.getCode(), invLotLocQtyBO.getWarehouseId());
                // 目标库位为加工台
                stockTransactionBO.setTotalQtyBO(to.setLocId(warehouseLoc.getId())
                        .setLocCode(warehouseLoc.getLocCode())
                        .setPalletNum(invLotLocQtyBO.getPalletNum())
                        .setLotNum(invLotLocQtyBO.getLotNum())
                        .setPalletNum(invLotLocQtyBO.getToPallet()));
                list.add(stockTransactionBO);
                break;
            case TransactionType.TRAN_SPLIT_IN:
                list.add(stockTransactionBO);
                break;
            case TransactionType.TRAN_SPLIT_OUT:
                stockTransactionBO.setFmLocQty(fm);
                to.setLocId(null);
                to.setLocCode(null);
                to.setOwnerName(null);
                to.setOwnerId(null);
                to.setWarehouseName(null);
                to.setWarehouseId(null);
                to.setPalletNum(TRACE_ID);
                warehouseLoc = warehouseLocService.getCdWhLoc(WarehouseLocUseTypeEnum.PROCESSING_PLATFORM.getCode(), invLotLocQtyBO.getWarehouseId());
                fm.setLocId(warehouseLoc.getId());
                fm.setLocCode(warehouseLoc.getLocCode());
                fm.setPalletNum(TRACE_ID);
                list.add(stockTransactionBO);
                break;
            case TransactionType.TRAN_SPLIT_PA:
                list.add(stockTransactionBO);
                break;
            case TransactionType.TRAN_SPLIT_RETURN:
                list.add(stockTransactionBO);
                break;
            default:
                break;
        }
        return list;
    }

    /**
     * 对应拆分相关交易类型
     * @return 交易类型列表
     */
    @Override
    public List<String> actionType() {
        return Lists.newArrayList(
                TransactionType.TRAN_SPLIT_ALLOC,
                TransactionType.TRAN_CR_SPLIT_ALLOC,
                TransactionType.TRAN_SPLIT_PICK,
                TransactionType.TRAN_SPLIT_IN,
                TransactionType.TRAN_SPLIT_OUT,
                TransactionType.TRAN_SPLIT_PA,
                TransactionType.TRAN_SPLIT_RETURN
        );
    }
}
