package com.chinaservices.wms.module.process.staff.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 资质证书附件
 */
@Data
@Entity
@Table(name = "cs_process_staff_certificate_file")
@EqualsAndHashCode(callSuper = true)
public class ProcessStaffCertificateFile extends ModuleBaseModel {
    /**
     * 关联id
     */
    private String businessId;
    /**
     * 附件ID
     */
    private String fileId;
    /**
     * 附件名
     */
    private String fileName;
    /**
     * 原始名称
     */
    private String originalName;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件大小
     */
    private String fileSize;
    /**
     * 存储路径
     */
    private String filePath;
}
