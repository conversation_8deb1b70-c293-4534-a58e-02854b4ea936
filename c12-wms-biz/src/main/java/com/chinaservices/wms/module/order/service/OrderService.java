package com.chinaservices.wms.module.order.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinaservices.auth.module.dict.domain.DictDataCondition;
import com.chinaservices.auth.module.dict.domain.DictDataQuery;
import com.chinaservices.auth.module.dict.feign.RemoteDictDataService;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.util.JsonUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constants.DictDataTypeConstants;
import com.chinaservices.wms.common.enums.DocumentTypeEnum;
import com.chinaservices.wms.common.enums.asn.AsnStatusEnum;
import com.chinaservices.wms.common.enums.common.AuditStatusEnum;
import com.chinaservices.wms.common.enums.order.OrderSourceEnum;
import com.chinaservices.wms.common.enums.order.OrderStatusEnum;
import com.chinaservices.wms.common.enums.order.OrderTypeEnum;
import com.chinaservices.wms.common.enums.so.OutboundAuditStatusEnum;
import com.chinaservices.wms.common.enums.so.SoOrderStatusEnum;
import com.chinaservices.wms.common.exception.OrderExceptionEnum;
import com.chinaservices.wms.module.archive.archives.service.LogisticsFileAutoGenerateService;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.asn.service.AsnHeaderService;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.folder.logistics.domain.LogisticsFolderDetailLinkCondition;
import com.chinaservices.wms.module.order.dao.OrderDao;
import com.chinaservices.wms.module.order.domain.*;
import com.chinaservices.wms.module.order.model.Order;
import com.chinaservices.wms.module.order.model.OrderRelation;
import com.chinaservices.wms.module.order.model.OrderSku;
import com.chinaservices.wms.module.so.so.model.SoHeader;
import com.chinaservices.wms.module.so.so.service.SoHeaderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName OrderService
 * <AUTHOR>
 * @Date 2025/4/23 14:52
 * @Description
 * @Version 1.0
 */
@Service
public class OrderService extends ModuleBaseServiceSupport<OrderDao, Order, Long> {

    @Autowired
    private OrderSkuService orderSkuService;

    @Autowired
    private NumberGenerator numberGenerator;

    @Autowired
    private OrderRelationService orderRelationService;

    @Autowired
    private PackageUnitService packageUnitService;

    @Autowired
    private SkuService skuService;

    @Autowired
    @Lazy
    private SoHeaderService soHeaderService;

    @Autowired
    @Lazy
    private AsnHeaderService asnHeaderService;

    @Autowired
    private LogisticsFileAutoGenerateService logisticsFileAutoGenerateService;

    @Autowired
    private RemoteDictDataService remoteDictDataService;

    /**
     *@Description 订单分页查询
     *@Param condition
     *@Return * {@link PageResult< OrderQuery> }
     *@Date 2025/4/23 15:23
     *<AUTHOR>
     **/
    public PageResult<OrderQuery> page(OrderPageCondition condition) {
        if(ObjectUtil.isNotNull(condition.getCreateTimeStart())){
            condition.setCreateTimeStart(DateUtil.beginOfDay(condition.getCreateTimeStart()));
        }
        if(ObjectUtil.isNotNull(condition.getCreateTimeEnd())){
            condition.setCreateTimeEnd(DateUtil.endOfDay(condition.getCreateTimeEnd()));
        }
        PageResult<OrderQuery> pageResult = dao.findPage(condition);
        if(CollectionUtil.isNotEmpty(pageResult.getRows())){
            //抽取orderNo
            List<String> orderNoList = pageResult.getRows().stream().map(OrderQuery::getOrderNo).collect(Collectors.toList());
            //根据orderNo集合查询订单商品信息
            List<OrderSkuQuery> orderSkuQueryList = orderSkuService.findByOrderNoList(orderNoList);
            //根据订单编号进行分组
            Map<String,List<OrderSkuQuery>> orderSkuMap = new HashMap<>();

            if(CollectionUtil.isNotEmpty(orderSkuQueryList)){
                //抽取商品id
                List<Long> skuIdList = orderSkuQueryList.stream().map(OrderSkuQuery::getSkuId).distinct().collect(Collectors.toList());
                List<Sku> skuList = skuService.findByIds(skuIdList.toArray(new Long[0]));
                Map<Long, Sku> skuMap = new HashMap<>();
                if(CollectionUtil.isNotEmpty(skuList)){
                    skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, item -> item));
                }
                orderSkuMap = orderSkuQueryList.stream().collect(Collectors.groupingBy(OrderSkuQuery::getOrderNo));
                List<Long> packageUnitIdList = orderSkuQueryList.stream().map(OrderSkuQuery::getPackageUnitId).distinct().collect(Collectors.toList());
                List<PackageUnit> packageUnitList = packageUnitService.findByIds(packageUnitIdList.toArray(new Long[0]));
                Map<Long, PackageUnit> packageUnitMap = new HashMap<>();
                if(CollectionUtil.isNotEmpty(packageUnitList)){
                    packageUnitMap = packageUnitList.stream().collect(Collectors.toMap(PackageUnit::getId, item -> item));
                }
                Map<Long, Sku> finalSkuMap = skuMap;
                Map<Long, PackageUnit> finalPackageUnitMap = packageUnitMap;
                orderSkuQueryList.forEach(orderSkuQuery -> {
                    Sku sku = finalSkuMap.get(orderSkuQuery.getSkuId());
                    if(ObjectUtil.isNotNull(sku)){
                        orderSkuQuery.setSkuCode(sku.getSkuCode());
                    }
                    PackageUnit packageUnit = finalPackageUnitMap.get(orderSkuQuery.getPackageUnitId());
                    if (ObjectUtil.isNotNull(packageUnit)){
                        orderSkuQuery.setMinQuantity(packageUnit.getMinQuantity());
                    }
                });
            }
            //查询关联单号
            List<OrderRelation> orderRelationList = orderRelationService.findByOrderNoList(orderNoList);
            Map<String,List<OrderRelation> > orderRelationMap = new HashMap<>();
            List<SoHeader> soHeaderList = new ArrayList<>();
            List<AsnHeader> asnHeaderList = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(orderRelationList)){
                orderRelationMap = orderRelationList.stream().collect(Collectors.groupingBy(OrderRelation::getOrderNo));
                List<String> businessNoList = orderRelationList.stream().map(OrderRelation::getBusinessNo).distinct().collect(Collectors.toList());
                if(OrderTypeEnum.SALES_ORDER.getCode().equals(condition.getOrderType())){
                    soHeaderList = soHeaderService.findBySoNoList(businessNoList);
                }else{
                    asnHeaderList = asnHeaderService.findByNoList(businessNoList);
                }
            }
            Map<String, List<OrderSkuQuery>> finalOrderSkuMap = orderSkuMap;
            Map<String, List<OrderRelation>> finalOrderRelationMap = orderRelationMap;

            List<SoHeader> finalSoHeaderList = soHeaderList;
            List<AsnHeader> finalAsnHeaderList = asnHeaderList;
            pageResult.getRows().forEach(orderQuery -> {
                List<OrderSkuQuery> orderSkuQueryList1 = finalOrderSkuMap.get(orderQuery.getOrderNo());
                if(CollectionUtil.isNotEmpty(orderSkuQueryList1)){
                    if(YesNoEnum.YES.getCode().equals(condition.getRemainCountFilter())){
                        //过滤出剩余数量大于 0 的数据
                        orderSkuQueryList1 = orderSkuQueryList1.stream().filter(orderSkuQuery -> ObjectUtil.isNotNull(orderSkuQuery.getRemainCountEa()) && orderSkuQuery.getRemainCount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                    }
                    orderQuery.setOrderSkuQueryList(orderSkuQueryList1);
                }
                List<OrderRelation> currentOrderRelationList = finalOrderRelationMap.get(orderQuery.getOrderNo());
                if(CollectionUtil.isNotEmpty(currentOrderRelationList)){
                    orderQuery.setRelatedNoList(currentOrderRelationList.stream().map(OrderRelation::getBusinessNo).distinct().collect(Collectors.toList()));
                }
                List<String> statusList = Arrays.asList(OrderStatusEnum.CANCELED.getCode(),OrderStatusEnum.COMPLETED.getCode());
                //销售订单
                if(OrderTypeEnum.SALES_ORDER.getCode().equals(orderQuery.getOrderType())){
                    //创建状态 且 未审核 展示审核按钮
                    if(OrderStatusEnum.CREATE.getCode().equals(orderQuery.getOrderStatus())
                            && AuditStatusEnum.UNREVIEWED.getCode().equals(orderQuery.getAuditStatus())){
                        orderQuery.setShowAuditBtn(Boolean.TRUE);
                    }
                    //已审核状态 且 未关联出库单 展示取消审核按钮
                    if(AuditStatusEnum.AUDITED.getCode().equals(orderQuery.getAuditStatus())
                            && CollectionUtil.isEmpty(orderQuery.getRelatedNoList())){
                        orderQuery.setShowCancelAuditBtn(Boolean.TRUE);
                    }
                }

                if(!statusList.contains(orderQuery.getOrderStatus())){
                    boolean result;
                    if(OrderStatusEnum.CREATE.getCode().equals(orderQuery.getOrderStatus())){
                        result = Boolean.TRUE;
                    }else{
                        if(OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderQuery.getOrderType())){
                            //采购订单
                            result = finalAsnHeaderList.stream().allMatch(asnHeader -> AuditStatusEnum.UNREVIEWED.getCode().equals(asnHeader.getAuditStatus()));
                        }else{
                            //销售订单
                            result = finalSoHeaderList.stream().allMatch(soHeader -> OutboundAuditStatusEnum.AUDIT_UN.getCode().equals(soHeader.getAuditStatus()));
                        }
                    }
                    orderQuery.setShowCancelBtn(result);
                    //如果是销售单，且订单审核通过
                    if(OrderTypeEnum.SALES_ORDER.getCode().equals(orderQuery.getOrderType())
                            && AuditStatusEnum.AUDITED.getCode().equals(orderQuery.getAuditStatus())){
                        orderQuery.setGenerateOrder(Boolean.TRUE);
                    }
                }

            });
        }
        return pageResult;
    }

    /**
     *@Description 订单保存
     *@Param item
     *@Return Void
     *@Date 2025/4/23 16:02
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveEntity(OrderSaveItem item) {
        Order order;
        boolean flag = Boolean.FALSE;
        if(ObjectUtil.isNull(item.getId())){
            //新增
            order = new Order();
            BeanUtil.copyProperties(item,order);
            String orderNo;
            if(OrderTypeEnum.PURCHASE_ORDER.getCode().equals(item.getOrderType())){
                orderNo = numberGenerator.nextValue(IdRuleConstant.P_ORDER_CODE);
            }else{
                orderNo = numberGenerator.nextValue(IdRuleConstant.S_ORDER_CODE);
            }
            order.setOrderNo(orderNo);
            order.setOrderSource(OrderSourceEnum.SELF_CREATE.getCode());
            order.setOrderStatus(OrderStatusEnum.CREATE.getCode());
            order.setAuditStatus(AuditStatusEnum.UNREVIEWED.getCode());
            flag = true;
        }else{
            order = findById(item.getId());
            List<String> statusList = Arrays.asList(OrderStatusEnum.PROCESSING.getCode(),OrderStatusEnum.COMPLETED.getCode());
            //是否存在已完成/进行中的订单
            if(statusList.contains(order.getOrderStatus())){
                throw new ServiceException(OrderExceptionEnum.ORDER_MODIFY_STATUS_ERROR);
            }
            BeanUtil.copyProperties(item,order);
        }
        if(CollectionUtil.isNotEmpty(item.getOrderSkuItemList())){
            //根据编号删除订单商品信息
            orderSkuService.deleteByOrderNoList(Arrays.asList(order.getOrderNo()));
            //批量保存订单商品信息
            List<OrderSku> orderSkuList = item.getOrderSkuItemList().stream().map(orderSkuItem -> {
                OrderSku orderSku = new OrderSku();
                BeanUtil.copyProperties(orderSkuItem,orderSku);
                orderSku.setOrderNo(order.getOrderNo());
                orderSku.setRemainCount(orderSku.getTotalCount());
                orderSkuService.preSave(orderSku);
                return orderSku;
            }).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(orderSkuList)){
                orderSkuService.batchInsert(orderSkuList);
            }
        }
        dao.saveOrUpdate(order);
        //新增才生物流文件
        if(flag){
            List<String> orderNoList = Arrays.asList(order.getOrderNo());
            DocumentTypeEnum code;
            if(OrderTypeEnum.PURCHASE_ORDER.getCode().equals(order.getOrderType())){
                code = DocumentTypeEnum.PURCHASE_ORDER;
            }else{
                code = DocumentTypeEnum.SALES_ORDER;
            }
            SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();
            LogisticsFolderDetailLinkCondition condition = new  LogisticsFolderDetailLinkCondition();
            condition.setDocumentNos(orderNoList);
            logger.info("采销订单订单生成物流文件参数：{}", JsonUtil.toJson(condition));
            logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(code,Arrays.asList(condition),sessionUserInfo.getCompanyId(),sessionUserInfo.getTenancy(), null);
        }

    }

    /**
     *@Description 根据id获取订单信息
     *@Param id
     *@Return * {@link OrderQuery }
     *@Date 2025/4/23 16:26
     *<AUTHOR>
     **/
    public OrderQuery getById(Long id) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(Order::getId,id);
        OrderQuery orderQuery = dao.findFirst(OrderQuery.class,conditionRule);
        if(ObjectUtil.isNotNull(orderQuery)){
            List<OrderSkuQuery> orderSkuQueryList = orderSkuService.findByOrderNoList(Arrays.asList(orderQuery.getOrderNo()));
            List<Long> packageUnitIdList = orderSkuQueryList.stream().map(OrderSkuQuery::getPackageUnitId).distinct().collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(packageUnitIdList)){
                List<PackageUnit> packageList = packageUnitService.findByIds(packageUnitIdList.toArray(new Long[]{}));
                Map<Long, PackageUnit> packageMap = packageList.stream().collect(Collectors.toMap(PackageUnit::getId, Function.identity(),  (key1, key2) -> key1));
                orderSkuQueryList.forEach(orderSkuQuery -> {
                    PackageUnit packageUnit = packageMap.get(orderSkuQuery.getPackageUnitId());
                    if(ObjectUtil.isNotNull(packageUnit)){
                        orderSkuQuery.setMinQuantity(packageUnit.getMinQuantity());
                    }
                });
            }
            orderQuery.setOrderSkuQueryList(orderSkuQueryList);
        }
        return orderQuery;
    }

    /**
     *@Description 根据订单号和订单类型获取订单信息
     *@Param id
     *@Return * {@link OrderQuery }
     *@Date 2025/4/23 16:26
     *<AUTHOR>
     **/
    public OrderPreviewQuery getByOrderNoAndOrderType(String orderNo, OrderTypeEnum orderTypeEnum) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(Order::getOrderNo, orderNo);
        conditionRule.andEqual(Order::getOrderType, orderTypeEnum.getCode());
        OrderPreviewQuery orderQuery = dao.findFirst(OrderPreviewQuery.class,conditionRule);
        if(ObjectUtil.isNotNull(orderQuery)){
            if(EmptyUtil.isNotEmpty(orderQuery.getPriorityLevel())){
                //查询数据字典
                DictDataCondition dictDataCondition = new DictDataCondition();
                dictDataCondition.setType(DictDataTypeConstants.ORDER_PRIORITY_LEVEL);
                ResponseData<List<DictDataQuery>> responseData = remoteDictDataService.getDictDataList(dictDataCondition);
                if(responseData.getSuccess()){
                    List<DictDataQuery> data = responseData.getData();
                    Map<String, String> dictMap = data.stream().collect(Collectors.toMap(DictDataQuery::getCode, DictDataQuery::getValue, (key1, key2) -> key1));
                    if(EmptyUtil.isNotEmpty(dictMap) && dictMap.containsKey(orderQuery.getPriorityLevel())) {
                        orderQuery.setPriorityLevelStr(dictMap.get(orderQuery.getPriorityLevel()));
                    }
                }
            }
            // 查询订单来源字典
            String orderSource;
            DictDataCondition dictDataCondition = new DictDataCondition();
            dictDataCondition.setType(DictDataTypeConstants.P_S_ORDER_SOURCE);
            ResponseData<List<DictDataQuery>> responseData = remoteDictDataService.getDictDataList(dictDataCondition);
            if(responseData.getSuccess()){
                List<DictDataQuery> data = responseData.getData();
                Map<String, String> dictMap = data.stream().collect(Collectors.toMap(DictDataQuery::getCode, DictDataQuery::getValue, (key1, key2) -> key1));
                if(EmptyUtil.isNotEmpty(dictMap) && dictMap.containsKey(orderQuery.getOrderSource())) {
                    orderSource = dictMap.get(orderQuery.getOrderSource());
                } else {
                    orderSource = "";
                }
            } else {
                orderSource = "";
            }

            Date deliveryDate = orderQuery.getDeliveryDate();
            List<OrderSkuPreviewQuery> orderSkuPreviewQueryList = orderSkuService.findOrderSkuPreviewByOrderNoList(Arrays.asList(orderQuery.getOrderNo()));
            orderSkuPreviewQueryList.forEach(item -> {
                item.setDeliveryDate(deliveryDate);
                item.setOrderSource(orderSource);
            });
            List<Long> packageUnitIdList = orderSkuPreviewQueryList.stream().map(OrderSkuPreviewQuery::getPackageUnitId).distinct().collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(packageUnitIdList)){
                List<PackageUnit> packageList = packageUnitService.findByIds(packageUnitIdList.toArray(new Long[]{}));
                Map<Long, PackageUnit> packageMap = packageList.stream().collect(Collectors.toMap(PackageUnit::getId, Function.identity(),  (key1, key2) -> key1));
                orderSkuPreviewQueryList.forEach(orderSkuQuery -> {
                    PackageUnit packageUnit = packageMap.get(orderSkuQuery.getPackageUnitId());
                    if(ObjectUtil.isNotNull(packageUnit)){
                        orderSkuQuery.setMinQuantity(packageUnit.getMinQuantity());
                    }
                });
            }
            orderQuery.setOrderSkuPreviewQueryList(orderSkuPreviewQueryList);
        }
        return orderQuery;
    }

    /**
     *@Description 根据ID批量删除
     *@Param item
     *@Return Void
     *@Date 2025/4/23 16:35
     *<AUTHOR>
     **/
    public void batchDel(BatchDelItme item) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(Order::getId,item.getIdList());
        List<Order> orderList = dao.find(conditionRule);
        List<String> statusList = Arrays.asList(OrderStatusEnum.PROCESSING.getCode(),OrderStatusEnum.COMPLETED.getCode());
        //是否存在已完成/进行中的订单
        boolean delFlag = orderList.stream().anyMatch(item1 -> statusList.contains(item1.getOrderStatus()));
        if(delFlag){
            throw new ServiceException(OrderExceptionEnum.ORDER_DELETE_STATUS_ERROR);
        }
        List<Order> cancelList = orderList.stream().filter(item1 -> OrderStatusEnum.CANCELED.getCode().equals(item1.getOrderStatus())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(cancelList)){
            //查询对应入库订单
            List<String> asnOrderNoList = cancelList.stream().filter(x -> OrderTypeEnum.PURCHASE_ORDER.getCode().equals(x.getOrderType())).map(Order::getOrderNo).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(asnOrderNoList)){
                List<AsnHeader> asnHeaderList = asnHeaderService.findByPOrderNoList(asnOrderNoList);
                if(CollectionUtil.isNotEmpty(asnHeaderList)){
                    List<String> errOrderNoList = asnHeaderList.stream().map(AsnHeader::getPOrderNo).distinct().collect(Collectors.toList());
                    throw new ServiceException(OrderExceptionEnum.ORDER_DELETE_RELATION_ERROR,errOrderNoList);
                }
            }
            //查询对应销售订单
            List<String> soOrderNoList = cancelList.stream().filter(x -> OrderTypeEnum.SALES_ORDER.getCode().equals(x.getOrderType())).map(Order::getOrderNo).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(soOrderNoList)){
                List<SoHeader> soHeaderList = soHeaderService.findBySaleOrderNoList(soOrderNoList);
                if(CollectionUtil.isNotEmpty(soHeaderList)){
                    List<String> errOrderNoList = soHeaderList.stream().map(SoHeader::getSaleOrderNo).distinct().collect(Collectors.toList());
                    throw new ServiceException(OrderExceptionEnum.ORDER_DELETE_RELATION_ERROR,errOrderNoList);
                }
            }
        }
        orderSkuService.deleteByOrderNoList(orderList.stream().map(Order::getOrderNo).collect(Collectors.toList()));
        dao.delete(conditionRule);
    }

    /**
     *@Description 根据ID批量取消订单
     *@Param item
     *@Return Void
     *@Date 2025/4/23 17:03
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void batchCancel(BatchDelItme item) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(Order::getId,item.getIdList());
        List<Order> orderList = dao.find(conditionRule);
        if(CollectionUtil.isNotEmpty(orderList)){
            List<String> statusList = Arrays.asList(OrderStatusEnum.CANCELED.getCode(),OrderStatusEnum.COMPLETED.getCode());
            boolean cancelFlag = orderList.stream().anyMatch(item1 -> statusList.contains(item1.getOrderStatus()));
            if (cancelFlag){
                throw new ServiceException(OrderExceptionEnum.ORDER_CANCEL_STATUS_ERROR);
            }
            //进行中状态订单
            List<Order> processingOrderList = orderList.stream().filter(x -> OrderStatusEnum.PROCESSING.getCode().equals(x.getOrderStatus())).collect(Collectors.toList());
            //销售订单
            List<Order> saleOrderList = processingOrderList.stream().filter(x -> OrderTypeEnum.SALES_ORDER.getCode().equals(x.getOrderType())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(saleOrderList)){
                List<String> orderNoList = saleOrderList.stream().map(Order::getOrderNo).collect(Collectors.toList());
                List<SoHeader> soHeadList = soHeaderService.findBySaleOrderNoList(orderNoList);
                //对应销售订单解除关联关系
                if(CollectionUtil.isNotEmpty(soHeadList)){
                    //是否存才已审核的数据
                    boolean result = soHeadList.stream().anyMatch(x -> OutboundAuditStatusEnum.AUDITED.getCode().equals(x.getAuditStatus()));
                    List<String> soNoList = soHeadList.stream().filter(x -> OutboundAuditStatusEnum.AUDITED.getCode().equals(x.getAuditStatus())).map(SoHeader::getSoNo).distinct().collect(Collectors.toList());
                    if(result){
                        throw new ServiceException(OrderExceptionEnum.ORDER_CANCEL_AUDIT_STATUS_ERROR,soNoList);
                    }
                    soHeadList.forEach(soHeader -> {
                        soHeader.setStatus(SoOrderStatusEnum.CANCEL.getCode());
                        soHeaderService.preSave(soHeader);
                    });
                    soHeaderService.batchUpdate(soHeadList);
                }
            }
            //采购订单
            List<Order> purchaseOrderList = processingOrderList.stream().filter(x -> OrderTypeEnum.PURCHASE_ORDER.getCode().equals(x.getOrderType())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(purchaseOrderList)){
                List<String> orderNoList = purchaseOrderList.stream().map(Order::getOrderNo).collect(Collectors.toList());
                List<AsnHeader> asnHeaderList = asnHeaderService.findByPOrderNoList(orderNoList);
                if(CollectionUtil.isNotEmpty(asnHeaderList)){
                    boolean result = asnHeaderList.stream().anyMatch(x -> AuditStatusEnum.AUDITED.getCode().equals(x.getAuditStatus()));
                    List<String> soNoList = asnHeaderList.stream().filter(x -> AuditStatusEnum.AUDITED.getCode().equals(x.getAuditStatus())).map(AsnHeader::getAsnNo).distinct().collect(Collectors.toList());
                    if(result){
                        throw new ServiceException(OrderExceptionEnum.ORDER_CANCEL_AUDIT_STATUS_ERROR,soNoList);
                    }
                    List<Long> asnIdList = asnHeaderList.stream().map(AsnHeader::getId).collect(Collectors.toList());
                    // 更新收货商品是否货架
                    asnHeaderService.updateReceiveWhetherShelf(asnIdList.toArray(new Long[0]));
                    // 更新出库单状态
                    asnHeaderService.updateAsnStatusByIds(asnIdList.toArray(new Long[0]), AsnStatusEnum.ASN_CANCEL.getCode());
                    // 删除sn码
                    asnHeaderService.deleteAsnDetailSnByAsnIds(asnIdList.toArray(new Long[0]));
                }
            }
            orderList.forEach(order -> {
                order.setOrderStatus(OrderStatusEnum.CANCELED.getCode());
                preSave(order);
            });
            dao.batchUpdate(orderList);
        }
    }

    /**
     *@Description 根据单号修改订单状态
     *@Param orderNo
     *@param orderStatus
     *@Return Void
     *@Date 2025/5/8 15:15
     *<AUTHOR>
     **/
    public void updateOrderStatus(String orderNo, String orderStatus) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(Order::getOrderNo,orderNo);
        Order order = dao.findFirst(Order.class,conditionRule);
        if(ObjectUtil.isNotNull(order)){
            order.setOrderStatus(orderStatus);
            preSave(order);
            dao.update(order);
        }
    }

    /**
     *@Description 根据编号进行查询
     *@Param orderNoList
     *@Return * {@link List< Order> }
     *@Date 2025/5/14 17:38
     *<AUTHOR>
     **/
    public List<Order> findByOrderNoList(List<String> orderNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(Order::getOrderNo,orderNoList);
        return dao.find(conditionRule);
    }

    /**
     *@Description 根据编号修改订单状态
     *@Param orderNoList
     *@param code
     *@Return Void
     *@Date 2025/5/23 17:30
     *<AUTHOR>
     **/
    public void updateOrderStatusByOrderNoList(List<String> orderNoList, String code) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(Order::getOrderNo,orderNoList);
        List<Order> orderList = dao.find(conditionRule);
        if(CollectionUtil.isNotEmpty(orderList)){
            orderList.forEach(item -> {
                item.setOrderStatus(code);
                preSave(item);
            });
            dao.batchUpdate(orderList);
        }
    }

    /**
     *@Description 销售单批量审核
     *@Param item
     *@Return Void
     *@Date 2025/6/11 14:24
     *<AUTHOR>
     **/
    public void batchAudit(BatchDelItme item) {
        List<Order> orderList = findByIds(item.getIdList().toArray(new Long[0]));
        //是否都是未审核状态
        boolean result = orderList.stream().anyMatch(x -> AuditStatusEnum.AUDITED.getCode().equals(x.getOrderStatus()));
        List<String> failNoList = orderList.stream().filter( x -> AuditStatusEnum.AUDITED.getCode().equals(x.getOrderStatus())).map(Order::getOrderNo).collect(Collectors.toList());
        if(result){
            throw new ServiceException(OrderExceptionEnum.ORDER_AUDIT_STATUS_ERROR,failNoList);
        }
        //只有创建状态才能审核
        boolean statusFlag = orderList.stream().anyMatch(x -> !OrderStatusEnum.CREATE.getCode().equals(x.getOrderStatus()));
        failNoList = orderList.stream().filter( x -> !OrderStatusEnum.CREATE.getCode().equals(x.getOrderStatus())).map(Order::getOrderNo).collect(Collectors.toList());
        if(statusFlag){
            throw new ServiceException(OrderExceptionEnum.ORDER_ORDER_STATUS_ERROR,failNoList);
        }
        orderList.forEach(order -> {
            order.setAuditStatus(AuditStatusEnum.AUDITED.getCode());
            preSave(order);
        });
        dao.batchUpdate(orderList);
    }

    /**
     *  批量取消审核
     *@Param item
     *@Return Void
     *@Date 2025/6/11 14:30
     *<AUTHOR>
     **/
    public void batchCancelAudit(BatchDelItme item) {
        List<Order> orderList = findByIds(item.getIdList().toArray(new Long[0]));
        //是否都是已审核状态
        boolean result = orderList.stream().anyMatch(x -> AuditStatusEnum.UNREVIEWED.getCode().equals(x.getOrderStatus()));
        List<String> failNoList = orderList.stream().filter( x -> AuditStatusEnum.UNREVIEWED.getCode().equals(x.getOrderStatus())).map(Order::getOrderNo).collect(Collectors.toList());
        if(result){
            throw new ServiceException(OrderExceptionEnum.ORDER_NOT_AUDIT_STATUS_ERROR,failNoList);
        }
        //是否有关联出库单
        List<SoHeader> soHeaderList = soHeaderService.findBySaleOrderNoList(orderList.stream().map(Order::getOrderNo).collect(Collectors.toList()));
        Map<String,List<SoHeader>> soHeaderMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(soHeaderList)){
            soHeaderMap = soHeaderList.stream().collect(Collectors.groupingBy(SoHeader::getSaleOrderNo));
        }
        Map<String, List<SoHeader>> finalSoHeaderMap = soHeaderMap;
        orderList.forEach(order -> {
            List<SoHeader> currentSoHeaderList = finalSoHeaderMap.get(order.getOrderNo());
            if(CollectionUtil.isNotEmpty(currentSoHeaderList)){
                throw new ServiceException(OrderExceptionEnum.ORDER_CANCEL_AUDIT_RELATION_ERROR,order.getOrderNo());
            }
            order.setAuditStatus(AuditStatusEnum.UNREVIEWED.getCode());
            preSave(order);
        });
        dao.batchUpdate(orderList);
    }
}
