package com.chinaservices.wms.module.passive.task.dao;


import com.chinaservices.wms.module.passive.task.domain.PassiveTaskPageCondition;
import com.chinaservices.wms.module.passive.task.domain.PassiveTaskQuery;
import com.chinaservices.wms.module.passive.task.model.PassiveTask;
import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import org.springframework.stereotype.Repository;

import static com.chinaservices.wms.common.constant.TaskConstant.CS_PASSIVE_TASK_QUERY_GET_PAGE_LIST;

@Repository
public class PassiveTaskDao extends ModuleBaseDaoSupport<PassiveTask,Long> {

    /**
    * 分页查询
    * @param condition 查询条件
    * @return PageResult<PassiveTaskQuery>
    */
    public PageResult<PassiveTaskQuery> page(PassiveTaskPageCondition condition) {
        return sqlExecutor.page(PassiveTaskQuery.class, CS_PASSIVE_TASK_QUERY_GET_PAGE_LIST, condition);
    }


}
