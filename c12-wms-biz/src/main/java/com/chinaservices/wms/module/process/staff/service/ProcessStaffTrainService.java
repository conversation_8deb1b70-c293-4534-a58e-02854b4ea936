package com.chinaservices.wms.module.process.staff.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjUtil;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.wms.module.process.staff.dao.ProcessStaffTrainDao;
import com.chinaservices.wms.module.process.staff.domain.ProcessStaffTrainTableItem;
import com.chinaservices.wms.module.process.staff.model.ProcessStaffTrain;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: Cyran.Chen
 * @Date: 2025/4/15
 * @Description: 加工人员培训记录服务层
 */
@Service
public class ProcessStaffTrainService extends ModuleBaseServiceSupport<ProcessStaffTrainDao, ProcessStaffTrain, Long> {

    /**
     * 新增培训记录
     * @param psTrainTableItemList 入参
     * @param staffId 入参
     * @return Boolean
     */
    public Boolean save(List<ProcessStaffTrainTableItem> psTrainTableItemList, Long staffId) {
        if(CollectionUtil.isEmpty(psTrainTableItemList)){
            return Boolean.TRUE;
        }
        List<ProcessStaffTrain> psTrainList = new ArrayList<>();
        for (ProcessStaffTrainTableItem psTrainTableItem : psTrainTableItemList) {
            ProcessStaffTrain psTrain = new ProcessStaffTrain();
            BeanUtil.copyProperties(psTrainTableItem, psTrain);
            psTrain.setStaffId(staffId);
            preSave(psTrain);
            psTrainList.add(psTrain);
        }
        return dao.batchInsert(psTrainList) > 0;
    }

    /**
     * 更新或删除培训记录
     * @param trainTableList 入参
     * @param staffId 入参
     * @return Boolean
     */
    public Boolean edit(List<ProcessStaffTrainTableItem> trainTableList, Long staffId) {
        // 1. 获取该stuffId下所有的培训记录
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(ProcessStaffTrain::getStaffId, staffId);
        List<ProcessStaffTrain> trainList = dao.find(conditionRule);
        // 2. 当培训记录为空时 删除所有数据库中的数据
        if(CollectionUtil.isEmpty(trainTableList)){
            if(CollectionUtil.isNotEmpty(trainList)){
                List<Long> trainIdList = trainList.stream().map(ProcessStaffTrain::getId).toList();
                return dao.delete(ArrayUtil.toArray(trainIdList, Long.class)) > 0;
            }
            return Boolean.TRUE;
        }
        // 3. 如果没有记录就直接全部新增
        if(CollectionUtil.isEmpty(trainList)){
            return save(trainTableList, staffId);
        }
        // 4. 存在记录的时 过滤出数据库中原有id与新的id
        List<Long> idOriginList = trainList.stream().map(ProcessStaffTrain::getId).toList();
        List<Long> idNewList = trainTableList.stream().map(ProcessStaffTrainTableItem::getId).toList();
        // 5. 构建新增列表、更新列表与删除列表
        List<ProcessStaffTrain> insertList = new ArrayList<>();
        List<ProcessStaffTrain> updateList = new ArrayList<>();
        List<Long> deleteList = idOriginList.stream().filter(id -> !idNewList.contains(id)).toList();
        // 6. 遍历所有的tableItem
        for (ProcessStaffTrainTableItem trainTableItem : trainTableList) {
            // 6.1 新增/更新前操作
            ProcessStaffTrain psTrain = new ProcessStaffTrain();
            // 7. 当不存在id时则意味着新增
            if(ObjUtil.isNull(trainTableItem.getId())){
                BeanUtil.copyProperties(trainTableItem, psTrain);
                psTrain.setStaffId(staffId);
                preSave(psTrain);
                insertList.add(psTrain);
            }
            // 8. 当两者都存在该id时则意味着更新
            else if(BooleanUtil.and(
                    idOriginList.contains(trainTableItem.getId()),
                    idNewList.contains(trainTableItem.getId())
            )){
                psTrain = dao.findById(trainTableItem.getId());
                psTrain.setTrainName(trainTableItem.getTrainName());
                psTrain.setTrainTime(trainTableItem.getTrainTime());
                psTrain.setTrainScore(trainTableItem.getTrainScore());
                preSave(psTrain);
                updateList.add(psTrain);
            }
        }
        Long[] deleteIds = ArrayUtil.toArray(deleteList, Long.class);
        return BooleanUtil.and(
                ArrayUtil.isNotEmpty(deleteIds) ? dao.delete(deleteIds) > 0 : Boolean.TRUE,
                CollectionUtil.isNotEmpty(insertList) ? dao.batchInsert(insertList) > 0 : Boolean.TRUE,
                CollectionUtil.isNotEmpty(updateList) ? dao.batchUpdate(updateList) > 0 : Boolean.TRUE
        );
    }
}
