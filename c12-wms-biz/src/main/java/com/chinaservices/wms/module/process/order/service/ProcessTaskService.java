package com.chinaservices.wms.module.process.order.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.enums.so.SoAllocStatusEnum;
import com.chinaservices.wms.common.enums.so.SoPickingStatusEnum;
import com.chinaservices.wms.module.process.order.dao.ProcessTaskDao;
import com.chinaservices.wms.module.process.order.domain.ProcessTaskDetailsItem;
import com.chinaservices.wms.module.process.order.domain.ProcessTaskDetailsQuery;
import com.chinaservices.wms.module.process.order.domain.ProcessTaskItem;
import com.chinaservices.wms.module.process.order.domain.ProcessTaskQuery;
import com.chinaservices.wms.module.process.order.model.ProcessTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName ProcessTaskService
 * <AUTHOR>
 * @Date 2025/6/26 20:34
 * @Description
 * @Version 1.0
 */
@Service
public class ProcessTaskService extends ModuleBaseServiceSupport<ProcessTaskDao, ProcessTask,Long> {


    @Autowired
    private ProcessTaskDetailsService processTaskDetailsService;

    @Autowired
    private NumberGenerator numberGenerator;



    /***
     *@Description 根据编号查询所有任务
     *@Param processOrderNoList
     *@Return * {@link List< ProcessTaskQuery> }
     *@Date 2025/6/30 11:52
     *<AUTHOR>
     **/
    public List<ProcessTaskQuery> findQueryByProcessOrderNoList(List<String> processOrderNoList) {
        ConditionRule conditionRule =  ConditionRule.getInstance();
        conditionRule.andIn(ProcessTask::getProcessOrderNo,processOrderNoList);
        List<ProcessTaskQuery> processTaskQueryList = dao.find(ProcessTaskQuery.class,conditionRule);
        List<ProcessTaskDetailsQuery> processTaskDetailsQueryList = processTaskDetailsService.findQueryByProcessOrderNoList(processOrderNoList);
        Map<String,List<ProcessTaskDetailsQuery>> processTaskDetailsQueryMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(processTaskQueryList)){
            processTaskDetailsQueryMap = processTaskDetailsQueryList.stream().collect(Collectors.groupingBy(ProcessTaskDetailsQuery::getProcessTaskNo));
        }
        Map<String, List<ProcessTaskDetailsQuery>> finalProcessTaskDetailsQueryMap = processTaskDetailsQueryMap;
        processTaskQueryList.forEach(processTaskQuery -> {
            List<ProcessTaskDetailsQuery> currentDetailsQueryList = finalProcessTaskDetailsQueryMap.get(processTaskQuery.getProcessTaskNo());
            if(CollectionUtil.isNotEmpty(currentDetailsQueryList)){
                processTaskQuery.setDetailsQueryList(currentDetailsQueryList);
            }
        });
        return processTaskQueryList;
    }

    /**
     *@Description 根据加工单号删除任务
     *@Param processOrderNoList
     *@Return Void
     *@Date 2025/6/30 15:38
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteByProcessOrderNoList(List<String> processOrderNoList) {
        ConditionRule conditionRule =  ConditionRule.getInstance();
        conditionRule.andIn(ProcessTask::getProcessOrderNo,processOrderNoList);
        dao.delete(conditionRule);
        //删除任务明细
        processTaskDetailsService.batchDeleteByProcessOrderNoList(processOrderNoList);
    }

    /**
     *@Description 批量保存任务
     *@Param taskItemList
     *@Return Void
     *@Date 2025/6/30 17:57
     *<AUTHOR>
     **/
    public void batchSaveEntity(List<ProcessTaskItem> taskItemList) {
        if(CollectionUtil.isNotEmpty(taskItemList)){
            List<ProcessTask> processTaskList = new ArrayList<>();
            List<ProcessTaskDetailsItem> taskDetailsItemList = new ArrayList<>();
            taskItemList.forEach(taskItem -> {
                ProcessTask processTask = new ProcessTask();
                BeanUtil.copyProperties(taskItem,processTask);
                processTask.setProcessTaskNo(numberGenerator.nextValue(IdRuleConstant.PROCESS_TASK_NO));
                processTask.setAllocStatus(SoAllocStatusEnum.UN_ALLOC.getCode());
                processTask.setQtyAllocationEa(BigDecimal.ZERO);
                processTask.setPickingStatus(SoPickingStatusEnum.UNPICKED.getCode());
                processTask.setQtyPickingEa(BigDecimal.ZERO);
                preSave(processTask);
                processTaskList.add(processTask);
                taskItem.getDetailsItemList().forEach(detailsItem -> {
                    detailsItem.setProcessTaskNo(processTask.getProcessTaskNo());
                    detailsItem.setProcessOrderNo(processTask.getProcessOrderNo());
                    taskDetailsItemList.add(detailsItem);
                });
            });
            dao.batchInsert(processTaskList);
            //保存任务明细
            processTaskDetailsService.batchSaveEntity(taskDetailsItemList);
        }
    }
}
