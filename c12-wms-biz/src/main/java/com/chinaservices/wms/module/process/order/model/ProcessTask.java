package com.chinaservices.wms.module.process.order.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 加工拆分任务表
 * cs_process_task
 */
@Data
@Entity
@Table(name = "cs_process_task")
public class ProcessTask extends ModuleBaseModel {

    /**
     * 加工单编号
     */
    private String processOrderNo;

    /**
     * 加工任务编号
     */
    private String processTaskNo;

    /**
     * 组合件id
     */
    private Long combinationsId;

    /**
     * 组合件编号
     */
    private String combinationsNo;

    /**
     * 组合件商品id
     */
    private Long combinationsSkuId;

    /**
     * 组合件商品编号
     */
    private String combinationsSkuCode;

    /**
     * 组合件商品名称
     */
    private String combinationsSkuName;

    /**
     * 组合件包装规格id
     */
    private Long combinationsPackageId;

    /**
     * 组合件包装规格名称
     */
    private String combinationsPackageName;

    /**
     * 组合件包装单位id
     */
    private Long combinationsPackageUnitId;

    /**
     * 组合件包装单位名称
     */
    private String combinationsPackageUnitName;

    /**
     * 待加工数量
     */
    private BigDecimal processQuantity;

    /**
     * 待加工数量ea
     */
    private BigDecimal processQuantityEa;

    /**
     * 实际加工数量
     */
    private BigDecimal actualProcessQuantity;

    /**
     * 实际加工数量ea
     */
    private BigDecimal actualProcessQuantityEa;

    /**
     * 分配数ea
     */
    private BigDecimal qtyAllocationEa;

    /**
     * 分配状态，数据字典：alloc_status
     */
    private String allocStatus;

    /**
     * 拣货数ea
     */
    private BigDecimal qtyPickingEa;

    /**
     * 拣货状态，数据字典：picking_status
     */
    private String pickingStatus;

}