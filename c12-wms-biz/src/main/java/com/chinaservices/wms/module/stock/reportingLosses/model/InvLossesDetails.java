package com.chinaservices.wms.module.stock.reportingLosses.model;

import java.math.BigDecimal;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 库存报损明细表
 * cs_inv_losses_details
 */
@Data
@Entity
@Table(name = "cs_inv_losses_details")
public class InvLossesDetails extends ModuleBaseModel {



    /**
     * 报损编号
     */
    private String lossesNo;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 库位id
     */
    private Long locId;

    /**
     * 库位名称
     */
    private String locName;

    /**
     * 包装规格id
     */
    private Long packageId;

    /**
     * 包装规格名称
     */
    private String packageName;

    /**
     * 包装单位id
     */
    private Long packageUnitId;

    /**
     * 包装单位名称
     */
    private String packageUnitName;

    /**
     * 报损数
     */
    private BigDecimal lossesQuantity;

    /**
     * 报损数ea
     */
    private BigDecimal lossesQuantityEa;

    /**
     * 容器号
     */
    private String palletNum;

    /**
     * 批次属性
     */
    private String lotAtt;

    /**
     * 批次号
     */
    private String lotNum;

    /**
     * 批次属性1
     */
    private String lotAtt01;

    /**
     * 批次属性2
     */
    private String lotAtt02;

    /**
     * 批次属性3
     */
    private String lotAtt03;

}