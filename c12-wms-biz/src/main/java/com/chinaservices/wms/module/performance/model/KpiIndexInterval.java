package com.chinaservices.wms.module.performance.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "cs_kpi_index_interval")
public class KpiIndexInterval extends ModuleBaseModel {

    /**
     * 指标编号
     */
    private String indexNo;
    /**
     * 区间量
     */
    private Long quantity;
    /**
     * 区间量单位
     */
    private String unit;
    /**
     * 公式
     */
    private String formula;
    /**
     * 平分规则-分数
     */
    private BigDecimal scoringRules;
}
