package com.chinaservices.wms.module.stock.reportingLosses.controller;


import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.folder.logistics.domain.BatchDelDetailItem;
import com.chinaservices.wms.module.stock.multi.domain.SnSkuPageCondition;
import com.chinaservices.wms.module.stock.multi.domain.SnSkuPageQuery;
import com.chinaservices.wms.module.stock.reportingLosses.domain.InvReportingLossesPageCondition;
import com.chinaservices.wms.module.stock.reportingLosses.domain.InvReportingLossesQuery;
import com.chinaservices.wms.module.stock.reportingLosses.domain.ReportingLossesSaveItem;
import com.chinaservices.wms.module.stock.reportingLosses.service.InvReportingLossesService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @ClassName 库存报损
 * <AUTHOR>
 * @Date 2025/7/11 16:35
 * @Description
 * @Version 1.0
 */
@RestController
@RequestMapping("/api/invReportingLosses")
public class InvReportingLossesController {

    @Autowired
    private InvReportingLossesService invReportingLossesService;

    /**
     *  库存报损分页查询
     *@Param queryCondition
     *@Return * {@link ResponseData< PageResult<InvReportingLossesQuery>> }
     *@Date 2025/7/11 16:37
     *<AUTHOR>
     **/
    @PostMapping("/getPage")
    public ResponseData<PageResult<InvReportingLossesQuery>> getPage(@RequestBody InvReportingLossesPageCondition queryCondition){
        PageResult<InvReportingLossesQuery> pageResult = invReportingLossesService.getPage(queryCondition);
        return ResponseData.success(pageResult);
    }

    /**
     * 根据id查询信息
     *@Param id
     *@Return * {@link ResponseData< InvReportingLossesQuery> }
     *@Date 2025/7/11 17:03
     *<AUTHOR>
     **/
    @PostMapping("/getById/{id}")
    public ResponseData<InvReportingLossesQuery> getPage(@PathVariable Long id){
        InvReportingLossesQuery query = invReportingLossesService.getQueryById(id);
        return ResponseData.success(query);
    }

    /**
     * 批量删除
     *@Param item
     *@Return * {@link ResponseData }
     *@Date 2025/7/11 17:31
     *<AUTHOR>
     **/
    @PostMapping("/batchDel")
    public ResponseData batchDel(@RequestBody @Valid BatchDelDetailItem item){
        invReportingLossesService.batchDel(item);
        return ResponseData.success();
    }

    /**
     *  批量审核
     *@Param item
     *@Return * {@link ResponseData }
     *@Date 2025/7/11 17:53
     *<AUTHOR>
     **/
    @PostMapping("/batchAudit")
    public ResponseData batchAudit(@RequestBody @Valid BatchDelDetailItem item){
        invReportingLossesService.batchAudit(item);
        return ResponseData.success();
    }

    /**
     *  批量取消审核
     *@Param item
     *@Return * {@link ResponseData }
     *@Date 2025/7/11 17:54
     *<AUTHOR>
     **/
    @PostMapping("/batchCancelAudit")
    public ResponseData batchCancelAudit(@RequestBody @Valid BatchDelDetailItem item){
        invReportingLossesService.batchCancelAudit(item);
        return ResponseData.success();
    }

    /**
     * 库存报损保存
     *@Param item
     *@Return * {@link ResponseData }
     *@Date 2025/7/14 9:54
     *<AUTHOR>
     **/
    @PostMapping("/save")
    public ResponseData saveEntity(@RequestBody @Valid ReportingLossesSaveItem item){
        invReportingLossesService.saveEntity(item);
        return ResponseData.success();
    }

    /**
     *  确认报损
     *@Param item
     *@Return * {@link ResponseData }
     *@Date 2025/7/14 16:33
     *<AUTHOR>
     **/
    @PostMapping("/batchConfirm")
    public ResponseData batchConfirm(@RequestBody @Valid BatchDelDetailItem item){
        invReportingLossesService.batchConfirm(item);
        return ResponseData.success();
    }

    /**
     * 序列号商品分页查询
     *@Param condition
     *@Return * {@link ResponseData< PageResult<SnSkuPageQuery>> }
     *@Date 2025/7/15 14:36
     *<AUTHOR>
     **/
    @PostMapping("/getSnSkuPage")
    public ResponseData<PageResult<SnSkuPageQuery>> getSnSkuPage(@RequestBody @Valid SnSkuPageCondition condition){
        PageResult<SnSkuPageQuery> pageResult = invReportingLossesService.getSnSkuPage(condition);
        return ResponseData.success(pageResult);
    }

    /**
     * 序列号商品查询序列号
     *@Param condition
     *@Return * {@link ResponseData< Map< String, List< String>>> }
     *@Date 2025/7/15 16:15
     *<AUTHOR>
     **/
    @PostMapping("/getSnMap")
    public ResponseData<Map<String, List<String>>> getSnMap(@RequestBody @Valid SnSkuPageCondition condition){
        Map<String, List<String>> snMap = invReportingLossesService.getSnMap(condition);
        return ResponseData.success(snMap);
    }

}
