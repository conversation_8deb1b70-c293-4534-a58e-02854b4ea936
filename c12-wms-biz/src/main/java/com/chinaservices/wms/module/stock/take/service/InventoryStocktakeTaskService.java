package com.chinaservices.wms.module.stock.take.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.JsonUtil;
import com.chinaservices.util.StringUtil;
import com.chinaservices.wms.common.domain.IdsReq;
import com.chinaservices.wms.common.enums.stock.StocktakeOrderStatusEnum;
import com.chinaservices.wms.common.enums.stock.StocktakeTaskStatusEnum;
import com.chinaservices.wms.common.enums.stock.StocktakeTaskTypeEnum;
import com.chinaservices.wms.common.exception.StockExcepitonEnum;
import com.chinaservices.wms.module.asn.asn.domain.AsnDetailSnItem;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.asn.asn.service.AsnDetailSnService;
import com.chinaservices.wms.module.excel.handler.ExportDataHandler;
import com.chinaservices.wms.module.performance.domain.PerformanceAnalysisPageCondition;
import com.chinaservices.wms.module.performance.domain.StocktakeTaskPerformanceAnalysisQuery;
import com.chinaservices.wms.module.stock.loc.domain.LocSnDetailCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocSnDetailItemQuery;
import com.chinaservices.wms.module.stock.loc.domain.LocSnDetailQuery;
import com.chinaservices.wms.module.stock.loc.domain.LocSnItem;
import com.chinaservices.wms.module.passive.take.service.PassiveAutoInventoryNewService;
import com.chinaservices.wms.module.stock.loc.domain.*;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLocSn;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocSnService;
import com.chinaservices.wms.module.stock.take.dao.InventoryStocktakeTaskDao;
import com.chinaservices.wms.module.stock.take.domain.*;
import com.chinaservices.wms.module.stock.take.model.*;
import com.chinaservices.wms.module.warehouse.loc.domain.WarehouseLocPageCondition;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName InventoryStocktakeTaskService
 * <AUTHOR>
 * @Date 2025/1/11 16:01
 * @Description
 * @Version 1.0
 */
@Service
public class InventoryStocktakeTaskService extends ModuleBaseServiceSupport<InventoryStocktakeTaskDao, InventoryStocktakeTask,Long> {

    @Autowired
    private InventoryStocktakeTaskDetailsService detailsService;

    @Autowired
    @Lazy
    private InventoryStocktakeOrderService stocktakeOrderService;

    @Autowired
    private ExportDataHandler exportDataHandler;

    @Autowired
    private InventoryStocktakeConditionService conditionService;

    @Autowired
    private AsnDetailSnService asnDetailSnService;

    @Autowired
    private InventoryStocktakeTaskDetailsSnService detailsSnService;

    @Autowired
    private InvLotLocSnService invLotLocSnService;

    @Autowired
    private WarehouseLocService warehouseLocService;

    @Autowired
    @Lazy
    private PassiveAutoInventoryNewService passiveAutoInventoryNewService;


    /**
     *@Description 盘点任务分页查询
     *@Param condition
     *@Return * {@link PageResult<  StocktakeTaskQuery > }
     *@Date 2025/1/11 17:53
     *<AUTHOR>
     **/
    public PageResult<StocktakeTaskQuery> page(StocktakeTaskPageCondition condition) {
        PageResult<StocktakeTaskQuery> pageResult = dao.page(condition);
        if(CollectionUtil.isNotEmpty(pageResult.getRows())){
            //抽取盘点单号
            List<String> orderNoList = pageResult.getRows().stream().map(StocktakeTaskQuery::getStocktakeNo).distinct().collect(Collectors.toList());
            //根据盘点单号查询对应查询条件集合
            List<InventoryStocktakeCondition> conditionList = conditionService.findByStocktakeNoList(orderNoList);
            Map<String, List<InventoryStocktakeCondition>> conditionMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(conditionList)){
                conditionMap = conditionList.stream().collect(Collectors.groupingBy(InventoryStocktakeCondition::getStocktakeNo));
            }
            //抽取任务编号
            List<String> taskNoList = pageResult.getRows().stream().map(StocktakeTaskQuery::getTaskNo).distinct().collect(Collectors.toList());
            //根据编号查询详情
            List<StocktakeTaskDetailsQuery> detailsQueryList = detailsService.findQueryByTaskNoList(taskNoList);
            //根据任务编号分组
            Map<String, List<StocktakeTaskDetailsQuery>> detailsMap = detailsQueryList.stream().collect(Collectors.groupingBy(StocktakeTaskDetailsQuery::getTaskNo));
            Map<String, List<InventoryStocktakeCondition>> finalConditionMap = conditionMap;
            pageResult.getRows().forEach(item->{
                List<StocktakeTaskDetailsQuery> currentDetails = detailsMap.get(item.getTaskNo());
                if(CollectionUtil.isNotEmpty(currentDetails)){
                    Long inventoryCount = currentDetails.stream().filter(x -> ObjectUtil.isNotNull(x.getInventoryCount())).mapToLong(StocktakeTaskDetailsQuery::getInventoryCount).sum();
                    item.setInventoryCount(inventoryCount);
                    boolean flag = currentDetails.stream().allMatch(x -> ObjectUtil.isNull(x.getActualCount()));
                    Long actualCount;
                    if(flag){
                        actualCount = null;
                    }else{
                        actualCount = currentDetails.stream().filter(x -> ObjectUtil.isNotNull(x.getActualCount())).mapToLong(StocktakeTaskDetailsQuery::getActualCount).sum();
                    }
                    item.setActualCount(actualCount);
                    Long profitLossCount = 0L;
                    for(StocktakeTaskDetailsQuery details : currentDetails){
                        if(ObjectUtil.isNotNull(details.getActualCount()) && ObjectUtil.isNotNull(details.getInventoryCount())){
                            profitLossCount +=  details.getActualCount() - details.getInventoryCount();
                        }
                    }
                    if(profitLossCount == 0){
                        profitLossCount = null;
                    }
                    item.setProfitLossCount(profitLossCount);
                    String warehouseZoneName = "";
                    //对应盘点单任务生成方式是不是直接生成
                    if(StocktakeTaskTypeEnum.DIRECT_GENERATION.getCode().equals(item.getTaskType())){
                        List<InventoryStocktakeCondition> currentConditionList = finalConditionMap.get(item.getStocktakeNo());
                        List<String> areaNameList = new ArrayList<>();
                        List<String> warehouseZoneNameList = new ArrayList<>();
                        List<String> locNameList = new ArrayList<>();
                        if(CollectionUtil.isNotEmpty(currentConditionList)){
                            areaNameList = currentConditionList.stream().map(InventoryStocktakeCondition::getAreaName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
                            warehouseZoneNameList = currentConditionList.stream().map(InventoryStocktakeCondition::getWarehouseZoneName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
                            locNameList = currentConditionList.stream().map(InventoryStocktakeCondition::getWarehouseLocName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
                            if(CollectionUtil.isNotEmpty(areaNameList)){
                                warehouseZoneName += String.join(",",areaNameList);
                            }
                            if (CollectionUtil.isNotEmpty(warehouseZoneNameList)){
                                warehouseZoneName += String.join(",",warehouseZoneNameList);
                            }
                            if(CollectionUtil.isNotEmpty(locNameList)){
                                warehouseZoneName += String.join(",",locNameList);
                            }
                        }
                    }else{
                        warehouseZoneName = currentDetails.stream().map(StocktakeTaskDetailsQuery::getWarehouseZoneName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.joining(","));
                    }
                    item.setWarehouseZoneName(warehouseZoneName);

                }

            });
        }
        return pageResult;
    }

    /**
     *@Description 根据任务编号查询任务详情
     *@Param taskNo
     *@Return * {@link StocktakeTaskQuery }
     *@Date 2025/1/13 19:42
     *<AUTHOR>
     **/
    public StocktakeTaskQuery getByTaskNo(String taskNo) {
        StocktakeTaskQuery query = new StocktakeTaskQuery();
        //根据任务号查询任务
        InventoryStocktakeTask task = findByTaskNo(taskNo);
        if(ObjectUtil.isNull(task)){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKETASK_NOT_EXIST,taskNo);
        }
        String no = task.getStocktakeNo();
        //根据单号查询盘点单信息
        InventoryStocktakeOrder order = stocktakeOrderService.findByStocktakeNo(no);
        //查询对应盘点条件
        List<StocktakeConditionQuery> conditionList = conditionService.findItemByStocktakeNo(order.getStocktakeNo());
        if(CollectionUtil.isNotEmpty(conditionList)){
            //对应货主id集合
         query.setOwnerIdList(conditionList.stream().map(StocktakeConditionQuery::getOwnerId).distinct().collect(Collectors.toList()));
        }
        query.setId(task.getId());
        query.setTaskNo(taskNo);
        query.setStocktakeNo(order.getStocktakeNo());
        query.setTaskStatus(task.getTaskStatus());
        query.setTaskAllocation(task.getTaskAllocation());
        query.setStocktakeTargetType(order.getStocktakeTargetType());
        query.setStocktakeType(order.getStocktakeType());
        query.setWarehouseId(order.getWarehouseId());
        query.setWarehouseName(order.getWarehouseName());
        List<StocktakeTaskDetailsQuery> detailsQueryList = detailsService.findQueryByTaskNoList(Arrays.asList(taskNo));
        query.setDetailsQueryList(detailsQueryList);
        //如果是直接生成盘点任务，组装所选目标Id集合用以过滤库位
        if(StocktakeTaskTypeEnum.DIRECT_GENERATION.getCode().equals(order.getTaskType())){
            query.setAreaIdList(conditionList.stream().map(StocktakeConditionQuery::getAreaId).distinct().collect(Collectors.toList()));
            query.setWarehouseZoneIdList(conditionList.stream().map(StocktakeConditionQuery::getWarehouseZoneId).distinct().collect(Collectors.toList()));
            query.setLocIdList(conditionList.stream().map(StocktakeConditionQuery::getWarehouseLocId).distinct().collect(Collectors.toList()));
        }else{
            query.setWarehouseZoneIdList(detailsQueryList.stream().map(StocktakeTaskDetailsQuery::getWarehouseZoneId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList()));
        }
        //汇总库存总数
        query.setInventoryCount(detailsQueryList.stream().filter(x -> ObjectUtil.isNotNull(x.getInventoryCount())).mapToLong(StocktakeTaskDetailsQuery::getInventoryCount).sum());
        //汇总盘点总数
        query.setActualCount(detailsQueryList.stream().filter(x -> ObjectUtil.isNotNull(x.getActualCount())).mapToLong(StocktakeTaskDetailsQuery::getActualCount).sum());
        //汇总损益数量
        query.setProfitLossCount(detailsQueryList.stream().filter(x -> ObjectUtil.isNotNull(x.getProfitLossCount())).mapToLong(StocktakeTaskDetailsQuery::getProfitLossCount).sum());
        return query;
    }

    /**
     *@Description 根据任务编号查询任务
     *@Param taskNo
     *@Return * {@link InventoryStocktakeTask }
     *@Date 2025/2/12 17:22
     *<AUTHOR>
     **/
    public InventoryStocktakeTask findByTaskNo(String taskNo) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(InventoryStocktakeTask::getTaskNo,taskNo);
        InventoryStocktakeTask task = dao.findFirst(conditionRule);
        return task;
    }

    /**
     *@Description 根据盘点单号删除任务及明细
     *@Param noList
     *@Return Void
     *@Date 2025/1/13 21:02
     *<AUTHOR>
     **/
    public void deleteByStocktakeNoList(List<String> noList) {
        detailsService.deleteByStocktakeNoList(noList);
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeTask::getStocktakeNo,noList);
        dao.delete(conditionRule);
    }

    /**
     *@Description 批量分配盘点员
     *@Param item
     *@Return Void
     *@Date 2025/1/13 21:41
     *<AUTHOR>
     **/
    public void batchAssignment(StocktakeTaskAssignmentItem item) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeTask::getId,item.getIdList());
        List<InventoryStocktakeTask> taskList = dao.find(conditionRule);
        if(CollectionUtil.isNotEmpty(taskList)){
            Date date = new Date();
            taskList.forEach(updateItem->{
                updateItem.setPersonnelId(item.getPersonnelId());
                updateItem.setPersonnelName(item.getPersonnelName());
                //已分配
                updateItem.setTaskAllocation(YesNoEnum.YES.getCode());
                updateItem.setTaskStartTime(date);
                preSave(updateItem);
            });
            dao.batchUpdate(taskList);
        }
    }

    /**
     *@Description 关闭盘点任务
     *@Param id
     *@Return Void
     *@Date 2025/1/14 10:53
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void closeTask(Long id) {
        InventoryStocktakeTask task = dao.findById(id);
        if(StocktakeTaskStatusEnum.CLOSE.getCode().equals(task.getTaskStatus())){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_TASK_CLOSE_ERROR);
        }
        task.setTaskStatus(StocktakeTaskStatusEnum.CLOSE.getCode());
        task.setTaskEndTime(new Date());
        dao.saveOrUpdate(task);
        //根据盘点单号查询所有盘点任务
        List<InventoryStocktakeTask> taskList = dao.find(ConditionRule.getInstance().andEqual(InventoryStocktakeTask::getStocktakeNo,task.getStocktakeNo()));
        //如果所有盘点任务都关闭，则盘点单状态为完全盘点
        if(CollectionUtil.isNotEmpty(taskList) && taskList.stream().allMatch(item->StocktakeTaskStatusEnum.CLOSE.getCode().equals(item.getTaskStatus()))){
            //盘点单状态为完全盘点
            stocktakeOrderService.updateByStocktakeNo(Arrays.asList(task.getStocktakeNo()),StocktakeOrderStatusEnum.COMPLETE_CHECK.getCode());
        }
        //对应无源盘点任务手工盘点数量更新
        passiveAutoInventoryNewService.modifyTaskCount(Arrays.asList(task.getTaskNo()));
    }

    /**
     *@Description 库存任务盘点
     *@Param stocktakeItem
     *@Return Void
     *@Date 2025/1/14 15:34
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void stocktakeByTask(StocktakeItem stocktakeItem) {
        List<String> statusList = Arrays.asList(StocktakeTaskStatusEnum.CLOSE.getCode(),StocktakeTaskStatusEnum.CANCEL.getCode());
        InventoryStocktakeTask task = dao.findById(stocktakeItem.getTaskId());
        if(statusList.contains(task.getTaskStatus())){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_TASK_STOCKTAKE_ERROR);
        }
        logger.info("盘点任务盘点请求参数:{}", JsonUtil.toJson(stocktakeItem));
        //更新明细盘点数量
        List<StocktakeTaskDetailsItem> detailsItemList = stocktakeItem.getDetailsItemList();
        if(CollectionUtil.isNotEmpty(detailsItemList)){
            List<InventoryStocktakeTaskDetails> allDetailsList = new ArrayList<>();
            //只要某一个商品有实盘数量盘点任务状态更新为盘点中
            boolean flag = detailsItemList.stream().anyMatch(x -> ObjectUtil.isNotNull(x.getActualCount()));
            List<InventoryStocktakeTaskDetails> updateList = new ArrayList<>();
            //无id表示新增的
            List<StocktakeTaskDetailsItem> noIdList = detailsItemList.stream().filter(x -> ObjectUtil.isNull(x.getId())).collect(Collectors.toList());
            //有id表示修改的
            List<StocktakeTaskDetailsItem> idList = detailsItemList.stream().filter(x -> ObjectUtil.isNotNull(x.getId())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(idList)){
                Map<Long, StocktakeTaskDetailsItem> idMap = idList.stream().collect(Collectors.toMap(StocktakeTaskDetailsItem::getId, item -> item));
                //根据id查询对应详情数据
                List<Long> ids = idList.stream().map(StocktakeTaskDetailsItem::getId).collect(Collectors.toList());
                List<InventoryStocktakeTaskDetails> detailsList = detailsService.findByIds(ids);
                if(CollectionUtil.isNotEmpty(detailsList)){
                    //更新盘点数量
                    detailsList.forEach(item->{
                        StocktakeTaskDetailsItem updateItem = idMap.get(item.getId());
                        if(ObjectUtil.isNotNull(updateItem)){
                            item.setActualCount(updateItem.getActualCount());
                            String pdaMatchStatus = updateItem.getPdaMatchStatus();
                            if(StringUtils.isBlank(pdaMatchStatus)){
                                pdaMatchStatus = YesNoEnum.NO.getCode();
                            }
                            item.setPdaMatchStatus(pdaMatchStatus);
                            detailsService.preSave(item);
                            updateList.add(item);
                        }
                    });
                }
            }
            //新增数据
            if(CollectionUtil.isNotEmpty(noIdList)){
                List<InventoryStocktakeTaskDetails> insertList = new ArrayList<>();
                for (StocktakeTaskDetailsItem item : noIdList){
                    InventoryStocktakeTaskDetails details = new InventoryStocktakeTaskDetails();
                    BeanUtil.copyProperties(item, details);
                    details.setStocktakeNo(task.getStocktakeNo());
                    details.setTaskNo(task.getTaskNo());
                    details.setId(null);
                    details.setDataSource(YesNoEnum.YES.getCode());
                    details.setPdaMatchStatus(YesNoEnum.YES.getCode());
                    detailsService.preSave(details);
                    insertList.add(details);
                }
                if (CollectionUtil.isNotEmpty(insertList)){
                    detailsService.batchInsert(insertList);
                    allDetailsList.addAll(insertList);
                }

            }
            if(CollectionUtil.isNotEmpty(updateList)){
                detailsService.batchUpdate(updateList);
                allDetailsList.addAll(updateList);
            }
            if(flag){
                //如果对应盘点单状态为创建更新为盘点中
                InventoryStocktakeOrder order = stocktakeOrderService.findByStocktakeNo(task.getStocktakeNo());
                if(ObjectUtil.isNotNull(order) && StocktakeOrderStatusEnum.CREATE.getCode().equals(order.getStocktakeStatus())){
                    order.setStocktakeStatus(StocktakeOrderStatusEnum.PARTIAL_CHECK.getCode());
                    stocktakeOrderService.saveOrUpdate(order);
                }
                if(StocktakeTaskStatusEnum.CREATE.getCode().equals(task.getTaskStatus())){
                    //当前盘点任务更新为盘点中
                    task.setTaskStatus(StocktakeTaskStatusEnum.IN_PROGRESS.getCode());
                    dao.saveOrUpdate(task);
                }
            }
            if(CollectionUtil.isNotEmpty(allDetailsList)){
                List<InventoryStocktakeTaskDetailsSn> snList = new ArrayList<>();
                List<String> snNoList = detailsItemList.stream().filter(x -> CollectionUtil.isNotEmpty(x.getSnNoList())).flatMap(x -> x.getSnNoList().stream()).collect(Collectors.toList());
                Map<String,AsnDetailSn> asnDetailSnMap = new HashMap<>();
                if(CollectionUtil.isNotEmpty(snNoList)){
                    //根据序列号集合查询对应的序列号详情信息
                    List<AsnDetailSn> asnDetailSnList = asnDetailSnService.listBySnNoList(snNoList);
                    if(CollectionUtil.isNotEmpty(asnDetailSnList)){
                        asnDetailSnMap = asnDetailSnList.stream().collect(Collectors.toMap(AsnDetailSn::getSnNo, Function.identity(), (v1, v2) -> v1));
                    }
                }
                //拿到序列号集合不为空的skuId
                List<Long> taskDetailsIdList = detailsItemList.stream().filter(x -> CollectionUtil.isNotEmpty(x.getSnNoList())).map(StocktakeTaskDetailsItem::getId).distinct().collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(taskDetailsIdList)){
                    //先删除对应的盘点明细序列号数据
                    detailsSnService.deleteByTaskDetailsIdList(taskDetailsIdList);
                }
                //根据商品id和库位id、批次号进行分组
                Map<Long, StocktakeTaskDetailsItem> groupMap = detailsItemList.stream().collect(Collectors.toMap(StocktakeTaskDetailsItem::getId, Function.identity(), (v1, v2) -> v1));
                for (InventoryStocktakeTaskDetails taskDetails:allDetailsList){
                    StocktakeTaskDetailsItem item = groupMap.get(taskDetails.getId());
                    if(ObjectUtil.isNotNull(item) && CollectionUtil.isNotEmpty(item.getSnNoList())){
                        //将进行过盘点的数据插入盘点明细序列号数据
                        for (String no : item.getSnNoList()){
                            AsnDetailSn asnDetailSn = asnDetailSnMap.get(no);
                            InventoryStocktakeTaskDetailsSn sn = new InventoryStocktakeTaskDetailsSn();
                            sn.setTaskDetailsId(taskDetails.getId());
                            sn.setTaskNo(taskDetails.getTaskNo());
                            sn.setSnNo(no);
                            sn.setLocId(taskDetails.getLocId());
                            sn.setSkuId(taskDetails.getSkuId());
                            sn.setLotNum(taskDetails.getLotNum());
                            if (ObjectUtil.isNotNull(asnDetailSn)){
                                sn.setBoxCode(asnDetailSn.getBoxCode());
                            }
                            detailsSnService.preSave(sn);
                            snList.add(sn);
                        }
                    }
                }
                if(CollectionUtil.isNotEmpty(snList)){
                    detailsSnService.batchInsert(snList);
                }
            }

        }

    }

    /**
     *@Description 根据盘点单号集合查询对应任务
     *@Param noList
     *@Return * {@link List< InventoryStocktakeTask> }
     *@Date 2025/1/15 19:12
     *<AUTHOR>
     **/
    public List<InventoryStocktakeTask> findByStocktakeNoList(List<String> noList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeTask::getStocktakeNo,noList);
        return dao.find(conditionRule);
    }

    /**
     *@Description 盘点任务导出
     *@Param exportCondition
     *@Return Void
     *@Date 2025/1/16 9:41
     *<AUTHOR>
     **/
    public void exportStocktakeTask(StocktakeTaskPageCondition condition, HttpServletResponse httpServletResponse) {
        List<InventoryStocktakeTask> taskList;
        if(CollectionUtil.isNotEmpty(condition.getIdList())){
            //根据id集合查询对应盘点任务
            taskList = dao.find(ConditionRule.getInstance().andIn(InventoryStocktakeTask::getId,condition.getIdList()));
        }else{
            taskList = dao.findListByCondition(condition);
        }

        //抽取盘点任务号
        List<String> noList = taskList.stream().map(InventoryStocktakeTask::getTaskNo).collect(Collectors.toList());
        //根据盘点任务号查询明细数据
        List<StocktakeTaskDetailsQuery> detailsQueryList = detailsService.findQueryByTaskNoList(noList);
        //组装数据并导出
        exportDataHandler.exportStocktakeTask(detailsQueryList,httpServletResponse);
    }

    /**
     *@Description 根据盘点单号查询最大任务号
     *@Param stocktakeNo
     *@Return * {@link Long }
     *@Date 2025/1/21 17:39
     *<AUTHOR>
     **/
    public Long getMaxTaskNo(String stocktakeNo) {
        return dao.getMaxTaskNo(stocktakeNo);
    }

    /**
     *@Description 根据盘点任务明细id查询盘点明细序列号信息
     *@Param taskDetailsId
     *@Return * {@link LocSnDetailQuery }
     *@Date 2025/3/24 13:58
     *<AUTHOR>
     **/
    public LocSnDetailQuery getSnDetailByTaskDetailsId(Long taskDetailsId) {
        LocSnDetailQuery query = detailsService.getSnDetailByTaskDetailsId(taskDetailsId);
        return query;
    }

    /**
     *@Description 根据key查询入库序列号
     *@Param condition
     *@Return * {@link List< LocSnDetailItemQuery> }
     *@Date 2025/3/25 9:30
     *<AUTHOR>
     **/
    public List<LocSnDetailItemQuery> getSnDetailByKey(LocSnDetailCondition condition) {
        List<LocSnDetailItemQuery> itemQueryList = new ArrayList<>();
        condition.setPalletNum(null);
        List<LocSnItem> invLotLocSnList = invLotLocSnService.findListByKey(condition);
        if(CollectionUtil.isEmpty(invLotLocSnList)){
            throw new ServiceException(StockExcepitonEnum.BOX_NO_OR_SN_NO_NOT_EXIST);
        }
        Map<String, List<AsnDetailSnItem>> map = new HashMap<>();
        List<AsnDetailSnItem> asnDetailSnList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(invLotLocSnList)){
            for (LocSnItem item : invLotLocSnList){
                AsnDetailSnItem  snItem = new AsnDetailSnItem();
                snItem.setBoxCode(item.getBoxCode());
                snItem.setSnNo(item.getSkuSn());
                asnDetailSnList.add(snItem);
            }
        }
        //过滤出没有箱号的数据
        List<AsnDetailSnItem> noBoxNoList = asnDetailSnList.stream().filter(x -> StringUtils.isBlank(x.getBoxCode())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(noBoxNoList)){
            map.put("无箱码", noBoxNoList) ;
        }
        //有箱号的数据
        List<AsnDetailSnItem> hasBoxNoList = asnDetailSnList.stream().filter(x -> StringUtils.isNotBlank(x.getBoxCode())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(hasBoxNoList)){
            map.putAll(hasBoxNoList.stream().collect(Collectors.groupingBy(AsnDetailSnItem::getBoxCode)));
        }
        if(CollectionUtil.isNotEmpty(map)){
            map.forEach((x,value)->{
                LocSnDetailItemQuery itemQuery = new LocSnDetailItemQuery();
                itemQuery.setKey(x);
                itemQuery.setValue(value);
                itemQueryList.add(itemQuery);
            });
        }
        return itemQueryList;
    }

    /**
     *@Description 盘点作业分析分页查询
     *@Param condition
     *@Return * {@link PageResult< StocktakeTaskPerformanceAnalysisQuery> }
     *@Date 2025/4/17 11:26
     *<AUTHOR>
     **/
    public PageResult<StocktakeTaskPerformanceAnalysisQuery> performanceAnalysisPage(PerformanceAnalysisPageCondition condition) {
        return dao.performanceAnalysisPage(condition);
    }

    /**
     *@Description 根据任务开始时间集合查询任务集合
     *@Param startTimeList
     *@Return * {@link List< InventoryStocktakeTask> }
     *@Date 2025/4/17 15:24
     *<AUTHOR>
     **/
    public List<InventoryStocktakeTask> findByStartTimeList(List<Date> startTimeList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InventoryStocktakeTask::getTaskStartTime,startTimeList);
        return dao.find(conditionRule);
    }

    /**
     *  pda盘点任务分页
     *@Param condition
     *@Return * {@link PageResult< StocktakeTaskPdaQuery> }
     *@Date 2025/6/5 16:20
     *<AUTHOR>
     **/
    public PageResult<StocktakeTaskPdaQuery> pdaPage(StocktakeTaskPdaPageCondition condition) {
        SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();
        if(ObjectUtil.isNull(condition.getPersonnelId())){
            if(ObjectUtil.isNotNull(sessionUserInfo)){
                condition.setPersonnelId(sessionUserInfo.getUserId());
            }
        }

        condition.setTaskStatusList(Arrays.asList(StocktakeTaskStatusEnum.CREATE.getCode(),StocktakeTaskStatusEnum.IN_PROGRESS.getCode()));
        PageResult<StocktakeTaskPdaQuery> result = dao.pdaPage(condition);
        if(CollectionUtil.isNotEmpty(result.getRows())){
            List<String> taskNoList = result.getRows().stream().map(StocktakeTaskPdaQuery::getTaskNo).distinct().collect(Collectors.toList());
            //查询盘点对应的盘点任务明细
            List<StocktakeTaskDetailsPdaQuery> detailsPdaQueryList = detailsService.findPdaQueryByTaskNoList(taskNoList);
            //根据任务编号进行分组
            Map<String, List<StocktakeTaskDetailsPdaQuery>> map = detailsPdaQueryList.stream().collect(Collectors.groupingBy(StocktakeTaskDetailsPdaQuery::getTaskNo));
            //抽取盘点单号
            List<String> orderNoList = result.getRows().stream().map(StocktakeTaskPdaQuery::getStocktakeNo).distinct().collect(Collectors.toList());
            //根据盘点单号查询对应查询条件集合
            List<InventoryStocktakeCondition> conditionList = conditionService.findByStocktakeNoList(orderNoList);
            Map<String, List<InventoryStocktakeCondition>> conditionMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(conditionList)){
                conditionMap = conditionList.stream().collect(Collectors.groupingBy(InventoryStocktakeCondition::getStocktakeNo));
            }
            Map<String, List<InventoryStocktakeCondition>> finalConditionMap = conditionMap;
            result.getRows().forEach(x->{
                List<StocktakeTaskDetailsPdaQuery> currentDetails = map.get(x.getTaskNo());
                x.setDetailsQueryList(currentDetails);
                //对应盘点单任务生成方式是不是直接生成
                String stocktakeTarget = "";
                if(StocktakeTaskTypeEnum.DIRECT_GENERATION.getCode().equals(x.getTaskType())){
                    List<InventoryStocktakeCondition> currentConditionList = finalConditionMap.get(x.getStocktakeNo());
                    List<String> areaNameList = new ArrayList<>();
                    List<String> warehouseZoneNameList = new ArrayList<>();
                    List<String> locNameList = new ArrayList<>();
                    if(CollectionUtil.isNotEmpty(currentConditionList)){
                        areaNameList = currentConditionList.stream().map(InventoryStocktakeCondition::getAreaName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
                        warehouseZoneNameList = currentConditionList.stream().map(InventoryStocktakeCondition::getWarehouseZoneName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
                        locNameList = currentConditionList.stream().map(InventoryStocktakeCondition::getWarehouseLocName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
                        if(CollectionUtil.isNotEmpty(areaNameList)){
                            stocktakeTarget += String.join(",",areaNameList);
                        }
                        if (CollectionUtil.isNotEmpty(warehouseZoneNameList)){
                            stocktakeTarget += String.join(",",warehouseZoneNameList);
                        }
                        if(CollectionUtil.isNotEmpty(locNameList)){
                            stocktakeTarget += String.join(",",locNameList);
                        }
                    }
                }else{
                    stocktakeTarget = currentDetails.stream().map(StocktakeTaskDetailsPdaQuery::getWarehouseZoneName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.joining(","));
                }
                x.setStocktakeTarget(stocktakeTarget);
            });


        }
        return result;
    }

    /**
     *@Description pda盘点任务关闭
     *@Param stocktakeItem
     *@Return Void
     *@Date 2025/6/5 19:27
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void pdaCloseTask(StocktakeItem stocktakeItem) {
        stocktakeByTask(stocktakeItem);
        closeTask(stocktakeItem.getTaskId());
    }

    /**
     *  根据盘点明细ID查询对应序列号
     *@Param id
     *@Return * {@link Map< String, List< String>> }
     *@Date 2025/6/5 19:43
     *<AUTHOR>
     **/
    public Map<String, List<String>> getStockSn(Long id) {
        InventoryStocktakeTaskDetails details = detailsService.findById(id);
        Map<String, List<String>> result = new HashMap<>();
        //根据库位Id和商品Id、批次号询序列号库存
        List<InvLotLocSn> invLotLocSnList = invLotLocSnService.getByLocIdListAndSkuIdListAndLotNumList(Arrays.asList(details.getSkuId()),Arrays.asList(details.getLocId()),Arrays.asList(details.getLotNum()),null,YesNoEnum.NO.getCode());
        if(CollectionUtil.isNotEmpty(invLotLocSnList)){
            //抽取序列号
            List<String> snNoList = invLotLocSnList.stream().map(InvLotLocSn::getSkuSn).distinct().collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(snNoList)){
                //根据序列号查询对应序列号详情
                Map<String,List<AsnDetailSnItem>> asnDetailSnMap = asnDetailSnService.getSnDetailBySnNoList(snNoList);
                if(MapUtil.isNotEmpty(asnDetailSnMap)){
                    asnDetailSnMap.forEach((snNo,asnDetailSnList)->{
                        result.put(snNo,asnDetailSnList.stream().map(AsnDetailSnItem::getSnNo).distinct().collect(Collectors.toList()));
                    });
                }
            }
        }
        return result;
    }

    /**
     *@Description 根据批次号和库位编码查询库存信息
     *@Param condition
     *@Return * {@link List< LocListPageQuery> }
     *@Date 2025/6/9 14:07
     *<AUTHOR>
     **/
    public List<StocktakeTaskDetailsPdaQuery> getStockInfo(PdaStockInfoQueryCondition condition) {
        List<StocktakeTaskDetailsPdaQuery> pageQueryList = new ArrayList<>();
        WarehouseLocPageCondition warehouseLocPageCondition = new WarehouseLocPageCondition();
        BeanUtil.copyProperties(condition,warehouseLocPageCondition);
        if (warehouseLocPageCondition.getPageSize() == null || warehouseLocPageCondition.getPageNumber() == null) {
            warehouseLocPageCondition.setPageSize(10);
            warehouseLocPageCondition.setPageNumber(1);
        }
        PageResult<LocListPageQuery> pageResult = warehouseLocService.pageLocList(warehouseLocPageCondition);
        if(CollectionUtil.isNotEmpty(pageResult.getRows())){
            List<LocListPageQuery> rows = pageResult.getRows();
            //将同仓库，同商品，同库位，同批次号的数据合并为一条
            Map<String, List<LocListPageQuery>>  map = rows.stream().collect(Collectors.groupingBy(e ->e.getWarehouseId() +"_"+ e.getSkuId()+"_"+ e.getLocId() + '_'+ e.getLotNum()));
            map.forEach((key,value)->{
                LocListPageQuery locListPageQuery = value.get(0);
                StocktakeTaskDetailsPdaQuery pdaQuery = new StocktakeTaskDetailsPdaQuery();
                BeanUtil.copyProperties(locListPageQuery,pdaQuery);
                pdaQuery.setId(null);
                pdaQuery.setLotLocId(locListPageQuery.getId());
                BigDecimal qty = value.stream().map(LocListPageQuery::getQty).reduce(BigDecimal.ZERO,BigDecimal::add);
                pdaQuery.setInventoryCount(qty.longValue());
                pdaQuery.setDataSource(YesNoEnum.YES.getCode());
                pdaQuery.setWarehouseZoneId(locListPageQuery.getZoneId());
                pdaQuery.setWarehouseZoneName(locListPageQuery.getZoneName());
                pdaQuery.setSkuNo(locListPageQuery.getSkuCode());
                pdaQuery.setLocName(locListPageQuery.getLocNum());
                pageQueryList.add(pdaQuery);
            });

        }
        logger.info("根据批次号和库位编码查询库存信息结果：{}",JsonUtil.toJson(pageQueryList));
        return pageQueryList;
    }

    /**
     *@Description pda盘点
     *@Param stocktakeItem
     *@Return Void
     *@Date 2025/6/11 14:03
     *<AUTHOR>
     **/
    public void stocktakeByPdaTask(StocktakeItem stocktakeItem) {
        InventoryStocktakeTask task = dao.findById(stocktakeItem.getTaskId());
        if(!task.getPersonnelId().equals(SessionContext.getSessionUserInfo().getUserId())){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_TASK_STOCKTAKE_EXECUTE_ERROR);
        }
        stocktakeByTask(stocktakeItem);
    }

    /**
     *@Description pda盲盘
     *@Param stocktakeItem
     *@Return Void
     *@Date 2025/6/12 13:58
     *<AUTHOR>
     **/
    public void aimlessStocktakeByPdaTask(StocktakeItem stocktakeItem) {
        InventoryStocktakeTask task = dao.findById(stocktakeItem.getTaskId());
        if(!task.getPersonnelId().equals(SessionContext.getSessionUserInfo().getUserId())){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_TASK_STOCKTAKE_EXECUTE_ERROR);
        }
        List<String> statusList = Arrays.asList(StocktakeTaskStatusEnum.CLOSE.getCode(),StocktakeTaskStatusEnum.CANCEL.getCode());
        if(statusList.contains(task.getTaskStatus())){
            throw new ServiceException(StockExcepitonEnum.STOCKTAKE_TASK_STOCKTAKE_ERROR);
        }
        logger.info("盘点任务盲盘请求参数:{}", JsonUtil.toJson(stocktakeItem));
        //更新明细盘点数量
        List<StocktakeTaskDetailsItem> detailsItemList = stocktakeItem.getDetailsItemList();
        if(CollectionUtil.isNotEmpty(detailsItemList)){
            List<InventoryStocktakeTaskDetails> allDetailsList = new ArrayList<>();
            //只要某一个商品有实盘数量盘点任务状态更新为盘点中
            boolean flag = detailsItemList.stream().anyMatch(x -> ObjectUtil.isNotNull(x.getActualCount()));
            List<InventoryStocktakeTaskDetails> updateList = new ArrayList<>();
            //根据任务编号查询当前任务所有明细
            List<InventoryStocktakeTaskDetails> detailsList = detailsService.findByTaskNo(task.getTaskNo());
            //有对应明细
            if(CollectionUtil.isNotEmpty(detailsList)){
                //根据仓库id、库位code、批次号进行分组
                Map<String,List<StocktakeTaskDetailsItem>> groupMap = detailsItemList.stream().collect(Collectors.groupingBy(e ->e.getWarehouseId() +"_"+ e.getLocName() + '_'+ e.getLotNum()));
                detailsList.forEach(details ->{
                    // todo 盲盘逻辑待完善
                });
            }
            if(flag){
                //如果对应盘点单状态为创建更新为盘点中
                InventoryStocktakeOrder order = stocktakeOrderService.findByStocktakeNo(task.getStocktakeNo());
                if(ObjectUtil.isNotNull(order) && StocktakeOrderStatusEnum.CREATE.getCode().equals(order.getStocktakeStatus())){
                    order.setStocktakeStatus(StocktakeOrderStatusEnum.PARTIAL_CHECK.getCode());
                    stocktakeOrderService.saveOrUpdate(order);
                }
                if(StocktakeTaskStatusEnum.CREATE.getCode().equals(task.getTaskStatus())){
                    //当前盘点任务更新为盘点中
                    task.setTaskStatus(StocktakeTaskStatusEnum.IN_PROGRESS.getCode());
                    dao.saveOrUpdate(task);
                }
            }
            if(CollectionUtil.isNotEmpty(allDetailsList)){
                List<InventoryStocktakeTaskDetailsSn> snList = new ArrayList<>();
                List<String> snNoList = detailsItemList.stream().filter(x -> CollectionUtil.isNotEmpty(x.getSnNoList())).flatMap(x -> x.getSnNoList().stream()).collect(Collectors.toList());
                Map<String,AsnDetailSn> asnDetailSnMap = new HashMap<>();
                if(CollectionUtil.isNotEmpty(snNoList)){
                    //根据序列号集合查询对应的序列号详情信息
                    List<AsnDetailSn> asnDetailSnList = asnDetailSnService.listBySnNoList(snNoList);
                    if(CollectionUtil.isNotEmpty(asnDetailSnList)){
                        asnDetailSnMap = asnDetailSnList.stream().collect(Collectors.toMap(AsnDetailSn::getSnNo, Function.identity(), (v1, v2) -> v1));
                    }
                }
                //拿到序列号集合不为空的skuId
                List<Long> taskDetailsIdList = detailsItemList.stream().filter(x -> CollectionUtil.isNotEmpty(x.getSnNoList())).map(StocktakeTaskDetailsItem::getId).distinct().collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(taskDetailsIdList)){
                    //先删除对应的盘点明细序列号数据
                    detailsSnService.deleteByTaskDetailsIdList(taskDetailsIdList);
                }
                //根据商品id和库位id、批次号进行分组
                Map<Long, StocktakeTaskDetailsItem> groupMap = detailsItemList.stream().collect(Collectors.toMap(StocktakeTaskDetailsItem::getId, Function.identity(), (v1, v2) -> v1));
                for (InventoryStocktakeTaskDetails taskDetails:allDetailsList){
                    StocktakeTaskDetailsItem item = groupMap.get(taskDetails.getId());
                    if(ObjectUtil.isNotNull(item) && CollectionUtil.isNotEmpty(item.getSnNoList())){
                        //将进行过盘点的数据插入盘点明细序列号数据
                        for (String no : item.getSnNoList()){
                            AsnDetailSn asnDetailSn = asnDetailSnMap.get(no);
                            InventoryStocktakeTaskDetailsSn sn = new InventoryStocktakeTaskDetailsSn();
                            sn.setTaskDetailsId(taskDetails.getId());
                            sn.setTaskNo(taskDetails.getTaskNo());
                            sn.setSnNo(no);
                            sn.setLocId(taskDetails.getLocId());
                            sn.setSkuId(taskDetails.getSkuId());
                            sn.setLotNum(taskDetails.getLotNum());
                            if (ObjectUtil.isNotNull(asnDetailSn)){
                                sn.setBoxCode(asnDetailSn.getBoxCode());
                            }
                            detailsSnService.preSave(sn);
                            snList.add(sn);
                        }
                    }
                }
                if(CollectionUtil.isNotEmpty(snList)){
                    detailsSnService.batchInsert(snList);
                }
            }

        }
    }

    /**
     *@Description 批量关闭盘点任务
     *@Param idsReq
     *@Return Void
     *@Date 2025/6/13 11:16
     *<AUTHOR>
     **/
    @Transactional(rollbackFor = Exception.class)
    public void batchCloseTask(IdsReq idsReq) {
        if(CollectionUtil.isEmpty(idsReq.getIds())){
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        List<InventoryStocktakeTask> taskList = dao.findByIds(idsReq.getIds().toArray(new Long[0]));
        //过滤出非已关闭状态的数据
        List<String> statusList = Arrays.asList(StocktakeTaskStatusEnum.CLOSE.getCode(),StocktakeTaskStatusEnum.CANCEL.getCode());
        List<InventoryStocktakeTask> notCloseList = taskList.stream().filter(item->!statusList.contains(item.getTaskStatus())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(notCloseList)){
            for (InventoryStocktakeTask task : notCloseList) {
                task.setTaskStatus(StocktakeTaskStatusEnum.CLOSE.getCode());
                preSave(task);
            }
            dao.batchUpdate(notCloseList);
            //抽取盘点单号
            List<String> noList = taskList.stream().map(InventoryStocktakeTask::getStocktakeNo).distinct().collect(Collectors.toList());
            //根据盘点单号查询所有盘点任务
            List<InventoryStocktakeTask> allTaskList = dao.find(ConditionRule.getInstance().andIn(InventoryStocktakeTask::getStocktakeNo,noList));
            //按照盘点单号分组
            Map<String, List<InventoryStocktakeTask>> map = allTaskList.stream().collect(Collectors.groupingBy(InventoryStocktakeTask::getStocktakeNo));
            List<String> updateNoList = new ArrayList<>();
            for (Map.Entry<String, List<InventoryStocktakeTask>> entry : map.entrySet()) {
                List<InventoryStocktakeTask> currentTaskList = entry.getValue();
                if(CollectionUtil.isNotEmpty(currentTaskList)){
                    //盘点任务状态是否都为关闭状态
                    boolean flag = currentTaskList.stream().allMatch(item->StocktakeTaskStatusEnum.CLOSE.getCode().equals(item.getTaskStatus()));
                    if(flag){
                        updateNoList.add(entry.getKey());
                    }
                }
            }
            if(CollectionUtil.isNotEmpty(updateNoList)){
                //去重
                updateNoList = updateNoList.stream().distinct().collect(Collectors.toList());
                //修改对应盘点单状态为完全盘点
                stocktakeOrderService.updateByStocktakeNo(updateNoList,StocktakeOrderStatusEnum.COMPLETE_CHECK.getCode());
            }
            //对应无源盘点任务手工盘点数量更新
            passiveAutoInventoryNewService.modifyTaskCount(notCloseList.stream().map(InventoryStocktakeTask::getTaskNo).collect(Collectors.toList()));
        }
    }
}
