package com.chinaservices.wms.module.performance.service;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinaservices.auth.module.user.domain.UserForOuterCondition;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.auth.module.user.feign.RemoteUserService;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.module.asn.pa.dao.CsPaTaskDetailDao;
import com.chinaservices.wms.module.performance.domain.*;
import com.chinaservices.wms.module.so.so.dao.SoAllocationDao;
import com.chinaservices.wms.module.stock.take.model.InventoryStocktakeTask;
import com.chinaservices.wms.module.stock.take.service.InventoryStocktakeTaskDetailsService;
import com.chinaservices.wms.module.stock.take.service.InventoryStocktakeTaskService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName PerformanceAnalysisService
 * <AUTHOR>
 * @Date 2025/4/17 9:11
 * @Description
 * @Version 1.0
 */
@Service
public class PerformanceAnalysisService {

    @Autowired
    private InventoryStocktakeTaskService inventoryStocktakeTaskService;

    @Autowired
    private InventoryStocktakeTaskDetailsService detailsService;

    @Autowired
    private SoAllocationDao soAllocationDao;

    @Resource
    private RemoteUserService remoteUserService;

    @Autowired
    private CsPaTaskDetailDao paTaskDetailDao;

    /**
     *@Description 盘点作业分析
     *@Param condition
     *@Return * {@link PageResult< StocktakeTaskPerformanceAnalysisQuery> }
     *@Date 2025/4/17 9:41
     *<AUTHOR>
     **/
    public PageResult<StocktakeTaskPerformanceAnalysisQuery> stocktakeTaskPerformanceAnalysis(PerformanceAnalysisPageCondition condition) {
        PageResult<StocktakeTaskPerformanceAnalysisQuery> pageResult = inventoryStocktakeTaskService.performanceAnalysisPage(condition);
        if(CollectionUtil.isNotEmpty(pageResult.getRows())){
            //抽取盘点员id
            List<Long> personnelIdList = pageResult.getRows().stream().map(StocktakeTaskPerformanceAnalysisQuery::getPersonnelId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
            Map<Long, UserForOuterQuery> userMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(personnelIdList)){
                UserForOuterCondition userForOuterCondition = new UserForOuterCondition();
                userForOuterCondition.setIds(personnelIdList.toArray(new Long[0]));
                ResponseData<List<UserForOuterQuery>> responseData =  remoteUserService.getUserList(userForOuterCondition);
                if(responseData.getSuccess()){
                    List<UserForOuterQuery> userList = responseData.getData();
                    userMap = userList.stream().collect(Collectors.toMap(UserForOuterQuery::getId, m -> m, (k1, k2) -> k1));
                }
            }
            //抽取任务开始时间
            List<Date> startTimeList = pageResult.getRows().stream().map(StocktakeTaskPerformanceAnalysisQuery::getTaskStartTime).distinct().collect(Collectors.toList());
            //根据任务开始时间集合查询任务集合
            List<InventoryStocktakeTask> taskList = inventoryStocktakeTaskService.findByStartTimeList(startTimeList);
            //根据任务开始时间进行分组
            Map<Date, List<InventoryStocktakeTask>> taskMap = taskList.stream().collect(Collectors.groupingBy(InventoryStocktakeTask::getTaskStartTime));
            for (StocktakeTaskPerformanceAnalysisQuery item : pageResult.getRows()) {
                UserForOuterQuery user = userMap.get(item.getPersonnelId());
                if(ObjectUtil.isNotNull(user)){
                    item.setAccount(user.getUserName());
                }
                List<InventoryStocktakeTask> taskListByStartTime = taskMap.get(item.getTaskStartTime());
                //如果当前集合中都没有任务结束时间
                boolean flag = taskListByStartTime.stream().allMatch(task -> ObjectUtil.isNull(task.getTaskEndTime()));
                if(!flag){
                    //获取当前集合中任务结束时间最大的任务
                    InventoryStocktakeTask maxTask = taskListByStartTime.stream().filter(task -> ObjectUtil.isNotNull(task.getTaskEndTime())).max(Comparator.comparing(InventoryStocktakeTask::getTaskEndTime)).get();
                    item.setTaskEndTime(maxTask.getTaskEndTime());
                    //计算作业时长，单位为分钟
                    Long ms = item.getTaskEndTime().getTime() - item.getTaskStartTime().getTime();
                    item.setWorkDuration(BigDecimal.valueOf(ms / (1000 * 60)).setScale(2, BigDecimal.ROUND_HALF_UP));
                    //计算平均处理时间 作业时长 / 任务总数
                    item.setAht(item.getWorkDuration().divide(BigDecimal.valueOf(item.getTaskCount()), 2, BigDecimal.ROUND_HALF_UP));
                }
            }
        }
        return pageResult;
    }


    /**
     * 拣货任务绩效分析
     *
     * @param condition 条件
     * @return {@link PageResult }<{@link PickingTaskPerformanceAnalysisQuery }>
     */
    public PageResult<PickingTaskPerformanceAnalysisQuery> pickingTaskPerformanceAnalysis(PerformanceAnalysisPageCondition condition)
    {
        PageResult<PickingTaskPerformanceAnalysisQuery> pageResult = soAllocationDao.pickingTaskPerformanceAnalysis(condition);
        if(CollectionUtil.isNotEmpty(pageResult.getRows())) {
            // 查询拣货员账号
            List<Long> userIdList = pageResult.getRows().stream().map(PickingTaskPerformanceAnalysisQuery::getUserId).filter(EmptyUtil::isNotEmpty).distinct().collect(Collectors.toList());
            Map<Long, UserForOuterQuery> userMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(userIdList)) {
                UserForOuterCondition userForOuterCondition = new UserForOuterCondition();
                userForOuterCondition.setIds(userIdList.toArray(new Long[0]));
                ResponseData<List<UserForOuterQuery>> responseData = remoteUserService.getUserList(userForOuterCondition);
                if (responseData.getSuccess()) {
                    List<UserForOuterQuery> userList = responseData.getData();
                    userMap = userList.stream().collect(Collectors.toMap(UserForOuterQuery::getId, m -> m, (k1, k2) -> k1));
                }
            }

            // 用户账号、作业时长、订单平均处理时间，数据赋值
            for (PickingTaskPerformanceAnalysisQuery task : pageResult.getRows()) {
                // 用户账号
                Long userId = task.getUserId();
                UserForOuterQuery user = userMap.get(userId);
                if(ObjectUtil.isNotNull(user)){
                    task.setAccount(user.getUserName());
                }
                // 作业时长
                long between = DateUtil.between(task.getTaskStartTime(), task.getTaskEndTime(), DateUnit.MINUTE);
                task.setWorkDuration(BigDecimal.valueOf(between).setScale(0, RoundingMode.HALF_UP));
                // 订单平均处理时间
                BigDecimal aht = task.getWorkDuration().divide(BigDecimal.valueOf(task.getPickingTaskCount()), 2,
                        RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);
                task.setAht(aht);
            }
        }
        return pageResult;
    }

    /**
     * 上架作业分析
     * @param condition 条件
     * @return {@link PageResult }<{@link PutOnShelvesPageQuery }>
     */
    public PageResult<PutOnShelvesPageQuery> analysisOfShelfAssignments(PutOnShelvesSearchCondition condition) {
        PageResult<PutOnShelvesPageQuery> result = paTaskDetailDao.analysisOfShelfAssignments(condition);
        List<PutOnShelvesPageQuery> rows = result.getRows();
        if(CollectionUtil.isNotEmpty(rows)) {
            //根据用户ID查询出用户信息
            List<Long> list = rows.stream().map(PutOnShelvesPageQuery::getUserId).toList();
            UserForOuterCondition userForOuterCondition = new UserForOuterCondition();
            userForOuterCondition.setIds(list.toArray(new Long[0]));
            ResponseData<List<UserForOuterQuery>> data = remoteUserService.getUserList(userForOuterCondition);
            if (data.getSuccess() && CollUtil.isNotEmpty(data.getData())){
                Map<Long, PutOnShelvesPageQuery> map = rows.stream().collect(Collectors.toMap(PutOnShelvesPageQuery::getUserId, m -> m, (k1, k2) -> k1));
                for (UserForOuterQuery datum : data.getData()) {
                    PutOnShelvesPageQuery shelvesPageQuery = map.get(datum.getId());
                    if (ObjUtil.isNotNull(shelvesPageQuery)){
                        shelvesPageQuery.setRealName(datum.getRealName());
                    }
                }
            }
            for (PutOnShelvesPageQuery row : rows) {
                String[] split = row.getPaNoJoin().split(",");
                long count = Arrays.stream(split).distinct().count();
                row.setTaskCount(Convert.toInt(count));
            }
        }
        return result;
    }
}
