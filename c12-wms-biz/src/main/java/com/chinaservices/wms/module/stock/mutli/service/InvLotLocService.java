package com.chinaservices.wms.module.stock.mutli.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.core.enums.StatusEnum;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.util.JsonUtil;
import com.chinaservices.util.StringUtil;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.domain.LotAttribute;
import com.chinaservices.wms.common.domain.LotDetailItem;
import com.chinaservices.wms.common.enums.passive.PassiveAutoInventoryStatusEnum;
import com.chinaservices.wms.common.enums.passive.PassiveTagStatusEnum;
import com.chinaservices.wms.common.enums.stock.AgeReportTimeTypeEnum;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.common.util.EasyExcelExportUtil;
import com.chinaservices.wms.common.util.LotUtil;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskConfirmItem;
import com.chinaservices.wms.module.asn.pa.model.CsPaTask;
import com.chinaservices.wms.module.asn.receive.service.AsnReceiveService;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.dashboard.domain.LocCalculationResultsItem;
import com.chinaservices.wms.module.dashboard.domain.StorageLocationStatistics;
import com.chinaservices.wms.module.excel.item.stock.*;
import com.chinaservices.wms.module.passive.tag.model.PassiveTag;
import com.chinaservices.wms.module.passive.tag.service.PassiveTagService;
import com.chinaservices.wms.module.passive.take.model.PassiveAutoInventory;
import com.chinaservices.wms.module.report.model.AgeReport;
import com.chinaservices.wms.module.report.service.AgeReportService;
import com.chinaservices.wms.module.screen.config.domain.BulletinBoardOverviewItem;
import com.chinaservices.wms.module.stock.common.model.qty.query.InvLotLocForStockTakeOrderQuery;
import com.chinaservices.wms.module.stock.common.model.qty.query.InvLotLocForStockTakeOrderQueryCondition;
import com.chinaservices.wms.module.stock.multi.domain.*;
import com.chinaservices.wms.module.stock.mutli.dao.InvLotLocDao;
import com.chinaservices.wms.module.stock.mutli.model.InvLotAtt;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLocSn;
import com.chinaservices.wms.module.stock.transaction.model.StockTransaction;
import com.chinaservices.wms.module.stock.transaction.service.StockTransactionService;
import com.chinaservices.wms.module.stock.warning.service.StockWarningService;
import com.chinaservices.wms.module.warehouse.area.model.WarehouseArea;
import com.chinaservices.wms.module.warehouse.area.service.WarehouseAreaService;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import com.google.common.collect.Lists;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Skyler He
 * @Description: 批次库位库存
 */
@Service
public class InvLotLocService extends ModuleBaseServiceSupport<InvLotLocDao, InvLotLoc, Long> {


    @Autowired
    private LotUtil lotUtil;

    @Autowired
    private InvLotLocDao invLotLocDao;
    @Autowired
    private StockTransactionService stockTransactionService;

    @Autowired
    private EasyExcelExportUtil easyExcelExportUtil;

    @Autowired
    private AgeReportService ageReportService;

    @Autowired
    private SkuService skuService;

    @Autowired
    private InvLotAttService invLotAttService;




    /**
     * 根据库位id查询可用库存是否存在大于0的库位
     * @param ids
     * @return
     */
    public List<InvLotLoc> findQtyCountByLocId(List<Long> ids){
        return dao.findQtyCountByLocId(ids);
    }

    public List<InvLotLoc> getSkuNumByLocId(Long locId, Long warehouseId){
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(InvLotLoc::getLocId, locId);
        conditionRule.andEqual(InvLotLoc::getWarehouseId, warehouseId);
        conditionRule.andGreaterThan(InvLotLoc::getQty, BigDecimal.ZERO);
        return dao.find(conditionRule);
    }

    public List<InvLotLoc> listByCondition(InvLotLocCondition condition) {
        return dao.find(InvLotLoc.class, condition);
    }

    /**
     * 按批次号查询
     */
    public InvLotLoc findById(long lotId) {
        return dao.findById(lotId);
    }

    /**
     * 根据货主分页查询库存
     */
    public PageResult<InvLotLocQuery> pageByOwner(InvLotLocPageCondition invLotLocCondition) {

        PageResult<InvLotLocQuery> pageResult = dao.pageByOwner(invLotLocCondition);
        List<InvLotLocQuery> queryList = pageResult.getRows();
        if (CollUtil.isNotEmpty(queryList)) {
            setAvailableQty(queryList);
        }
        return pageResult;
    }

    /**
     * 根据商品分页查询库存
     */
    public PageResult<InvLotLocQuery> pageBySku(InvLotLocPageCondition invLotLocCondition) {

        PageResult<InvLotLocQuery> pageResult = dao.pageBySku(invLotLocCondition);
        List<InvLotLocQuery> queryList = pageResult.getRows();
        if (CollUtil.isNotEmpty(queryList)) {
            setAvailableQty(queryList);
        }
        return pageResult;

    }

    /**
     * 根据批次分页查询库存
     */
    public PageResult<InvLotLocQuery> pageByLotNum(InvLotLocPageCondition invLotLocCondition) {

        PageResult<InvLotLocQuery> pageResult = dao.pageByLotNum(invLotLocCondition);
        List<InvLotLocQuery> queryList = pageResult.getRows();
        if (CollUtil.isNotEmpty(queryList)) {
            setAvailableQty(queryList);
            converterList(queryList);
        }
        return pageResult;
    }

    /**
     * 根据库位分页查询库存
     */
    public PageResult<InvLotLocQuery> pageByLoc(InvLotLocPageCondition invLotLocCondition) {
        PageResult<InvLotLocQuery> pageResult = dao.pageByLoc(invLotLocCondition);
        List<InvLotLocQuery> queryList = pageResult.getRows();
        if (CollUtil.isNotEmpty(queryList)) {
            setAvailableQtyByLoc(queryList);

        }
        return pageResult;
    }

    /**
     * 根据商品/库位分页查询库存
     */
    public PageResult<InvLotLocQuery> pageBySkuLoc(InvLotLocPageCondition invLotLocCondition) {
        PageResult<InvLotLocQuery> pageResult = dao.pageBySkuLoc(invLotLocCondition);
        List<InvLotLocQuery> queryList = pageResult.getRows();
        if (CollUtil.isNotEmpty(queryList)) {
            setAvailableQtyByLoc(queryList);

        }
        return pageResult;

    }

    /**
     * 根据商品/库位/批次号分页查询库存
     */
    public PageResult<InvLotLocQuery> pageBySkuLocLot(InvLotLocPageCondition invLotLocCondition) {

        PageResult<InvLotLocQuery> pageResult = dao.pageBySkuLocLot(invLotLocCondition);
        List<InvLotLocQuery> queryList = pageResult.getRows();
        if (CollUtil.isNotEmpty(queryList)) {
            setAvailableQtyByLoc(queryList);
            converterList(queryList);
        }
        return pageResult;
    }

    public void setAvailableQty(List<InvLotLocQuery> queryList) {
        for (InvLotLocQuery query : queryList) {
            query.setAvailableQty(query.getQty().subtract(query.getQtyHold()).subtract(query.getQtyAlloc()).subtract(query.getQtyQc()).subtract(query.getQtyPk()).subtract(query.getQtyWaitMove()).subtract(query.getQtyWaitDamage()));
        }
    }

    public void setAvailableQtyByLoc(List<InvLotLocQuery> queryList) {
        for (InvLotLocQuery query : queryList) {
            query.setAvailableQty(query.getQty().subtract(query.getQtyHold()).subtract(query.getQtyAlloc()).subtract(query.getQtyQc()).subtract(query.getQtyWaitMove()).subtract(query.getQtyWaitDamage()));
        }
    }

    private void converterList(List<InvLotLocQuery> queryList) {
        Set<Long> lotIdSet = queryList.stream().map(InvLotLocQuery::getLotId).collect(Collectors.toSet());
        List<LotDetailItem> lotDetailItems = lotUtil.getLotDetailItemsByLotIds(lotIdSet);
        Map<Long,List<LotDetailItem>> groupByLotId = new HashMap<>();
        if (CollUtil.isNotEmpty(lotDetailItems)){
            groupByLotId = lotDetailItems.stream().collect(Collectors.groupingBy(LotDetailItem::getLotId));
        }
        for (InvLotLocQuery query : queryList) {
            LotAttribute attribute = new LotAttribute();
            BeanUtil.copyProperties(query, attribute);
            List<LotDetailItem> lotDetailItemsByLotId = groupByLotId.get(query.getLotId());
            if (CollUtil.isEmpty(lotDetailItemsByLotId)){
                continue;
            }
            String lotAtt = lotUtil.lotAttributeToStr(lotDetailItemsByLotId,attribute);
            query.setLotStr(lotAtt);
        }
    }

    /**
     * 查询商品库位库存分页查询
     */
    public PageResult<InvLotLocQuery> page(InvLotLocPageCondition invLotLocCondition) {
        return dao.pageByinvLotLocCondition(invLotLocCondition);
    }

    /**
     * 库存盘点
     * @param invLotLocCondition
     * @return
     */
    public PageResult<InvLotLocForStockTakeOrderQuery> getInvLotLocForStockTakeOrderQuery(InvLotLocForStockTakeOrderQueryCondition invLotLocCondition){
        if(ObjectUtil.isNotNull(invLotLocCondition.getOperatorTimeStart()) ||
                ObjectUtil.isNotNull(invLotLocCondition.getOperatorTimeEnd())){
            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andGreaterEqual(StockTransaction::getCreateTime, invLotLocCondition.getOperatorTimeStart());
            conditionRule.andLessEqual(StockTransaction::getModifyTime, invLotLocCondition.getOperatorTimeEnd());
            conditionRule.andEqual(StockTransaction::getWarehouseId, invLotLocCondition.getWarehouseId());
            if(CollUtil.isNotEmpty(invLotLocCondition.getPalletNumList())){
                conditionRule.andIn(StockTransaction::getFmPalletNum, invLotLocCondition.getPalletNumList());
            }
            List<StockTransaction> list = stockTransactionService.find(conditionRule);
            Set<String> p = list.stream().map(StockTransaction::getFmLotNum).collect(Collectors.toSet());
            if(CollUtil.isNotEmpty(p)){
                invLotLocCondition.setLocNumList(Lists.newArrayList(p));
            }else {
                return new PageResult<>();
            }
        }
        return dao.getInvLotLocForStockTakeOrderQuery(invLotLocCondition);
    }

    /**
     * 保存或更新
     */
    public boolean saveOrUpdate(InvLotLocItem invLotLocItem) {
        return dao.saveOrUpdate(invLotLocItem);
    }


    /**
     * 根据Ids批量保存
     */
    public void saveOrUpdate(InvLotAtt invLotAtt) {
        dao.saveOrUpdate(invLotAtt);
    }





    public boolean saveOrUpdate(InvLotAttItem invLotAttItem) {
        return dao.saveOrUpdate(invLotAttItem);
    }
    /**
     * 出入库通过批次号、库位、容器号查找库存记录
     *
     * @param lotNum
     * @param locId
     * @param palletNum
     * @param warehouseId
     * @return InvLotLoc
     */
    public InvLotLoc getByLotNumAndLocIdAndPalletNumCr(String lotNum, Long locId, String palletNum, Long warehouseId,Long ownerId) {
        InvLotLocCondition invLotLocCondition = new InvLotLocCondition();
        invLotLocCondition.setLocId(locId);
        invLotLocCondition.setLotNum(lotNum);
        invLotLocCondition.setPalletNum(palletNum);
        invLotLocCondition.setWarehouseId(warehouseId);
        invLotLocCondition.setOwnerId(ownerId);
        List<InvLotLoc> list = dao.getByLotNumAndLocIdAndPalletNumCr(invLotLocCondition);
        if (!list.isEmpty()) {
            logger.info("查询库存记录数据：{}",list.size());
            return list.getFirst();
        } else {
            return null;
        }
    }





    /**
     * 通过id增加库位的库存数
     * @Date 14:10 2025/1/11
     * @Param [id, qtyPaOut]
     * @return void
     **/
    public void increaseQty(Long id, BigDecimal qty){
        InvLotLocIncreaseNumberCondition invLotLocIncreaseNumberCondition = new InvLotLocIncreaseNumberCondition();
        invLotLocIncreaseNumberCondition.setId(id);
        invLotLocIncreaseNumberCondition.setQty(qty);
        invLotLocDao.increaseQty(invLotLocIncreaseNumberCondition);
    }

    /**
     * 通过id减少库位的库存数
     * @Date 14:10 2025/1/11
     * @Param [id, qtyPaOut]
     * @return void
     **/
    public void decreaseQty(Long id, BigDecimal qty){
        InvLotLocIncreaseNumberCondition invLotLocIncreaseNumberCondition = new InvLotLocIncreaseNumberCondition();
        invLotLocIncreaseNumberCondition.setId(id);
        invLotLocIncreaseNumberCondition.setQty(qty);
        invLotLocDao.decreaseQty(invLotLocIncreaseNumberCondition);
    }

    /**
     * 通过id增加库位的上架待出数
     * @Date 14:10 2025/1/11
     * @Param [id, qtyPaOut]
     * @return void
     **/
    public List<InvLotLoc> findByLocIds(Set<Long> locIdList){
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(InvLotLoc::getLocId, locIdList);
        return this.find(conditionRule);
    }

    public void createInvLotLoc(PaTaskConfirmItem paTaskConfirmItem, CsPaTask csPaTask){
        InvLotLoc invLotLoc = new InvLotLoc();
        invLotLoc.setWarehouseId(csPaTask.getWarehouseId());
        invLotLoc.setWarehouseName(csPaTask.getWarehouseName());
        invLotLoc.setOwnerId(csPaTask.getOwnerId());
        invLotLoc.setOwnerName(csPaTask.getOwnerName());
        invLotLoc.setSkuId(csPaTask.getSkuId());
        invLotLoc.setSkuName(csPaTask.getSkuName());
        invLotLoc.setLotNum(csPaTask.getLotNum());
        invLotLoc.setLocId(paTaskConfirmItem.getToLocId());
        invLotLoc.setPalletNum(csPaTask.getToPalletNo());
        invLotLoc.setQty(csPaTask.getQtyPlanEa());
        invLotLoc.setStatus(StatusEnum.YES.getCode());
        preSave(invLotLoc);
        this.insert(invLotLoc);
    }

    public List<InvLotLoc> getByPalletNum(String palletNum, Long warehouseId) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(InvLotLoc::getPalletNum, palletNum);
        conditionRule.andEqual(InvLotLoc::getWarehouseId, warehouseId);
        return dao.find(InvLotLoc.class, conditionRule);
    }


    public InvLotLocDao getDao(){
        return dao;
    }

    public void findByWarehouseId(Long warehouseId){
        InvLotLocCondition invLotLocCondition = new InvLotLocCondition();
        invLotLocCondition.setWarehouseId(warehouseId);
        List<InvLotLoc> invLotLocList = this.find(invLotLocCondition);
    }

    public void pageByOwnerExport(InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {
        List<InvLotLocOwnerExcelItem> list = dao.listByOwner(invLotLocCondition);
        easyExcelExportUtil.export("货主库存", list, InvLotLocOwnerExcelItem.class, httpServletResponse);

    }

    public void pageBySkuExport(InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {
        List<InvLotLocSkuExcelItem> list = dao.listBySku(invLotLocCondition);
        easyExcelExportUtil.export("商品库存", list, InvLotLocSkuExcelItem.class, httpServletResponse);
    }

    public void pageByLotNumExport(InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {
        List<InvLotLocLotNumExcelItem> list = dao.listByLotNum(invLotLocCondition);
        easyExcelExportUtil.export("批次库存", list, InvLotLocLotNumExcelItem.class, httpServletResponse);
    }

    public void pageByLocExport(InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {
        List<InvLotLocLocExcelItem> list = dao.listByLoc(invLotLocCondition);
        easyExcelExportUtil.export("库位库存", list, InvLotLocLocExcelItem.class, httpServletResponse);
    }

    public void pageBySkuLocExport(InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {
        List<InvLotLocSkuLocExcelItem> list = dao.listBySkuLoc(invLotLocCondition);
        easyExcelExportUtil.export("商品库位库存", list, InvLotLocSkuLocExcelItem.class, httpServletResponse);
    }

    public void pageBySkuLocLotExport(InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {
        List<InvLotLocSkuLocLotExcelItem> list = dao.listBySkuLocLot(invLotLocCondition);
        easyExcelExportUtil.export("商品库位批次库存", list, InvLotLocSkuLocLotExcelItem.class, httpServletResponse);
    }

    public List<InvLotLoc> storageLocationStatistics(StorageLocationStatisticsCondition condition){
        return dao.storageLocationStatistics(condition);
    }

    public List<StorageLocationStatistics.Ranking> storageLocationRanking(StorageLocationStatisticsCondition condition){
        return dao.storageLocationRanking(condition);
    }

    /**
     *@Description 按商品/库位/批次号/容器号查询
     *@Param invLotLocCondition
     *@Return * {@link PageResult< InvLotLocQuery> }
     *@Date 2025/3/7 9:36
     *<AUTHOR>
     **/
    public PageResult<InvLotLocQuery> pageBySkuLocLotAndPalletNum(InvLotLocPageCondition invLotLocCondition) {
        PageResult<InvLotLocQuery> pageResult = dao.pageBySkuLocLotAndPalletNum(invLotLocCondition);
        List<InvLotLocQuery> queryList = pageResult.getRows();
        if (CollUtil.isNotEmpty(queryList)) {
            setAvailableQtyByLoc(queryList);
            converterList(queryList);
        }
        return pageResult;
    }

    public PageResult<InvLotLocQuery> pageBySkuInv(InvLotLocPageCondition invLotLocCondition) {
        PageResult<InvLotLocQuery> pageResult = dao.pageBySkuInv(invLotLocCondition);
        List<InvLotLocQuery> queryList = pageResult.getRows();
        if (CollUtil.isNotEmpty(queryList)) {
            setAvailableQtyByLoc(queryList);
            converterList(queryList);
        }
        return pageResult;
    }

    /**
     *@Description 按商品/库位/批次号/容器号查询导出
     *@Param invLotLocCondition
     *@param httpServletResponse
     *@Return Void
     *@Date 2025/3/10 11:48
     *<AUTHOR>
     **/
    public void pageBySkuLocLotAndPalletNumExport(InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws Exception{
        List<InvSkuLocLotAndPalletNumItem> list = dao.listBySkuLocLotAndPalletNum(invLotLocCondition);
        easyExcelExportUtil.export("商品库位批次号容器号", list, InvSkuLocLotAndPalletNumItem.class, httpServletResponse);
    }


    /**
     *@Description 定时生成库龄报表数据
     *@Param
     *@Return Void
     *@Date 2025/3/12 19:30
     *<AUTHOR>
     **/
    public void ageReportData() {
        logger.info("开始生成库龄报表数据");
        //查询一天产生变更的库存数据
        Date date = DateUtil.offsetDay(new Date(),-1);
        logger.info("查询日期为：{}", DateUtil.formatDate(date));
        InvLotLocPageCondition condition = new InvLotLocPageCondition();
        condition.setUpdateTime(date);
        Boolean flag = Boolean.TRUE;
        Integer pageNumber = 1;
        Integer pageSize = 100;
        condition.setPageSize(pageSize);
        List<InvLotLoc> list = new ArrayList<>();
        while (flag) {
            condition.setPageNumber(pageNumber);
            PageResult<InvLotLoc> pageResult = dao.getByUpdateTime(condition);
            if(CollectionUtil.isNotEmpty(pageResult.getRows())){
                list.addAll(pageResult.getRows());
                if(pageResult.getRows().size() < pageSize){
                    flag = Boolean.FALSE;
                }else {
                    pageNumber++;
                }
            }else{
                flag = Boolean.FALSE;
            }
        }
        if(CollectionUtil.isNotEmpty(list)){
            logger.info("查询到的数据为：{}", JsonUtil.toJson(list));
            //抽取批次号
            List<String> lotNumList = list.stream().map(InvLotLoc::getLotNum).distinct().collect(Collectors.toList());
            //抽取商品id
            List<Long> skuIdList = list.stream().map(InvLotLoc::getSkuId).distinct().collect(Collectors.toList());
            //查询商品信息
            List<Sku> skuList = skuService.findByIds(skuIdList.toArray(new Long[0]));
            Map<Long, Sku> skuMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(skuList)){
                skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Function.identity()));
            }
            //根据批次号查询库龄记录
            List<AgeReport> ageReportList = ageReportService.findByLotNumList(lotNumList);
            Map<String, AgeReport> ageReportMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(ageReportList)){
                ageReportMap = ageReportList.stream().collect(Collectors.toMap(AgeReport::getLotNum, item -> item));
            }
            //根据批次号查询库存交易记录
            List<StockTransaction> stockTransactionList = stockTransactionService.findByLotNumListAndType(lotNumList, Arrays.asList(TransactionType.TRAN_RCV,TransactionType.TRAN_SP));
            Map<String, List<StockTransaction>> rcvMap = new HashMap<>();
            Map<String, List<StockTransaction>> spMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(stockTransactionList)){
                //过滤type为 入库的交易记录
                List<StockTransaction> rcvList = stockTransactionList.stream().filter(item -> TransactionType.TRAN_RCV.equals(item.getTransactionType())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(rcvList)){
                    rcvMap = rcvList.stream().collect(Collectors.groupingBy(StockTransaction::getToLotNum));
                }
                //过滤type 为 出库的交易记录
                List<StockTransaction> spList = stockTransactionList.stream().filter(item -> TransactionType.TRAN_SP.equals(item.getTransactionType())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(spList)){
                    spMap = spList.stream().collect(Collectors.groupingBy(StockTransaction::getFmLotNum));
                }
            }
            //根据批次号查询对应入库日期
            List<InvLotAtt> attrList = invLotAttService.listByLotNums(lotNumList);
            Map<String,InvLotAtt> attMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(attrList)){
                attMap = attrList.stream().collect(Collectors.toMap(InvLotAtt::getLotNum, item -> item, (v1, v2) -> v1));
            }

            List<AgeReport> insertList = new ArrayList<>();
            List<AgeReport> updateList = new ArrayList<>();
            //组装数据
            for (InvLotLoc invLotLoc : list) {
                AgeReport ageReport = ageReportMap.get(invLotLoc.getLotNum());
                //滞库时间
                Integer stockDay = null;
                Sku sku = skuMap.get(invLotLoc.getSkuId());
                if(ObjectUtil.isNull(ageReport)){
                    ageReport = new AgeReport();
                    BeanUtil.copyProperties(invLotLoc, ageReport);
                    ageReport.setId(null);

                }
                if(ObjectUtil.isNotNull(sku)){
                    ageReport.setSkuCode(sku.getSkuCode());
                    stockDay = sku.getStockDays();
                }
                //库存数
                ageReport.setCurrentQty(invLotLoc.getQty());
                List<StockTransaction> currentRcvList = rcvMap.get(invLotLoc.getLotNum());
                Date minDate = null;
                if(CollectionUtil.isNotEmpty(currentRcvList)){
                    //入库数
                    ageReport.setStockQty(currentRcvList.stream().map(StockTransaction::getToQtyEaOp).reduce(BigDecimal.ZERO, BigDecimal::add));
                }else{
                    ageReport.setStockQty(invLotLoc.getQty());
                }
                InvLotAtt att = attMap.get(invLotLoc.getLotNum());
                if(ObjectUtil.isNotNull(att)){
                    if(StringUtil.isNotBlank(att.getLotAtt01())){
                        //入库时间
                        Date stockTime = DateUtil.parse(att.getLotAtt01());
                        ageReport.setStockTime(stockTime);
                        minDate = stockTime;
                        //是否滞库
                        String warningType = YesNoEnum.NO.getCode();
                        //滞库率
                        BigDecimal stockPercent = BigDecimal.ZERO;
                        if(ObjectUtil.isNotNull(stockDay)){
                            //滞库到期时间 = 入库时间 往后偏移天数
                            Date expirationDate = DateUtil.offsetDay(stockTime, stockDay);
                            //抽取存在滞库到期的数据
                            Date currentDay = new Date();
                            if(currentDay.after(expirationDate) || currentDay.equals(expirationDate)){
                                warningType = YesNoEnum.YES.getCode();
                                stockPercent = ageReport.getCurrentQty().divide(ageReport.getStockQty(), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                            }
                        }
                        ageReport.setWarningType(warningType);
                        ageReport.setStockPercent(stockPercent);
                    }
                }
                //出库数
                ageReport.setOutboundQty(BigDecimal.ZERO);
                Date outbondDate = new Date();
                List<StockTransaction> currentSpList = spMap.get(invLotLoc.getLotNum());
                if(CollectionUtil.isNotEmpty(currentSpList)){
                    BigDecimal outboundQty = currentSpList.stream().map(StockTransaction::getFmQtyEaOp).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //如果入库数和出库数一致，则出库时间 = 出库时间最大值
                    if(ageReport.getStockQty().compareTo(outboundQty) == 0){
                        outbondDate = currentSpList.stream().map(StockTransaction::getCreateTime).max(Comparator.naturalOrder()).get();
                    }
                    ageReport.setOutboundQty(outboundQty);
                }
                //库龄 = 两个时间的相隔天数
                if(ObjectUtil.isNotNull(minDate) && ObjectUtil.isNotNull(outbondDate)){
                    Long day = DateUtil.betweenDay(minDate, outbondDate, true);
                    ageReport.setStockAge(day);
                    String timeType = null;
                    if(day >= 0 && day <= 30){
                        timeType = AgeReportTimeTypeEnum.ONE.getCode();
                    }else if(day > 30 && day <= 60){
                        timeType = AgeReportTimeTypeEnum.TWO.getCode();
                    }else if(day > 60 && day <= 90){
                        timeType = AgeReportTimeTypeEnum.THREE.getCode();
                    }else if(day > 90 && day <= 180){
                        timeType = AgeReportTimeTypeEnum.FOUR.getCode();
                    }else if(day > 180){
                        timeType =AgeReportTimeTypeEnum.FIVE.getCode();
                    }
                    ageReport.setTimeType(timeType);
                    ageReport.setStockAge(day);
                }
                Date createTime = new Date();
                if(ObjectUtil.isNotNull(ageReport.getId())){
                    ageReport.setModifyTime(createTime);
                    updateList.add(ageReport);
                }else {
                    ageReport.setCreateTime(createTime);
                    ageReport.setModifyTime(createTime);
                    insertList.add(ageReport);
                }
            }
            if(CollectionUtil.isNotEmpty(insertList)){
                logger.info("库存龄期数据新增:{}", JsonUtil.toJson(insertList));
                for (AgeReport ageReport : insertList) {
                    SessionUserInfo sessionUserInfo = SessionContext.get();
                    sessionUserInfo.setTenancy(ageReport.getTenancy());
                    sessionUserInfo.setCompanyId(ageReport.getCompanyId());
                    SessionContext.put(sessionUserInfo);
                    ageReportService.preSave(ageReport);
                }
                ageReportService.batchInsert(insertList);
            }
            if(CollectionUtil.isNotEmpty(updateList)){
                logger.info("库存龄期数据修改:{}", JsonUtil.toJson(updateList));
                for (AgeReport ageReport : updateList) {
                    SessionUserInfo sessionUserInfo = SessionContext.get();
                    sessionUserInfo.setTenancy(ageReport.getTenancy());
                    sessionUserInfo.setCompanyId(ageReport.getCompanyId());
                    SessionContext.put(sessionUserInfo);
                    ageReportService.preSave(ageReport);
                }
                ageReportService.batchUpdate(updateList);
            }
        }
        logger.info("库存龄期数据更新完成");
    }

    public PageResult<InvLotLocBySkuGroupQuery> pageBySkuGroupLoc(InvLotLocBySkuGroupPageCondition invLotLocCondition) {
        PageResult<InvLotLocBySkuGroupQuery> result = dao.pageBySkuGroupLoc(invLotLocCondition);
        List<InvLotLocBySkuGroupQuery> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(result.getRows())){
            Map<String, List<InvLotLocBySkuGroupQuery>> listMap = result.getRows().stream().collect(Collectors.groupingBy(InvLotLocBySkuGroupQuery::getSkuGroupCode));
            listMap.forEach((key, value) -> {
                list.addAll(value);
            });
        }
        result.setRows(list);
        return result;
    }

    public List<BulletinBoardOverviewItem.BulletinBoardGoodsItem> getSkuInfoByLocId(Long locId) {
        return dao.getSkuInfoByLocId(locId);
    }

    /**
     * 根据库位id集合查询库存信息
     * @param locIdList
     * @return
     */
    public List<InvLotLoc> findLocListByLocIds(List<Long> locIdList) {
        if(EmptyUtil.isEmpty(locIdList)){
            return new ArrayList<>();
        }
        return dao.findLocListByLocIds(locIdList);
    }

    public LocCalculationResultsItem findOccupyLocByWarehouseId(String begin, String end, Long warehouseId) {
        return dao.findOccupyLocByWarehouseId(new OccupyLocCondition().setWarehouseId(warehouseId)
                .setBeginDate(begin).setEndDate(end));
    }

    public LocCalculationResultsItem findEmptyLocByWarehouseId(String begin, String end, Long warehouseId) {
        return dao.findEmptyLocByWarehouseId(new OccupyLocCondition().setWarehouseId(warehouseId)
                .setBeginDate(begin).setEndDate(end));
    }
}
