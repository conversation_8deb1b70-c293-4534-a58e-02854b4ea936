package com.chinaservices.wms.module.process.staff.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjUtil;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.wms.module.process.staff.dao.ProcessStaffCertificateFileDao;
import com.chinaservices.wms.module.process.staff.domain.ProcessStaffCertificateFileTableItem;
import com.chinaservices.wms.module.process.staff.domain.ProcessStaffCertificateTableItem;
import com.chinaservices.wms.module.process.staff.model.ProcessStaffCertificateFile;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: <PERSON>ran.Chen
 * @Date: 2025/4/15
 * @Description: 加工人员资格证书附件服务层
 */
@Service
public class ProcessStaffCertificateFileService extends ModuleBaseServiceSupport<ProcessStaffCertificateFileDao, ProcessStaffCertificateFile, Long> {

    /**
     * 保存资质证书附件信息
     * @param psCertificateTableList 入参
     * @return Boolean
     */
    public Boolean saveOrUpdate(List<ProcessStaffCertificateTableItem> psCertificateTableList) {
        List<ProcessStaffCertificateFile> psCertificateFileList = new ArrayList<>();
        // 1. 遍历所有的资质证书
        for (ProcessStaffCertificateTableItem tableItem : psCertificateTableList) {
            // 2. 获取当前businessId下的所有附件信息
            ConditionRule conditionRule = ConditionRule.getInstance();
            conditionRule.andEqual(ProcessStaffCertificateFile::getBusinessId, tableItem.getId());
            List<ProcessStaffCertificateFile> fileList = dao.find(conditionRule);
            // 3. 当资质证书附件为空时 删除所有数据库中的数据
            if(CollectionUtil.isEmpty(tableItem.getCertificateFileList())){
                if(CollectionUtil.isNotEmpty(fileList)){
                    List<Long> fileIdList = fileList.stream().map(ProcessStaffCertificateFile::getId).toList();
                    return dao.delete(ArrayUtil.toArray(fileIdList, Long.class)) > 0;
                }
                return Boolean.TRUE;
            }
            // 4. 当不存在附件信息时 直接全部新增
            if(CollectionUtil.isEmpty(fileList)){
                for (ProcessStaffCertificateFileTableItem fileTableItem : tableItem.getCertificateFileList()) {
                    ProcessStaffCertificateFile psCertificateFile = new ProcessStaffCertificateFile();
                    BeanUtil.copyProperties(fileTableItem, psCertificateFile);
                    psCertificateFile.setBusinessId(String.valueOf(tableItem.getId()));
                    preSave(psCertificateFile);
                    psCertificateFileList.add(psCertificateFile);
                }
                return dao.batchInsert(psCertificateFileList) > 0;
            }
            // 5. 当数据库中已存在附件信息时 过滤出数据库中已存在的id信息
            List<Long> originIdList = fileList.stream().map(ProcessStaffCertificateFile::getId).toList();
            List<Long> newIdList = tableItem.getCertificateFileList().stream().map(ProcessStaffCertificateFileTableItem::getId).toList();
            // 6. 创建新增、更新、删除列表
            ArrayList<ProcessStaffCertificateFile> insertList = new ArrayList<>();
            ArrayList<ProcessStaffCertificateFile> updateList = new ArrayList<>();
            Long[] deleteIds = ArrayUtil.toArray(originIdList.stream().filter(id -> !newIdList.contains(id)).toList(), Long.class);
            // 7. 遍历所有附件列表
            for (ProcessStaffCertificateFileTableItem fileTableItem : tableItem.getCertificateFileList()) {
                // 8. 新增/更新前操作
                ProcessStaffCertificateFile psCertificateFile = new ProcessStaffCertificateFile();
                // 9. 当不存在id时 加入新增列表
                if(ObjUtil.isNull(fileTableItem.getId())){
                    BeanUtil.copyProperties(fileTableItem, psCertificateFile);
                    psCertificateFile.setBusinessId(String.valueOf(tableItem.getId()));
                    preSave(psCertificateFile);
                    insertList.add(psCertificateFile);
                }
                // 10. 当id在新旧列表都存在时 加入更新列表
                if(originIdList.contains(fileTableItem.getId()) && newIdList.contains(fileTableItem.getId())){
                    psCertificateFile = dao.findById(fileTableItem.getId());
                    psCertificateFile.setFileName(fileTableItem.getFileName());
                    psCertificateFile.setFileId(fileTableItem.getFileId());
                    psCertificateFile.setFileType(fileTableItem.getFileType());
                    psCertificateFile.setFileSize(fileTableItem.getFileSize());
                    psCertificateFile.setFilePath(fileTableItem.getFilePath());
                    psCertificateFile.setOriginalName(fileTableItem.getOriginalName());
                    preSave(psCertificateFile);
                    updateList.add(psCertificateFile);
                }
            }
            return BooleanUtil.and(
                CollectionUtil.isNotEmpty(insertList) ? dao.batchInsert(insertList) > 0 : Boolean.TRUE,
                CollectionUtil.isNotEmpty(updateList) ? dao.batchUpdate(updateList) > 0 : Boolean.TRUE,
                ArrayUtil.isNotEmpty(deleteIds) ? dao.delete(deleteIds) > 0 : Boolean.TRUE
            );
        }
        return Boolean.TRUE;
    }

}
