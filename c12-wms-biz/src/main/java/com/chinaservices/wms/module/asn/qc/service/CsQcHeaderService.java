package com.chinaservices.wms.module.asn.qc.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.domain.LotAttribute;
import com.chinaservices.wms.common.domain.LotDetailItem;
import com.chinaservices.wms.common.enums.DocumentTypeEnum;
import com.chinaservices.wms.common.enums.asn.*;
import com.chinaservices.wms.common.exception.AsnExceptionEnum;
import com.chinaservices.wms.common.util.LotUtil;
import com.chinaservices.wms.common.util.MapObjectUtil;
import com.chinaservices.wms.module.archive.archives.service.LogisticsFileAutoGenerateService;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailSnDao;
import com.chinaservices.wms.module.asn.qc.dao.CsQcHeaderDao;
import com.chinaservices.wms.module.asn.qc.domain.*;
import com.chinaservices.wms.module.asn.qc.model.CsQcDetail;
import com.chinaservices.wms.module.asn.qc.model.CsQcHeader;
import com.chinaservices.wms.module.asn.qc.model.CsQcReturnDetail;
import com.chinaservices.wms.module.asn.receive.dao.AsnReceiveDao;
import com.chinaservices.wms.module.asn.receive.model.AsnReceive;
import com.chinaservices.wms.module.basic.sku.domain.SkuPageCondition;
import com.chinaservices.wms.module.basic.sku.domain.SkuQuery;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.folder.logistics.domain.LogisticsFolderDetailLinkCondition;
import com.chinaservices.wms.module.so.so.domain.AssignLocQuery;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageQuery;
import com.chinaservices.wms.module.stock.loc.enums.LotSearchTypeEnum;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 质检管理
* @date  2024-12-26
**/
@Service
public class CsQcHeaderService extends ModuleBaseServiceSupport<CsQcHeaderDao,CsQcHeader,Long> {

    @Autowired
    protected CsQcDetailService csQcDetailService;

    @Autowired
    private NumberGenerator numberGenerator;

    @Autowired
    private StockService stockService;


    @Autowired
    private SkuService skuService;

    @Autowired
    private CsQcReturnDetailService csQcReturnDetailService;

    @Autowired
    private WarehouseLocService warehouseLocService;

    @Autowired
    private LotUtil lotUtil;

    @Autowired
    private AsnReceiveDao asnReceiveDao;

    @Autowired
    private AsnDetailSnDao asnDetailSnDao;
    @Autowired
    private TaskExecutor taskExecutor;
    @Autowired
    private LogisticsFileAutoGenerateService logisticsFileAutoGenerateService;


    /**
    * 分页查询
    * @param qcHeaderConditon
    * @return PageResult<CsQcHeaderQuery>
    */
    public PageResult<CsQcHeaderQuery> page(CsQcHeaderPageCondition qcHeaderConditon) {
        PageResult<CsQcHeaderQuery> pageResult = dao.page(qcHeaderConditon);
        List<CsQcHeaderQuery> csQcHeaderQueries = pageResult.getRows();
        if (CollUtil.isNotEmpty(csQcHeaderQueries)) {
            List<Long> idList = csQcHeaderQueries.stream().map(CsQcHeaderQuery::getId).toList();
            List<CsQcDetailQuery> detailList = dao.getQcDetailByHeaderId(idList);

            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andIn("headerId", idList);
            Map<Long, List<CsQcReturnDetail>> detailMap = new HashMap<>();
            List<CsQcReturnDetail> returnDetails = csQcReturnDetailService.find(conditionRule);
            if (CollectionUtils.isNotEmpty(returnDetails)) {
                detailMap = returnDetails.stream().collect(Collectors.groupingBy(CsQcReturnDetail::getHeaderId));
            }
            if (CollUtil.isNotEmpty(detailList)) {
                for (CsQcHeaderQuery csQcHeaderQuery : csQcHeaderQueries) {
                    List<CsQcDetailQuery> detailQueryList = detailList.stream().filter(detail -> detail.getHeaderId().equals(csQcHeaderQuery.getId())).toList();

                    BigDecimal actualQualityInspection = detailQueryList.getFirst().getActualQualityInspection() == null ? BigDecimal.ZERO : detailQueryList.getFirst().getActualQualityInspection();
                    BigDecimal returnWarehouseNum = detailQueryList.getFirst().getReturnWarehouseNum() == null ? BigDecimal.ZERO  : detailQueryList.getFirst().getReturnWarehouseNum();
                    detailQueryList.forEach(e ->
                            processField(e.getSnStr(), e::setSnMap)
                    );
                    if (QcStatusEnum.NOTINSPECTED.getCode().equals(csQcHeaderQuery.getQcStatus())) {
                        csQcHeaderQuery.setEdit(true);
                    }
                    if (QcTypeEnum.OUTBOUNDINSPECTED.getCode().equals(csQcHeaderQuery.getQcType()) && actualQualityInspection.compareTo(returnWarehouseNum) > 0) {
                        csQcHeaderQuery.setReturnWarehouse(true);
                        csQcHeaderQuery.setUnReturnMap(detailQueryList.getFirst().getSnMap());
                    }
                    List<CsQcReturnDetail> csQcReturnDetailList = detailMap.get(csQcHeaderQuery.getId());
                    if (CollectionUtils.isNotEmpty(csQcReturnDetailList)) {
                        csQcHeaderQuery.setReturnWarehouseDetail(true);
                        if (csQcHeaderQuery.isReturnWarehouse()){
                            // 1. 获取已返库上架的序列号，并扁平化为Set
                            Set<String> filterSet = csQcReturnDetailList.stream()
                                    .filter(e -> StrUtil.isNotBlank(e.getSnStr()))
                                    .flatMap(e -> MapObjectUtil.getMapToObjectValue(e.getSnStr(), Boolean.TRUE).stream())
                                    .collect(Collectors.toSet());
                            // 2. 获取质检单SN数据，直接转为Map
                            Map<String, List<String>> unReturnMap = detailQueryList.getFirst().getSnMap().entrySet().stream()
                                    // 过滤每个List：移除存在于filterSet的元素
                                    .map(entry -> {
                                        List<String> filteredValues = entry.getValue().stream()
                                                .filter(value -> !filterSet.contains(value))
                                                .collect(Collectors.toList());
                                        return new AbstractMap.SimpleEntry<>(entry.getKey(), filteredValues);
                                    })
                                    // 移除过滤后为空的List
                                    .filter(entry -> !entry.getValue().isEmpty())
                                    // 收集为新的Map
                                    .collect(Collectors.toMap(
                                            Map.Entry::getKey,
                                            Map.Entry::getValue
                                    ));
                            csQcHeaderQuery.setUnReturnMap(unReturnMap);
                        }
                    }
                    csQcHeaderQuery.setDetailQueryList(detailQueryList);
                }
            }
        }
        return pageResult;
    }

    public CsQcDetailQuery getReturnSn(Long id){
        CsQcDetailQuery csQcDetailQuery = new CsQcDetailQuery();
        CsQcHeader csQcHeader = dao.findById(id);
        if (csQcHeader == null){
            throw new ServiceException(AsnExceptionEnum.QC_HEADER_NOT_EXIST);
        }
        // 不是出库质检
        if (!QcTypeEnum.OUTBOUNDINSPECTED.getCode().equals(csQcHeader.getQcType())){
            throw new ServiceException(AsnExceptionEnum.QC_HEADER_NOT_OUTBOUNDINSPECTED);
        }
        // 查询质检明细,是否有序列号明细
        List<CsQcDetail> csQcDetails = csQcDetailService.find(new ConditionRule().andEqual(CsQcDetail::getHeaderId, csQcHeader.getId()));
        if (StrUtil.isBlank(csQcDetails.getFirst().getSnStr())){
            return csQcDetailQuery;
        }
        BeanUtil.copyProperties(csQcDetails.getFirst(),csQcDetailQuery);
        processField(csQcDetailQuery.getSnStr(), csQcDetailQuery::setSnMap);

        // 查询是否有出库上架
        List<CsQcReturnDetail> csQcReturnDetails = csQcReturnDetailService.find(new ConditionRule().andEqual(CsQcReturnDetail::getHeaderId, csQcHeader.getId()));
        if (CollectionUtils.isEmpty(csQcReturnDetails)){
            return csQcDetailQuery;
        }
        // 1. 获取已返库上架的序列号，并扁平化为Set
        Set<String> filterSet = csQcReturnDetails.stream()
                .filter(e -> StrUtil.isNotBlank(e.getSnStr()))
                .flatMap(e -> MapObjectUtil.getMapToObjectValue(e.getSnStr(), Boolean.TRUE).stream())
                .collect(Collectors.toSet());
        // 2. 获取质检单SN数据，直接转为Map
        Map<String, List<String>> unReturnMap = csQcDetailQuery.getSnMap().entrySet().stream()
                // 过滤每个List：移除存在于filterSet的元素
                .map(entry -> {
                    List<String> filteredValues = entry.getValue().stream()
                            .filter(value -> !filterSet.contains(value))
                            .collect(Collectors.toList());
                    return new AbstractMap.SimpleEntry<>(entry.getKey(), filteredValues);
                })
                // 移除过滤后为空的List
                .filter(entry -> !entry.getValue().isEmpty())
                // 收集为新的Map
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                ));
        csQcDetailQuery.setSnMap(unReturnMap);
        return csQcDetailQuery;
    }

    /**
     * 根据Id查询费用信息
     *
     * @param id
     * @return AsnHeader
     */
    public CsQcHeaderQuery getById(Long id) {
        CsQcHeader csQcHeader = dao.findById(id);
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual("headerId", id);
        List<CsQcDetail> detailList = csQcDetailService.find(conditionRule);
        List<CsQcDetailQuery> detailQueryList = BeanUtil.copyToList(detailList, CsQcDetailQuery.class);
        detailQueryList.forEach(e->{
            processField(e.getSnStr(), e::setSnMap);
            processField(e.getQcSn(), e::setQcSnMap);
            processField(e.getUnQcSn(), e::setUnQcSnMap);
        });
        CsQcHeaderQuery csQcHeaderQuery = new CsQcHeaderQuery();
        BeanUtil.copyProperties(csQcHeader, csQcHeaderQuery);
        csQcHeaderQuery.setDetailQueryList(detailQueryList);
        return csQcHeaderQuery;
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertOrModify(CsQcHeaderItem item) {
        logger.info("保存质检单信息:{}", JSONObject.toJSONString(item));
        Boolean idIsNull = Objects.isNull(item.getId()) ? Boolean.TRUE : Boolean.FALSE;
        // 1.保存主表
        if (idIsNull) {
            item.setQualityInspectionOrderNo(numberGenerator.nextValue(IdRuleConstant.QC_NO));
        }
        item.setQcStatus(QcStatusEnum.NOTINSPECTED.getCode());
        saveOrUpdate(item);
        CsQcHeader csQcHeader = dao.findById(item.getId());;
        // 2. 保存明细表
        if (Objects.nonNull(csQcHeader)) {
            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andEqual("headerId", csQcHeader.getId());
            csQcDetailService.delete(conditionRule);
        } else {
            throw new ServiceException(AsnExceptionEnum.QC_HEADER_NOT_EXIST);
        }
        // 新增质检时,商品需要冻结
        // 更新质检明细
        List<String> snNoList = new ArrayList<>();
        List<CsQcDetailQuery> detailItemList = item.getDetailQueryList();
        for (CsQcDetailQuery detailItem : detailItemList){
            detailItem.setHeaderId(csQcHeader.getId());
            if (EmptyUtil.isNotEmpty(detailItem.getSnMap())) {
                detailItem.setSnStr(MapObjectUtil.mapToObject(detailItem.getSnMap()));
                snNoList.addAll(MapObjectUtil.getMapToListValue(detailItem.getSnMap()));
            }
            if (Objects.isNull(detailItem.getPalletNo())) {
                detailItem.setPalletNo("*");
            }
        }
        List<CsQcDetail> detailList = BeanUtil.copyToList(detailItemList, CsQcDetail.class);
        detailList.forEach(
                detail -> csQcDetailService.preSave(detail)
        );
        csQcDetailService.batchInsert(detailList);

        // 更新sn表的质检状态
        if (CollectionUtils.isNotEmpty(snNoList)){
            asnDetailSnDao.updateStatusBySn(new QcSnDetailCondition().setQcStatus(YesNoEnum.YES.getCode()).setSnNos(snNoList));
        }
        CsQcDetailQuery detailQuery = detailItemList.getFirst();
        String type = TransactionType.TRAN_QC_CRT_NF;
        if (Objects.isNull(item.getId())) {
            if (item.getQcType().equals(QcTypeEnum.OUTBOUNDINSPECTED.getCode())) {
                type = TransactionType.TRAN_QC_OUT_CRT;
            }
        } else {
            if (item.getQcType().equals(QcTypeEnum.OUTBOUNDINSPECTED.getCode())) {
                type = TransactionType.TRAN_QC_OUT_EDT_SAVE;
            } else {
                type = TransactionType.TRAN_QC_EDT_ADD_NF;
            }
        }
        if (!item.getQcInspection().equals(QcInspectedEnum.BEFORERECEIVING.getCode())) {
            execStock(detailQuery, type, item.getQualityInspectionOrderNo(),snNoList);
        }

        if (idIsNull) {
            logisticsDocuments(item.getAsnNo(),csQcHeader.getQualityInspectionOrderNo(),SessionContext.getSessionUserInfo());
        }
    }

    /**
     * 物流文件管理
     * @param upDocumentNo 上游单号eg:入库单号
     * @param orderNo 下游单号eg:质检单号
     * @param sessionUserInfo user
     */
    public void logisticsDocuments(String upDocumentNo,String orderNo, SessionUserInfo sessionUserInfo){
        logger.info("生成物流文件管理");
        taskExecutor.execute(() -> {
            //单号集合
            LogisticsFolderDetailLinkCondition logisticsFolderDetailLinkCondition = new LogisticsFolderDetailLinkCondition();
            logisticsFolderDetailLinkCondition.setUpDocumentNo(upDocumentNo);
            logisticsFolderDetailLinkCondition.setDocumentNos(Collections.singletonList(orderNo));

            List<LogisticsFolderDetailLinkCondition> linkConditions =new ArrayList<>();
            linkConditions.add(logisticsFolderDetailLinkCondition);
            logger.info("生成物流文件管理开始:{}",linkConditions);
            //这⾥第三个参数，为上级的单号，这⾥需替换成⼊库单号号码
            logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                    DocumentTypeEnum.QC,linkConditions,sessionUserInfo.getCompanyId(),sessionUserInfo.getTenancy(), null);
            //这⾥第三个参数，为上级的单号，这⾥需替换成⼊库单号号码
            logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                    DocumentTypeEnum.ASN_QC,linkConditions,sessionUserInfo.getCompanyId(),sessionUserInfo.getTenancy(), null);
        });
    }

    public void execStock(CsQcDetailQuery detailQuery, String type, String qualityInspectionOrderNo,List<String> snNoList) {
        stockService.exec(new InvLotLocQtyBO()
                .setLocId(detailQuery.getLocId())
                .setOwnerId(detailQuery.getOwnerId())
                .setSkuId(detailQuery.getSkuId())
                .setWarehouseId(detailQuery.getWarehouseId())
                .setLotNum(detailQuery.getLotNum())
                .setTransactionType(type)
                .setOrderNo(qualityInspectionOrderNo)
                .setSkuSn(snNoList)
                .setPalletNum(detailQuery.getPalletNo())
                .setUpdateNum(new BigDecimal(detailQuery.getPlannedQualityInspection())));
    }


    @Transactional(rollbackFor = Exception.class)
    public void cancel(CsQcHeaderCancelItem item) {
        // 1.更新质检主表状态
        preSave(item);
        updateByIds(item, item.getIds().toArray(Long[]::new));
        // 2.根据id查询质检主表信息
        List<CsQcHeader> csQcHeaderList = find(new ConditionRule().andIn("id", item.getIds()));
        Map<Long, CsQcHeader> csQcHeaderMap = csQcHeaderList.stream().collect(Collectors.toMap(CsQcHeader::getId, Function.identity()));
        // 3.获取 质检类型 = 出库质检 的质检数据
        List<Long> headerIds = csQcHeaderList.stream().map(CsQcHeader::getId).toList();
        if (CollUtil.isNotEmpty(headerIds)) {
            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andIn("headerId", headerIds);
            List<CsQcDetail> csQcDetailList = csQcDetailService.find(conditionRule);
            List<String> snNoList = new ArrayList<>();
            for (CsQcDetail csQcDetail : csQcDetailList) {
                CsQcHeader csQcHeader = csQcHeaderMap.get(csQcDetail.getHeaderId());
                CsQcDetailQuery csQcDetailQuery = BeanUtil.copyProperties(csQcDetail, CsQcDetailQuery.class);
                List<String> snNos = MapObjectUtil.getMapToObjectValue(csQcDetail.getSnStr(), Boolean.TRUE);
                snNoList.addAll(snNos);
                // 库内质检单取消质检（非冻结商品）
                String type = TransactionTypeEnum.TRAN_QC_CAN_NF.getCode();
                if (csQcHeader.getQcType().equals(QcTypeEnum.OUTBOUNDINSPECTED.getCode())) {
                    type = TransactionTypeEnum.TRAN_QC_OUT_CAN.getCode();
                }
                if (!csQcHeader.getQcInspection().equals(QcInspectedEnum.BEFORERECEIVING.getCode())) {
                    execStock(csQcDetailQuery, type, csQcHeader.getQualityInspectionOrderNo(),snNos);
                }
            }
            // 更新sn表的质检状态
            if (CollectionUtils.isNotEmpty(snNoList)){
                asnDetailSnDao.updateStatusBySn(new QcSnDetailCondition().setQcStatus(YesNoEnum.NO.getCode()).setSnNos(snNoList));
            }
        }else {

        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void returnWarehouse(ReturnWarehouseItem item) {
        CsQcHeader csQcHeader = dao.findById(item.getId());
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn("headerId", item.getId());
        CsQcDetail csQcDetail = csQcDetailService.findFirst(conditionRule);
        if (csQcDetail.getActualQualityInspection().equals(csQcDetail.getReturnWarehouseNum())) {
            throw new ServiceException(AsnExceptionEnum.ACTUALQUALITYINSPECTION_RETURNWAREHOUSE);
        }
        stockService.exec(new InvLotLocQtyBO()
                .setToLocId(item.getToLocId())
                .setLocId(item.getToLocId())
                .setOwnerId(csQcDetail.getOwnerId())
                .setSkuId(item.getSkuId())
                .setWarehouseId(csQcDetail.getWarehouseId())
                .setLotNum(csQcDetail.getLotNum())
                .setOrderNo(csQcHeader.getQualityInspectionOrderNo())
                .setTransactionType(TransactionType.TRAN_QC_OUT_RET)
                .setPalletNum(csQcDetail.getPalletNo())
                .setToPallet(item.getToPallet())
                .setUpdateNum(item.getQtyPaEa()));
        csQcDetail.setReturnWarehouseNum((csQcDetail.getReturnWarehouseNum() == null ? BigDecimal.ZERO : csQcDetail.getReturnWarehouseNum()).add(item.getQtyPaEa()));
        csQcDetailService.update(csQcDetail);
        int num = 1;
        List<CsQcReturnDetail> csQcReturnDetailList = csQcReturnDetailService.find(new ConditionRule().andEqual("headerId", item.getId()));
        if (CollectionUtils.isNotEmpty(csQcReturnDetailList)) {
            num = csQcReturnDetailList.size() + 1;
        }

        WarehouseLoc warehouseLoc = warehouseLocService.findById(item.getToLocId());
        CsQcReturnDetail csQcReturnDetail = new CsQcReturnDetail();
        csQcReturnDetail.setStatus(PaStatusEnum.SHELVESED.getCode());
        csQcReturnDetail.setActualListNum(item.getQtyPaEa());
        csQcReturnDetail.setHeaderId(item.getId());
        csQcReturnDetail.setQcTaskNo(numberGenerator.nextValue(IdRuleConstant.PA_CODE));
        csQcReturnDetail.setQcSubTaskNo(csQcReturnDetail.getQcTaskNo() + "-" + num);
        csQcReturnDetail.setLocId(item.getToLocId());
        csQcReturnDetail.setSnStr(MapObjectUtil.mapToObject(item.getSnMap()));
        csQcReturnDetail.setLocCode(warehouseLoc.getLocCode());
        csQcReturnDetail.setPallet(item.getToPallet());
        csQcReturnDetail.setZoneId(warehouseLoc.getZoneId());
        csQcReturnDetail.setZoneName(warehouseLoc.getZoneName());
        csQcReturnDetail.setShelfName(warehouseLoc.getShelfName());
        csQcReturnDetail.setCreateName(SessionContext.get().getUserName());
        csQcReturnDetailService.preSave(csQcReturnDetail);
        csQcReturnDetailService.saveOrUpdate(csQcReturnDetail);
    }


    public PageResult<AssignLocQuery> getAsnSkuPageByOwner(AsnHeaderQcPageCondition condition) {
        if (QcInspectedEnum.BEFORERECEIVING.getCode().equals(condition.getQcInspection())) {
            SkuPageCondition skuPageCondition = new SkuPageCondition();
            skuPageCondition.setAsnNo(condition.getAsnNo());
            skuPageCondition.setPageNumber(condition.getPageNumber());
            skuPageCondition.setPageSize(condition.getPageSize());
            skuPageCondition.setSkuCode(condition.getSkuCode());
            skuPageCondition.setSkuName(condition.getSkuName());
            skuPageCondition.setQcInspection(condition.getQcInspection());
            PageResult<SkuQuery> pageResult = skuService.pageByIds(skuPageCondition);
            PageResult<AssignLocQuery> convertRest = new PageResult<>();
            convertRest.setCount(pageResult.getCount());
            convertRest.setLastPage(pageResult.isLastPage());
            List<AssignLocQuery> convert = new ArrayList<>();
            if (CollUtil.isNotEmpty(pageResult.getRows())) {
                pageResult.getRows().forEach(item -> {
                    AssignLocQuery assignLocQuery = new AssignLocQuery();
                    BeanUtil.copyProperties(item, assignLocQuery);
                    assignLocQuery.setQtyAvailableNumber(item.getQtyRcvEa());
                    convert.add(assignLocQuery);
                });
                convertRest.setRows(convert);
            } else {
                convertRest.setRows(convert);
            }
            return convertRest;
        } else {
            LocListPageCondition pageCondition = new LocListPageCondition();
            List<Sku> skus = new ArrayList<>();
            if (StringUtils.isNotEmpty(condition.getAsnNo())) {
                List<AsnReceive> receiveList = asnReceiveDao.getSkuByAsnNo(condition);
                List<String> skuCodes = receiveList.stream().map(AsnReceive::getSkuCode).distinct().toList();
                if (CollUtil.isNotEmpty(skuCodes)) {
                    skus = skuService.find(new ConditionRule().andIn(Sku::getSkuCode, skuCodes));
                }
                List<String> lotNumList = receiveList.stream().map(AsnReceive::getLotNum).distinct().toList();
                pageCondition.setLotNumList(lotNumList);
            } else {
                skus = skuService.find(new ConditionRule().andEqual(Sku::getOwnerId, condition.getOwnerId()));
            }
            List<Long> list = skus.stream().map(Sku::getId).toList();
            if (CollUtil.isEmpty(list)) {
                return new PageResult<>(0,true, new ArrayList<>());
            }

            if (StringUtils.isNotEmpty(condition.getSkuCode())) {
                pageCondition.setLotSearchType(LotSearchTypeEnum.SKU_CODE);
                pageCondition.setKeyword(condition.getSkuCode());
            } else {
                pageCondition.setLotSearchType(LotSearchTypeEnum.SKU_NAME);
                pageCondition.setKeyword(condition.getSkuName());

            }
            pageCondition.setPageNumber(condition.getPageNumber());
            pageCondition.setPageSize(condition.getPageSize());
            pageCondition.setSkuIds(list);
            pageCondition.setWarehouseId(condition.getWarehouseId());
            if (condition.getQcInspection().equals(QcInspectedEnum.RECEIVING.getCode())) {
                pageCondition.setLocCode("STAGE");
                pageCondition.setTransactionType(TransactionType.TRAN_RCV);
            } else if (condition.getQcInspection().equals(QcInspectedEnum.AFTERRECEIVING.getCode())) {
                List<String> hideLocList = new ArrayList<>();
                hideLocList.add("STAGE");
                hideLocList.add("SORTATION");
                pageCondition.setHideLocList(hideLocList.toArray(String[]::new));
                pageCondition.setTransactionType(TransactionType.TRAN_PA);
            }
            PageResult<LocListPageQuery> result = stockService.exec(pageCondition);
            PageResult<AssignLocQuery> convertRest = new PageResult<>();
            convertRest.setCount(result.getCount());
            convertRest.setLastPage(result.isLastPage());
            List<AssignLocQuery> convert = new ArrayList<>();
            if (CollUtil.isNotEmpty(result.getRows())) {
                result.getRows().forEach(item -> {
                    AssignLocQuery assignLocQuery = new AssignLocQuery();
                    BeanUtil.copyProperties(item, assignLocQuery);
                    LotAttribute attribute = new LotAttribute();
                    BeanUtil.copyProperties(item, attribute);
                    List<LotDetailItem> lotDetailItems = lotUtil.getLotDetailItemsByLotId(item.getLotId());
                    String lotAtt = lotUtil.lotAttributeToStr(lotDetailItems,attribute);
                    assignLocQuery.setLotAtt(lotAtt);
                    assignLocQuery.setLocName(item.getLocNum());
                    if (item.getInvAvailableNum() != null && item.getInvAvailableNum() > 0) {
                        assignLocQuery.setQtyAvailableNumber(Convert.toBigDecimal(item.getInvAvailableNum()));
                    } else {
                        assignLocQuery.setQtyAvailableNumber(BigDecimal.ZERO);
                    }
                    assignLocQuery.setPalletNo(item.getPalletNum());
                    assignLocQuery.setWareHouseId(item.getWarehouseId());
                    assignLocQuery.setWareHouseName(item.getWarehouseName());
                    convert.add(assignLocQuery);
                });
                convertRest.setRows(convert);
            } else {
                convertRest.setRows(convert);
            }
            return convertRest;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateQualifiedNum(CsQcHeaderConfirmItem item) {
        csQcDetailService.updateQualifiedNum(item);
        CsQcHeader csQcHeader = dao.findById(item.getId());
        CsQcDetail csQcDetail = csQcDetailService.findFirst(new ConditionRule().andEqual(CsQcDetail::getHeaderId, item.getId()));
        List<String> snNoList = new ArrayList<>();
        if (Objects.nonNull(item.getQcSn())){
            snNoList.addAll(MapObjectUtil.getMapToListValue(item.getQcSn()));
            csQcDetail.setQcSn(MapObjectUtil.mapToObject(item.getQcSn()));
        }
        if (Objects.nonNull(item.getUnQcSn())){
            snNoList.addAll(MapObjectUtil.getMapToListValue(item.getUnQcSn()));
            csQcDetail.setUnQcSn(MapObjectUtil.mapToObject(item.getUnQcSn()));
        }
        // 更新sn表的质检状态
        if (CollectionUtils.isNotEmpty(snNoList)){
            asnDetailSnDao.updateStatusBySn(new QcSnDetailCondition().setQcStatus(YesNoEnum.NO.getCode()).setSnNos(snNoList));
        }
        if (!csQcHeader.getQcInspection().equals(QcInspectedEnum.BEFORERECEIVING.getCode())) {
            String transactionType = TransactionType.TRAN_QC_CONF_NF;
            if (csQcHeader.getQcType().equals(QcTypeEnum.OUTBOUNDINSPECTED.getCode())) {
                transactionType = TransactionType.TRAN_QC_OUT_CONF;
            }
            stockService.exec(new InvLotLocQtyBO()
                    .setLocId(csQcDetail.getLocId())
                    .setOwnerId(csQcDetail.getOwnerId())
                    .setSkuId(csQcDetail.getSkuId())
                    .setWarehouseId(csQcDetail.getWarehouseId())
                    .setLotNum(csQcDetail.getLotNum())
                    .setOrderNo(csQcHeader.getQualityInspectionOrderNo())
                    .setPalletNum(csQcDetail.getPalletNo())
                    .setTransactionType(transactionType)
                    .setUpdateNum(BigDecimal.valueOf(csQcDetail.getPlannedQualityInspection())));
        }
        csQcDetailService.saveOrUpdate(csQcDetail);
    }

    public PageResult<ReturnWarehouseDetailItem> returnWarehouseDetail(CsQcHeaderPageCondition condition) {
        PageResult<ReturnWarehouseDetailItem> pageResult = csQcReturnDetailService.returnWarehouseDetailPage(condition);
        pageResult.getRows().forEach(e->{
            processField(e.getSnStr(), e::setSnMap);
        });
        return pageResult;
    }

    private void processField(String fieldValue, Consumer<Map<String, List<String>>> setter) {
        Optional.ofNullable(fieldValue)
                .filter(StringUtils::isNotBlank)
                .map(MapObjectUtil::objectToMap)
                .ifPresent(setter);
    }

    /**
     * 根据质检单号查询质检单信息-单据预览数据查询
     * @param qualityInspectionOrderNo
     */
    public QCNPreviewQuery getQCNPreviewQueryByQualityInspectionOrderNo(String qualityInspectionOrderNo) {
        QCNPreviewQuery result = new QCNPreviewQuery();
        CsQcHeaderQuery csQcHeaderQuery = dao.getCsQcHeaderQueryByQualityInspectionOrderNo(qualityInspectionOrderNo);

        if (EmptyUtil.isNotEmpty(csQcHeaderQuery)) {
            Long id = csQcHeaderQuery.getId();
            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andEqual("headerId", id);
            List<CsQcDetail> detailList = csQcDetailService.find(conditionRule);

            conditionRule = new ConditionRule();
            conditionRule.andEqual("headerId", id);
            Map<Long, List<CsQcReturnDetail>> detailMap = new HashMap<>();
            List<CsQcReturnDetail> returnDetails = csQcReturnDetailService.find(conditionRule);
            if (CollectionUtils.isNotEmpty(returnDetails)) {
                detailMap = returnDetails.stream().collect(Collectors.groupingBy(CsQcReturnDetail::getHeaderId));
            }
            if (CollUtil.isNotEmpty(detailList)) {
                List<CsQcDetail> csRuleQcDetailList = detailList.stream().filter(detail -> detail.getHeaderId().equals(csQcHeaderQuery.getId())).toList();
                List<CsQcDetailQuery> detailQueryList = BeanUtil.copyToList(csRuleQcDetailList, CsQcDetailQuery.class);

                BigDecimal actualQualityInspection = detailQueryList.getFirst().getActualQualityInspection() == null ? BigDecimal.ZERO : detailQueryList.getFirst().getActualQualityInspection();
                BigDecimal returnWarehouseNum = detailQueryList.getFirst().getReturnWarehouseNum() == null ? BigDecimal.ZERO  : detailQueryList.getFirst().getReturnWarehouseNum();

                if (QcStatusEnum.NOTINSPECTED.getCode().equals(csQcHeaderQuery.getQcStatus())) {
                    csQcHeaderQuery.setEdit(true);
                }
                if (QcTypeEnum.OUTBOUNDINSPECTED.getCode().equals(csQcHeaderQuery.getQcType()) && actualQualityInspection.compareTo(returnWarehouseNum) > 0) {
                    csQcHeaderQuery.setReturnWarehouse(true);
                }
                List<CsQcReturnDetail> csQcReturnDetailList = detailMap.get(csQcHeaderQuery.getId());
                if (CollectionUtils.isNotEmpty(csQcReturnDetailList)) {
                    csQcHeaderQuery.setReturnWarehouseDetail(true);
                }
                csQcHeaderQuery.setDetailQueryList(detailQueryList);
            }
        }
        BeanUtil.copyProperties(csQcHeaderQuery, result);
        result.setDetailQueryList(new ArrayList<>());
        if(EmptyUtil.isNotEmpty(csQcHeaderQuery.getDetailQueryList())){
            CsQcDetailQuery csQcDetailQuery = csQcHeaderQuery.getDetailQueryList().getFirst();
            result.setWarehouseName(csQcDetailQuery.getWarehouseName());
            result.setOwnerName(csQcDetailQuery.getOwnerName());
            result.setSkuName(csQcDetailQuery.getSkuName());
            result.setDetailQueryList(csQcHeaderQuery.getDetailQueryList());
        }
        return result;
    }
}
