package com.chinaservices.wms.module.consumables.product.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.auth.module.user.domain.UserForOuterCondition;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.auth.module.user.feign.RemoteUserService;
import com.chinaservices.core.enums.StatusEnum;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constant.StatusConstant;
import com.chinaservices.wms.common.exception.ConsumablesExceptionEnum;
import com.chinaservices.wms.common.util.ConsumablesGeneratorUtil;
import com.chinaservices.wms.module.consumables.product.ConsumablesProductEnableItem;
import com.chinaservices.wms.module.consumables.product.ConsumablesProductItem;
import com.chinaservices.wms.module.consumables.product.ConsumablesProductPageCondition;
import com.chinaservices.wms.module.consumables.product.ConsumablesProductQuery;
import com.chinaservices.wms.module.consumables.product.dao.ConsumablesProductDao;
import com.chinaservices.wms.module.consumables.product.model.ConsumablesProduct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zhou
 * @version 1.0
 * @description 耗材管理service
 * @date 2025/5/14 13:55
 **/
@Service
@Slf4j
public class ConsumablesProductService extends ModuleBaseServiceSupport<ConsumablesProductDao, ConsumablesProduct, Long> {
    @Autowired
    private ConsumablesGeneratorUtil consumablesGeneratorUtil;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private NumberGenerator numberGenerator;
    /**
     * 分页查询耗材产品
     *
     * @param condition
     * @return PageResult<CarrierQuery>
     */
    public PageResult<ConsumablesProductQuery> page(ConsumablesProductPageCondition condition) {
        PageResult<ConsumablesProductQuery> pageResult = dao.page(condition);
        List<ConsumablesProductQuery> rows = pageResult.getRows();
        if (CollectionUtil.isNotEmpty(rows)) {
            Long[] longs = rows.stream().map(ConsumablesProductQuery::getCreator).toList().toArray(Long[]::new);
            UserForOuterCondition userForOuterCondition = new UserForOuterCondition();
            userForOuterCondition.setIds(longs);
            ResponseData<List<UserForOuterQuery>> responseData = remoteUserService.getUserList(userForOuterCondition);
            Map<Long, UserForOuterQuery> userNameByIds = Optional.ofNullable(responseData.getData()).orElse(new ArrayList<>())
                    .stream().collect(Collectors.toMap(UserForOuterQuery::getId, e -> e));
            // 设置按钮权限和用户名称
            setButtonPermissionsAndUserNames(rows,userNameByIds);
        }
        return pageResult;
    }

    /**
     * 根据ID查询耗材产品
     *
     * @param id
     * @return ConsumablesProduct
     */
    public ConsumablesProductQuery getById(long id) {
        if (ObjUtil.isNull(id)) {
            throw new ServiceException(GlobalExceptionEnum.ID_NOT_EMPTY);
        }
        return dao.selectConsumablesProductById(id);
    }

    /**
     * 新增或编辑耗材产品
     *
     * @param item
     * @return boolean
     */
    public boolean saveOrEdit(ConsumablesProductItem item) {
     //  校验数据
        checkIsExist(item);

        if (ObjUtil.isEmpty(item.getId()) && ObjUtil.isEmpty(item.getStatus())){
            item.setConsumableCode(numberGenerator.nextValue(IdRuleConstant.CON_NO));
            item.setStatus(StatusEnum.YES.getCode());
        }
        return dao.saveOrUpdate(item);
    }
    /**
     * 检查耗材产品是否重复
     *
     * @param item
     * @return boolean
     */
    public void checkIsExist(ConsumablesProductItem item) {
        // 编辑时检查
        if (ObjUtil.isNotEmpty(item.getId())) {
            ConsumablesProduct consumablesProductById = dao.findById(item.getId());
            if (consumablesProductById == null) {
                throw new ServiceException(ConsumablesExceptionEnum.DATA_NOT_EXIST);
            }
            if (StatusEnum.YES.getCode().equals(consumablesProductById.getStatus())) {
                throw new ServiceException(ConsumablesExceptionEnum.CONSUMABLES_INVENTORY_DISABLE_NOT_EDIT);
            }
            // 如果name都没修改，直接返回
            if (ObjUtil.equal(consumablesProductById.getConsumableName(), item.getConsumableName())) {
                return;
            }
        }

        // 检查耗材名称(name)是否重复
        ConditionRule nameCondition = new ConditionRule();
        nameCondition.andEqual(ConsumablesProduct::getConsumableName, item.getConsumableName());
        nameCondition.andEqual(ConsumablesProduct::getWarehouseId, item.getWarehouseId());
        if (ObjUtil.isNotEmpty(item.getId())) {
            nameCondition.andNotEqual(ConsumablesProduct::getId, item.getId());
        }
        ConsumablesProduct existByName = dao.findFirst(nameCondition);
        if (ObjUtil.isNotNull(existByName)) {
            throw new ServiceException(ConsumablesExceptionEnum.NAME_ALREADY_EXIST);
        }
    }
    /**
     * 根据IDS批量删除承运商
     *
     * @param ids
     * @return boolean
     */
    public boolean deleteByIds(Object[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }

        Long[] idArray = Arrays.stream(ids).map(Convert::toLong).toArray(Long[]::new);

        List<ConsumablesProduct> byIds = dao.findByIds(idArray);
        // 存在启用状态则不允许删除
        byIds.stream().forEach(i -> {
            if (StatusEnum.YES.getCode().equals(i.getStatus())) {
                throw new ServiceException(ConsumablesExceptionEnum.CONSUMABLES_INVENTORY_DISABLE_DELETE);
            }
        });
        return dao.delete(idArray) > 0;
    }
    /**
     * 批量启用禁用
     */
    public Boolean enableByIds(ConsumablesProductEnableItem item) {
        Long[] idArray = Arrays.stream(item.getIds()).map(Convert::toLong).toArray(Long[]::new);
        List<ConsumablesProduct> byIds = dao.findByIds(idArray);

        AtomicBoolean flag = new AtomicBoolean(false);
        ArrayList<String> strings = new ArrayList<>();
        //启用
        if (StrUtil.equals(item.getType(), StatusEnum.YES.getCode())){

            byIds.forEach(e->{
                //如果已经启用则不允许启用
                if (StatusEnum.YES.getCode().equals(e.getStatus())){
                    flag.set(true);
                    strings.add(e.getConsumableName());
                }
                e.setStatus(StatusEnum.YES.getCode());
            });
            if (flag.get()){
                throw new ServiceException(ConsumablesExceptionEnum.CONSUMABLES_INVENTORY_ENABLE,strings);
            }
            return dao.batchUpdate(byIds)>0;

            //禁用
        }else if (StrUtil.equals(item.getType(), StatusEnum.NO.getCode())){
            byIds.forEach(e->{
                //如果已经禁用则不允许禁用
                if (StatusEnum.NO.getCode().equals(e.getStatus())){
                    flag.set(true);
                    strings.add(e.getConsumableName());
                }
                e.setStatus(StatusEnum.NO.getCode());
            });
            if (flag.get()){
                throw new ServiceException(ConsumablesExceptionEnum.CONSUMABLES_INVENTORY_ENABLE,strings);
            }
            return dao.batchUpdate(byIds)>0;

        }
        return false;
    }
    /**
     * 修改物料类型
     */
    public Boolean updateType(ConsumablesProductEnableItem item) {
        if (ArrayUtil.isEmpty(item.getIds())) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        Long[] idArray = Arrays.stream(item.getIds()).map(Convert::toLong).toArray(Long[]::new);
        List<ConsumablesProduct> byIds = dao.findByIds(idArray);
        AtomicBoolean flag = new AtomicBoolean(false);
        ArrayList<String> strings = new ArrayList<>();
        byIds.forEach(e->{
            //如果已经启用则不允许启用
            if (StatusEnum.YES.getCode().equals(e.getStatus())){
                flag.set(true);
                strings.add(e.getConsumableName());
            }
            e.setStatus(StatusEnum.YES.getCode());
        });
        if (flag.get()){
            throw new ServiceException(ConsumablesExceptionEnum.CONSUMABLES_INVENTORY_ENABLE,strings);
        }

        byIds.forEach(e->e.setMaterialType(item.getType()));
        return dao.batchUpdate(byIds)>0;
    }
    /**
     * 修改物料大小
     */
    public Boolean updateSize(ConsumablesProductEnableItem item) {
        if (ArrayUtil.isEmpty(item.getIds())) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        Long[] idArray = Arrays.stream(item.getIds()).map(Convert::toLong).toArray(Long[]::new);
        List<ConsumablesProduct> byIds = dao.findByIds(idArray);
        AtomicBoolean flag = new AtomicBoolean(false);
        ArrayList<String> strings = new ArrayList<>();
        byIds.forEach(e->{
            //如果已经启用则不允许修改
            if (StatusEnum.YES.getCode().equals(e.getStatus())){
                flag.set(true);
                strings.add(e.getConsumableName());
            }
            e.setStatus(StatusEnum.YES.getCode());
        });
        if (flag.get()){
            throw new ServiceException(ConsumablesExceptionEnum.CONSUMABLES_INVENTORY_ENABLE,strings);
        }
        byIds.forEach(e->e.setMaterialSize(item.getType()));
        return dao.batchUpdate(byIds)>0;
    }

    public ResponseData<PageResult<ConsumablesProductQuery>> queryConsumablesProductList(ConsumablesProductPageCondition condition) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(ConsumablesProduct::getStatus, StatusEnum.YES.getCode());
        conditionRule.andLike(ConsumablesProduct::getConsumableName, condition.getConsumableName());
        conditionRule.andLike(ConsumablesProduct::getConsumableCode, condition.getConsumableCode());
        PageResult<ConsumablesProductQuery> pageResult = dao.page(condition);
        return ResponseData.success(pageResult);
    }

    /**
     * 设置按钮权限和用户名称
     */
    private void setButtonPermissionsAndUserNames(List<ConsumablesProductQuery> rows, Map<Long, UserForOuterQuery> userNameByIds) {
        rows.forEach(row -> {
            if (StatusConstant.DISABLE.equals(row.getStatus())) {
                row.setDeleteBtn(Boolean.TRUE);
                row.setEditBtn(Boolean.TRUE);
            }
            if (!userNameByIds.isEmpty() && userNameByIds.containsKey(row.getCreator())) {
                row.setCreatorName(userNameByIds.get(row.getCreator()).getRealName());
            }
        });
    }
}
