package com.chinaservices.wms.module.asn.pa.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.auth.module.dict.domain.DictDataCondition;
import com.chinaservices.auth.module.dict.domain.DictDataQuery;
import com.chinaservices.auth.module.dict.feign.RemoteDictDataService;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.core.redis.lock.RedisLock;
import com.chinaservices.edi.module.asn.domain.CrzPaTask;
import com.chinaservices.edi.module.asn.domain.CrzPaTaskEntity;
import com.chinaservices.edi.module.asn.domain.PaTaskUpdateIncInvItem;
import com.chinaservices.edi.module.asn.feign.RemoteAsnStatusPushToCrzFegin;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseModel;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.exception.BusinessException;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ErrorResponseData;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.*;
import com.chinaservices.wms.common.constants.DictDataTypeConstants;
import com.chinaservices.wms.common.enums.DocumentTypeEnum;
import com.chinaservices.wms.common.enums.PaTaskDeleteTypeEnum;
import com.chinaservices.wms.common.enums.ReceiveStatusEnum;
import com.chinaservices.wms.common.enums.asn.AsnStatusEnum;
import com.chinaservices.wms.common.enums.asn.PaSourceEnum;
import com.chinaservices.wms.common.enums.asn.PaStatusEnum;
import com.chinaservices.wms.common.enums.stock.TraceBackBusinessTypeEnum;
import com.chinaservices.wms.common.exception.AsnExceptionEnum;
import com.chinaservices.wms.module.archive.archives.service.LogisticsFileAutoGenerateService;
import com.chinaservices.wms.module.asn.asn.model.AsnDetail;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.asn.service.AsnDetailService;
import com.chinaservices.wms.module.asn.asn.service.AsnDetailSnService;
import com.chinaservices.wms.module.asn.asn.service.AsnHeaderService;
import com.chinaservices.wms.module.asn.pa.dao.CsPaTaskAssociationDao;
import com.chinaservices.wms.module.asn.pa.dao.CsPaTaskDao;
import com.chinaservices.wms.module.asn.pa.domain.*;
import com.chinaservices.wms.module.asn.pa.model.CsPaTask;
import com.chinaservices.wms.module.asn.pa.model.CsPaTaskAssociation;
import com.chinaservices.wms.module.asn.pa.model.CsPaTaskDetail;
import com.chinaservices.wms.module.asn.receive.dao.AsnReceiveDao;
import com.chinaservices.wms.module.asn.receive.domain.*;
import com.chinaservices.wms.module.asn.receive.model.AsnReceive;
import com.chinaservices.wms.module.asn.receive.service.AsnReceiveService;
import com.chinaservices.wms.module.basic.cspackage.model.Package;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageService;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.basic.pallet.model.Pallet;
import com.chinaservices.wms.module.basic.pallet.service.PalletService;
import com.chinaservices.wms.module.basic.sku.domain.SkuDetailQuery;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.common.InboundCommonService;
import com.chinaservices.wms.module.folder.logistics.domain.LogisticsFolderDetailLinkCondition;
import com.chinaservices.wms.module.so.pack.service.PackHeaderService;
import com.chinaservices.wms.module.so.so.dao.SoAllocationDao;
import com.chinaservices.wms.module.rule.common.RulePaCommonService;
import com.chinaservices.wms.module.so.so.dao.SoAllocationPalletDao;
import com.chinaservices.wms.module.so.so.model.SoAllocation;
import com.chinaservices.wms.module.so.so.model.SoAllocationPallet;
import com.chinaservices.wms.module.so.so.model.SoHeader;
import com.chinaservices.wms.module.so.so.service.SoHeaderService;
import com.chinaservices.wms.module.stock.back.model.TraceBackRecordItem;
import com.chinaservices.wms.module.stock.back.service.TraceBackRecordService;
import com.chinaservices.wms.module.so.wv.model.WvDetail;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.mutli.model.InvLotAtt;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLocQty;
import com.chinaservices.wms.module.stock.mutli.service.InvLotAttService;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocService;
import com.chinaservices.wms.module.stock.mutli.service.UpdateInventoryQtyService;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import com.google.common.collect.Lists;
import jakarta.validation.Valid;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description: 上架任务 Service
 */
@Service
public class CsPaTaskService extends ModuleBaseServiceSupport<CsPaTaskDao, CsPaTask, Long> {

    public static String DEFAULT_BOX_CODE = "无箱码";

    @Autowired
    private AsnReceiveDao asnReceiveDao;

    @Autowired
    private CsPaTaskDao csPaTaskDao;

    @Autowired
    private InboundCommonService inboundCommonService;

    @Autowired
    private WarehouseLocService warehouseLocService;

    @Autowired
    private InvLotAttService invLotAttService;

    @Autowired
    private NumberGenerator numberGenerator;

    @Autowired
    private PalletService palletService;

    @Autowired
    private PackageUnitService packageUnitService;

    @Autowired
    private UpdateInventoryQtyService updateInventoryQtyService;
    @Autowired
    @Lazy
    private AsnReceiveService asnReceiveService;
    @Autowired
    private InvLotLocService invLotLocService;
    @Autowired
    private SkuService skuService;
    @Autowired
    private PackageService packageService;

    @Lazy
    @Autowired
    private AsnHeaderService asnHeaderService;
    @Autowired
    private OwnerService ownerService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private CsPaTaskDetailService csPaTaskDetailService;
    @Autowired
    private StockService stockService;
    @Autowired
    private SoHeaderService soHeaderService;
    @Autowired
    private AsnDetailSnService asnDetailSnService;

    @Autowired
    private AsnDetailService asnDetailService;

    @Autowired
    private RulePaCommonService rulePaCommonService;

    @Autowired
    private RemoteAsnStatusPushToCrzFegin remoteAsnStatusPushToCrzFegin;
    @Autowired
    private CsPaTaskAssociationDao csPaTaskAssociationDao;

    @Autowired
    private TraceBackRecordService traceBackRecordService;

    @Autowired
    private TaskExecutor taskExecutor;
    @Autowired
    private LogisticsFileAutoGenerateService logisticsFileAutoGenerateService;
    @Autowired
    private RemoteDictDataService remoteDictDataService;
    @Autowired
    private PackHeaderService packHeaderService;
    @Autowired
    private SoAllocationPalletDao soAllocationPalletDao;
    @Autowired
    private SoAllocationDao soAllocationDao;

    /**
     * 上架任务分页查询
     * @param condition
     * @return
     */
        public PageResult<PaTaskQuery> page(PaTaskPageCondition condition){
        //获取上架任务列表
        PaTaskPageCondition conditions = this.setCommonCondition(condition);
        PageResult<PaTaskQuery> page = new PageResult<>();
        //时间查询
        if (EmptyUtil.isNotEmpty(conditions.getOrderTimeFrom())) {
            conditions.setOrderTimeFrom(condition.getOrderTimeFrom() + " 00:00:00");
        }
        if (EmptyUtil.isNotEmpty(conditions.getOrderTimeTo())) {
            conditions.setOrderTimeTo(condition.getOrderTimeTo() + " 23:59:59");
        }
        Set<String> ownerIds = new HashSet<>();
        if (EmptyUtil.isNotEmpty(conditions.getClientId())){
            ownerIds.add(conditions.getClientId());
            conditions.setOwnerIds(ownerIds);
        }
        if (EmptyUtil.isNotEmpty(conditions.getOwnerId())){
            ownerIds.add(conditions.getOwnerId());
            conditions.setOwnerIds(ownerIds);
        }
//        if(PaSourceEnum.ASN.getCode().equals(conditions.getPaSource())){
            page = dao.inPage(conditions);
//        }else if(PaSourceEnum.SO.getCode().equals(conditions.getPaSource())){
//            page = dao.outPage(conditions);
//        }

        List<PaTaskQuery> rows = page.getRows();
        if (CollUtil.isNotEmpty(rows)){
            List<String> list = rows.stream().map(PaTaskQuery::getPaNo).toList();
            List<CsPaTaskAssociation> associations = csPaTaskAssociationDao.find(new ConditionRule().andIn(CsPaTaskAssociation::getPaNo, list));
            if (CollUtil.isNotEmpty(associations)){
                Map<String, List<CsPaTaskAssociation>> associationMap = associations.stream().collect(Collectors.groupingBy(CsPaTaskAssociation::getPaNo));
                for (PaTaskQuery row : rows) {
                    List<CsPaTaskAssociation> association = associationMap.get(row.getPaNo());
                    if (CollUtil.isNotEmpty(association)){
                        row.setFmPalletNoList(association.stream().map(m -> new PalletListByPaNoQuery()
                                .setPickingTaskNo(m.getPickingTaskNo())
                                .setPaNo(m.getPaNo())
                                .setPalletNum(m.getToPalletNum())
                                .setPickingEa(m.getPickingEa())).toList());
                        row.setFmPalletNo(row.getFmPalletNoList().stream().map(PalletListByPaNoQuery::getPalletNum).collect(Collectors.joining(",")));
                    }
                }
            }
        }
        Map<String, List<CsPaTaskDetail>> detailMap = new HashMap<>();
        if (EmptyUtil.isNotEmpty(rows)) {
            List<String> list = rows.stream().map(PaTaskQuery::getPaNo).toList();
            List<CsPaTaskDetail> detailList = csPaTaskDetailService.find(new ConditionRule().andIn(CsPaTaskDetail::getPaNo, list));
            if (EmptyUtil.isNotEmpty(detailList)) {
                detailMap = detailList.stream().collect(Collectors.groupingBy(CsPaTaskDetail::getPaNo));
            }
        }
        for (PaTaskQuery row : rows) {
            List<CsPaTaskDetail> detailList = detailMap.get(row.getPaNo());
            if (EmptyUtil.isNotEmpty(detailList)) {
                row.setTagNoList(detailList.stream().map(CsPaTaskDetail::getTagNo).toList());
            }

            BigDecimal qtyPlanEa = row.getQtyPlanEa();
            BigDecimal qtyPaEa = row.getQtyPaEa();
            if (ObjectUtil.isEmpty(qtyPaEa)){
                qtyPaEa = BigDecimal.ZERO;
            }
            if (qtyPlanEa.compareTo(qtyPaEa) == 0){
                row.setConfirmBtn(false);
            }else{
                if (EmptyUtil.isEmpty(row.getPaPersonName())){
                    row.setAllocPaPersonBtn(Boolean.TRUE);
                }else{
                    row.setAdjustPaPersonBtn(Boolean.TRUE);
                }
            }
            if (qtyPaEa.compareTo(BigDecimal.ZERO) != 0){
                row.setCancelBtn(false);
            }
            if (qtyPaEa.compareTo(BigDecimal.ZERO) == 0){
                row.setDetailBtn(false);
            }
            if (PaSourceEnum.SO.getCode().equals(row.getPaSource())){
                row.setCancelBtn(Boolean.FALSE);
                if (row.isConfirmBtn()){
                    if (EmptyUtil.isEmpty(row.getPaPersonName())){
                        row.setAllocPaPersonBtn(Boolean.TRUE);
                    }else{
                        row.setAdjustPaPersonBtn(Boolean.TRUE);
                    }
                }
            }
        }
        return page;
    }

    /**
     * 批量上架确认
     * <AUTHOR>
     * @Date 14:03 2025/1/10
     * @Param [paTaskBatchConfirmItem]
     * @return com.chinaservices.sdk.support.result.ResponseData
     **/
    @Transactional(rollbackFor = Exception.class)
    public void batchConfirm(PaTaskBatchConfirmItem paTaskBatchConfirmItem){
        //1.获取数据
        //1.1获取上架数据集合
        List<PaTaskConfirmItem> paTaskConfirmItemList = paTaskBatchConfirmItem.getPaTaskConfirmItemList();
        List<Long> idList = paTaskConfirmItemList.stream().map(PaTaskConfirmItem::getId).collect(Collectors.toList());
        List<CsPaTask> csPaTaskList = this.findByIds(ArrayUtil.toArray(idList, Long.class));
        CsPaTask first = csPaTaskList.getFirst();
        Map<Long, CsPaTask> csPaTaskMap = csPaTaskList.stream().collect(Collectors.toMap(CsPaTask::getId, Function.identity()));
        //1.2获取原上架明细数据
        Set<String> paNoSet = csPaTaskList.stream().map(CsPaTask::getPaNo).collect(Collectors.toSet());
        List<CsPaTaskDetail> csPaTaskDetailList = csPaTaskDetailService.findByPaNos(paNoSet);
        Map<String, BigDecimal> paNoToTotalQtyPaEaMap = csPaTaskDetailList.stream()
                .collect(Collectors.groupingBy(
                        CsPaTaskDetail::getPaNo,
                        Collectors.mapping(
                                CsPaTaskDetail::getQtyPaEa,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<String, List<AsnDetailSn>> receiveCodeToAsnDetailSnListMap = new HashMap<>();
        //1.3获取sn码数据
        if (PaSourceEnum.ASN.getCode().equals(first.getPaSource())){
            List<String> receiveNoList = csPaTaskList.stream().map(CsPaTask::getReceiveNo).collect(Collectors.toList());
            List<AsnDetailSn> asnDetailSnList = asnDetailSnService.findByReceiveNos(receiveNoList);
            receiveCodeToAsnDetailSnListMap = asnDetailSnList.stream().collect(Collectors.groupingBy(AsnDetailSn::getReceiveCode));
        }
        //2.校验数据
        this.checkConfirmData(paTaskConfirmItemList, csPaTaskMap, paNoToTotalQtyPaEaMap, receiveCodeToAsnDetailSnListMap,first.getPaSource());
        //3.构建上架任务明细更新集合
        List<CsPaTaskDetail> csPaTaskDetailInsertList = csPaTaskDetailService.buildPaTaskDetailConfirmItemList(paTaskConfirmItemList, csPaTaskMap);
        //4.构建上架任务
        List<PaTaskMainConfirmItem> paTaskMainConfirmItemList = this.buildPaTaskConfirmItemList(paTaskConfirmItemList, csPaTaskMap);
        //5.调整源库位和目标库位的批次库位库存
        this.changeInvLotLocList(csPaTaskList, paTaskConfirmItemList);
        //6.更新上架任务明细
        csPaTaskDetailService.batchInsert(csPaTaskDetailInsertList);
        //7.更新上架任务
        this.batchUpdateItemToSqlExecution(paTaskMainConfirmItemList);
        //8.更新订单完结状态
        if (PaSourceEnum.ASN.getCode().equals(first.getPaSource())){
            this.updateAsnHeaderStatus(csPaTaskList, paTaskConfirmItemList, csPaTaskMap, csPaTaskDetailList);
            //9.绑定上架对应的sn码
            this.bindPataskSnRelation(csPaTaskDetailInsertList, paTaskConfirmItemList);
        }

        List<CsPaTask> list = csPaTaskList.stream().filter(f -> PaSourceEnum.SO.getCode().equals(f.getPaSource())).toList();
        if (CollUtil.isNotEmpty(list)){
            //执行上架确认操作后，校验是否存在已打包的打包任务，存在自动取消打包任务；若打包任务用户已手动取消则无须再取消。
            List<CsPaTaskAssociation> associationList = csPaTaskAssociationDao.find(new ConditionRule().andIn(CsPaTaskAssociation::getPaNo, list.stream().map(CsPaTask::getPaNo).toList())
                    .andEqual(CsPaTaskAssociation::getIsPack,YesNoEnum.YES.getCode())
                    .andIsNotNull(CsPaTaskAssociation::getPackNo));
            if (CollUtil.isNotEmpty(associationList)){
                Map<String, List<CsPaTaskAssociation>> packNoGroupMap = associationList.stream().collect(Collectors.groupingBy(CsPaTaskAssociation::getPackNo));
                Set<String> keySet = packNoGroupMap.keySet();
                //根据打包任务查询所有上架任务
                List<PaTaskAssociationQuery> byPackNoListdao = dao.searchTaskByPackNoList(keySet);
                boolean allSuccessPa = byPackNoListdao.stream().allMatch(f -> PaStatusEnum.SHELVESED.getCode().equals(f.getStatus()));
                if (allSuccessPa){
                    //全部完成上架，取消打包任务
                    packHeaderService.deletePack(keySet);
                }
            }
        }
    }



    public void bindPataskSnRelation(List<CsPaTaskDetail> csPaTaskDetailInsertList, List<PaTaskConfirmItem> paTaskConfirmItemList){
        Map<Long, List<String>> paTaskIdToSoNoListMap = paTaskConfirmItemList.stream().filter(item -> CollUtil.isNotEmpty(item.getSnNoList())).collect(Collectors.toMap(PaTaskConfirmItem::getId, PaTaskConfirmItem::getSnNoList));
        if (ObjectUtil.isEmpty(paTaskIdToSoNoListMap)){
            return;
        }
        for (CsPaTaskDetail csPaTaskDetail : csPaTaskDetailInsertList) {
            List<String> soNoList = paTaskIdToSoNoListMap.get(csPaTaskDetail.getPaId());
            if (CollUtil.isEmpty(soNoList)) {
                continue;
            }
            asnDetailSnService.updatePaTaskDetailIdBySnNos(csPaTaskDetail.getId(), soNoList);
            //记录日志
            traceBackRecordService.execute(new TraceBackRecordItem()
                    .setBusinessNo(csPaTaskDetail.getPaNo())
                            .setOwnerName(csPaTaskDetail.getOwnerName())
                            .setWarehouseName(csPaTaskDetail.getWarehouseName())
                    .setBusinessType(TraceBackBusinessTypeEnum.PUT_AWAY)
                    .setSourceNo(csPaTaskDetail.getReceiveNo())
                    .setSnNoList(soNoList));
        }
    }

    public void updateAsnHeaderStatus(List<CsPaTask> csPaTaskList, List<PaTaskConfirmItem> paTaskConfirmItemList, Map<Long, CsPaTask> csPaTaskMap, List<CsPaTaskDetail> csPaTaskDetailSourceList){
        Map<String, List<CsPaTaskDetail>> csPaTaskDetailSourceMap = csPaTaskDetailSourceList.stream().collect(Collectors.groupingBy(CsPaTaskDetail::getPaNo));
        if (CollUtil.isNotEmpty(csPaTaskDetailSourceMap)){
            for (PaTaskConfirmItem paTaskConfirmItem : paTaskConfirmItemList) {
                CsPaTask csPaTask = csPaTaskMap.get(paTaskConfirmItem.getId());
                List<CsPaTaskDetail> csPaTaskDetailSubList = csPaTaskDetailSourceMap.get(csPaTask.getPaNo());
                BigDecimal totalAmount = BigDecimal.ZERO;
                if (CollUtil.isNotEmpty(csPaTaskDetailSubList)){
                    totalAmount = csPaTaskDetailSubList.stream()
                            .map(CsPaTaskDetail::getQtyPaEa)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                totalAmount = paTaskConfirmItem.getQtyPaEa().add(totalAmount);
                if (csPaTask.getQtyPlanEa().compareTo(totalAmount) != 0){
                    return;
                }

            }
        }

        List<Long> paTaskIdSourceList = paTaskConfirmItemList.stream().map(PaTaskConfirmItem::getId).collect(Collectors.toList());

        Long orderId = csPaTaskList.get(0).getOrderId();
        AsnHeader asnHeader = asnHeaderService.findById(orderId);
        if (ObjectUtil.isEmpty(asnHeader)){
            return;
        }
        String asnNo = asnHeader.getAsnNo();
        List<AsnDetail> asnDetailList = asnDetailService.findByAsnNo(asnNo);
        if (CollUtil.isEmpty(asnDetailList)){
            return;
        }
        if (!ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode().equals(asnHeader.getReceiveStatus())){
            return;
        }

        List<CsPaTask> csPaTasks = this.findByOrderNo(asnNo);
        long count = csPaTasks.stream().filter(obj -> PaStatusEnum.SHELVESING.getCode().equals(obj.getStatus()) && !paTaskIdSourceList.contains(obj.getId())).count();
        if (count > 0){
            return;
        }

        List<AsnReceiveDetailQuery> asnReceiveDetailQueryTotalList = new ArrayList<>();
        for (AsnDetail asnDetail : asnDetailList) {
            AsnReceiveDetailCondition asnReceiveDetailCondition = new AsnReceiveDetailCondition();
            asnReceiveDetailCondition.setAsnNo(asnDetail.getAsnNo());
            asnReceiveDetailCondition.setSkuCode(asnDetail.getSkuCode());
            asnReceiveDetailCondition.setAsnDetailId(asnDetail.getId());
            List<AsnReceiveDetailQuery> asnReceiveDetailQueryList = asnReceiveService.queryListByAsnNoAndSkuCode(asnReceiveDetailCondition);
            BigDecimal totalAmount = asnReceiveDetailQueryList.stream()
                    .map(AsnReceiveDetailQuery::getQtyRcvEa)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (asnDetail.getQtyPlanEa().compareTo(totalAmount) > 0){
                return;
            }
            asnReceiveDetailQueryTotalList.addAll(asnReceiveDetailQueryList);
        }
        Set<String> receiveNoSet = asnReceiveDetailQueryTotalList.stream().map(AsnReceiveDetailQuery::getReceiveNo).collect(Collectors.toSet());
        List<CsPaTaskDetail> csPaTaskDetailList = csPaTaskDetailService.findByReceiveNos(receiveNoSet);
        Map<String, List<CsPaTaskDetail>> csPaTaskDetailMap = csPaTaskDetailList.stream().collect(Collectors.groupingBy(CsPaTaskDetail::getReceiveNo));
        boolean notUpdated = false;
        for (AsnReceiveDetailQuery asnReceiveDetailQuery : asnReceiveDetailQueryTotalList) {
            List<CsPaTaskDetail> csPaTaskDetailSubList = csPaTaskDetailMap.get(asnReceiveDetailQuery.getReceiveNo());
            if (CollUtil.isEmpty(csPaTaskDetailSubList)){
                notUpdated = true;
                continue;
            }
            BigDecimal totalAmount = csPaTaskDetailSubList.stream()
                    .map(CsPaTaskDetail::getQtyPaEa)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (asnReceiveDetailQuery.getQtyRcvEa().compareTo(totalAmount) != 0){
                return;
            }
        }
        // 存在多个商品完全收货，但是只有一个商品生成上级任务并上架确认时不需要修改订单状态
        if (notUpdated){
            return;
        }
        asnHeader.setStatus(AsnStatusEnum.ASN_CLOSE.getCode());
        asnHeaderService.updateAsnStatusByIds(new Long[] { asnHeader.getId() }, asnHeader.getStatus());
    }

    public List<CsPaTask> findByOrderNo(String asnNo){
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(CsPaTask::getOrderNo, asnNo);
        return this.find(conditionRule);
    }



    public List<PaTaskMainConfirmItem> buildPaTaskConfirmItemList(List<PaTaskConfirmItem> paTaskConfirmItemList, Map<Long, CsPaTask> csPaTaskMap){
        List<PaTaskMainConfirmItem> paTaskMainConfirmItemList = new ArrayList<>();
        for (PaTaskConfirmItem paTaskConfirmItem : paTaskConfirmItemList) {
            CsPaTask csPaTask = csPaTaskMap.get(paTaskConfirmItem.getId());
            PaTaskMainConfirmItem paTaskMainConfirmItem = new PaTaskMainConfirmItem();
            paTaskMainConfirmItem.setId(paTaskConfirmItem.getId());
            BigDecimal sourceQtyPaEa = csPaTask.getQtyPaEa();
            if (ObjectUtil.isEmpty(sourceQtyPaEa)){
                sourceQtyPaEa = BigDecimal.ZERO;
            }
            BigDecimal qtyPaEa = sourceQtyPaEa.add(paTaskConfirmItem.getQtyPaEa());
            paTaskMainConfirmItem.setQtyPaEa(qtyPaEa);
            if (csPaTask.getQtyPlanEa().compareTo(qtyPaEa) == 0){
                paTaskMainConfirmItem.setStatus(PaStatusEnum.SHELVESED.getCode());
            }else {
                paTaskMainConfirmItem.setStatus(PaStatusEnum.SHELVESING.getCode());
            }
            preSave(paTaskConfirmItem);
            paTaskMainConfirmItemList.add(paTaskMainConfirmItem);
        }
        return paTaskMainConfirmItemList;
    }



    public List<AsnReceive> getAsnReceiveList(List<PaTaskConfirmItem> paTaskConfirmItemList, Map<Long, CsPaTask> csPaTaskMap){
        List<Long> receiveIdList = new ArrayList<>();
        for (PaTaskConfirmItem paTaskConfirmItem : paTaskConfirmItemList) {
            CsPaTask csPaTask = csPaTaskMap.get(paTaskConfirmItem.getId());
            receiveIdList.add(csPaTask.getId());
        }
        return asnReceiveService.findByIds(ArrayUtil.toArray(receiveIdList, Long.class));
    }

    public Map<Long, CsPaTask> getCsPaTaskMap(List<PaTaskConfirmItem> paTaskConfirmItemList){
        List<Long> idList = paTaskConfirmItemList.stream().map(PaTaskConfirmItem::getId).collect(Collectors.toList());
        List<CsPaTask> csPaTaskList = this.findByIds(ArrayUtil.toArray(idList, Long.class));
        return csPaTaskList.stream().collect(Collectors.toMap(CsPaTask::getId, Function.identity()));

    }

    public void changeInvLotLocList(List<CsPaTask> csPaTaskList, List<PaTaskConfirmItem> paTaskConfirmItemList){
        Map<Long, PaTaskConfirmItem> paTaskConfirmItemMap = paTaskConfirmItemList.stream().collect(Collectors.toMap(PaTaskConfirmItem::getId, Function.identity()));
        for (CsPaTask csPaTask : csPaTaskList) {
            this.changeConfirmInvLotLoc(csPaTask, paTaskConfirmItemMap);
        }
    }

    @RedisLock(value = "CsPaTaskService.changeInvLotLoc", param = "#paTaskConfirmItem.toLocId")
    public void changeConfirmInvLotLoc(CsPaTask csPaTask, Map<Long, PaTaskConfirmItem> paTaskConfirmItemMap){
        PaTaskConfirmItem paTaskConfirmItem = paTaskConfirmItemMap.get(csPaTask.getId());
        Long skuId = csPaTask.getSkuId();
        Long ownerId = csPaTask.getOwnerId();
        Long warehouseId = csPaTask.getWarehouseId();
        String lotNum = csPaTask.getLotNum();
        Long fmLocId = csPaTask.getFmLocId();
        String fmPalletNo = paTaskConfirmItem.getFmPalletNo();
        BigDecimal qtyPaEa = paTaskConfirmItem.getQtyPaEa();
        Long toLocId = paTaskConfirmItem.getToLocId();
        String toPalletNo = paTaskConfirmItem.getToPalletNo();
        List<String> snNoList = paTaskConfirmItem.getSnNoList();

        if (PaSourceEnum.SO.getCode().equals(csPaTask.getPaSource())){
            //出库上架可能存在多个 源容器号
            if (CollUtil.isEmpty(paTaskConfirmItem.getFmPalletNoList())){
                throw new ServiceException(AsnExceptionEnum.PA_FM_PALLET_LIST_NOT_NULL);
            }
            List<CsPaTaskAssociation> associations = csPaTaskAssociationDao.find(new ConditionRule().andEqual(CsPaTaskAssociation::getPaNo, csPaTask.getPaNo())
                    .andIn(CsPaTaskAssociation::getToPalletNum, paTaskConfirmItem.getFmPalletNoList()));
            if (associations.size() > 1){
                //勾选多条
                BigDecimal sumEa = associations.stream().map(CsPaTaskAssociation::getPickingEa).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (qtyPaEa.compareTo(sumEa) != 0){
                    throw new ServiceException(AsnExceptionEnum.PA_FM_PALLET_SUM_NOT_EQ_PAEA);
                }
                for (CsPaTaskAssociation association : associations) {
                    stockService.exec(new InvLotLocQtyBO()
                            .setSkuId(skuId)
                            .setOwnerId(ownerId)
                            .setWarehouseId(warehouseId)
                            .setLotNum(lotNum)
                            .setLocId(fmLocId)
                            .setToLocId(toLocId)
                            .setPalletNum(association.getToPalletNum())
                            .setToPallet(toPalletNo)
                            .setUpdateNum(association.getPickingEa())
                            .setOrderNo(csPaTask.getPaNo())
                            .setSkuSn(snNoList)
                            .setTransactionType(TransactionType.TRAN_OUT_PA));
                    association.setPickingEa(BigDecimal.ZERO);
                }
            }else{
                //只勾选一条
                CsPaTaskAssociation association = associations.getFirst();
                if (qtyPaEa.compareTo(association.getPickingEa()) > 0){
                    throw new ServiceException(AsnExceptionEnum.PA_FM_PALLET_SUM_GT_EQ_PAEA);
                }
                stockService.exec(new InvLotLocQtyBO()
                        .setSkuId(skuId)
                        .setOwnerId(ownerId)
                        .setWarehouseId(warehouseId)
                        .setLotNum(lotNum)
                        .setLocId(fmLocId)
                        .setToLocId(toLocId)
                        .setPalletNum(association.getToPalletNum())
                        .setToPallet(toPalletNo)
                        .setUpdateNum(qtyPaEa)
                        .setOrderNo(csPaTask.getPaNo())
                        .setSkuSn(snNoList)
                        .setTransactionType(TransactionType.TRAN_OUT_PA));
                association.setPickingEa(association.getPickingEa().subtract(qtyPaEa));
            }
            //更新关联关系表数据
            csPaTaskAssociationDao.batchUpdate(associations);
        }else{
            //更新库位库存
            stockService.exec(new InvLotLocQtyBO()
                    .setSkuId(skuId)
                    .setOwnerId(ownerId)
                    .setWarehouseId(warehouseId)
                    .setLotNum(lotNum)
                    .setLocId(fmLocId)
                    .setToLocId(toLocId)
                    .setPalletNum(fmPalletNo)
                    .setToPallet(toPalletNo)
                    .setUpdateNum(qtyPaEa)
                    .setOrderNo(csPaTask.getPaNo())
                    .setSkuSn(snNoList)
                    .setTransactionType(TransactionType.TRAN_PA));
        }
        boolean push;
        if (PaSourceEnum.ASN.getCode().equals(csPaTask.getPaSource())){
            //入库
            AsnHeader header = asnHeaderService.findById(csPaTask.getOrderId());
            push = StrUtil.isNotEmpty(header.getLogisticNo());
        }else{
            //出库
            push = Boolean.FALSE;//StrUtil.isNotEmpty(header.getLogisticNo());
        }
        if (push){
            PaTaskUpdateIncInvItem incInvItem = new PaTaskUpdateIncInvItem();

            Sku sku = skuService.findById(skuId);
            Owner owner = ownerService.findById(ownerId);
            Warehouse warehouse = warehouseService.findById(warehouseId);
            WarehouseLoc fmLoc = warehouseLocService.findById(fmLocId);
            WarehouseLoc toLoc = warehouseLocService.findById(toLocId);

            incInvItem.setFmLocCode(fmLoc.getUpLocCode());
            incInvItem.setSkuCode(sku.getUpSkuCode());
            incInvItem.setOwnerCode(owner.getUpstreamOwnerCode());
            incInvItem.setToLocCode(toLoc.getUpLocCode());
            incInvItem.setWarehouseCode(warehouse.getUpWarehouseCode());

            CrzPaTaskEntity paTaskEntity = new CrzPaTaskEntity();
            BeanUtil.copyProperties(csPaTask,paTaskEntity);
            paTaskEntity.setCurrentPaQtyEa(qtyPaEa);
            incInvItem.setPaTaskEntity(paTaskEntity);
            CrzPaTask paTask = new CrzPaTask();
            BeanUtil.copyProperties(csPaTask,paTaskEntity);
            BeanUtil.copyProperties(csPaTask,paTaskConfirmItem);
            incInvItem.setPaTask(paTask);
            remoteAsnStatusPushToCrzFegin.updateWarehouseLocInv(incInvItem);
        }
    }

    @RedisLock(value = "CsPaTaskService.changeDetailInvLotLoc", param = "#paTaskConfirmItem.toLocId")
    public void changeDetailInvLotLoc(CsPaTaskDetail csPaTaskDetail){
        Long skuId = csPaTaskDetail.getSkuId();
        Long ownerId = csPaTaskDetail.getOwnerId();
        Long warehouseId = csPaTaskDetail.getWarehouseId();
        String lotNum = csPaTaskDetail.getLotNum();
        BigDecimal qtyPaEa = csPaTaskDetail.getQtyPaEa();
        List<AsnDetailSn> detailSns = asnDetailSnService.find(new ConditionRule().andEqual(AsnDetailSn::getPaTaskDetailId, csPaTaskDetail.getId()));
        List<String> snNo = new ArrayList<>();
        if (CollUtil.isNotEmpty(detailSns)){
            snNo = detailSns.stream().map(AsnDetailSn::getSnNo).toList();
        }
        //更新库位库存
        stockService.exec(new InvLotLocQtyBO()
                .setSkuId(skuId)
                .setOwnerId(ownerId)
                .setWarehouseId(warehouseId)
                .setLotNum(lotNum)
                .setLocId(csPaTaskDetail.getToLocId())
                .setToLocId(csPaTaskDetail.getFmLocId())
                .setPalletNum(csPaTaskDetail.getToPalletNo())
                .setToPallet(csPaTaskDetail.getFmPalletNo())
                .setOrderNo(csPaTaskDetail.getPaNo())
                .setUpdateNum(qtyPaEa)
                .setSkuSn(snNo)
                .setTransactionType(TransactionType.TRAN_CR_PA));
    }



    /**
     * 通过id集合更新状态
     * @Date 10:21 2025/1/13
     * @Param [ids, status]
     * @return void
     **/

    /**
     * 校验上架确认数据
     * <AUTHOR>
     * @Date 14:05 2025/1/10
     * @Param []
     * @return com.chinaservices.sdk.support.result.ResponseData
     **/
    public void checkConfirmData(List<PaTaskConfirmItem> paTaskConfirmItemList,
                                 Map<Long, CsPaTask> csPaTaskMap,
                                 Map<String, BigDecimal> paNoToTotalQtyPaEaMap,
                                 Map<String, List<AsnDetailSn>> receiveCodeToAsnDetailSnListMap,
                                 String paSource){
        if (PaSourceEnum.SO.getCode().equals(paSource)){
            PaTaskConfirmItem first = paTaskConfirmItemList.getFirst();
            CsPaTask csPaTask = csPaTaskMap.get(first.getId());
            List<CsPaTaskAssociation> association = csPaTaskAssociationDao.find(new ConditionRule().andEqual(CsPaTaskAssociation::getPaNo, csPaTask.getPaNo())
                    .andIn(CsPaTaskAssociation::getToPalletNum, first.getFmPalletNoList()));
            if (EmptyUtil.isNotEmpty(association)){
                BigDecimal reduce = association.stream().map(CsPaTaskAssociation::getPickingEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (first.getQtyPaEa().compareTo(reduce) > 0){
                    throw new ServiceException(AsnExceptionEnum.SO_PA_EA_GT_PICKING_EA);
                }
            }
        }
        Set<String> toLocCodeSet = paTaskConfirmItemList.stream().filter(obj -> StrUtil.isNotBlank(obj.getToLocCode())).map(PaTaskConfirmItem::getToLocCode).collect(Collectors.toSet());
        List<WarehouseLoc> warehouseLocList = warehouseLocService.findByLocCodes(toLocCodeSet);
        if (CollectionUtil.isNotEmpty(toLocCodeSet)) {
            if (CollectionUtil.isEmpty(warehouseLocList)) {
                throw new ServiceException(AsnExceptionEnum.THE_TARGET_LOCATION_IS_NOT_EXIST_WHEN_IT_IS_PUT_ON_THE_SHELF);
            } else {
                warehouseLocList = warehouseLocList.stream().filter(warehouseLoc -> ObjUtil.equals(YesNoEnum.YES.getCode(), warehouseLoc.getIsEnable())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(warehouseLocList)) {
                    throw new ServiceException(AsnExceptionEnum.THE_TARGET_LOCATION_IS_NOT_ENABLE_WHEN_IT_IS_PUT_ON_THE_SHELF);

                }
            }
        }
        //Map<String, WarehouseLoc> locCodeToWarehouseLocMap = warehouseLocList.stream().collect(Collectors.toMap(WarehouseLoc::getLocCode, Function.identity()));
        Set<String> toPalletNoSet = paTaskConfirmItemList.stream().filter(obj -> StrUtil.isNotBlank(obj.getToPalletNo())).map(PaTaskConfirmItem::getToPalletNo).collect(Collectors.toSet());
        List<Pallet> palletList = palletService.findByPalletNos(toPalletNoSet);
        if (CollectionUtil.isNotEmpty(toPalletNoSet)) {
            if (CollectionUtil.isEmpty(palletList)) {
                throw new ServiceException(AsnExceptionEnum.THE_TARGET_CONTAINER_NUMBER_IS_NOT_EXIST_WHEN_IT_IS_PUT_ON_THE_SHELF);
            } else {
                palletList = palletList.stream().filter(pallet -> ObjUtil.equals(YesNoEnum.YES.getCode(), pallet.getStatus())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(palletList)) {
                    throw new ServiceException(AsnExceptionEnum.THE_TARGET_CONTAINER_NUMBER_IS_NOT_ENABLE_WHEN_IT_IS_PUT_ON_THE_SHELF);
                }
                palletList = palletList.stream().filter(pallet -> ObjUtil.equals(YesNoEnum.NO.getCode(), pallet.getIsUsed())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(palletList)) {
                    throw new ServiceException(AsnExceptionEnum.THE_TARGET_CONTAINER_NUMBER_IS_USED_WHEN_IT_IS_PUT_ON_THE_SHELF);
                }
            }
        }
        Map<String, Pallet> palletNoToPalletMap = palletList.stream().collect(Collectors.toMap(Pallet::getPalletNo, Function.identity()));
        //1.校验状态
        for (PaTaskConfirmItem paTaskConfirmItem : paTaskConfirmItemList) {
            if (ObjectUtil.isEmpty(paTaskConfirmItem.getToLocId()) && StrUtil.isBlank(paTaskConfirmItem.getToLocCode())){
                throw new ServiceException(AsnExceptionEnum.THE_TARGET_LOCATION_IS_EMPTY_WHEN_IT_IS_PUT_ON_THE_SHELF);
            }
//            if (ObjectUtil.isNotEmpty(locCodeToWarehouseLocMap)){
//                if (StrUtil.isNotBlank(paTaskConfirmItem.getToLocCode()) && ObjectUtil.isEmpty(paTaskConfirmItem.getToLocId())){
//                    WarehouseLoc warehouseLoc = locCodeToWarehouseLocMap.get(paTaskConfirmItem.getToLocCode());
//                    if (ObjectUtil.isEmpty(warehouseLoc)){
//                        throw new ServiceException(AsnExceptionEnum.THE_TARGET_LOCATION_IS_EMPTY_WHEN_IT_IS_PUT_ON_THE_SHELF);
//                    }
//                    paTaskConfirmItem.setToLocId(warehouseLoc.getId());
//                    paTaskConfirmItem.setToLocName(warehouseLoc.getLocName());
//                }
//            }
            if (ObjectUtil.isNotEmpty(palletNoToPalletMap)){
                if (StrUtil.isNotBlank(paTaskConfirmItem.getToPalletNo()) && ObjectUtil.isEmpty(paTaskConfirmItem.getToPalletId())){
                    Pallet pallet = palletNoToPalletMap.get(paTaskConfirmItem.getToPalletNo());
                    if (ObjectUtil.isEmpty(pallet)){
                        throw new ServiceException(AsnExceptionEnum.THE_TARGET_CONTAINER_NUMBER_IS_EMPTY_WHEN_IT_IS_PUT_ON_THE_SHELF);
                    }
                    paTaskConfirmItem.setToPalletId(pallet.getId());
                    paTaskConfirmItem.setToPalletNo(pallet.getPalletNo());
                }
            }

            CsPaTask csPaTask = csPaTaskMap.get(paTaskConfirmItem.getId());
            if (ObjectUtil.isEmpty(csPaTask)){
                throw new ServiceException(AsnExceptionEnum.PA_TASK_NOT_EXIST);
            }
            if (PaStatusEnum.SHELVESED.getCode().equals(csPaTask.getStatus())){
                throw new ServiceException(AsnExceptionEnum.PA_TASK_ALREADY_PA);
            }
            BigDecimal totalQtyPaEa = paNoToTotalQtyPaEaMap.get(csPaTask.getPaNo());
            if (ObjectUtil.isEmpty(totalQtyPaEa)){
                totalQtyPaEa = BigDecimal.ZERO;
            }
            //2.校验总的实际上架数要小于计划上架数
            if (csPaTask.getQtyPlanEa().subtract(totalQtyPaEa).subtract(paTaskConfirmItem.getQtyPaEa()).compareTo(BigDecimal.ZERO) < 0){
                throw new ServiceException(AsnExceptionEnum.PA_NUM_LT_READY_PA_NUM);
            }
            if (PaSourceEnum.ASN.getCode().equals(paSource)){
                //3.校验上架的sn码是否在范围内
                List<String> snNoList = paTaskConfirmItem.getSnNoList();
                List<AsnDetailSn> asnDetailSnSubList = receiveCodeToAsnDetailSnListMap.get(csPaTask.getReceiveNo());
                if (CollUtil.isEmpty(asnDetailSnSubList)){
                    continue;
                }
                List<String> snNoSourceList = asnDetailSnSubList.stream().map(AsnDetailSn::getSnNo).collect(Collectors.toList());
                boolean b = snNoSourceList.containsAll(snNoList);
                if (!b){
                    throw new ServiceException(AsnExceptionEnum.THE_SN_CODE_MUST_HAVE_BEEN_RECEIVED);
                }
            }
        }
    }

    /**
     * 批量生成上架任务
     * <AUTHOR>
     * @Date 14:05 2025/1/10
     * @Param [paTaskBatchConfirmItem]
     * @return com.chinaservices.sdk.support.result.ResponseData
     **/
    @Transactional(rollbackFor = Exception.class)
    public void batchCreate(PaTaskBatchCreateItem paTaskBatchCreateItem){
        //1.查询数据
        //1.1查询收货明细集合
        List<AsnReceive> asnReceiveList = asnReceiveService.findByIds(ArrayUtil.toArray(paTaskBatchCreateItem.getRcvIdList(), Long.class));
        if (CollUtil.isEmpty(asnReceiveList)){
            throw new ServiceException(AsnExceptionEnum.PA_TASK_RECEIVE_EMPTY);
        }
        //2.校验数据
        this.checkCreateData(asnReceiveList);
        //3.构建上架任务
        List<CsPaTask> csPaTaskList = this.buildPaTaskList(asnReceiveList, paTaskBatchCreateItem);
        //4.更新收货明细状态
        List<Long> asnReceiveIdList = asnReceiveList.stream().map(AsnReceive::getId).collect(Collectors.toList());
        asnReceiveService.updateWhetherShelfByIds(asnReceiveIdList, YesNoEnum.YES.getCode());
        //5.批量保存上架任务
        batchInsertToSqlExecution(csPaTaskList);
        List<CsPaTask> paTaskList = csPaTaskList.stream().filter(f -> PaSourceEnum.ASN.getCode().equals(f.getPaSource())).toList();
        if (CollUtil.isNotEmpty(paTaskList)) {
            Map<String, List<CsPaTask>> listMap = paTaskList.stream().collect(Collectors.groupingBy(CsPaTask::getOrderNo));
            List<LogisticsFolderDetailLinkCondition> linkConditions = new ArrayList<>();
            listMap.forEach((key,value) -> {
                linkConditions.add(new LogisticsFolderDetailLinkCondition().setUpDocumentNo(key).setDocumentNos(value.stream().map(CsPaTask::getPaNo).toList()));
            });
            taskExecutor.execute(() -> {
                //上架单集合
                CsPaTask paTask = csPaTaskList.getFirst();
                //这⾥第三个参数，为上级的单号，这⾥需替换成⼊库单号号码
                logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                        DocumentTypeEnum.PUTAWAY,linkConditions,
                        SessionContext.getSessionUserInfo().getCompanyId(),
                        SessionContext.getSessionUserInfo().getTenancy(),paTask.getWarehouseId());
                //这⾥第三个参数，为上级的单号，这⾥需替换成⼊库单号号码
                logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                        DocumentTypeEnum.ASN_PUTAWAY,linkConditions,
                        SessionContext.getSessionUserInfo().getCompanyId(),
                        SessionContext.getSessionUserInfo().getTenancy(),paTask.getWarehouseId());
            });
        }
    }

    public List<CsPaTask> buildPaTaskList(List<AsnReceive> asnReceiveList, PaTaskBatchCreateItem paTaskBatchCreateItem){
        List<CsPaTask> pataskList = new ArrayList<>();
        if (CollUtil.isEmpty(asnReceiveList)){
            throw new ServiceException(GlobalExceptionEnum.DELETE_FAILED);
        }

        //包装映射
        Map<Long, Package> packageMap = this.getPackageMap(asnReceiveList);
        //商品映射
        Map<String, Sku> skuMap = this.getSkuMap(asnReceiveList);

        Set<String> asnCodeSet = asnReceiveList.stream().map(AsnReceive::getAsnCode).collect(Collectors.toSet());
        List<AsnHeader> asnHeaderList = asnHeaderService.findByAsnNos(asnCodeSet);
        //入库单-》货主映射
        Map<String, Long> asnOwnerMap = this.getAsnOwnerMap(asnHeaderList);
        //货主映射
        Map<Long, Owner> ownerMap = this.getOwnerMap(asnHeaderList);
        //入库单-》仓库映射
        Map<String, Long> asnWareHouseMap = this.getAsnWareHouseMap(asnHeaderList);
        //仓库映射
        Map<Long, Warehouse> wareHouseMap = this.getWareHouseMap(asnHeaderList);

        //入库单
        Set<Long> asnIdSet = asnReceiveList.stream().map(AsnReceive::getAsnId).collect(Collectors.toSet());
        List<AsnHeader> asnHeaders = asnHeaderService.findByIds(ArrayUtil.toArray(asnIdSet, Long.class));
        Map<Long, AsnHeader> asnHeaderMap = asnHeaders.stream().collect(Collectors.toMap(AsnHeader::getId, Function.identity()));

        //出库单
        Set<String> soNoSet = asnHeaders.stream().map(AsnHeader::getSoNo).collect(Collectors.toSet());
        List<SoHeader> soHeaderList = soHeaderService.findBySoNos(soNoSet);
        Map<String, SoHeader> soHeaderMap = soHeaderList.stream().collect(Collectors.toMap(SoHeader::getSoNo, Function.identity()));

        //TODO HJH 还没有设置出库单号
        //初始化上架任务集合
        for (AsnReceive asnReceive : asnReceiveList) {
            CsPaTask csPaTask = this.convertAsnReceiveToCsPaTask(asnReceive, paTaskBatchCreateItem, packageMap, skuMap, asnOwnerMap,
                    ownerMap, asnWareHouseMap, wareHouseMap, asnHeaderMap, soHeaderMap);
            preSave(csPaTask);
            pataskList.add(csPaTask);
        }

        return pataskList;
    }

    public Map<Long, Package> getPackageMap(List<AsnReceive> asnReceiveList){
        Set<Long> packageIdSet = asnReceiveList.stream().map(AsnReceive::getPackageId).collect(Collectors.toSet());
        List<Package> packageList = packageService.findByIds(ArrayUtil.toArray(packageIdSet, Long.class));
        return packageList.stream().collect(Collectors.toMap(Package::getId, Function.identity()));
    }
    public Map<String, Sku> getSkuMap(List<AsnReceive> asnReceiveList){
        List<String> skuCodeList = asnReceiveList.stream().map(AsnReceive::getSkuCode).distinct().collect(Collectors.toList());
        List<Sku> skuList = skuService.listBySkuCode(skuCodeList);
        return skuList.stream().collect(Collectors.toMap(Sku::getSkuCode, Function.identity()));
    }

    public Map<String, Long> getAsnWareHouseMap(List<AsnHeader> asnHeaderList){
        return asnHeaderList.stream().collect(Collectors.toMap(AsnHeader::getAsnNo, AsnHeader::getWarehouseId));
    }
    public Map<Long, Warehouse> getWareHouseMap(List<AsnHeader> asnHeaderList){
        Set<Long> wareHouseIdSet = asnHeaderList.stream().map(AsnHeader::getWarehouseId).collect(Collectors.toSet());
        List<Warehouse> warehouseList = warehouseService.findByIds(ArrayUtil.toArray(wareHouseIdSet, Long.class));
        return warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, Function.identity()));
    }

    public Map<String, Long> getAsnOwnerMap(List<AsnHeader> asnHeaderList){
        return asnHeaderList.stream().collect(Collectors.toMap(AsnHeader::getAsnNo, AsnHeader::getOwnerId));
    }

    public Map<Long, Owner> getOwnerMap(List<AsnHeader> asnHeaderList){
        Set<Long> ownerIdSet = asnHeaderList.stream().map(AsnHeader::getOwnerId).collect(Collectors.toSet());
        List<Owner> ownerList = ownerService.findByIds(ArrayUtil.toArray(ownerIdSet, Long.class));
        return ownerList.stream().collect(Collectors.toMap(Owner::getId, Function.identity()));
    }

    public CsPaTask convertAsnReceiveToCsPaTask(AsnReceive asnReceive, PaTaskBatchCreateItem paTaskBatchCreateItem,
                                                Map<Long, Package> packageMap, Map<String, Sku> skuMap,
                                                Map<String, Long> asnOwnerMap, Map<Long, Owner> ownerMap,
                                                Map<String, Long> asnWareHouseMap, Map<Long, Warehouse> wareHouseMap,
                                                Map<Long, AsnHeader> asnHeaderMap, Map<String, SoHeader> soHeaderMap){
        Date today = new Date();
        SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();

        Long receiveId = rulePaCommonService.checkRulePaTaskAndGetLoc(asnReceive);
        //字段设置
        CsPaTask csPaTask = new CsPaTask();
        BeanUtil.copyProperties(asnReceive, csPaTask, "id");
        String paNo = numberGenerator.nextValue(IdRuleConstant.PA_CODE);
        csPaTask.setPaNo(paNo);
        csPaTask.setPaSource(PaSourceEnum.ASN.getCode());
        csPaTask.setOrderId(asnReceive.getAsnId());
        csPaTask.setOrderNo(asnReceive.getAsnCode());
        csPaTask.setStatus(PaStatusEnum.SHELVESING.getCode());
        csPaTask.setFmLocId(asnReceive.getRcvLocId());
        csPaTask.setFmLocName(asnReceive.getRcvLocName());
        csPaTask.setFmPalletId(asnReceive.getRcvPalletId());
        csPaTask.setFmPalletNo(asnReceive.getRcvPallet());
        if (EmptyUtil.isNotEmpty(receiveId)){
            WarehouseLoc warehouseLoc = warehouseLocService.findById(receiveId);
            csPaTask.setToLocId(warehouseLoc.getId());
            csPaTask.setToLocName(warehouseLoc.getLocName());
        }
        csPaTask.setQtyPlanEa(asnReceive.getQtyRcvEa());
        csPaTask.setPackageId(asnReceive.getPackageId());
        csPaTask.setPaPersonId(asnReceive.getPaPersonId());
        csPaTask.setPaPersonName(asnReceive.getPaPersonName());
        Package aPackage = packageMap.get(csPaTask.getPackageId());
        if (ObjectUtil.isNotEmpty(aPackage)){
            csPaTask.setPackageName(aPackage.getName());
        }
        Sku sku = skuMap.get(asnReceive.getSkuCode());
        if (ObjectUtil.isNotEmpty(sku)){
            csPaTask.setSkuId(sku.getId());
            csPaTask.setSkuName(sku.getSkuName());
        }
        Long ownerId = asnOwnerMap.get(asnReceive.getAsnCode());
        if (ObjectUtil.isNotEmpty(ownerId)){
            Owner owner = ownerMap.get(ownerId);
            if (ObjectUtil.isNotEmpty(owner)){
                csPaTask.setOwnerId(owner.getId());
                csPaTask.setOwnerName(owner.getOwnerName());
            }
        }

        Long wareHouseId = asnWareHouseMap.get(asnReceive.getAsnCode());
        if (ObjectUtil.isNotEmpty(wareHouseId)){
            Warehouse warehouse = wareHouseMap.get(wareHouseId);
            if (ObjectUtil.isNotEmpty(warehouse)){
                csPaTask.setWarehouseId(warehouse.getId());
                csPaTask.setWarehouseName(warehouse.getWarehouseName());
            }
        }

        csPaTask.setPaTime(today);


//        AsnHeader asnHeader = asnHeaderMap.get(asnReceive.getAsnId());
//        if (ObjectUtil.isNotEmpty(asnHeader)){
//            String paSource = PaSourceEnum.ASN.getCode();
//            if (AsnTypeEnum.OS.getCode().equals(asnHeader.getAsnType())){
//                paSource = PaSourceEnum.SO.getCode();
//            }
//            csPaTask.setPaSource(paSource);
//            csPaTask.setOrderNo(asnHeader.getSoNo());
//            SoHeader soHeader = soHeaderMap.get(asnHeader.getSoNo());
//            csPaTask.setOrderId(soHeader.getId());
//        }


        return csPaTask;
    }

    /**
     * 校验生成上架任务数据
     * <AUTHOR>
     * @Date 14:05 2025/1/10
     * @Param []
     * @return com.chinaservices.sdk.support.result.ResponseData
     **/
    public void checkCreateData(List<AsnReceive> asnReceiveList){
        List<String> skuCodeList = asnReceiveList.stream().map(AsnReceive::getSkuCode).distinct().collect(Collectors.toList());
        List<Sku> skuList = skuService.listBySkuCode(skuCodeList);
        //map映射skuId->isQc
        Map<Long, String> skuMap = new HashMap<>();
        if (CollUtil.isNotEmpty(skuList)){
            skuMap = skuList.stream().filter(obj -> ObjectUtil.isNotEmpty(obj.getIsQc())).collect(Collectors.toMap(Sku::getId, Sku::getIsQc));
        }

        for (AsnReceive asnReceive : asnReceiveList) {
            //1.校验是否上架
            if (YesNoEnum.YES.getCode().equals(asnReceive.getWhetherShelf())){
                throw new ServiceException(AsnExceptionEnum.RECEIVE_ALREADY_PA);
            }
            String isQc = skuMap.get(asnReceive.getSkuCode());
            //2.校验是否质检，无需质检则跳过 TODO HJH 质检状态产品还没出来
//            if (YesNoEnum.YES.getCode().equals(isQc)){
//                if (YesNoEnum.NO.getCode().equals(asnReceive.getWhetherQc())){
//                    ResponseData.error(ExcepitonEnum.THE_CHECKED_RECEIPT_DETAILS_HAVE_NOT_YET_BEEN_INSPECTED.getMsg());
//                }
//            }
            //2.校验是否设置上架人
            Long paPersonId = asnReceive.getPaPersonId();
            if (ObjectUtil.isEmpty(paPersonId)) {
                throw new ServiceException(AsnExceptionEnum.ASSIGN_A_LOADER_BEFORE_LISTING);
            }
        }
    }

    /**
     * 根据id查询
     * @param id id
     * @return CsSupplierQuery
     */
    public PaTaskQuery getById(Long id) {
        PaTaskQuery paTaskQuery = csPaTaskDao.findQueryById(id);
        if (ObjectUtil.isEmpty(paTaskQuery)){
            return new PaTaskQuery();
        }
        List<CsPaTaskDetail> csPaTaskDetailList = csPaTaskDetailService.findByPaNo(paTaskQuery.getPaNo());
        if (CollUtil.isNotEmpty(csPaTaskDetailList)){
            BigDecimal QtyPaEa = csPaTaskDetailList.stream()
                    .map(CsPaTaskDetail::getQtyPaEa)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            paTaskQuery.setTreatQtyEa(paTaskQuery.getQtyPlanEa().subtract(QtyPaEa));
        }else {
            paTaskQuery.setTreatQtyEa(paTaskQuery.getQtyPlanEa());
        }

        Long packageUnitId = paTaskQuery.getPackageUnitId();
        PackageUnit packageUnit = packageUnitService.findById(packageUnitId);
        if (ObjectUtil.isNotEmpty(packageUnit)){
            paTaskQuery.setMinQuantity(packageUnit.getMinQuantity());
        }
        if (PaSourceEnum.SO.getCode().equals(paTaskQuery.getPaSource())){
            //出库验证是否开启sn收货
            SkuDetailQuery skuDetailQuery = skuService.getById(paTaskQuery.getSkuId());
            paTaskQuery.setSerialCode(YesNoEnum.YES.getCode().equals(skuDetailQuery.getWhetherSerialController()));
        }else{
            Long count = asnDetailSnService.countByReceiveCode(paTaskQuery.getReceiveNo());
            if (count.compareTo(0L) == 0){
                paTaskQuery.setSerialCode(false);
            }
        }

        if (ObjectUtil.isNotEmpty(paTaskQuery.getTreatQtyEa())){
            paTaskQuery.setTreatQty(paTaskQuery.getTreatQtyEa().divide(new BigDecimal(paTaskQuery.getMinQuantity())));
        }

        Sku sku = skuService.findById(paTaskQuery.getSkuId());
        if (ObjUtil.isNotNull(sku)) {
            paTaskQuery.setSkuCode(sku.getSkuCode());
        }

        return paTaskQuery;
    }


    /**
     * 根据条件获取上架任务列表
     * @param paTaskCondition
     * @return
     */
    public List<PaTaskInfoQuery> findByCondition(PaTaskPageCondition paTaskCondition) {
        PaTaskPageCondition paTaskConditions = setCommonCondition(paTaskCondition);
        return dao.findList(paTaskConditions);
    }

    /**
     * 根据条件获取上架任务列表
     * @param condition
     * @return
     */
    public PaTaskPageCondition setCommonCondition(PaTaskPageCondition condition) {
//        if (EmptyUtil.isNotEmpty(condition.getOwnerId())) {
//            condition.setOwnerIdArray(condition.getOwnerId().split(","));
//        }
//        if (EmptyUtil.isNotEmpty(condition.getSkuId())) {
//            condition.setSkuIdIdArray(condition.getSkuId().split(","));
//        }
//        if (EmptyUtil.isNotEmpty(condition.getWarehouseId())) {
//            condition.setWarehouseIdArray(condition.getWarehouseId().split(","));
//        }
//        if (EmptyUtil.isNotEmpty(condition.getFmPallet())) {
//            condition.setFmPalletArray(condition.getFmPallet().split(","));
//        }
        return condition;
    }

    /**
     * 批量上架确认
     * @param taskVo
     * @return
     */
    public ResponseData<CsPaTask> inboundBatchPutaway(PaTaskPutwayConfirmInfo taskVo){
        ResponseData<CsPaTask> responseData = new ResponseData<>();
        StringBuilder errorMsg = new StringBuilder();
        String[] paIds = taskVo.getPaIds();//前端已控制只能单个上架
        for (String paId : paIds) {
            //根据Id查询上架任务
            CsPaTask csPaTaskDb = this.findById(Long.valueOf(paId));
            PaTaskInfo paTaskInfo = new PaTaskInfo();
            BeanUtil.copyProperties(csPaTaskDb, paTaskInfo);
            BeanUtil.copyProperties(taskVo, paTaskInfo);
            //上架数量为空则默认按计划数去上架
            if(EmptyUtil.isEmpty(paTaskInfo.getCurrentPaQtyEa())){
                paTaskInfo.setCurrentPaQtyEa(csPaTaskDb.getQtyPlanEa());
            }
            if(EmptyUtil.isEmpty(paTaskInfo.getToPallet())){
                paTaskInfo.setToPallet(paTaskInfo.getFmPallet());
            }
            CsPaTask csPaTask = new CsPaTask();
            try {
                ResponseData<CsPaTask> msg = this.inboundPutaway(paTaskInfo);
                if(!msg.getSuccess()){
                    ErrorResponseData<String> errorDate = ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, paTaskInfo.getPaNo(), paTaskInfo.getLineNo(), msg.getMsg());
                    errorMsg.append(errorDate.getMsg());

                }
                csPaTask = msg.getData();
            } catch (Exception e) {
                logger.error(e.getMessage());
                ErrorResponseData<String> errorDate = ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, paTaskInfo.getPaNo(), paTaskInfo.getLineNo(), e.getMessage());
                errorMsg.append(errorDate.getMsg());

            }
            try {
//                //释放源容器号
//                String fmPallet = csPaTask.getFmPallet();
//                if (EmptyUtil.isNotEmpty(fmPallet)&&!IdRuleConstant.TRACE_ID.equals(fmPallet)) {
//                    PaTaskPageCondition condition = new PaTaskPageCondition();
//                    condition.setFmPallet(fmPallet);
////                    condition.setWarehouseId(fmPallet);
//                    condition.setStatus(TaskStatus.TSK_NEW);
//                    List<PaTaskInfoQuery> paTasks = this.findByCondition(condition);
//                    if (EmptyUtil.isEmpty(paTasks)) {
//                        palletService.updateIsUsed(fmPallet, csPaTask.getWarehouseId(), SystemStatus.NO);
//                    }
//                }
//                //完全收货状态，并且不存在未完成的上架任务，是否自动关闭ASN
//                inboundCommonService.checkRcvAutoCloseAsn(csPaTask.getAsnId(), csPaTask.getWarehouseId());
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }
        if(EmptyUtil.isNotEmpty(errorMsg.toString())){
            responseData.setSuccess(false);
            responseData.setMsg(errorMsg.toString());
        }else{
            responseData.setSuccess(true);
        }
        return responseData;
    }

    /**
     * 根据Id查询上架任务
     * @param id
     * @return
     */
    public CsPaTask findById(long id) {
        return dao.findById(id);
    }

    /**
     * 上架任务，前台页面收集到的上架任务信息
     * @param paTaskInfo
     * @return
     */
    public ResponseData<CsPaTask> inboundPutaway(PaTaskInfo paTaskInfo){

        /* 上架确认前校验
         * 1.校验上架任务状态，判断是否为新建
         * 2.校验目标数量不能超过任务数量
         * 3.上架库位不能和收货库位相同
         * 4.如果是部分上架时，一定需要上到不同的TraceID
         */
        ResponseData<CsPaTask> msg = this.checkBeforePutaway(paTaskInfo);
        if(!msg.getSuccess()){
            return ResponseData.error(msg.getMsg());
        }
        CsPaTask csPaTask = msg.getData();
        /*
         * 更新库存记录，返回上架后的库存信息
         */
        //实例化上架更新库存参数
        InvLotLocQty fmEntity = new InvLotLocQty();
        BeanUtil.copyProperties(csPaTask,fmEntity);
        fmEntity.setAction(ActionCode.PUTAWAY);
        fmEntity.setUpdateNum(paTaskInfo.getCurrentPaQtyEa());
        BigDecimal qtyPackOp = paTaskInfo.getCurrentPaQtyEa();
        if(EmptyUtil.isNotEmpty(paTaskInfo.getPackageUomId())){
            PackageUnit packageUnit = packageUnitService.getById(paTaskInfo.getPackageUomId());
            int quantity = packageUnit.getQuantity();
            qtyPackOp = paTaskInfo.getCurrentPaQtyEa().divide(new BigDecimal(quantity));
        }
        fmEntity.setQtyPackOp(qtyPackOp);
        //源库位编码
        fmEntity.setLocId(csPaTask.getFmLocId());
//        fmEntity.setLocCode(csPaTask.getFmLocCode());
        fmEntity.setOrderNo(csPaTask.getPaNo());
        //源容器号
//        fmEntity.setPalletNum(csPaTask.getFmPallet());

        InvLotLocQty toEntity = new InvLotLocQty();
        BeanUtil.copyProperties(fmEntity,toEntity);
        toEntity.setPalletNum(paTaskInfo.getToPallet());//实际容器号
        toEntity.setLocId(paTaskInfo.getToLocId());//实际库位
        toEntity.setLocCode(paTaskInfo.getToLocCode());//实际库位
//        toEntity.setPlanLocCode(csPaTask.getFmLocCode());//计划库位
//        toEntity.setPlanPalletNum(csPaTask.getFmPallet());//计划容器号
        //执行上架更新，更新收货库位待出数，计划库位待入数

//        ResponseData<InvLotLocQty> msgInvLotLocQty = updateInventoryQtyService.updateInventory(fmEntity, toEntity);
//        if (!msgInvLotLocQty.getSuccess()){
//            return ResponseData.error(msgInvLotLocQty.getMsg());
//        }
//        toEntity = msgInvLotLocQty.getData();
        //库位为忽略ID时，更新完库存，容器号置*
        BeanUtil.copyProperties(paTaskInfo, csPaTask);
//        csPaTask.setToPallet(toEntity.getPalletNum());
        BigDecimal currentPaQtyEa = paTaskInfo.getCurrentPaQtyEa();
        BigDecimal qtyPlanEa = paTaskInfo.getQtyPlanEa();
        /*
         * 更新上架任务记录。
         */
        if(currentPaQtyEa.compareTo(qtyPlanEa)< 0){
            csPaTask.setQtyPlanEa(currentPaQtyEa);//部分上架后计划上架数更新为实际上架数，剩余待上架数拆分出一条上架任务
        }
        csPaTask.setQtyPaEa(currentPaQtyEa);
        csPaTask.setStatus(TaskStatus.TSK_COMPLETE);
//        if(EmptyUtil.isEmpty(csPaTask.getPaPerson())&&  SessionContext.getSessionUserInfo()!=null){
////            csPaTask.setPaPerson(SessionContext.getSessionUserInfo().getRealName());
//        }
        if(null== csPaTask.getPaTime()){
            csPaTask.setPaTime(new Date());
        }
        //更新
        dao.update(csPaTask);

        //部分上架时，新增一个上架任务。
        if(currentPaQtyEa.compareTo(qtyPlanEa)< 0){
            CsPaTask newCsPaTask = new CsPaTask();
            BeanUtil.copyProperties(csPaTask, newCsPaTask);
            newCsPaTask.setId(null);
            newCsPaTask.setStatus(TaskStatus.TSK_NEW);
//            newCsPaTask.setLineNo(this.getPaLineNo(csPaTask.getPaNo(), csPaTask.getWarehouseId()));
//            newCsPaTask.setToPallet(csPaTask.getFmPallet());
            newCsPaTask.setToLocId(csPaTask.getFmLocId());
//            newCsPaTask.setToLocCode(csPaTask.getFmLocCode());
            newCsPaTask.setQtyPlanEa(qtyPlanEa.subtract(currentPaQtyEa));
            newCsPaTask.setQtyPaEa(BigDecimal.ZERO);
//            newCsPaTask.setPaPerson(null);
            newCsPaTask.setPaTime(null);
            dao.insert(newCsPaTask);
        }
        return ResponseData.success(csPaTask);
    }

    /**
     * 生成上架任务
     * 如果收货库位是过渡库位&TraceID不为“*”，可以生成上架任务
     * @param paTaskInfo
     * @return
     */
    public ResponseData<CsPaTask> inboundCreatePaTask(PaTaskInfo paTaskInfo){

        //批次号
        String lotNum = paTaskInfo.getLotNum();
        //收货库位
        Long fmLocId = paTaskInfo.getFmLocId();
        //仓库id
        Long warehouseId = paTaskInfo.getWarehouseId();
        //根据收货明细id判断是否已经生成上架任务
        PaTaskCondition paTaskCondition = new PaTaskCondition();
        paTaskCondition.setRcvId(paTaskInfo.getRcvId());
        List<CsPaTask> csPaTasks = csPaTaskDao.findByRcvId(paTaskCondition);
        if(csPaTasks.size()>0){
            return ResponseData.error(MsgConstant.PA_TASK_DATE);
        }

        WarehouseLoc loc = warehouseLocService.findById(fmLocId.longValue());
        //先校验是否可以上架任务。
        //ASN收货库位为过渡库位，才能生成上架任务。
        if(!LocUseType.LOC_USE_ST.equals(loc.getLocUseType())){
            return ResponseData.error(MsgConstant.ASN_RECEIVE_RCV_LOC_NAME_LOC_USE_ST);
        }

        //上架数=收货数
        InvLotAtt wmInvLotAttModel = invLotAttService.getByLotNum(lotNum,warehouseId);
        if(wmInvLotAttModel!=null){
            return ResponseData.error(MsgConstant.ASN_RECEIVE_QC_ATT_TO_QC);
        }
        /*
         * 生成上架任务
         */
        CsPaTask csPaTask = this.saveTaskPa(paTaskInfo);
        return ResponseData.success(csPaTask);

    }

    /**
     * 生成上架任务记录
     * @param paTaskInfo
     * @return
     */
    public CsPaTask saveTaskPa(PaTaskInfo paTaskInfo){
        String paNo ="";
        CsPaTask csPaTask = new CsPaTask();
        BeanUtil.copyProperties(paTaskInfo, csPaTask);
        //如果收货明细已存在上架Id，不新增上架ID
        if(EmptyUtil.isEmpty(paTaskInfo.getPaNoRcv())){
            //流水号(上架任务号)
            paNo = numberGenerator.nextValue(IdRuleConstant.PA_NO);
        } else {
            paNo = paTaskInfo.getPaNoRcv();
        }
        csPaTask.setPaNo(paNo);
//        csPaTask.setLineNo(this.getPaLineNo(paNo, csPaTask.getWarehouseId()));
        csPaTask.setStatus(TaskStatus.TSK_NEW);
        if(csPaTask.getId()==null){
            dao.insert(csPaTask);
        }else{
            dao.update(csPaTask);
        }
        return csPaTask;
    }

    /**
     * 上架确认前校验
     * @param paTaskInfo
     * @return
     */

    protected ResponseData<CsPaTask> checkBeforePutaway(PaTaskInfo paTaskInfo){
        CsPaTask paModel = this.findById(paTaskInfo.getId().longValue());
        //校验上架任务状态，判断是否为新建
        if(null==paModel){
            return ResponseData.error(MsgConstant.PA_TASK_DATE_NOT);
        }else{
            if(!TaskStatus.TSK_NEW.equals(paModel.getStatus())){
                return ResponseData.error(MsgConstant.PA_TASK_STATUS_NEW);
            }
        }
        //校验目标数量不能超过任务数量
        if(EmptyUtil.isEmpty(paTaskInfo.getCurrentPaQtyEa())|| paTaskInfo.getCurrentPaQtyEa().compareTo(BigDecimal.ZERO)==0){
            return ResponseData.error(MsgConstant.PA_TASK_PA_QTY_EA_NOT_ZERO);
        }

        //实际页面收集到的上架数>数据库查询出的计划上架数
        if(paTaskInfo.getCurrentPaQtyEa().compareTo(paModel.getQtyPlanEa())>0){
            return ResponseData.error(MsgConstant.PA_TASK_PA_QTY_EA_NOT_EXCEED);
        }

        //上架库位不能和收货库位相同
        if(EmptyUtil.isEmpty(paTaskInfo.getToLocCode())){
            return ResponseData.error(MsgConstant.PA_TASK_TO_LOC_CODE_NOT_NULL);
        }
        if(paTaskInfo.getToLocCode().equals(paTaskInfo.getFmLocCode())){
            return ResponseData.error(MsgConstant.PA_TASK_TO_LOC_CODE_NOT_FM);
        }
        //如果是部分上架并且库位不忽略容器号，一定需要上到不同的TraceID
        if(EmptyUtil.isEmpty(paTaskInfo.getToPallet())){
            return ResponseData.error(MsgConstant.PA_TASK_TO_PALLET);
        }
        WarehouseLoc loc = warehouseLocService.findById(paTaskInfo.getToLocId().longValue());
        if(loc == null)
        {
            return ResponseData.error(MsgConstant.PA_TASK_TO_LOC_CODE, paTaskInfo.getToLocCode());
        }
        if(SystemStatus.NO.equals(loc.getIsLoseId())&&!IdRuleConstant.TRACE_ID.equals(paTaskInfo.getToPallet())
                && paTaskInfo.getCurrentPaQtyEa().compareTo(paModel.getQtyPaEa())< 0 && paTaskInfo.getFmPallet().equals(paTaskInfo.getToPallet())){
            return ResponseData.error(MsgConstant.PA_TASK_TRACE_ID);
        }
        //如果批次是待检，不能操作。
        InvLotAtt invLotAtt = invLotAttService.getByLotNum(paModel.getLotNum(), paModel.getWarehouseId());
        if(invLotAtt!=null){
            return ResponseData.error(MsgConstant.ASN_RECEIVE_QC_ATT_TO_QC);
        }
        return ResponseData.success(paModel);
    }

    /**
     * 删除上架任务
     * @param paId
     * @return
     */
    public ResponseData<CsPaTask> inboundRemovePutaway(Long paId){
        CsPaTask paModel = this.findById(paId.longValue());
        //校验上架任务状态，判断是否为新建
        if(null==paModel){
            return ResponseData.error(MsgConstant.PA_TASK_DATE_NOT);
        }else{
//            if(!TaskStatus.TSK_NEW.equals(paModel.getStatus())){
//                return ResponseData.error(MsgConstant.PA_TASK_LINE_NO_STATUS_NEW, paModel.getPaNo(), paModel.getLineNo());
//            }
        }
        //删除上架任务
        this.deleteByIds(new Object[]{paModel.getId()});
        //上架任务删除后，如果paNo在上架任务记录里不存在，但在收货明细里有存在，则收货明细里的上架任务号需置空
        AsnReceiveCondition condition = new AsnReceiveCondition();
        condition.setPaNo(paModel.getPaNo());
        condition.setWarehouseId(paModel.getWarehouseId());
        asnReceiveDao.updateClearPaNo(condition);

        return ResponseData.success();
    }

    /**
     * 批量取消上架任务
     * @param paTaskDeleteItem
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(PaTaskBatchDeleteItem paTaskDeleteItem){
        List<Long> idList = paTaskDeleteItem.getIdList();
        if (PaTaskDeleteTypeEnum.PA_TASK_DETAIL.getCode().equals(paTaskDeleteItem.getType())){
            //1.获取上架任务集合
            List<CsPaTaskDetail> csPaTaskDetailList = csPaTaskDetailService.findByIds(ArrayUtil.toArray(idList, Long.class));
            //2.校验数据
            checkDetailDeleteData(csPaTaskDetailList);
            //3.调整库存
            this.changeDeleteInvLotLocList(csPaTaskDetailList);
            //4.更新收货明细状态
//            Set<String> receiveNoSet = csPaTaskDetailList.stream().map(CsPaTaskDetail::getReceiveNo).collect(Collectors.toSet());
//            asnReceiveService.updateWhetherShelfByReceiveNos(receiveNoSet, YesNoEnum.NO.getCode());
            //5.批量删除上架任务明细
            csPaTaskDetailService.delete(ArrayUtil.toArray(idList, Long.class));
            //6.构建更新上架任务
            List<PaTaskMainConfirmItem> paTaskMainConfirmItemList = this.buildDeleteMainConfirmItem(csPaTaskDetailList);
            //7.更新上架任务
            this.batchUpdateItemToSqlExecution(paTaskMainConfirmItemList);
            //8.已完结入库单更新为创建状态
            this.updateSoHeaderWithCreate(csPaTaskDetailList);
            //8.解绑上架对应的sn码
            this.unbindPaTaskSnRelation(idList);
            //删除记录日志
            Set<Long> collect = csPaTaskDetailList.stream().map(CsPaTaskDetail::getSkuId).collect(Collectors.toSet());
            List<Sku> skuList = skuService.findByIds(collect.toArray(Long[]::new));
            Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(ModuleBaseModel::getId, Function.identity()));
            csPaTaskDetailList.forEach(paTaskItem -> traceBackRecordService.executeDel(new TraceBackRecordItem()
                    .setSkuCodeList(Lists.newArrayList(skuMap.get(paTaskItem.getSkuId()).getSkuCode()))
                    .setBusinessNo(paTaskItem.getPaNo()).setBusinessType(TraceBackBusinessTypeEnum.PUT_AWAY)));
        } else if (PaTaskDeleteTypeEnum.PA_TASK.getCode().equals(paTaskDeleteItem.getType())) {
            //1.获取上架任务集合
            List<CsPaTask> csPaTaskList = this.findByIds(ArrayUtil.toArray(idList, Long.class));
            //2.校验数据
            checkDeleteData(csPaTaskList);
            //3.更新收货明细状态
            Set<String> receiveNoSet = csPaTaskList.stream().map(CsPaTask::getReceiveNo).collect(Collectors.toSet());
            asnReceiveService.updateWhetherShelfByReceiveNos(receiveNoSet, YesNoEnum.NO.getCode());
            //4.批量删除上架任务
            this.deleteByIds(ArrayUtil.toArray(idList, Long.class));
        }

    }

    public void updateSoHeaderWithCreate(List<CsPaTaskDetail> csPaTaskDetailList){
        Set<Long> orderIdSet = csPaTaskDetailList.stream().map(CsPaTaskDetail::getOrderId).collect(Collectors.toSet());
        List<AsnHeader> asnHeaderList = asnHeaderService.findByIds(ArrayUtil.toArray(orderIdSet, Long.class));
        Set<Long> asnIdList = new HashSet<>();
        for (AsnHeader asnHeader : asnHeaderList) {
            if (AsnStatusEnum.ASN_CLOSE.getCode().equals(asnHeader.getStatus())){
                asnIdList.add(asnHeader.getId());
            }
        }
        if (CollUtil.isEmpty(asnIdList)){
            return;
        }
        asnHeaderService.updateAsnStatusByIds(ArrayUtil.toArray(asnIdList, Long.class) ,AsnStatusEnum.ASN_NEW.getCode());
    }

    public void unbindPaTaskSnRelation(List<Long> idList){
        asnDetailSnService.updateNullByPaTaskDetailIds(idList);
    }

    public List<PaTaskMainConfirmItem> buildDeleteMainConfirmItem(List<CsPaTaskDetail> csPaTaskDetailList){
        Set<Long> paIdList = csPaTaskDetailList.stream().map(CsPaTaskDetail::getPaId).collect(Collectors.toSet());
        List<CsPaTask> csPaTaskList = this.findByIds(ArrayUtil.toArray(paIdList, Long.class));
        Map<Long, CsPaTask> csPaTaskMap = csPaTaskList.stream().collect(Collectors.toMap(CsPaTask::getId, Function.identity()));
        List<PaTaskMainConfirmItem> paTaskMainConfirmItemList = new ArrayList<>();
        for (CsPaTaskDetail csPaTaskDetail : csPaTaskDetailList) {
            Long paId = csPaTaskDetail.getPaId();
            CsPaTask csPaTask = csPaTaskMap.get(paId);
            if (ObjectUtil.isEmpty(csPaTask)){
                continue;
            }
            PaTaskMainConfirmItem paTaskMainConfirmItem = new PaTaskMainConfirmItem();
            paTaskMainConfirmItem.setId(csPaTask.getId());
            paTaskMainConfirmItem.setQtyPaEa(csPaTask.getQtyPaEa().subtract(csPaTaskDetail.getQtyPaEa()));
            String status = null;
            if (paTaskMainConfirmItem.getQtyPaEa().compareTo(csPaTask.getQtyPlanEa()) == 0){
                status = PaStatusEnum.SHELVESED.getCode();
            }else {
                status = PaStatusEnum.SHELVESING.getCode();
            }
            paTaskMainConfirmItem.setStatus(status);
            paTaskMainConfirmItemList.add(paTaskMainConfirmItem);
        }
        return paTaskMainConfirmItemList;
    }

    public void changeDeleteInvLotLocList(List<CsPaTaskDetail> csPaTaskDetailList){
        for (CsPaTaskDetail csPaTaskDetail : csPaTaskDetailList) {
            if (PaStatusEnum.SHELVESED.getCode().equals(csPaTaskDetail.getStatus())){
                this.changeDetailInvLotLoc(csPaTaskDetail);
            }
        }

    }



    public void checkDetailDeleteData(List<CsPaTaskDetail> csPaTaskDetailList){
        if (CollUtil.isEmpty(csPaTaskDetailList)){
            throw new ServiceException(GlobalExceptionEnum.DELETE_REPEATEDLY);
        }
        List<CsPaTaskDetail> list = csPaTaskDetailList.stream().filter(f -> PaSourceEnum.SO.getCode().equals(f.getPaSource())).toList();
        if (CollUtil.isNotEmpty(list)){
            //出库上架任务不允许取消！
            String join = String.join(",", list.stream().map(CsPaTaskDetail::getPaDetailNo).toList());
            throw new ServiceException(AsnExceptionEnum.SO_PA_DETAIL_NOT_CANANL,join);
        }

    }

    public void checkDeleteData(List<CsPaTask> csPaTaskList){
        if (CollUtil.isEmpty(csPaTaskList)){
            throw new ServiceException(GlobalExceptionEnum.DELETE_REPEATEDLY);
        }
        List<CsPaTask> list = csPaTaskList.stream().filter(f -> PaSourceEnum.SO.getCode().equals(f.getPaSource())).toList();
        if (CollUtil.isNotEmpty(list)){
            //出库上架任务不允许取消！
            String join = String.join(",", list.stream().map(CsPaTask::getPaNo).toList());
            throw new ServiceException(AsnExceptionEnum.SO_PA_DETAIL_NOT_CANANL,join);
        }
    }

    /**
     * 根据Ids批量删除
     * @param ids
     * @return boolean
     */
    public boolean deleteByIds(Object[] ids) {
        Long[] idArray = Arrays.stream(ids).map(Convert::toLong).toArray(Long[]::new);
        return dao.delete(idArray) > 0;
    }

    /**
     * 通过上架任务号paNo，获得最新任务序号
     * @param paNo
     * @param warehouseId
     * @return
     */
    public String getPaLineNo(String paNo, Long warehouseId){
        PaTaskCondition paTaskCondition = new PaTaskCondition();
        paTaskCondition.setPaNo(paNo);
        paTaskCondition.setWarehouseId(warehouseId);
        Integer lineNo = dao.findByPaNo(paTaskCondition);
        return String.format("%04d",lineNo);
    }

    /**
     * 批量生成上架任务：只对于收货后的明细，收货库位使用类型为过渡库位，才能生成上架任务。
     * @param ids
     * @return
     */
    public ResponseData<CsPaTask> inboundBatchCreatePaTask(@RequestBody Object[] ids) {
        ResponseData<CsPaTask> responseData = new ResponseData<>();
        StringBuilder error = new StringBuilder();
        //key值为容器号
        Map<String, List<AsnReceiveInfoQuery>> map =new HashMap<>();
        List<AsnReceiveInfoQuery> receiveGroup;
        for (Object id:ids){
            AsnReceiveInfoQuery asnReceive = inboundCommonService.getEntityById(Long.valueOf(id.toString()));
            if(!AsnStatus.ASN_FULL_RECEIVING.equals(asnReceive.getStatus())){
              ErrorResponseData<String> errorDate = ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_STATUS_UN_RECEIVE,  asnReceive.getAsnNo(), asnReceive.getLineNo());
                error.append(errorDate.getMsg());
            }
            //质检校验
            if((EmptyUtil.isNotEmpty(asnReceive.getQcStatus())&& QcPhase.PA.equals(asnReceive.getQcPhase()))|| QcStatus.QC_NEW.equals(asnReceive.getQcStatus())){
                ErrorResponseData<String> errorDate = ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_QC_STATUS, asnReceive.getAsnNo(), asnReceive.getLineNo());
                error.append(errorDate.getMsg());
            }else if(EmptyUtil.isEmpty(asnReceive.getQcStatus())&& SystemStatus.YES.equals(asnReceive.getIsQc())){
                ErrorResponseData<String> errorDate = ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_QC_PHASE,asnReceive.getAsnNo(), asnReceive.getLineNo());
                error.append(errorDate.getMsg());
            }
            //1.忽略容器号，一个收货明细生成一个上架任务
            if(IdRuleConstant.TRACE_ID.equals(asnReceive.getRcvPallet())){
                //上架任务
                PaTaskInfo paTaskInfo = new PaTaskInfo();
                BeanUtil.copyProperties(asnReceive, paTaskInfo);
                paTaskInfo.setId(null);
                paTaskInfo.setFmLocId(asnReceive.getRcvLocId());
                paTaskInfo.setFmLocCode(asnReceive.getRcvLocName());
                paTaskInfo.setFmPallet(asnReceive.getRcvPallet());
                paTaskInfo.setQtyPlanEa(asnReceive.getQtyRcvEa());
                paTaskInfo.setQtyPaEa(BigDecimal.ZERO);
                paTaskInfo.setPaNoRcv(asnReceive.getPaNo());
                paTaskInfo.setRcvId(asnReceive.getId());
                if(EmptyUtil.isNotEmpty(asnReceive.getPaNo())){
                    Long qtyPaEa = this.getQtyPaByPaId(asnReceive.getPaNo(), asnReceive.getWarehouseId());//已生成上架数
                    paTaskInfo.setQtyPlanEa(asnReceive.getQtyRcvEa().subtract(new BigDecimal(qtyPaEa)));
                }else{
                    paTaskInfo.setQtyPlanEa(asnReceive.getQtyRcvEa());
                }
                if(paTaskInfo.getQtyPlanEa().compareTo(BigDecimal.ZERO)>0){
                    try {
                        //生成上架任务
                        ResponseData<CsPaTask> msg = this.inboundCreatePaTask(paTaskInfo);
                        if(msg.getSuccess()){
                            CsPaTask csPaTask = msg.getData();
                            String paNo = csPaTask.getPaNo();
                            asnReceive.setPaNo(paNo);
                            AsnReceiveInfo receiveEntity = new AsnReceiveInfo();
                            BeanUtil.copyProperties(asnReceive,receiveEntity);
                            inboundCommonService.saveAsnDetailReceiveEntity(receiveEntity);
                        }else{
                            ErrorResponseData<String> errorDate = ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, asnReceive.getAsnNo(), asnReceive.getLineNo(), msg.getMsg());
                            error.append(errorDate.getMsg());
                        }
                    } catch (Exception e) {
                        logger.error(e.getMessage());
                        ErrorResponseData<String> errorDate = ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, asnReceive.getAsnNo(), asnReceive.getLineNo(), e.getMessage());
                        error.append(errorDate.getMsg());
                    }
                }
            }else{
                //2.如果存在容器号，则分组汇总
                String key = asnReceive.getRcvPallet();
                if(!map.containsKey(key)){
                    receiveGroup = new ArrayList<>();
                }else{
                    receiveGroup = map.get(key);
                }
                receiveGroup.add(asnReceive);
                map.put(key, receiveGroup);
            }
        }
        //存在容器号时，分组构造生成上架任务的参数
        Set<String> keySet = map.keySet();
        for (String key : keySet) {
            //同个traceId在不同收货明细行，如果收货信息有不同，则只取第一条
            AsnReceiveInfoQuery receiveEntity = map.get(key).get(0);
            PaTaskInfo paTaskInfo = new PaTaskInfo();
            BeanUtil.copyProperties(receiveEntity, paTaskInfo);
            paTaskInfo.setId(null);
            paTaskInfo.setFmLocId(receiveEntity.getRcvLocId());
            paTaskInfo.setFmLocCode(receiveEntity.getRcvLocName());
            paTaskInfo.setFmPallet(receiveEntity.getRcvPallet());
            paTaskInfo.setQtyPlanEa(receiveEntity.getQtyRcvEa());
            paTaskInfo.setQtyPaEa(BigDecimal.ZERO);
            paTaskInfo.setPaNoRcv(receiveEntity.getPaNo());
            paTaskInfo.setRcvId(receiveEntity.getId());
            try {
                ResponseData<CsPaTask> msg = this.inboundCreatePaTask(paTaskInfo);
                if(msg.getSuccess()){
                    CsPaTask csPaTask = msg.getData();
                    String paNo = csPaTask.getPaNo();
                    //更新相同容器号的收货明细行的PaID
                    AsnReceiveCondition asnReceiveCondition = new AsnReceiveCondition();
                    asnReceiveCondition.setAsnId(receiveEntity.getAsnId());
                    asnReceiveCondition.setRcvPallet(receiveEntity.getRcvPallet());
                    asnReceiveCondition.setStatus(AsnStatus.ASN_FULL_RECEIVING);
                    asnReceiveCondition.setPaNo(paNo);
                    asnReceiveDao.updatePaNo(asnReceiveCondition);
                }else{
                    ErrorResponseData<String> errorDate = ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, receiveEntity.getAsnNo(), receiveEntity.getLineNo(), msg.getMsg());
                    error.append(errorDate.getMsg());
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
                ErrorResponseData<String> errorDate = ResponseData.error(MsgConstant.ASN_RECEIVE_LINE_NO_ASN_NO, receiveEntity.getAsnNo(), receiveEntity.getLineNo(), e.getMessage());
                error.append(errorDate.getMsg());
            }
        }
        if(EmptyUtil.isNotEmpty(error.toString())){
            responseData.setSuccess(false);
            responseData.setMsg(error.toString());

        }else{
            responseData.setSuccess(true);
        }
        return responseData;
    }

    /**
     * 汇总上架ID的上架数
     * @param paNo
     * @param warehouseId
     * @return
     */
    public Long getQtyPaByPaId(String paNo,Long warehouseId){
        PaTaskCondition paTaskCondition = new PaTaskCondition();
        paTaskCondition.setPaNo(paNo);
        paTaskCondition.setWarehouseId(warehouseId);
        Long qtyPaNo;
        qtyPaNo = dao.findSumByPaNo(paTaskCondition);
        return qtyPaNo;
    }

    /**
     * 出库取消拣货生成上架任务
     * @param paTaskInfo
     * @return Result
     */
    public ResponseData<CsPaTask> outboundCreateTaskPa(PaTaskInfo paTaskInfo){
        ResponseData responseData;
        //货主
        Long ownerId = paTaskInfo.getOwnerId() ;
        //商品
        Long skuId = paTaskInfo.getSkuId();
        //批次号
        String lotNum = paTaskInfo.getLotNum();
        //收货库位
        Long fmLocId = paTaskInfo.getFmLocId();
        //收货traceID
        String fmPallet = paTaskInfo.getFmPallet();
        //推荐库位
        //实例化查询库存参数
        InvLotLocQty checkEntity = new InvLotLocQty();
        checkEntity.setOwnerId(ownerId);
        checkEntity.setSkuId(skuId);
        checkEntity.setLotNum(lotNum);
        checkEntity.setPalletNum(fmPallet);
        checkEntity.setLocId(fmLocId);
        checkEntity.setWarehouseId(paTaskInfo.getWarehouseId());
        responseData = updateInventoryQtyService.getAvailableAllocQty(checkEntity);
        if(!responseData.getSuccess()){
            return responseData;
        }
        BigDecimal qtyAvailable = (BigDecimal) responseData.getData();
        if(qtyAvailable.compareTo(BigDecimal.ZERO)<=0){
            //有效库存不足,批次{0} 库位{1} 跟踪号{2}，不能操作
            return ResponseData.error(MsgConstant.AVAILABLEQTY_NOT_ENOUGH,lotNum, paTaskInfo.getFmLocCode(),fmPallet);
        }
        //生成上架任务
        CsPaTask csPaTask = this.saveTaskPa(paTaskInfo);
        responseData.setData(csPaTask);
        responseData.setSuccess(true);
        return responseData;
    }

    public List<PaTaskSnQuery> findSn(PaTaskCondition paTaskCondition){
        CsPaTask paTask = findFirst(new ConditionRule().andEqual(CsPaTask::getPaNo, paTaskCondition.getPaNo()));
        if (PaSourceEnum.SO.getCode().equals(paTask.getPaSource())){
            CsPaTaskAssociation association = csPaTaskAssociationDao.findFirst(new ConditionRule().andEqual(CsPaTask::getPaNo, paTaskCondition.getPaNo()));
            SoAllocation allocation = soAllocationDao.findFirst(new ConditionRule().andEqual(SoAllocation::getPickingTaskNo, association.getPickingTaskNo()));
            if (EmptyUtil.isNotEmpty(allocation) && EmptyUtil.isNotEmpty(allocation.getPickingSnNoStr())){
                List<String> list = Arrays.asList(allocation.getPickingSnNoStr().split(","));
                List<AsnDetailSn> asnDetailSnList = asnDetailSnService.find(new ConditionRule().andIn(AsnDetailSn::getSnNo, list));
                return packageResultList(asnDetailSnList);
            }
            return new ArrayList<>();
        }else if (PaSourceEnum.ASN.getCode().equals(paTask.getPaSource())){
            if (StrUtil.isBlank(paTaskCondition.getReceiveNo())){
                if (StrUtil.isEmpty(paTaskCondition.getPaNo())){
                    return new ArrayList<>();
                }else{
                    paTaskCondition.setReceiveNo(paTask.getReceiveNo());
                }
            }
            List<AsnDetailSn> asnDetailSnList = asnDetailSnService.findByReceiveNo(paTaskCondition.getReceiveNo());
            return packageResultList(asnDetailSnList);
        }
        return new ArrayList<>();
    }

    private static @NotNull List<PaTaskSnQuery> packageResultList(List<AsnDetailSn> asnDetailSnList) {
        Map<String, List<AsnDetailSn>> boxCodeToAsnDetailSnListMap = asnDetailSnList.stream().map(obj -> {
            if (StrUtil.isBlank(obj.getBoxCode())) {
                obj.setBoxCode(DEFAULT_BOX_CODE);
            }
            return obj;
        }).collect(Collectors.groupingBy(AsnDetailSn::getBoxCode));
        if (ObjectUtil.isEmpty(boxCodeToAsnDetailSnListMap)){
            return new ArrayList<>();
        }

        List<PaTaskSnQuery> paTaskSnQueryList = new ArrayList<>();
        boxCodeToAsnDetailSnListMap.forEach((boxCode, asnDetailSnListGroup) -> {
            PaTaskSnQuery paTaskSnQuery = new PaTaskSnQuery();
            paTaskSnQuery.setBoxCode(boxCode);
            List<String> snNoList = asnDetailSnListGroup.stream().map(AsnDetailSn::getSnNo).collect(Collectors.toList());
            paTaskSnQuery.setSnNoList(snNoList);
            paTaskSnQueryList.add(paTaskSnQuery);
        });
        return paTaskSnQueryList;
    }

    /**
     * 计算库位上架平均时效
     * @param warehouseId
     * @return
     */
    public Double findAvgTimeByWarehouseId(Long warehouseId) {
        return dao.findAvgTimeByWarehouseId(warehouseId);
    }

    /**
     * 根据上架任务号获取上架任务信息-单据预览数据查询
     * @param paNo
     * @return
     */
    public PaTaskPreviewQuery getPaTaskPreviewQueryByPaNo(String paNo) {
        PaTaskPreviewQuery result = new PaTaskPreviewQuery();
        PaTaskQuery paTaskQuery = dao.getPaTaskPreviewQueryByPaNo(paNo);
        if(EmptyUtil.isEmpty(paTaskQuery)){
            throw new ServiceException(AsnExceptionEnum.PA_TASK_NOT_EXIST);
        }
        BeanUtil.copyProperties(paTaskQuery,result);
        //
        if(EmptyUtil.isNotEmpty(result.getStatus())){
            //查询数据字典
            DictDataCondition dictDataCondition = new DictDataCondition();
            dictDataCondition.setType(DictDataTypeConstants.PA_STATUS);
            ResponseData<List<DictDataQuery>> responseData = remoteDictDataService.getDictDataList(dictDataCondition);
            if(responseData.getSuccess()){
                List<DictDataQuery> data = responseData.getData();
                Map<String, String> dictMap = data.stream().collect(Collectors.toMap(DictDataQuery::getCode, DictDataQuery::getValue, (key1, key2) -> key1));
                if(EmptyUtil.isNotEmpty(dictMap) && dictMap.containsKey(result.getStatus())) {
                    result.setStatusStr(dictMap.get(result.getStatus()));
                }
            }
        }
        result.setTaskQueryList(List.of(paTaskQuery));
        return result;
    }


    /**
     * 生成出库上架任务
     **/
    @Transactional(rollbackFor = Exception.class)
    public void createSoPaTask(PaTaskSoCreateItem paTaskBatchCreateItem){
        List<CsPaTask> list = new ArrayList<>();
        for (PaTaskSoCreateItem.CreateItemInfo itemInfo : paTaskBatchCreateItem.getCreateItemInfoList()) {
            CsPaTask csPaTask = new CsPaTask();
            BeanUtils.copyProperties(itemInfo,csPaTask);
            preSave(csPaTask);
            list.add(csPaTask);
        }
        batchInsert(list);
    }

    /**
     * 分配上架员
     * @param item
     * @return
     */
    public Boolean allocPaPerson(@Valid AllocPaPersonItem item) {
        List<CsPaTask> paTaskList = find(new ConditionRule().andIn(CsPaTask::getPaNo, item.getPaNos()));
        if (EmptyUtil.isEmpty(paTaskList)){
            throw new ServiceException(AsnExceptionEnum.PA_TASK_NOT_EXIST);
        }
        paTaskList.stream().forEach(paTask -> {
            paTask.setPaPersonId(item.getPaPersonId());
            paTask.setPaPersonName(item.getPaPersonName());
        });
        batchUpdate(paTaskList);
        return Boolean.TRUE;
    }

    /**
     * 查看关联单据
     * @param paNo
     * @return
     */
    public List<TaskAssociationListQuery> getTaskAssociationByNo(String paNo) {
        return csPaTaskAssociationDao.find(TaskAssociationListQuery.class,new ConditionRule().andEqual(CsPaTaskAssociation::getPaNo, paNo));
    }

    public PageResult<PalletListByPaNoQuery> getSoPalletNum(PaTaskPageCondition paTaskCondition) {
        return csPaTaskAssociationDao.getSoPalletNumPage(paTaskCondition);
    }
}
