package com.chinaservices.wms.module.stock.common.exec;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.util.JsonUtil;
import com.chinaservices.util.StringPool;
import com.chinaservices.util.StringUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constant.StatusConstant;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.module.asn.receive.model.AsnReceiveFile;
import com.chinaservices.wms.module.asn.receive.service.AsnReceiveFileService;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.stock.common.model.att.query.InvLotAttQuery;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.common.model.qty.bo.StockTransactionBO;
import com.chinaservices.wms.module.stock.common.model.qty.query.AvailableAllocQtyQuery;
import com.chinaservices.wms.module.stock.loc.domain.InvLotLocQuery;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageQuery;
import com.chinaservices.wms.module.stock.multi.domain.InvLocAttByLotNumCondition;
import com.chinaservices.wms.module.stock.multi.domain.InvLotLocUpdateQtyCondition;
import com.chinaservices.wms.module.stock.mutli.model.InvLotAtt;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.chinaservices.wms.module.stock.mutli.service.InvLotAttService;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocService;
import com.chinaservices.wms.module.stock.transaction.model.StockTransaction;
import com.chinaservices.wms.module.stock.transaction.service.StockTransactionService;
import com.chinaservices.wms.module.warehouse.loc.domain.WarehouseLocCondition;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import com.chinaservices.wms.module.warning.service.InvWarningService;
import com.google.common.collect.Lists;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static com.chinaservices.wms.common.constant.IdRuleConstant.TRACE_ID;
import static com.chinaservices.wms.common.constant.TransactionType.TRAN_QC_OUT_CRT;
import static com.chinaservices.wms.common.constant.TransactionType.TRAN_QC_OUT_EDT_SAVE;
import static com.chinaservices.wms.common.exception.InvExceptionEnum.*;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class StockCommonBaseService {

    @Autowired
    protected InvLotLocService invLotLocService;
    @Autowired
    protected SkuService skuService;
    @Autowired
    protected OwnerService ownerService;
    @Autowired
    protected WarehouseLocService warehouseLocService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private NumberGenerator numberGenerator;
    @Autowired
    private InvLotAttService invLotAttService;
    @Autowired
    private StockTransactionService stockTransactionService;
    @Autowired
    private StockSkuSnService stockSkuSnService;
    @Autowired
    private AsnReceiveFileService asnReceiveFileService;
    @Autowired
    protected InvWarningService invWarningService;
    //手动校验器
    protected Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 判断逻辑，如果是增加库存是 正数，如果是负数就是扣减，保持和数据库一致，数据库  字段 = 字段 + qty
     *
     * @param qty
     * @param toQty
     * @param toQtyAlloc
     * @param toQtyHold
     * @param toQtyQc
     */
    private static void extracted(BigDecimal qty, BigDecimal toQty, BigDecimal toQtyAlloc,
                                  BigDecimal toQtyHold, BigDecimal toQtyQc,
                                  BigDecimal fmQtyAlloc, BigDecimal fmQtyHold,
                                  BigDecimal fmQc,BigDecimal fmQtyWaitMove,
                                  BigDecimal toQtyWaitMove,BigDecimal fmQtyWaitDamage,
                                  BigDecimal toQtyDamage) {
        // 检查 qty 是否小于 toQtyAlloc
        if (NumberUtil.sub(qty, toQtyAlloc).compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(QTYALLOC_NOT_ENOUGH);
        }
        // 检查 qty 是否小于 toQtyHold
        if (NumberUtil.sub(qty, toQtyHold).compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(QTYHOLD_NOT_ENOUGHE);
        }
        // 检查 qty 是否小于 toQtyQc
        if (NumberUtil.sub(qty, toQtyQc).compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(QC_NOT_ENOUGHE);
        }
        if (NumberUtil.add(fmQtyAlloc, toQtyAlloc).compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(QTYALLOC_NOT_ENOUGH);
        }
        if (NumberUtil.add(fmQtyHold, toQtyHold).compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(QTYHOLD_NOT_ENOUGHE);
        }
        if (NumberUtil.add(fmQc, toQtyQc).compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(QC_NOT_ENOUGHE);
        }
        if (NumberUtil.add(fmQtyWaitMove, toQtyWaitMove).compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(WAIT_MOVE_NOT_ENOUGHE);
        }
        if (NumberUtil.add(fmQtyWaitDamage, toQtyDamage).compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(WAIT_DAMAGE_NOT_ENOUGHE);
        }
        if (NumberUtil.add(qty, toQty).compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(AVAILABLEQTY_NOT_ENOUGH_STRING, qty.toBigInteger(), toQty.abs().intValue());
        }
    }

    /**
     * 添加 库存交易记录
     */
    protected void addStockTransaction(StockTransactionBO stockTransactionBO,InvLotLocQtyBO invLotLocQty) {
        StockTransaction stockTransaction = new StockTransaction();
        if (ObjectUtil.isNull(stockTransactionBO.getFmLocQty()) && ObjectUtil.isNull(stockTransactionBO.getTotalQtyBO())) {
            log.info("【执行库存】交易记录全部参数为空，忽略操作");
            return;
        }
        stockTransaction.setWarehouseId(stockTransactionBO.getWarehouseId());
        stockTransaction.setOrderNo(stockTransactionBO.getOrderNo());
        stockTransaction.setWarehouseName(stockTransactionBO.getWarehouseName());
        if(StringUtil.isBlank(stockTransactionBO.getTransactionNo())){
            stockTransaction.setTransactionNo(numberGenerator.nextValue(IdRuleConstant.STOCK_TRANSACTION));
        }else{
            stockTransaction.setTransactionNo(stockTransactionBO.getTransactionNo());
        }
        stockTransaction.setTransactionType(stockTransactionBO.getTransactionType());
        //处理类型
        if(TRAN_QC_OUT_EDT_SAVE.equals(stockTransaction.getTransactionType())){
            stockTransaction.setTransactionType(TRAN_QC_OUT_CRT);
        }
        if (ObjectUtil.isNotNull(stockTransactionBO.getFmLocQty())) {
            stockTransaction.setFmOwnerId(stockTransactionBO.getFmLocQty().getOwnerId());
            stockTransaction.setFmOwnerName(stockTransactionBO.getFmLocQty().getOwnerName());
            stockTransaction.setFmSkuId(stockTransactionBO.getFmLocQty().getSkuId());
            stockTransaction.setFmSkuName(stockTransactionBO.getFmLocQty().getSkuName());
            stockTransaction.setFmLocId(stockTransactionBO.getFmLocQty().getLocId());
            stockTransaction.setFmLocCode(stockTransactionBO.getFmLocQty().getLocCode());
            stockTransaction.setFmLotNum(stockTransactionBO.getFmLocQty().getLotNum());
            stockTransaction.setFmPalletNum(stockTransactionBO.getFmLocQty().getPalletNum());
            stockTransaction.setWarehouseId(stockTransactionBO.getFmLocQty().getWarehouseId());
            stockTransaction.setWarehouseName(stockTransactionBO.getFmLocQty().getWarehouseName());
            stockTransaction.setFmQtyEaOp(stockTransactionBO.getFmLocQty().getUpdateNum());
            stockTransaction.setFmQtyEaAfter(NumberUtil.add(invLotLocQty.getQtyEaBefore(),stockTransactionBO.getFmLocQty().getUpdateNum()));
            stockTransaction.setFmQtyEaBefore(invLotLocQty.getQtyEaBefore());
        }
        if (ObjectUtil.isNotNull(stockTransactionBO.getTotalQtyBO())) {
            stockTransaction.setToOwnerId(stockTransactionBO.getTotalQtyBO().getOwnerId());
            stockTransaction.setToOwnerName(stockTransactionBO.getTotalQtyBO().getOwnerName());
            stockTransaction.setToSkuId(stockTransactionBO.getTotalQtyBO().getSkuId());
            stockTransaction.setToOwnerName(stockTransactionBO.getTotalQtyBO().getOwnerName());
            stockTransaction.setToSkuName(stockTransactionBO.getTotalQtyBO().getSkuName());
            stockTransaction.setToLocCode(stockTransactionBO.getTotalQtyBO().getLocCode());
            stockTransaction.setToLocId(stockTransactionBO.getTotalQtyBO().getLocId());
            stockTransaction.setToLotNum(stockTransactionBO.getTotalQtyBO().getLotNum());
            stockTransaction.setToPalletNum(stockTransactionBO.getTotalQtyBO().getPalletNum());
            stockTransaction.setToWarehouseId(stockTransactionBO.getTotalQtyBO().getWarehouseId());
            stockTransaction.setToWarehouseName(stockTransactionBO.getTotalQtyBO().getWarehouseName());
            stockTransaction.setToQtyEaOp(stockTransactionBO.getTotalQtyBO().getUpdateNum());
            stockTransaction.setToQtyEaAfter(NumberUtil.add(invLotLocQty.getQtyEaBefore(),stockTransactionBO.getFmLocQty().getUpdateNum()));
            stockTransaction.setToQtyEaBefore(invLotLocQty.getQtyEaBefore());
        }
        stockTransaction.setSnStr(CollUtil.join(invLotLocQty.getSkuSn(),StringPool.COMMA));
        stockTransactionService.preSave(stockTransaction);
        stockTransactionService.insert(stockTransaction);
    }

    /**
     * 执行库存
     *
     * @param invLotLocQty 库存参数
     */
    public List<String> run(InvLotLocQtyBO invLotLocQty) {
        log.info("【库存执行】库存执行开始。。。执行参数：{}", JsonUtil.toJson(invLotLocQty));
        List<String> rtnList = Lists.newArrayList();
        //子类校验，并添加个性化值填充
        this.checkInit(invLotLocQty);
        if (invLotLocQty.getTransactionType().equals(TransactionType.TRAN_FREE) || invLotLocQty.getTransactionType().equals(TransactionType.TRAN_CR_FREE) || invLotLocQty.getTransactionType().equals(TransactionType.TRAN_CAD) || invLotLocQty.getTransactionType().equals(TransactionType.TRAN_AD)) {
            log.info("【库存执行】冻结逻辑处理、调整单逻辑处理");
        } else {
            this.checkViolations(invLotLocQty);
            //父类校验并添加初始化值
            this.checkInitBO(invLotLocQty);
        }
        log.info("--》容器号：{} ，批次号：{}", invLotLocQty.getPalletNum(), invLotLocQty.getLotNum());
        List<InvLotLoc> list = compileInvLotLoc(invLotLocQty);
        if (CollUtil.isNotEmpty(list)) {
            for (InvLotLoc invLotLoc : list) {
                this.addInvLotLoc(invLotLoc,invLotLocQty);
                this.delete(invLotLoc, invLotLocQty.getTransactionType());
            }
        } else {
            log.warn("【执行库存】lot_loc表数据为空");
        }
        List<StockTransactionBO> stockTransactionList = this.compileStockTransaction(invLotLocQty);
        if (CollUtil.isNotEmpty(stockTransactionList)) {
            stockTransactionList.forEach(obj->{
                addStockTransaction(obj,invLotLocQty);
            });
        } else {
            log.info("【执行库存】组装的交易记录为空，不执行");
        }
        // 库存预警
        invWarningService.messageSave(invLotLocQty);
        //记录序列号库存明细
        stockSkuSnService.sn(invLotLocQty);
        //记录日志 序列号追溯
        stockSkuSnService.snTraceBackRecord(invLotLocQty);
        log.info("【库存执行完成】。。。执行action：{}", invLotLocQty.getTransactionType());
        return rtnList;
    }

    protected abstract void checkInit(InvLotLocQtyBO invLotLocQty);

    /**
     * 组装库存 实体
     */
    protected abstract List<InvLotLoc> compileInvLotLoc(InvLotLocQtyBO invLotLocQtyBO);

    /**
     * 组装库存交易记录表
     */
    protected abstract List<StockTransactionBO> compileStockTransaction(InvLotLocQtyBO invLotLocQtyBO);

    /**
     * 清理数据
     */
    protected void delete(InvLotLoc invLotLoc, String transactionType) {
    }

    /**
     * 执行类型 action
     *
     * @return
     */
    public abstract List<String> actionType();

    protected InvLotLoc invLotLoc(InvLotLoc invLotLoc) {
        log.info("查询条件：{}", JSONUtil.toJsonStr(invLotLoc));
        Long locId = invLotLoc.getLocId();
        String lotNum = invLotLoc.getLotNum();
        String palletNum = invLotLoc.getPalletNum();
        Long warehouseId = invLotLoc.getWarehouseId();
        Long ownerId = invLotLoc.getOwnerId();
        return invLotLocService.getByLotNumAndLocIdAndPalletNumCr(lotNum, locId, palletNum, warehouseId, ownerId);
    }

    protected InvLotLoc invLotLoc(InvLotLocQtyBO invLotLoc) {
        log.info("查询条件：{}", JSONUtil.toJsonStr(invLotLoc));
        Long locId = invLotLoc.getLocId();
        String lotNum = invLotLoc.getLotNum();
        String palletNum = invLotLoc.getPalletNum();
        Long warehouseId = invLotLoc.getWarehouseId();
        Long ownerId = invLotLoc.getOwnerId();
        return invLotLocService.getByLotNumAndLocIdAndPalletNumCr(lotNum, locId, palletNum, warehouseId, ownerId);
    }

    private void checkInitBO(InvLotLocQtyBO invLotLocQtyBO) {
        this.checkViolations(invLotLocQtyBO);
        //设置初始化值
        Sku sku = skuService.findById(invLotLocQtyBO.getSkuId());
        if (ObjectUtil.isNull(sku)) {
            throw new ServiceException(SKU_FIND_IS_NOT_FOUND);
        }
        Owner owner = ownerService.findById(invLotLocQtyBO.getOwnerId());
        if (ObjectUtil.isNull(owner)) {
            throw new ServiceException(OWNER_FIND_IS_NOT_FOUND);
        }
        Warehouse warehouse = warehouseService.findById(invLotLocQtyBO.getWarehouseId());
        if (ObjectUtil.isNull(warehouse)) {
            throw new ServiceException(WAREHOUSE_FIND_IS_NOT_FOUND);
        }
        if (ObjectUtil.isNull(invLotLocQtyBO.getLocCode()) && ObjectUtil.isNotNull(invLotLocQtyBO.getLocId())) {
            WarehouseLoc warehouseLoc = warehouseLocService.findById(invLotLocQtyBO.getLocId());
            if (ObjectUtil.isNull(warehouseLoc)) {
                throw new ServiceException(LOCCODE_NOT_FOUND_ISNULL);
            }
            invLotLocQtyBO.setLocCode(warehouseLoc.getLocCode());
        }
        if (ObjectUtil.isNull(invLotLocQtyBO.getToLocCode()) && ObjectUtil.isNotNull(invLotLocQtyBO.getToLocId())) {
            WarehouseLoc warehouseLoc = warehouseLocService.findById(invLotLocQtyBO.getToLocId());
            invLotLocQtyBO.setToLocCode(warehouseLoc.getLocCode());
        }
        if(ObjectUtil.isNotNull(invLotLocQtyBO.getToOwnerId()) && StrUtil.isBlank(invLotLocQtyBO.getToOwnerName())) {
            owner = ownerService.findById(invLotLocQtyBO.getOwnerId());
            invLotLocQtyBO.setToOwnerName(owner.getOwnerName());
        }
        if(ObjectUtil.isNotNull(invLotLocQtyBO.getToWarehouseId()) && StrUtil.isBlank(invLotLocQtyBO.getToWarehouseName())) {
            warehouse = warehouseService.findById(invLotLocQtyBO.getToWarehouseId());
            invLotLocQtyBO.setToWarehouseName(warehouse.getWarehouseName());
        }
        invLotLocQtyBO.setWhetherSerialController(sku.getWhetherSerialController());
        invLotLocQtyBO.setSkuCode(sku.getSkuCode());
        invLotLocQtyBO.setSkuName(sku.getSkuName());
        invLotLocQtyBO.setOwnerCode(owner.getOwnerCode());
        invLotLocQtyBO.setOwnerName(owner.getOwnerName());
        invLotLocQtyBO.setWarehouseCode(warehouse.getWarehouseCode());
        invLotLocQtyBO.setWarehouseName(warehouse.getWarehouseName());
        if (Convert.toBigDecimal(invLotLocQtyBO.getUpdateNum()).compareTo(new BigDecimal(0)) <= 0) {
            throw new ServiceException(UPDATE_NUM_IS_GT);
        }
        if (ObjectUtil.isAllEmpty(invLotLocQtyBO.getLocId(), invLotLocQtyBO.getLotNum(), invLotLocQtyBO.getPalletNum(), invLotLocQtyBO.getWarehouseId(), invLotLocQtyBO.getOwnerId())) {
            throw new ServiceException(PP_LOC_PALLETNUM_NOT_NULL);
        }
    }

    /**
     * 更新库存记录
     */
    protected void addInvLotLoc(InvLotLoc entity,InvLotLocQtyBO invLotLocQtyBO) {
        String locCode = entity.getLocCode();
        String lotNum = entity.getLotNum();
        String palletNum = entity.getPalletNum();
        InvLotLoc invLotLoc = this.invLotLoc(entity);
        BigDecimal toQty = Convert.toBigDecimal(entity.getQty(), BigDecimal.ZERO);
        BigDecimal toQtyAlloc = Convert.toBigDecimal(entity.getQtyAlloc(), BigDecimal.ZERO);
        BigDecimal toQtyHold = Convert.toBigDecimal(entity.getQtyHold(), BigDecimal.ZERO);
        BigDecimal toQtyQc = Convert.toBigDecimal(entity.getQtyQc(), BigDecimal.ZERO);
        BigDecimal toQtyPk = Convert.toBigDecimal(entity.getQtyPk(), BigDecimal.ZERO);
        BigDecimal toQtyWaitMv = Convert.toBigDecimal(entity.getQtyWaitMove(), BigDecimal.ZERO);
        BigDecimal toQtyDamage = Convert.toBigDecimal(entity.getQtyWaitDamage(), BigDecimal.ZERO);
        log.info("原始数据={}", JSONUtil.toJsonStr(invLotLoc));
        if (EmptyUtil.isNotEmpty(invLotLoc)) {
            List<BigDecimal> bigDecimals = Lists.newArrayList(toQty,toQtyAlloc,toQtyHold,toQtyQc,toQtyPk,toQtyWaitMv);
            //真实可用库存
            BigDecimal qty = getAvailableAllocQty(invLotLoc,bigDecimals);
            invLotLocQtyBO.setAvailableQty(qty);
            invLotLocQtyBO.setQtyEaBefore(invLotLoc.getQty());
            invLotLocQtyBO.setQtyPackOp(entity.getQty());
            log.info("可用库存数：{}", qty);
            // 更新库存条件类
            boolean isUpdateQty = false;
            InvLotLocUpdateQtyCondition condition = new InvLotLocUpdateQtyCondition();
            //批次库存主键
            condition.setId(invLotLoc.getId());
            //版本号
            condition.setRecVer(EmptyUtil.isNotEmpty(invLotLoc.getRecVer()) ? invLotLoc.getRecVer() : 0);
            //扣减拣货数
            if (toQtyPk.abs().compareTo(BigDecimal.ZERO) > 0) {
                // 更新库存
                isUpdateQty = true;
                condition.setUpdateQtyPk(entity.getQtyPk());
                //拣货的时候，防止库存双扣，真实库存 + 分配库存 导致库存双扣验证不通过，因为分配数会减掉库存，这里要加上拣货的避免双扣后再去判断
                qty = qty.add(invLotLoc.getQtyAlloc());
            }
            //库存冻结，如果是扣减，不判断
            if (toQtyHold.abs().compareTo(BigDecimal.ZERO) > 0) {
                // 更新库存
                isUpdateQty = true;
                condition.setUpdateQtyHold(entity.getQtyHold());
                qty = qty.add(toQtyHold.abs());
            }
            //扣减待移动数
            if (toQtyWaitMv.abs().compareTo(BigDecimal.ZERO) > 0) {
                // 更新库存
                isUpdateQty = true;
                condition.setUpdateQtyWaitMove(entity.getQtyWaitMove());
                qty = qty.add(toQtyWaitMv.abs());
            }
            if(toQtyDamage.abs().compareTo(BigDecimal.ZERO) > 0) {
                // 更新库存
                isUpdateQty = true;
                condition.setUpdateQtyWaitDamage(entity.getQtyWaitDamage());
                qty = qty.add(toQtyDamage.abs());
            }
            //防止库存变成负数校验
            extracted(qty, toQty, toQtyAlloc,
                    toQtyHold, toQtyQc,
                    invLotLoc.getQtyAlloc(), invLotLoc.getQtyHold(),
                    invLotLoc.getQtyQc(),
                    invLotLoc.getQtyWaitMove(),toQtyWaitMv
                    ,invLotLoc.getQtyWaitDamage(),toQtyDamage);
            //扣减库存
            if (toQty.abs().compareTo(BigDecimal.ZERO) > 0) {
                // 更新库存
                isUpdateQty = true;
                condition.setUpdateQty(entity.getQty());
            }
            //扣减分配数
            if (toQtyAlloc.abs().compareTo(BigDecimal.ZERO) > 0) {
                // 更新库存
                isUpdateQty = true;
                condition.setUpdateQtyAlloc(entity.getQtyAlloc());
            }
            //扣减质检数
            if (toQtyQc.abs().compareTo(BigDecimal.ZERO) > 0) {
                // 更新库存
                isUpdateQty = true;
                condition.setUpdateQtyQc(entity.getQtyQc());
            }
            // 开始执行更新库存
            if (isUpdateQty) {
                log.info("【库存更新】---> {} {} {} {} {}", toQty, toQtyAlloc, toQtyQc, entity.getLotNum(), entity.getPalletNum());
                int count = invLotLocService.getDao().updateQtyByCondition(condition);
                if (count <= 0) {
                    throw new ServiceException(VALILD_INVLOTLOC_HAS_EXPIRED, lotNum, locCode, palletNum);
                }
            }
        } else {
            log.info("【库存新增】---> {} {} {} {} {}", toQty, toQtyAlloc, toQtyQc, entity.getLotNum(), entity.getPalletNum());
            if (toQty.compareTo(BigDecimal.ZERO) <= 0 || (toQtyAlloc.compareTo(BigDecimal.ZERO) > 0 || toQtyQc.compareTo(BigDecimal.ZERO) > 0)) {
                throw new ServiceException(AVAILABLEQTY_NOT_ENOUGH, lotNum, locCode, palletNum);
            }
            //新增数据的时候，要验证容器号是否存在  和产品沟通，暂时去掉这块业务
            //this.checkInvTraceIdDiffLoc(entity.getPalletNum(), entity.getWarehouseId());
            entity.setRecVer(0L);
            invLotLocService.saveOrUpdate(entity);
        }
    }

    /**
     * 库存校验：混商品混批次库位容量校验。
     *
     * @param invLotLocQtyBO 参数
     */
    public void checkInvMixSkuMixLot(InvLotLocQtyBO invLotLocQtyBO) {
        Long locId = invLotLocQtyBO.getLocId();
        Long skuId = invLotLocQtyBO.getSkuId();
        String lotNum = invLotLocQtyBO.getLotNum();
        Long warehouseId = invLotLocQtyBO.getWarehouseId();
        WarehouseLocCondition warehouseLocCondition = new WarehouseLocCondition();
        warehouseLocCondition.setId(locId);
        warehouseLocCondition.setWarehouseId(warehouseId);
        WarehouseLoc warehouseLoc = warehouseLocService.findFirst(WarehouseLoc.class, warehouseLocCondition);
        if (ObjectUtil.isNull(warehouseLoc)) {
            throw new ServiceException(LOCCODE_NOT_FOUND_ISNULL);
        }
        String locCode = warehouseLoc.getLocCode();
        if (WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode().equals(locCode) || WarehouseLocUseTypeEnum.TALLYING_STATION.getCode().equals(locCode)) {
            log.info("混商品排除当前库位：{}", locCode);
            return;
        }
        List<InvLotLoc> invLotLocList = invLotLocService.getSkuNumByLocId(locId, warehouseId);
        List<Long> skuList = invLotLocList.stream().map(InvLotLoc::getSkuId).distinct().toList();
        //最大混商品数
        Long maxMixSku = Convert.toLong(warehouseLoc.getMaxMixSku(), 0L);
        //如果是费混商品逻辑，
        if (StatusConstant.NO.equals(warehouseLoc.getIsMixSku()) && CollUtil.isNotEmpty(skuList) && !skuList.contains(skuId)) {
            throw new ServiceException(LOCCODE_NOT_ALLOW_MIXSKU, locCode);
        }
        if (StatusConstant.YES.equals(warehouseLoc.getIsMixSku()) && maxMixSku > 0 && skuList.size() >= maxMixSku) {
            //库位超过最大的混放商品数
            throw new ServiceException(LOCCODE_ISFULL_FOR_MIXSKU, locCode);
        }
        //最大混批次数
        Long maxMixLot = Convert.toLong(warehouseLoc.getMaxMixLot(), 0L);
        //混批次，验证同商品下的批次混放
        List<String> skuLotNumList = invLotLocList.stream().filter(item -> item.getSkuId().equals(skuId)).map(InvLotLoc::getLotNum).distinct().toList();
        if (StatusConstant.NO.equals(warehouseLoc.getIsMixLot()) && CollUtil.isNotEmpty(skuLotNumList) && !skuLotNumList.contains(lotNum)) {
            //库位不允许混放批次
            throw new ServiceException(LOCCODE_NOT_ALLOW_MIXPP, locCode);
        }
        if (StatusConstant.YES.equals(warehouseLoc.getIsMixLot()) && maxMixLot >= 0 && skuLotNumList.size() >= maxMixLot) {
            //库位超过最大的混放批次数
            throw new ServiceException(LOCCODE_MORETHAN_MAX_MIXPP, locCode);
        }
    }

    /**
     * 获取真实库存数
     */
    protected InvLotLocQuery getAvailableAllocQtyFrozen(AvailableAllocQtyQuery qtyQuery) {
        this.checkViolations(qtyQuery);
        InvLotLoc invLotLoc = invLotLocService.getByLotNumAndLocIdAndPalletNumCr(qtyQuery.getLotNum(), qtyQuery.getLocId(), qtyQuery.getPalletNum(), qtyQuery.getWarehouseId(), qtyQuery.getOwnerId());
        if (null != invLotLoc) {
            BigDecimal qty = Convert.toBigDecimal(invLotLoc.getQty(), BigDecimal.ZERO);
            //冻结数
            BigDecimal qtyHold = Convert.toBigDecimal(invLotLoc.getQtyHold(), BigDecimal.ZERO);
            //分配数
            BigDecimal qtyAlloc = Convert.toBigDecimal(invLotLoc.getQtyAlloc(), BigDecimal.ZERO);
            //待质检数
            BigDecimal qtyQc = Convert.toBigDecimal(invLotLoc.getQtyQc(), BigDecimal.ZERO);
            //待移动数
            BigDecimal qtyWaitMove = Convert.toBigDecimal(invLotLoc.getQtyWaitMove(), BigDecimal.ZERO);
            //可用库存数
            BigDecimal availableQtyResult = NumberUtil.sub(qty, qtyHold, qtyAlloc, qtyQc,qtyWaitMove);
            if (availableQtyResult.compareTo(BigDecimal.ZERO) < 0) {
                //库存数量错误
                throw new ServiceException(AVAILABLEQTY_NOT_ENOUGH, invLotLoc.getLotNum(), invLotLoc.getLocCode(), invLotLoc.getPalletNum());
            }
            InvLotLocQuery query = new InvLotLocQuery();
            query.setQty(qty);
            query.setQtyHold(qtyHold);
            query.setQtyAlloc(qtyAlloc);
            query.setQtyQc(qtyQc);
            query.setAvailableQtyResult(availableQtyResult);
            return query;
        }
        throw new ServiceException(NOT_FOUND_LOTNUM_AND_PALLETNUM, qtyQuery.getLotNum(), qtyQuery.getLocCode(), qtyQuery.getPalletNum());
    }

    /**
     * 获取冻结/非冻结真实库存数
     */
    protected BigDecimal getAvailableAllocQty(AvailableAllocQtyQuery qtyQuery) {
        this.checkViolations(qtyQuery);
        InvLotLoc invLotLoc = invLotLocService.getByLotNumAndLocIdAndPalletNumCr(qtyQuery.getLotNum(), qtyQuery.getLocId(), qtyQuery.getPalletNum(), qtyQuery.getWarehouseId(), qtyQuery.getOwnerId());
        if (null != invLotLoc) {
            BigDecimal qty = Convert.toBigDecimal(invLotLoc.getQty(), BigDecimal.ZERO);
            //冻结数
            BigDecimal qtyHold = Convert.toBigDecimal(invLotLoc.getQtyHold(), BigDecimal.ZERO);
            //分配数
            BigDecimal qtyAlloc = Convert.toBigDecimal(invLotLoc.getQtyAlloc(), BigDecimal.ZERO);
            //待质检数
            BigDecimal qtyQc = Convert.toBigDecimal(invLotLoc.getQtyQc(), BigDecimal.ZERO);
            //待移动数
            BigDecimal qtyWaitMove = Convert.toBigDecimal(invLotLoc.getQtyWaitMove(), BigDecimal.ZERO);
            //可用库存数
            BigDecimal availableQtyResult = NumberUtil.sub(qty, qtyHold, qtyAlloc, qtyQc,qtyWaitMove);
            if (availableQtyResult.compareTo(BigDecimal.ZERO) < 0) {
                //库存数量错误
                throw new ServiceException(AVAILABLEQTY_NOT_ENOUGH, invLotLoc.getLotNum(), invLotLoc.getLocCode(), invLotLoc.getPalletNum());
            }
            return availableQtyResult;
        }
        throw new ServiceException(NOT_FOUND_LOTNUM_AND_PALLETNUM, qtyQuery.getLotNum(), qtyQuery.getLocCode(), qtyQuery.getPalletNum());
    }

    protected BigDecimal getAvailableAllocQty(InvLotLoc invLotLoc,List<BigDecimal> bigDecimals) {
        if (null != invLotLoc) {
            boolean allNegative = bigDecimals.stream()
                    .allMatch(n -> n.compareTo(BigDecimal.ZERO) < 0);
            BigDecimal qty = Convert.toBigDecimal(invLotLoc.getQty(), BigDecimal.ZERO);
            //总库存 + 拣货库存都为0的情况下，才判断
            if (qty.compareTo(BigDecimal.ZERO) <= 0 && invLotLoc.getQtyPk().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException(AVAILABLEQTY_NOT_ENOUGH, invLotLoc.getLotNum(), invLotLoc.getLocCode(), invLotLoc.getPalletNum());
            }
            //冻结数
            BigDecimal qtyHold = Convert.toBigDecimal(invLotLoc.getQtyHold(), BigDecimal.ZERO);
            //分配数
            BigDecimal qtyAlloc = Convert.toBigDecimal(invLotLoc.getQtyAlloc(), BigDecimal.ZERO);
            //待质检数
            BigDecimal qtyQc = Convert.toBigDecimal(invLotLoc.getQtyQc(), BigDecimal.ZERO);
            //待移动数
            BigDecimal qtyWaitMove = Convert.toBigDecimal(invLotLoc.getQtyWaitMove(), BigDecimal.ZERO);
            //待报损数
            BigDecimal qtyWaitDamage = Convert.toBigDecimal(invLotLoc.getQtyWaitDamage(), BigDecimal.ZERO);
            //可用库存数
            BigDecimal availableQtyResult = NumberUtil.sub(qty, qtyHold, qtyAlloc, qtyQc, qtyWaitMove, qtyWaitDamage);
            if (availableQtyResult.compareTo(BigDecimal.ZERO) < 0 && allNegative) {
                //库存数量错误
                throw new ServiceException(AVAILABLEQTY_NOT_ENOUGH, invLotLoc.getLotNum(), invLotLoc.getLocCode(), invLotLoc.getPalletNum());
            }
            return availableQtyResult;
        }
        return BigDecimal.ZERO;
    }


    /**
     * 库存校验：同一个容器号不允许出现在不同库位。  增加库位逻辑
     */
    protected void checkInvTraceIdDiffLoc(String palletNum, Long warehouseId) {
        if (IdRuleConstant.TRACE_ID.equals(palletNum)) {
            return;
        }
        List<InvLotLoc> list = invLotLocService.getByPalletNum(palletNum, warehouseId);
        log.info("容器号判断：{}", JSONUtil.toJsonStr(list));
        if (CollUtil.isNotEmpty(list.stream().filter(item -> Convert.toBigDecimal(item.getQty(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0).toList())) {
            throw new ServiceException(ONE_PALLETNUM_NOT_ALLOW_IN_DIFFERENT_LOC, palletNum);
        }
    }

    /**
     * 获取批次号
     *
     * @param invLotAttQuery 批次号查询
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    protected String getInvLotNum(InvLotAttQuery invLotAttQuery) {
        this.checkViolations(invLotAttQuery);
        Long ownerId = invLotAttQuery.getOwnerId();
        Long skuId = invLotAttQuery.getSkuId();
        Long warehouseId = invLotAttQuery.getWarehouseId();
        InvLocAttByLotNumCondition invLocAttCondition = new InvLocAttByLotNumCondition();
        invLocAttCondition.setOwnerId(ownerId);
        invLocAttCondition.setSkuId(skuId);
        invLocAttCondition.setWarehouseId(warehouseId);
        //批次字段
        String LOT_ATTR = "lotAtt01,lotAtt02,lotAtt03,lotAtt04,lotAtt05,lotAtt06,lotAtt07,lotAtt08,lotAtt09,lotAtt10,lotAtt11,lotAtt12";
        Arrays.stream(LOT_ATTR.split(StringPool.COMMA)).forEach(key -> {
            Object value = MapUtil.get(invLotAttQuery.getAttMap(), key, Object.class);
            if (ObjUtil.isNotEmpty(value)) {
                ReflectUtil.setFieldValue(invLocAttCondition, key, value);
            }
        });
        List<InvLotAtt> invLotAttList = invLotAttService.getDao().findInvLotAtt(invLocAttCondition);
        if (CollUtil.isNotEmpty(invLotAttList) && invLotAttList.size() > 1) {
            throw new ServiceException(WAREHOUSE_LOT_ERROR);
        } else if (invLotAttList != null && invLotAttList.size() == 1) {
            String lotNum = invLotAttList.getFirst().getLotNum();
            AsnReceiveFile receiveFile = new AsnReceiveFile();
            receiveFile.setBusinessId(lotNum);
            Boolean duplicate = asnReceiveFileService.isDuplicate(receiveFile, "businessId");
            if (BooleanUtil.isFalse(duplicate)) {
                // 入库收货批次号条形码生成
                asnReceiveFileService.save(lotNum);
            }
            return lotNum;
        } else {
            // 新增库存批次记录
            InvLotAtt invLotAttModel = new InvLotAtt();
            invLotAttModel.setOwnerId(ownerId);
            Arrays.stream(LOT_ATTR.split(StringPool.COMMA)).forEach(key -> {
                Object value = MapUtil.get(invLotAttQuery.getAttMap(), key, Object.class);
                if (ObjUtil.isNotEmpty(value)) {
                    ReflectUtil.setFieldValue(invLotAttModel, key, value);
                }
            });
            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andEqual(Sku::getId, skuId);
            Sku sku = skuService.findFirst(conditionRule);
            if (ObjectUtil.isNull(sku)) {
                throw new ServiceException(SKU_FIND_IS_NOT_FOUND);
            }
            Owner owner = ownerService.findById(invLotAttQuery.getOwnerId());
            if (ObjectUtil.isNull(owner)) {
                throw new ServiceException(OWNER_FIND_IS_NOT_FOUND);
            }
            Warehouse warehouse = warehouseService.findById(invLotAttQuery.getWarehouseId());
            if (ObjectUtil.isNull(warehouse)) {
                throw new ServiceException(WAREHOUSE_FIND_IS_NOT_FOUND);
            }
            invLotAttModel.setOwnerName(owner.getOwnerName());
            invLotAttModel.setSkuId(skuId);
            invLotAttModel.setSkuName(sku.getSkuName());
            invLotAttModel.setWarehouseId(warehouse.getId());
            invLotAttModel.setWarehouseName(warehouse.getWarehouseName());
            invLotAttModel.setLotNum(numberGenerator.nextValue(IdRuleConstant.LOT_NUM));
            invLotAttService.preSave(invLotAttModel);
            // 入库收货批次号条形码生成
            asnReceiveFileService.save(invLotAttModel.getLotNum());

            invLotAttService.insert(invLotAttModel);
            return invLotAttModel.getLotNum();
        }
    }

    /**
     * 组装数据
     *
     * @param invLotLocQtyBO 原始参数
     * @param flag           是否负数  true 标识负数 false 标识正数
     * @return
     */
    protected InvLotLoc getInvLotloc(InvLotLocQtyBO invLotLocQtyBO, boolean flag) {
        InvLotLoc invLotLoc = new InvLotLoc().setLocId(invLotLocQtyBO.getLocId())
                .setLocCode(invLotLocQtyBO.getLocCode())
                .setLotNum(invLotLocQtyBO.getLotNum())
                .setWarehouseId(invLotLocQtyBO.getWarehouseId())
                .setWarehouseName(invLotLocQtyBO.getWarehouseName())
                .setQty(invLotLocQtyBO.getUpdateNum())
                .setOwnerId(invLotLocQtyBO.getOwnerId())
                .setOwnerName(invLotLocQtyBO.getOwnerName())
                .setSkuId(invLotLocQtyBO.getSkuId())
                .setSkuName(invLotLocQtyBO.getSkuName())
                .setPalletNum(invLotLocQtyBO.getPalletNum());
        //数量赋值
        //冻结数
        invLotLoc.setQtyHold(BigDecimal.ZERO);
        //分配数
        invLotLoc.setQtyAlloc(BigDecimal.ZERO);
        //拣货数
        invLotLoc.setQtyPk(BigDecimal.ZERO);
        //质检数
        invLotLoc.setQtyQc(BigDecimal.ZERO);
        //待移动数
        invLotLoc.setQtyWaitMove(BigDecimal.ZERO);
        if (flag) {
            invLotLoc.setQty(invLotLocQtyBO.getUpdateNum().negate());
        }
        return invLotLoc;
    }

    /**
     * 组装bo
     * 默认和to的数据一致
     *
     * @param invLotLocQtyBO
     * @return
     */
    protected InvLotLocQtyBO getINvLotLocQtyFm(InvLotLocQtyBO invLotLocQtyBO) {
        return new InvLotLocQtyBO()
                .setLocId(invLotLocQtyBO.getLocId())
                .setLocCode(invLotLocQtyBO.getLocCode())
                .setWarehouseId(invLotLocQtyBO.getWarehouseId())
                .setWarehouseName(invLotLocQtyBO.getWarehouseName())
                .setWarehouseCode(invLotLocQtyBO.getWarehouseCode())
                .setLotNum(invLotLocQtyBO.getLotNum())
                .setSkuId(invLotLocQtyBO.getSkuId())
                .setSkuCode(invLotLocQtyBO.getSkuCode())
                .setSkuName(invLotLocQtyBO.getSkuName())
                .setOwnerId(invLotLocQtyBO.getOwnerId())
                .setOwnerName(invLotLocQtyBO.getOwnerName())
                .setOwnerCode(invLotLocQtyBO.getOwnerCode())
                .setPalletNum(invLotLocQtyBO.getPalletNum())
                .setUpdateNum(invLotLocQtyBO.getUpdateNum())
                .setSkuSn(invLotLocQtyBO.getSkuSn())
                .setOrderNo(invLotLocQtyBO.getOrderNo())
                .setQtyPackOp(invLotLocQtyBO.getUpdateNum())
                .setAvailableQty(invLotLocQtyBO.getAvailableQty())
                .setQtyEaBefore(invLotLocQtyBO.getQtyEaBefore())
                .setToWarehouseId(ObjUtil.isNotNull(invLotLocQtyBO.getToWarehouseId())?invLotLocQtyBO.getToWarehouseId():invLotLocQtyBO.getWarehouseId())
                .setToWarehouseName(ObjUtil.isNotNull(invLotLocQtyBO.getToWarehouseId())?invLotLocQtyBO.getToWarehouseName():invLotLocQtyBO.getWarehouseName())
                ;
    }

    /**
     * 默认和fm的数据一致
     *
     * @param invLotLocQtyBO
     * @return
     */
    protected InvLotLocQtyBO getINvLotLocQtyTo(InvLotLocQtyBO invLotLocQtyBO) {
        return new InvLotLocQtyBO()
                .setLocId(ObjectUtil.isNull(invLotLocQtyBO.getToLocId())?invLotLocQtyBO.getLocId():invLotLocQtyBO.getToLocId())
                .setLocCode(StrUtil.isBlank(invLotLocQtyBO.getToLocCode())?invLotLocQtyBO.getLocCode():invLotLocQtyBO.getToLocCode())
                .setSkuId(invLotLocQtyBO.getSkuId())
                .setSkuCode(invLotLocQtyBO.getSkuCode())
                .setSkuName(invLotLocQtyBO.getSkuName())
                .setLotNum(StrUtil.isBlank(invLotLocQtyBO.getToLocNum())?invLotLocQtyBO.getLotNum():invLotLocQtyBO.getToLocNum())
                .setOwnerId(ObjectUtil.isNull(invLotLocQtyBO.getToOwnerId())?invLotLocQtyBO.getOwnerId():invLotLocQtyBO.getToOwnerId())
                .setOwnerName(StrUtil.isBlank(invLotLocQtyBO.getToOwnerName())?invLotLocQtyBO.getOwnerName():invLotLocQtyBO.getToOwnerName())
                .setPalletNum(invLotLocQtyBO.getToPallet().equals(TRACE_ID)?invLotLocQtyBO.getPalletNum():invLotLocQtyBO.getToPallet())
                .setUpdateNum(invLotLocQtyBO.getUpdateNum())
                .setSkuSn(invLotLocQtyBO.getSkuSn())
                .setOrderNo(invLotLocQtyBO.getOrderNo())
                .setQtyPackOp(invLotLocQtyBO.getUpdateNum())
                .setAvailableQty(invLotLocQtyBO.getAvailableQty())
                .setQtyEaBefore(invLotLocQtyBO.getQtyEaBefore())
                .setWarehouseId(ObjUtil.isNotNull(invLotLocQtyBO.getToWarehouseId())?invLotLocQtyBO.getToWarehouseId():invLotLocQtyBO.getWarehouseId())
                .setWarehouseName(ObjUtil.isNotNull(invLotLocQtyBO.getToWarehouseId())?invLotLocQtyBO.getToWarehouseName():invLotLocQtyBO.getWarehouseName())
                ;

    }

    protected void checkViolations(Object o) {
        Set<ConstraintViolation<Object>> violations = validator.validate(o);
        if (!violations.isEmpty()) {
            StringBuilder errorMessages = new StringBuilder();
            violations.forEach(violation -> errorMessages.append(violation.getMessage()).append("; "));
            log.error(errorMessages.toString());
            // 抛出 ValidationException
            throw new ServiceException(violations.stream().findFirst().get().getMessage());
        }
    }


    /**
     * 获取库存查询列表
     *
     * @param pageCondition
     * @return
     */
    @Deprecated
    public PageResult<LocListPageQuery> page(LocListPageCondition pageCondition) {
        return null;
    }

}
