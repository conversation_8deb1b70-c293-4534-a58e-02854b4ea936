package com.chinaservices.wms.module.archive.thirdParty;

import com.chinaservices.auth.module.message.domain.MessageSendItem;
import com.chinaservices.auth.module.message.feign.RemoteMessageSendService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 调用第三方消息服务
 */
@Service
public class MessageService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RemoteMessageSendService remoteMessageSendService;


    /**
     * 调用消息-发送站内信
     * @param userIds 发送用户
     * @param messageCode 模版编码
     * @param modeData 模版填充内容
     */
    public void sendMessage(List<Long> userIds, String messageCode, Map<String,String> modeData) {
        try{
            MessageSendItem messageSendItem = new MessageSendItem();
            messageSendItem.setUserIds(userIds);
            messageSendItem.setMessageCode(messageCode);
            messageSendItem.setModeData(modeData);
            remoteMessageSendService.sendNotice(messageSendItem);
        }catch (Exception e){
            logger.error("发送站内信异常:",e);
        }
    }

    /**
     * 调用消息-发送站内信
     */
    public void sendMessageList(List<MessageSendItem> list) {
        for (MessageSendItem messageSendItem : list) {
            try {
                remoteMessageSendService.sendNotice(messageSendItem);
            } catch (Exception e) {
                logger.error("发送站内信异常:", e);
            }
        }
    }


}
