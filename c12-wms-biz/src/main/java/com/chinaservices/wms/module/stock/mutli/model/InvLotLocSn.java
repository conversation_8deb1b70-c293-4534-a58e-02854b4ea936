package com.chinaservices.wms.module.stock.mutli.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;

import java.math.BigDecimal;


@Entity
@Table(name = "cs_inv_lot_loc_sn")
@Data
public class InvLotLocSn extends ModuleBaseModel {
    // 仓库 ID
    private Long warehouseId;
    // 仓库名称
    private String warehouseName;
    // 货主 ID
    private Long ownerId;
    // 货主名称
    private String ownerName;
    // 货主代码
    private String ownerCode;
    // 业务单号
    private String businessNo;
    // 批次号
    private String lotNum;
    // 商品 ID
    private Long skuId;
    // 商品代码
    private String skuCode;
    // 商品名称
    private String skuName;
    // 商品序列号
    private String skuSn;
    // 库位 ID
    private Long locId;
    // 库位代码
    private String locCode;
    // 托盘号
    private String palletNum;
    // 库存数
    private BigDecimal qty;
    //是否冻结
    private String isFrozen;
    //是否报损
    private String isLosses;

    // 箱码
    @Transient
    private String boxCode;
    // 库区ID
    @Transient
    private Long zoneId;
    // 库区名称
    @Transient
    private String zoneName;
    // 货架ID
    @Transient
    private Long shelfId;
    // 货架名称
    @Transient
    private String shelfName;
}
