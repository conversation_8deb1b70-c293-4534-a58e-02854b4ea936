package com.chinaservices.wms.module.rule.pa.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.core.enums.StatusEnum;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.enums.common.DataOperationValidatorEnum;
import com.chinaservices.wms.common.exception.RuleExceptionEnum;
import com.chinaservices.wms.common.service.CommonService;
import com.chinaservices.wms.module.basic.sku.dao.SkuDao;
import com.chinaservices.wms.module.init.domain.InitCondition;
import com.chinaservices.wms.module.rule.pa.dao.RulePaTaskDao;
import com.chinaservices.wms.module.rule.pa.dao.RulePaTaskDetailDao;
import com.chinaservices.wms.module.rule.pa.domain.RulePaTaskDetailItem;
import com.chinaservices.wms.module.rule.pa.domain.RulePaTaskItem;
import com.chinaservices.wms.module.rule.pa.domain.RulePaTaskPageCondition;
import com.chinaservices.wms.module.rule.pa.domain.RulePaTaskQuery;
import com.chinaservices.wms.module.rule.pa.model.RulePaTask;
import com.chinaservices.wms.module.rule.pa.model.RulePaTaskDetail;
import com.chinaservices.wms.module.validator.DataOperationValidationEvent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 规则策略 -- 上架规则
 *
 * <AUTHOR>
 */
@Service
public class RulePaTaskService extends ModuleBaseServiceSupport<RulePaTaskDao, RulePaTask, Long> {


    @Autowired
    private RulePaTaskDetailService rulePaTaskDetailService;
    @Autowired
    private NumberGenerator numberGenerator;
    @Autowired
    private SkuDao skuDao;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RulePaTaskDetailDao rulePaTaskDetailDao;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 根据条件查询分页列表
     *
     * @param rulePaTaskPageCondition
     * @return
     */
    public PageResult<RulePaTaskQuery> page(RulePaTaskPageCondition rulePaTaskPageCondition) {
        PageResult<RulePaTaskQuery> page = dao.page(rulePaTaskPageCondition);
        if (!CollectionUtils.isEmpty(page.getRows())) {
            Long[] array = page.getRows().stream().map(RulePaTaskQuery::getCreator).collect(Collectors.toList()).toArray(Long[]::new);
            page.getRows().forEach(
                    row -> {
                        Map<Long, UserForOuterQuery> userMap = commonService.getUserNameByIds(array);
                        if (!EmptyUtil.isEmpty(userMap.get(row.getCreator()))) {
                            row.setCreatorName(userMap.get(row.getCreator()).getRealName());
                        }
                    }
            );
        }
        return page;
    }

    /**
     * 根据规则名称集合查询上架规则
     *
     * @param ruleNameList
     * @return
     */
    public List<RulePaTask> listByRuleName(List<String> ruleNameList) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(RulePaTask::getRuleName, ruleNameList);
        return dao.find(RulePaTask.class, conditionRule);
    }

    //规则未被商品引用的，可以删除操作，否则提示“规则已被商品使用，不允许删除

    @Transactional(rollbackFor = {Exception.class})
    public void deleteByIds(Long[] ids) {
        DataOperationValidationEvent dataOperationValidationEvent = new DataOperationValidationEvent(this, DataOperationValidatorEnum.PA_TASK_RULE.getDeleteObj(), RulePaTask.class, Arrays.asList(ids), DataOperationValidationEvent.OperationType.DELETE);
        eventPublisher.publishEvent(dataOperationValidationEvent);
        dao.delete(ids);
        ConditionRule condition = new ConditionRule();
        condition.andIn(RulePaTaskDetail::getRuleId, ids);
        rulePaTaskDetailDao.delete(condition);
    }

    public RulePaTaskItem findRulePaTaskById(Long id) {
        if (EmptyUtil.isEmpty(id)) {
            throw new ServiceException(GlobalExceptionEnum.ID_NOT_EMPTY.getMsg());
        }
        RulePaTask rulePaTask = dao.findById(id);
        if (Objects.isNull(rulePaTask)) {
            throw new ServiceException(RuleExceptionEnum.RULE_NOT_EXIST.getMsg());
        }
        RulePaTaskItem rulePaTaskItem = new RulePaTaskItem();
        BeanUtil.copyProperties(rulePaTask, rulePaTaskItem);
        List<RulePaTaskDetailItem> rulePaTaskDetailItemList = rulePaTaskDetailService.find(RulePaTaskDetailItem.class,
                new ConditionRule().andEqual(RulePaTaskDetail::getRuleId, id));
        rulePaTaskDetailItemList.forEach(item -> {
            item.setAbcExists(stringToList(item.getAbcExist()));
            item.setAbcAbsents(stringToList(item.getAbcAbsent()));
            item.setCategoryExists(stringToList(item.getCategoryExist()));
            item.setCategoryAbsents(stringToList(item.getCategoryAbsent()));
            item.setLocUseTypeExists(stringToList(item.getLocUseTypeExist()));
            item.setLocUseTypeAbsents(stringToList(item.getLocUseTypeAbsent()));
        });
        rulePaTaskItem.setRulePaTaskDetailItemList(rulePaTaskDetailItemList);
        return rulePaTaskItem;
    }


    @Transactional(rollbackFor = Exception.class)
    public void save(RulePaTaskItem item) {
        if (EmptyUtil.isEmpty(item.getId())) {
            // 1. 判断字段唯一
            this.checkIsDuplicate(item);
            item.setRuleCode(numberGenerator.nextValue(IdRuleConstant.RULE_CODE));
            dao.saveOrUpdate(item);

            List<RulePaTaskDetail> detailList = new ArrayList<>();
            item.getRulePaTaskDetailItemList().forEach(e -> {
                RulePaTaskDetail detail = saveDetail(e, new RulePaTaskDetail(), item.getId(),item.getRuleCode());
                detailList.add(detail);
            });

            rulePaTaskDetailDao.batchInsert(detailList);
        } else {
            this.edit(item);
        }
    }

    private void edit(RulePaTaskItem item) {
        // 1.主表判断是否存在
        RulePaTask header = dao.findById(item.getId());
        if (Objects.isNull(header)) {
            throw new ServiceException(RuleExceptionEnum.RULE_NOT_EXIST.getMsg());
        }
        // 2. 判断字段唯一
        if (!item.getRuleName().equals(header.getRuleName())) {
            this.checkIsDuplicate(item);
        }
        // 3. 主表数据处理
        header.setRuleName(item.getRuleName());
        header.setWarehouseId(item.getWarehouseId());
        header.setWarehouseName(item.getWarehouseName());
        header.setRemark(item.getRemark());
        dao.saveOrUpdate(header);
        // 4. 明细表数据处理
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(RulePaTaskDetail::getRuleId, item.getId());
        List<RulePaTaskDetail> detailList = rulePaTaskDetailDao.find(conditionRule);
        Map<Long, RulePaTaskDetail> detailMap = detailList.stream().collect(Collectors.toMap(RulePaTaskDetail::getId, Function.identity(), (v1, v2) -> v1));
        if (CollectionUtil.isNotEmpty(detailList)) {
            // 获取 addData 中的所有 id
            Set<Long> addDataIds = item.getRulePaTaskDetailItemList().stream()
                    .map(RulePaTaskDetailItem::getId)
                    .collect(Collectors.toSet());

            // 过滤出 list 中不在 addData 的 id 需要删除
            Long[] filteredList = detailList.stream()
                    .map(RulePaTaskDetail::getId)
                    .filter(id -> !addDataIds.contains(id))
                    .toArray(Long[]::new);
            rulePaTaskDetailDao.delete(filteredList);
        }
        // 5. 保存明细
        item.getRulePaTaskDetailItemList().forEach(e -> {
            RulePaTaskDetail detail;
            if (EmptyUtil.isEmpty(e.getId())) {
                detail = saveDetail(e, new RulePaTaskDetail(), header.getId(), header.getRuleCode());
            } else {
                detail = detailMap.get(e.getId());
                detail = saveDetail(e, detail, header.getId(), header.getRuleCode());
            }
            rulePaTaskDetailDao.saveOrUpdate(detail);
        });
    }

    private String listToString(String[] list) {
        StringBuilder stringBuilder = new StringBuilder();
        if (!EmptyUtil.isEmpty(list)) {
            for (String s : list) {
                stringBuilder.append(s).append(",");
            }
        }
        return stringBuilder.toString();
    }


    private String[] stringToList(String s) {
        if (StringUtils.isNotBlank(s)) {
            return s.split(",");
        }
        return new String[0];
    }

    /**
     * 校验规则代码是否重复
     *
     * @param item
     * @return
     */
    public void checkIsDuplicate(RulePaTaskItem item) {
        RulePaTask rule = new RulePaTask();
        BeanUtil.copyProperties(item, rule);
        Boolean duplicate = dao.isDuplicate(rule, "id", "warehouseId", "ruleName");
        if (duplicate) {
            throw new ServiceException(RuleExceptionEnum.RULE_WAREHOUSENAME_EXIST.getMsg());
        }
    }

    public RulePaTaskDetail saveDetail(RulePaTaskDetailItem item, RulePaTaskDetail detail,Long ruleId,String ruleCode) {
        BeanUtil.copyProperties(item, detail);
        detail.setRuleId(ruleId);
        detail.setRuleCode(ruleCode);
        detail.setAbcAbsent(listToString(item.getAbcAbsents()));
        detail.setAbcExist(listToString(item.getAbcExists()));
        detail.setLocUseTypeExist(listToString(item.getLocUseTypeExists()));
        detail.setLocUseTypeAbsent(listToString(item.getLocUseTypeAbsents()));
        detail.setCategoryExist(listToString(item.getCategoryExists()));
        detail.setCategoryAbsent(listToString(item.getCategoryAbsents()));
        rulePaTaskDetailService.preSave(detail);
        return detail;
    }

    /**
     * @description 初始化分配规则
     * <AUTHOR> Tang
     * @param condition 参数(公司id和租户id)
     * @date 2025-05-07 16:50
     **/
    public void initRulePaTask(InitCondition condition) {
        dao.initRulePaTask(condition);
    }

    public void enableRuleStatus(Long[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        // 过滤只有禁用的上架规则去启用
        List<RulePaTask> rulePaTasks = dao.findByIds(ids);
        if(CollectionUtil.isNotEmpty(rulePaTasks)) {
            List<RulePaTask> rulePaTaskList = rulePaTasks.stream().filter(item -> StringUtils.isNotBlank(item.getStatus()) && StatusEnum.NO.getCode().equals(item.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(rulePaTaskList)) {
                rulePaTaskList.forEach(rulePaTask -> rulePaTask.setStatus(StatusEnum.YES.getCode()));
                dao.batchUpdate(rulePaTaskList);
            }
        }
    }

    public void disableRuleStatus(Long[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }

        // 过滤只有启用的上架规则去禁用
        List<RulePaTask> rulePaTasks = dao.findByIds(ids);
        if(CollectionUtil.isNotEmpty(rulePaTasks)) {
            List<RulePaTask> rulePaTaskList = rulePaTasks.stream().filter(item -> StringUtils.isNotBlank(item.getStatus()) && StatusEnum.YES.getCode().equals(item.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(rulePaTaskList)) {
                rulePaTaskList.forEach(rulePaTask -> rulePaTask.setStatus(StatusEnum.NO.getCode()));
                dao.batchUpdate(rulePaTaskList);
            }
        }
    }
}
