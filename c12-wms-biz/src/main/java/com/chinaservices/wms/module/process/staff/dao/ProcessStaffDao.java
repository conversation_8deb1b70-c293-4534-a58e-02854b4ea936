package com.chinaservices.wms.module.process.staff.dao;

import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.sqlid.process.staff.ProcessStaffSqlId;
import com.chinaservices.wms.module.process.staff.domain.ProcessStaffPageCondition;
import com.chinaservices.wms.module.process.staff.domain.ProcessStaffPageQuery;
import com.chinaservices.wms.module.process.staff.model.ProcessStaff;
import org.springframework.stereotype.Repository;

/**
 * @author: <PERSON><PERSON>.<PERSON>
 * @Date: 2025/4/14
 * @Description: 加工人员管理Dao层
 */
@Repository
public class ProcessStaffDao extends ModuleBaseDaoSupport<ProcessStaff, Long> {

    /**
     * 人员管理分页查询
     * @param pageCondition 查询条件
     * @return PageResult<ProcessStaffPageQuery>
     */
    public PageResult<ProcessStaffPageQuery> page(ProcessStaffPageCondition pageCondition) {
        return sqlExecutor.page(ProcessStaffPageQuery.class, ProcessStaffSqlId.PROCESS_STAFF_QUERY_PAGE_LIST, pageCondition);
    }
}
