package com.chinaservices.wms.module.performance.queue;

import cn.hutool.core.util.ObjUtil;
import com.chinaservices.core.redis.delay.RedisDelayQueueProcessor;
import com.chinaservices.core.redis.util.RedisKeyGenerator;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.wms.common.constant.RedisKeys;
import com.chinaservices.wms.common.enums.performance.KpiStatusEnum;
import com.chinaservices.wms.module.performance.model.KpiCycle;
import com.chinaservices.wms.module.performance.model.KpiHeader;
import com.chinaservices.wms.module.performance.service.KpiAllocationService;
import com.chinaservices.wms.module.performance.service.KpiCycleService;
import com.chinaservices.wms.module.performance.service.KpiHeaderService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * Redis 延迟队列具体执行类
 */
@Data
@Slf4j
@Component
public class ManualMaticallyEndTheAssessmentCycleListener implements RedisDelayQueueProcessor {

    @Autowired
    private KpiAllocationService kpiAllocationService;
    @Autowired
    private KpiCycleService kpiCycleService;
    @Autowired
    private KpiHeaderService kpiHeaderService;
    @Override
    public void execute(String sign) {
        log.info("开始执行Redis延迟队列：{}", sign);
        KpiCycle first = kpiCycleService.findFirst(new ConditionRule().andEqual(KpiCycle::getSign, sign));
        if (ObjUtil.isNull(first)){
            return;
        }
        kpiAllocationService.updateKpiCycleAndReturnNewKpiCycle(first);
        //设置用户状态
        List<KpiHeader> kpiHeaders = kpiHeaderService.find(new ConditionRule());
        kpiHeaders.forEach(a -> {
            a.setKpiStatus(KpiStatusEnum.NO_INVENTORY.getCode());
            if (!first.getSign().equals(a.getSign())){
                a.setAssessorFraction(null);
                a.setAssessorTime(null);
            }
        });
        kpiHeaderService.batchUpdate(kpiHeaders);
    }

    @Override
    public String delayKey() {
        return RedisKeyGenerator.gen(RedisKeys.KPI_CYCLE_QUEUE_MANUAL);
    }
}
