package com.chinaservices.wms.module.process.order.dao;

import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.wms.common.sqlid.process.order.ProcessOrderSqlId;
import com.chinaservices.wms.module.process.order.model.ProcessTaskDetails;
import com.chinaservices.wms.module.so.so.domain.GetLeftGoodsListQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ProcessTaskDetailsDao extends ModuleBaseDaoSupport<ProcessTaskDetails,Long> {

    /**
     * 查询加工单分配左侧列表基础数据
     * @param keyword
     * @return
     */
    public List<GetLeftGoodsListQuery> getAllocLeftGoodsListQuery(String keyword) {
        return sqlExecutor.find(GetLeftGoodsListQuery.class, ProcessOrderSqlId.PROCESS_GET_ALLOC_LEFT_GOODS_LIST_QUERY,"keyword",keyword);
    }

    /**
     * 查询拆分单分配左侧列表基础数据
     * @param keyword
     * @return
     */
    public List<GetLeftGoodsListQuery> getAllocLeftGoodsListByCfdQuery(String keyword) {
        return sqlExecutor.find(GetLeftGoodsListQuery.class, ProcessOrderSqlId.PROCESS_GET_ALLOC_LEFT_GOODS_LIST_BY_CFD_QUERY,"keyword",keyword);
    }
}