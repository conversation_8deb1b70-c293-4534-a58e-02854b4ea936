package com.chinaservices.wms.module.so.pack.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.so.pack.domain.*;
import com.chinaservices.wms.module.so.pack.model.PackHeader;
import com.chinaservices.wms.module.so.pack.service.PackHeaderService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

/**
 * 出库打包
 **/
@RestController
@RequestMapping("/api/outbound/pack")
public class ShippedController {

    @Autowired
    private PackHeaderService packHeaderService;

    /**
     * 发运管理分页查询
     * @param condition
     * @return
     */
    @PostMapping("/pageShipped")
    @SaCheckPermission("shipped:shippedList:headBtn:page")
    public ResponseData<PageResult<ShippedQuery>> pageShipped(@RequestBody ShippedPageCondition condition){
        return ResponseData.success(packHeaderService.pageShipped(condition));
    }


    /**
     * 编辑承运商
     * @param id
     * @return
     */
    @PostMapping("/getById/{id}")
    @SaCheckPermission("packing:packingList:table:through")
    public ResponseData<PackHeader> getById(@PathVariable(value = "id") Long id){
        return ResponseData.success(packHeaderService.getById(id));
    }


    /**
     * 分配承运商
     * @param packHeaderItem
     * @return
     */
    @PostMapping("/save")
    @SaCheckPermission(value = {"shipped:shippedList:table:edit", "shipped:shippedList:table:alloc"}, mode = SaMode.OR)
    public ResponseData save(@RequestBody PackHeaderItem packHeaderItem){
        packHeaderService.save(packHeaderItem);
        return ResponseData.success();
    }


    /**
     * 批量分配承运商
     * @param packHeaderItem
     * @return
     */
    @PostMapping("/batchSave")
    @SaCheckPermission("shipped:shippedList:headBtn:autoAlloc")
    public ResponseData batchSave(@RequestBody @Valid PackHeaderItem packHeaderItem){
        packHeaderService.batchSave(packHeaderItem);
        return ResponseData.success();
    }

    /**
     * 发运确认
     * @param id
     * @return
     */
    @PostMapping("/confirm/{id}")
    @SaCheckPermission("shipped:shippedList:table:confirm")
    public ResponseData confirm(@PathVariable(value = "id") Long id){
        packHeaderService.confirm(id);
        return ResponseData.success();
    }


    /**
     * 出库打包分页查询
     * @param condition
     * @return
     */
    @PostMapping("/page")
    @SaCheckPermission("packing:packingList:headBtn:page")
    public ResponseData<PageResult<PackBaseQuery>> page(@RequestBody PackPageCondition condition){
        PageResult page = packHeaderService.page(condition);
        return ResponseData.success(page);
    }

    /**
     * 打包接口
     */
    @SaCheckPermission(value = {"packing:packingList:headBtn:merge", "packing:packingList:table:package"}, mode = SaMode.OR)
    @PostMapping("/pack")
    public ResponseData<PackInfo> pack(@RequestBody Long[] ids){
        PackInfo pack = packHeaderService.pack(ids);
        return ResponseData.success(pack);
    }
    /**
     * 获取出库单商品信息接口
     */
    @SaCheckPermission(value = {"packing:packingList:headBtn:merge", "packing:packingList:table:package"}, mode = SaMode.OR)
    @PostMapping("/packByPackNo/{packNo}")
    public ResponseData<PackInfo> pack(@PathVariable(value = "packNo") String packNo){
        PackInfo pack = packHeaderService.packByPackNo(packNo);
        return ResponseData.success(pack);
    }
    /**
     * 新增打包箱
     * @return
     */
    @SaCheckPermission("packing:packingBoxList:headBtn:add")
    @PostMapping("/getBoxNo")
    public ResponseData<PackBoxInfo> getBoxNo(){
        PackBoxInfo boxNo = packHeaderService.getBoxNo();
        return ResponseData.success(boxNo);
    }

    /**
     * 生成打包任务
     */
    @SaCheckPermission("packing:packingDetailList:headBtn:add")
    @PostMapping("/savePack")
    public ResponseData savePack(@RequestBody @Valid PackHeaderInfo packHeaderInfo, BindingResult bindingResult){
        if (bindingResult.hasErrors()){
            return ResponseData.error(bindingResult.getFieldErrors().getFirst().getDefaultMessage());
        }
        packHeaderService.savePack(packHeaderInfo);
        return ResponseData.success();
    }

    /**
     * 打包详情
     *
     * @param id
     * @return
     */
    @SaCheckPermission("packing:packingList:table:package")
    @PostMapping("/getPackInfoById/{id}")
    public ResponseData<PackHeaderInfo> getPackInfoById(@PathVariable(value = "id") Long id){
        PackHeaderInfo packInfoById = packHeaderService.getPackInfoById(id);
        return ResponseData.success(packInfoById);
    }

    /**
     * 取消打包任务
     *
     * @return
     */
    @SaCheckPermission("packing:packingList:headBtn:cancel")
    @PostMapping("/deletePack")
    public ResponseData deletePack(@RequestBody Long[] ids){
        packHeaderService.deletePack(ids);
        return ResponseData.success();
    }
    /**
     * 取消
     * @param ids
     * @return
     */
    @PostMapping("/cancel")
    @SaCheckPermission("packing:packingList:headBtn:cancelPack")
    public ResponseData cancel(@RequestBody Long[] ids){
       packHeaderService.cancel(ids);
        return ResponseData.success();
    }
    /**
     * 打印打包清单
     * @param packNo
     * @return
     */
    @PostMapping("/printPack/{packNo}")
    @SaCheckPermission("packing:packingList:headBtn:printPack")
    public ResponseData<PackHeaderInfoQuery> printPack(@PathVariable(value = "packNo") String packNo){
        return ResponseData.success(packHeaderService.printPack(packNo));
    }
    /**
     * 打印DN单
     * @param packNo
     * @return
     */
    @PostMapping("/printDnPack/{packNo}")
    @SaCheckPermission("packing:packingList:headBtn:printDn")
    public ResponseData<PackHeaderDnInfoQuery> printDnPack(@PathVariable(value = "packNo") String packNo){
        return ResponseData.success(packHeaderService.printDnPack(packNo));
    }
    /**
     * 推送运单信息
     * @param id 主键
     * @return ResponseData<Boolean>
     */
    @PostMapping("/pushShipped/{id}")
    public ResponseData<Boolean> pushShipped(@PathVariable Long id){
        return ResponseData.success(packHeaderService.push(id));
    }

    /**
     * 取消运单信息
     * @param id 主键
     * @return ResponseData<Boolean>
     */
    @PostMapping("/cancelShipped/{id}")
    public ResponseData<Boolean> cancelShipped(@PathVariable Long id){
        return ResponseData.success(packHeaderService.cancel(id));
    }

    /**
     * 运单运输完成
     * @param id 主键
     * @return ResponseData<Boolean>
     */
    @PostMapping("/shippedComplete/{id}")
    public ResponseData<Boolean> shippedComplete(@PathVariable Long id){
        return ResponseData.success(packHeaderService.shippedComplete(id));
    }

    /**
     * 三方推送发车时间/卸货时间
     * @param request 请求体
     * @return ResponseData<Boolean>
     */
    @PostMapping("/pushShippedTime")
    public ResponseData<Boolean> pushShippedTime(@RequestBody ShippedTimeRequest request){
        return ResponseData.success(packHeaderService.pushShippedTime(request));
    }

    /**
     * 三方推送承运商信息
     * @param request 请求体
     * @return ResponseData<Boolean>
     */
    @PostMapping("/pushShipCarrier")
    public ResponseData<Boolean> pushShipCarrier(@RequestBody ShippedCarrierRequest request){
        return ResponseData.success(packHeaderService.pushShipCarrier(request));
    }

    /**
     * 分配打包员
     * @param packerAssignItem 打包员分配信息
     * @return ResponseData<PackHeaderInfo>
     */
    @PostMapping("/assignPacker")
    @SaCheckPermission(value = {
            "packing:packingDetailList:headBtn:assignPacker",
            "packing:packingList:table:assignPacker",
            "packing:packingList:table:adjust"}, mode = SaMode.OR)
    public ResponseData<PackHeaderInfo> assignPacker(@RequestBody PackerAssignItem packerAssignItem) {
        return ResponseData.success(packHeaderService.assignPacker(packerAssignItem));
    }

}
