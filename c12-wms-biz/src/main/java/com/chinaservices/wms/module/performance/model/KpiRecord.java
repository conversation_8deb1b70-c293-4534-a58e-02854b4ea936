package com.chinaservices.wms.module.performance.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "cs_kpi_record")
public class KpiRecord extends ModuleBaseModel {

    /**
     * 考核单ID
     */
    private Long kpiId;

    /**
     * 考核指标ID
     */
    private Long indexId;

    /**
     * 考核时间
     */
    private String examineTime;

    /**
     * 周期类型
     */
    private String ruleType;

    /**
     * 分数
     */
    private BigDecimal source;

    /**
     * 标识
     */
    private String sign;
}
