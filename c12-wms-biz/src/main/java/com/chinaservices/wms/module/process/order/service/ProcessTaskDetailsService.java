package com.chinaservices.wms.module.process.order.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.wms.common.enums.so.SoAllocStatusEnum;
import com.chinaservices.wms.common.enums.so.SoPickingStatusEnum;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.process.order.dao.ProcessTaskDetailsDao;
import com.chinaservices.wms.module.process.order.domain.ProcessTaskDetailsItem;
import com.chinaservices.wms.module.process.order.domain.ProcessTaskDetailsQuery;
import com.chinaservices.wms.module.process.order.model.ProcessTaskDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName ProcessTaskDetailsService
 * <AUTHOR>
 * @Date 2025/6/26 20:36
 * @Description
 * @Version 1.0
 */
@Service
public class ProcessTaskDetailsService extends ModuleBaseServiceSupport<ProcessTaskDetailsDao, ProcessTaskDetails,Long> {

    @Autowired
    private PackageUnitService packageUnitService;

    /**
     *@Description 根据加工编号查询加工任务详情列表
     *@Param processOrderNoList
     *@Return * {@link List< ProcessTaskDetailsQuery> }
     *@Date 2025/6/30 11:54
     *<AUTHOR>
     **/
    public List<ProcessTaskDetailsQuery> findQueryByProcessOrderNoList(List<String> processOrderNoList) {
        ConditionRule conditionRule =  ConditionRule.getInstance();
        conditionRule.andIn(ProcessTaskDetails::getProcessOrderNo,processOrderNoList);
        return dao.find(ProcessTaskDetailsQuery.class,conditionRule);
    }

    /**
     *@Description 根据加工单号删除任务明细
     *@Param processOrderNoList
     *@Return Void
     *@Date 2025/6/30 15:38
     *<AUTHOR>
     **/
    public void batchDeleteByProcessOrderNoList(List<String> processOrderNoList) {
        ConditionRule conditionRule =  ConditionRule.getInstance();
        conditionRule.andIn(ProcessTaskDetails::getProcessOrderNo,processOrderNoList);
        dao.delete(conditionRule);
    }

    /**
     *@Description 新增任务明细
     *@Param taskDetailsItemList
     *@Return Void
     *@Date 2025/6/30 18:06
     *<AUTHOR>
     **/
    public void batchSaveEntity(List<ProcessTaskDetailsItem> taskDetailsItemList) {
        //抽取包装单位id
        List<Long> packageUnitIdList = taskDetailsItemList.stream().map(ProcessTaskDetailsItem::getPackageUnitId).distinct().collect(Collectors.toList());
        //查询包装单位
        List<PackageUnit> packageUnitList = packageUnitService.findByIds(packageUnitIdList.toArray(Long[]::new));
        Map<Long,PackageUnit> packageUnitMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(packageUnitList)){
            packageUnitMap = packageUnitList.stream().collect(Collectors.toMap(PackageUnit::getId, Function.identity(), (v1, v2) -> v1));
        }
        List<ProcessTaskDetails> taskDetailsList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(taskDetailsItemList)){
            Map<Long, PackageUnit> finalPackageUnitMap = packageUnitMap;
            taskDetailsItemList.forEach(item->{
                PackageUnit packageUnit = finalPackageUnitMap.get(item.getPackageUnitId());
                ProcessTaskDetails processTaskDetails = new ProcessTaskDetails();
                BeanUtil.copyProperties(item,processTaskDetails);
                processTaskDetails.setAllocStatus(SoAllocStatusEnum.UN_ALLOC.getCode());
                processTaskDetails.setQtyAllocationEa(BigDecimal.ZERO);
                processTaskDetails.setPickingStatus(SoPickingStatusEnum.UNPICKED.getCode());
                processTaskDetails.setQtyPickingEa(BigDecimal.ZERO);
                if (ObjectUtil.isNotNull(packageUnit)){
                    processTaskDetails.setDetailsProcessQuantityEa(processTaskDetails.getDetailsProcessQuantity().multiply(new BigDecimal(packageUnit.getMinQuantity())));
                }
                preSave(processTaskDetails);
                taskDetailsList.add(processTaskDetails);
            });
            dao.batchInsert(taskDetailsList);
        }
    }

    /**
     *@Description 根据任务编号查询数据
     *@Param processTaskNo
     *@Return * {@link List< ProcessTaskDetails> }
     *@Date 2025/7/1 11:47
     *<AUTHOR>
     **/
    public List<ProcessTaskDetails> findByTaskNo(String processTaskNo) {
        ConditionRule conditionRule =  ConditionRule.getInstance();
        conditionRule.andEqual(ProcessTaskDetails::getProcessTaskNo,processTaskNo);
        return dao.find(conditionRule);
    }
}
