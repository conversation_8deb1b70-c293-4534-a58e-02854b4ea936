package com.chinaservices.wms.module.so.picking.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constants.RegexStrings;
import com.chinaservices.wms.common.enums.AllocationTypeEnum;
import com.chinaservices.wms.common.enums.DocumentTypeEnum;
import com.chinaservices.wms.common.enums.common.SoEnum;
import com.chinaservices.wms.common.enums.so.SoPickingStatusEnum;
import com.chinaservices.wms.common.enums.so.TaskStatusEnum;
import com.chinaservices.wms.common.exception.SoExcepitonEnum;
import com.chinaservices.wms.module.archive.archives.service.LogisticsFileAutoGenerateService;
import com.chinaservices.wms.module.basic.cspackage.dao.PackageUnitDao;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.folder.logistics.domain.LogisticsFolderDetailLinkCondition;
import com.chinaservices.wms.module.passive.tag.model.PassiveTag;
import com.chinaservices.wms.module.passive.tag.service.PassiveTagService;
import com.chinaservices.wms.module.so.picking.dao.SoPickingDao;
import com.chinaservices.wms.module.so.picking.domain.*;
import com.chinaservices.wms.module.so.picking.model.SoPicking;
import com.chinaservices.wms.module.so.so.domain.*;
import com.chinaservices.wms.module.so.so.model.SoAllocation;
import com.chinaservices.wms.module.so.so.model.SoAllocationPickingOp;
import com.chinaservices.wms.module.so.so.model.SoDetail;
import com.chinaservices.wms.module.so.so.model.SoHeader;
import com.chinaservices.wms.module.so.so.service.*;
import com.chinaservices.wms.module.so.wv.model.WvDetail;
import com.chinaservices.wms.module.so.wv.model.WvHeader;
import com.chinaservices.wms.module.so.wv.service.WvDetailService;
import com.chinaservices.wms.module.so.wv.service.WvHeaderService;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.service.WarehouseLocService;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 拣货管理服务
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Service
public class SoPickingService extends ModuleBaseServiceSupport<SoPickingDao, SoPicking, Long>
{

    @Autowired
    private SoAllocationPickingService sopickingServiceAllocation;
    @Autowired
    private NumberGenerator numberGenerator;
    @Autowired
    private SoPickingFileService soPickingFileService;
    @Autowired
    private WvDetailService wvDetailService;
    @Autowired
    private WarehouseLocService warehouseLocService;
    @Autowired
    private SoAllocationService soAllocationService;
    @Autowired
    private PackageUnitDao packageUnitDao;
    @Autowired
    private SoHeaderService soHeaderService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private OwnerService ownerService;
    @Autowired
    private SoAllocationPickingOpService soAllocationPickingOpService;
    @Autowired
    private WvHeaderService wvHeaderService;
    @Autowired
    private SkuService skuService;
    @Autowired
    private PackageUnitService packageUnitService;
    @Autowired
    private TaskExecutor taskExecutor;
    @Autowired
    private LogisticsFileAutoGenerateService logisticsFileAutoGenerateService;

    /**
     * 拣货固定库位
     */
    private static final String SORTATION = "SORTATION";
    @Autowired
    private SoDetailService soDetailService;
    @Autowired
    private PassiveTagService passiveTagService;


    /**
     * 分页
     *
     * @param condition 条件
     * @return {@link PageResult }<{@link SoPickingQuery }>
     */
    public PageResult<SoPickingQuery> page(SoPickingPageCondition condition)
    {
        if (EmptyUtil.isNotEmpty(condition.getPickingOpName())) {
            // 根据拣货员查询拣货单号
            List<SoPicking> pickingList = dao.getPickingNoByPickingOpName(condition.getPickingOpName());
            if (EmptyUtil.isNotEmpty(pickingList)) {
                // 赋值拣货单号集合为参数
                List<String> pickingNoList = pickingList.stream()
                        .map(SoPicking::getPickingNo)
                        .filter(EmptyUtil::isNotEmpty)
                        .distinct()
                        .toList();
                condition.setPickingNoList(pickingNoList);
            }
            // 拣货员名称置null
            condition.setPickingOpName(null);
        }

        // 拣货列表
        PageResult<SoPickingQuery> page = dao.page(condition);
        if (EmptyUtil.isEmpty(page.getRows())) {
            return page;
        }
        List<SoPickingQuery> queryList = page.getRows();
        // 拣货明细
        String[] pickingNos =
                queryList.stream().map(SoPickingQuery::getPickingNo)
                        .filter(EmptyUtil::isNotEmpty)
                        .toArray(String[]::new);
        SoAllocationCondition allocationCondition = new SoAllocationCondition();
        allocationCondition.setPickingNos(pickingNos);
        List<SoAllocation> allocationList = sopickingServiceAllocation.findSoAllocationByCondition(allocationCondition);
        if (EmptyUtil.isEmpty(allocationList)) {
            return page;
        }
        Map<String, List<SoAllocation>> allocationGroup =
                allocationList.stream().collect(Collectors.groupingBy(SoAllocation::getPickingNo));

        // 查询波次关联出库单信息
        String[] wvNos =
                queryList.stream().map(SoPickingQuery::getWvNo)
                        .filter(EmptyUtil::isNotEmpty)
                        .toArray(String[]::new);
        Map<String, List<WvDetail>> wvDetailMap = null;
        if (EmptyUtil.isNotEmpty(wvNos)) {
            ConditionRule rule = new ConditionRule();
            rule.andIn(WvDetail::getWvNo, (Object[]) wvNos);
            List<WvDetail> wvDetailList = wvDetailService.find(rule);
            if (EmptyUtil.isNotEmpty(wvDetailList)) {
                wvDetailMap = wvDetailList.stream().collect(Collectors.groupingBy(WvDetail::getWvNo));
            }
        }

        // 标签号
        List<String> tagNosList = queryList.stream().map(SoPickingQuery::getTagNo).toList();
        List<String> tagNoAllList = tagNosList.stream()
                .filter(EmptyUtil::isNotEmpty)
                .flatMap(s -> Arrays.stream(s.split(",")))
                .toList();
        List<PassiveTag> tagList = passiveTagService.find(new ConditionRule().andIn(PassiveTag::getTagNo, tagNoAllList));
        Map<String, Long> tagNoMap = tagList.stream().collect(Collectors.toMap(PassiveTag::getTagNo, PassiveTag::getId));

        // 拣货状态查询
        Map<String, List<WvDetail>> finalWvDetailMap = wvDetailMap;
        queryList.forEach(query ->
        {
            String pickingNo = query.getPickingNo();
            List<SoAllocation> list = allocationGroup.get(pickingNo);
            if (EmptyUtil.isEmpty(list)) {
                list = new ArrayList<>();
            }

            String wvNo = query.getWvNo();
            if (finalWvDetailMap != null && EmptyUtil.isNotEmpty(wvNo) && EmptyUtil.isNotEmpty(finalWvDetailMap.get(wvNo))) {
                // 判断为波次单分配拣货任务，查询波次单包含的出库单号
                List<WvDetail> wvDetailList = finalWvDetailMap.get(wvNo);
                // 查询拣货详情关联的出库详情Id
                List<Long> soDetailIdList = list.stream().map(SoAllocation::getSoDetailId).distinct().toList();
                List<SoPickingSoNoQuery> soNoList =
                        wvDetailList.stream().map(wv ->
                        {
                            if (!soDetailIdList.contains(wv.getSoDetailId())) {
                                return null;
                            }
                            SoPickingSoNoQuery soNoQuery = new SoPickingSoNoQuery();
                            soNoQuery.setSoId(wv.getSoId());
                            soNoQuery.setSoNo(wv.getSoNo());
                            return soNoQuery;
                        }).filter(EmptyUtil::isNotEmpty).distinct().toList();
                query.setSoNoList(soNoList);
                query.setSoNoQty((long) soNoList.size());
                if (soNoList.size() == 1) {
                    query.setSoNo(soNoList.getFirst().getSoNo());
                }
            }

            // 标签数据处理
            String tagNoStr = query.getTagNo();
            if (EmptyUtil.isNotEmpty(tagNoStr)) {
                String[] split = tagNoStr.split(",");
                List<TagNoQuery> tagNoList = Arrays.stream(split).map(tagNo ->
                {
                    Long tagId = tagNoMap.get(tagNo);
                    TagNoQuery tagNoQuery = new TagNoQuery();
                    tagNoQuery.setTagId(tagId);
                    tagNoQuery.setTagNo(tagNo);
                    return tagNoQuery;
                }).filter(EmptyUtil::isNotEmpty).distinct().toList();
                query.setTagNoList(tagNoList);
                query.setTagNoQty((long) tagNoList.size());
                if (tagNoList.size() == 1) {
                    query.setTagNo(tagNoList.getFirst().getTagNo());
                }
            }

            if (SoPickingStatusEnum.UNPICKED.getCode().equals(query.getStatus())) {
                query.setAssignPicked(true);
                query.setCancelled(true);
            }
            if (SoPickingStatusEnum.PARTIAL_PICKING.getCode().equals(query.getStatus()) && !TaskStatusEnum.COMPLETED.getCode().equals(query.getTaskStatus())) {
                query.setAssignPicked(true);
            }

        });
        return page;
    }

    /**
     * 根据id查询
     *
     * @param id ID
     * @return {@link SoPickingQuery }
     */
    public SoPickingQuery getById(Long id)
    {
        SoPicking soPicking = dao.findById(id);
        SoPickingQuery soPickingQuery = new SoPickingQuery();
        BeanUtil.copyProperties(soPicking, soPickingQuery);
        return soPickingQuery;
    }

    /**
     * 拣货单保存
     *
     * @param item 拣货item
     * @return boolean
     */
    public boolean save(SoPickingItem item)
    {
        // 库区id没有值判断为新增，执行库区代码生成；库区id有值判断为更新，执行库区代码置null避免误更新；
        if (EmptyUtil.isEmpty(item.getId())) {
            // 库区代码生成
            String pickingNo = numberGenerator.nextValue(IdRuleConstant.PICKING_NO);
            item.setPickingNo(pickingNo);
        }
        // 校验拣货单号是否唯一
        boolean duplicate = isDuplicateOfNo(item);
        if (duplicate) {
            throw new ServiceException(GlobalExceptionEnum.NO_EXIST, SoEnum.PICKING.getName(), item.getPickingNo());
        }
        // 保存拣货单条形码
        soPickingFileService.save(item.getPickingNo());
        // 拣货状态--未生成
        item.setStatus(SoPickingStatusEnum.NOT_GENERATE.getCode());
        // 保存
        return dao.saveOrUpdate(item);


    }


    /**
     * 拣货单号是否重复
     *
     * @param item 拣货item--拣货id、拣货单号
     * @return boolean
     */
    public boolean isDuplicateOfNo(SoPickingItem item)
    {
        SoPicking soPicking = new SoPicking();
        BeanUtil.copyProperties(item, soPicking);
        return dao.isDuplicate(soPicking, "id", "pickingNo");
    }

    public List<SoPicking> findBySoNo(String soNo)
    {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(SoPicking::getSoNo, soNo);
        return this.find(conditionRule);
    }

    public List<SoPicking> findByPickingNos(Set<String> pickingNoList) {
        if (CollUtil.isEmpty(pickingNoList)){
            return new ArrayList<>();
        }
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(SoPicking::getPickingNo, pickingNoList);
        return this.find(conditionRule);
    }

    public List<SoPicking> findByWvNos(Set<String> wvNoList) {
        if (CollUtil.isEmpty(wvNoList)){
            return new ArrayList<>();
        }
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(SoPicking::getWvNo, wvNoList);
        return this.find(conditionRule);
    }

    public void deleteByPickingNos(Set<String> pickingNoSet)
    {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(SoPicking::getPickingNo, pickingNoSet);
        dao.delete(conditionRule);
    }

    /**
     * 更新拣货单状态
     *
     * @param updateStatusItem 更新状态条件
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(SoPickingUpdateStatusItem updateStatusItem)
    {
        // 校验拣货单号
        String pickingNo = updateStatusItem.getPickingNo();
        SoPickingPageCondition condition = new SoPickingPageCondition();
        condition.setPickingNo(pickingNo);
        SoPicking soPicking = dao.findFirst(condition);
        if (EmptyUtil.isEmpty(soPicking)) {
            throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, SoEnum.PICKING.getName(), pickingNo);
        }
        // 检查拣货单状态
        this.checkPickingStatus(updateStatusItem, soPicking);

        // 查询未取消的拣货任务
        ConditionRule rule = new ConditionRule();
        rule.andEqual(SoAllocation::getPickingNo, pickingNo);
        rule.andNotEqual(SoAllocation::getPickingStatus, SoPickingStatusEnum.CANCELLED.getCode());
        List<SoAllocation> list = sopickingServiceAllocation.find(rule);
        if (EmptyUtil.isEmpty(list)) {
            throw new ServiceException(SoExcepitonEnum.PICKING_NOT_ALLOCATION, pickingNo);
        }

        // 生成拣货单，更新目标库位
        if (SoPickingStatusEnum.UNPICKED.getCode().equals(updateStatusItem.getStatus())) {
            updateAllocationToLocId(list);
        }

        // 取消拣货单，分配数回退
        if (SoPickingStatusEnum.CANCELLED.getCode().equals(updateStatusItem.getStatus())) {
            this.cancelPicking(list, soPicking);
        }

        // 更新拣货任务
        SoAllocationUpdatePickingStatusCondition updateCondition = new SoAllocationUpdatePickingStatusCondition();
        updateCondition.setPickingNo(pickingNo);
        updateCondition.setPickingStatus(updateStatusItem.getStatus());
        sopickingServiceAllocation.updateStatus(updateCondition);
        //更新任务状态，由于SoAllocationUpdatePickingStatusCondition使用位置较多，无法改造增加字段，所以重新写一行代码
        sopickingServiceAllocation.updateByCondition(new AllocUpdateTaskStatusItem().setTaskStatus(TaskStatusEnum.NOT_STARTED.getCode()),updateCondition);
        // 更新拣货单状态
        dao.updateByIds(updateStatusItem, soPicking.getId());

        // 拣货物流文件
        taskExecutor.execute(() ->
                this.logisticsFileAutoGenerate(list, pickingNo)
        );
    }

    /**
     * 取消拣货
     *
     * @param list      列表
     * @param soPicking 拣货信息
     */
    private void cancelPicking(List<SoAllocation> list, SoPicking soPicking) {
        CancalSaveItem cancalSaveItem = new CancalSaveItem();
        List<Long> idList = list.stream().map(SoAllocation::getId).toList();
        cancalSaveItem.setIdList(idList);
        cancalSaveItem.setCancelPicking(true);
        // 出库单
        String soNo = soPicking.getSoNo();
        if (EmptyUtil.isNotEmpty(soNo)) {
            cancalSaveItem.setAllocationType(AllocationTypeEnum.OUTBOUND.getCode());
        }
        // 波次单
        String wvNo = soPicking.getWvNo();
        if (EmptyUtil.isNotEmpty(wvNo)) {
            cancalSaveItem.setAllocationType(AllocationTypeEnum.WAVE_TIMES.getCode());
        }
        // 加工单
        String processOrderNo = soPicking.getProcessOrderNo();
        if (EmptyUtil.isNotEmpty(processOrderNo)) {
            cancalSaveItem.setAllocationType(AllocationTypeEnum.PROCES_ALLOC.getCode());
        }
        // 取消拣货任务分配，回退库存
        soAllocationService.cancelSave(cancalSaveItem);
    }

    /**
     * 物流文件自动生成
     *
     * @param list      拣货商品详情列表
     * @param pickingNo 拣货单号
     */
    private void logisticsFileAutoGenerate(List<SoAllocation> list, String pickingNo) {
        // 文件参数处理
        List<Long> soDetailIdList = list.stream().map(SoAllocation::getSoDetailId).toList();
        List<SoDetail> detailList = soDetailService.findByIds(soDetailIdList.toArray(new Long[0]));
        List<String> soNoList = detailList.stream().map(SoDetail::getSoNo).filter(EmptyUtil::isNotEmpty).distinct().toList();

        // 拣货单号集合
        List<LogisticsFolderDetailLinkCondition> conditionList = new ArrayList<>();
        for (String soNo : soNoList) {
            ArrayList<String> objects = new ArrayList<>();
            objects.add(pickingNo);
            LogisticsFolderDetailLinkCondition logisticsFolderDetailLinkCondition = new LogisticsFolderDetailLinkCondition();
            logisticsFolderDetailLinkCondition.setDocumentNos(objects);
            logisticsFolderDetailLinkCondition.setUpDocumentNo(soNo);
            conditionList.add(logisticsFolderDetailLinkCondition);
        }

        logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                DocumentTypeEnum.PICKING, conditionList, SessionContext.getSessionUserInfo().getCompanyId(),
                SessionContext.getSessionUserInfo().getTenancy(), null
        );

        logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                DocumentTypeEnum.SO_PICKING, conditionList, SessionContext.getSessionUserInfo().getCompanyId(),
                SessionContext.getSessionUserInfo().getTenancy(), null
        );
    }

    /**
     * 更新拣货任务目标库位
     *
     * @param list 列表
     */
    private void updateAllocationToLocId(List<SoAllocation> list)
    {
        // 查询源库位的库区
        Long[] warehouseIds =
                list.stream().map(SoAllocation::getWarehouseId).filter(EmptyUtil::isNotEmpty).distinct().toArray(Long[]::new);

        // 查询同仓库的拣货库位SORTATION
        List<WarehouseLoc> sortationList =
                warehouseLocService.find(new ConditionRule()
                        .andIn(WarehouseLoc::getWarehouseId, (Object[]) warehouseIds)
                        .andEqual(WarehouseLoc::getLocCode, SORTATION));
        Map<Long, Long> warehouseIdMap = sortationList.stream().collect(Collectors.toMap(WarehouseLoc::getWarehouseId,
                WarehouseLoc::getId));

        // 组装目标库位更新数据
        List<SoAllocationToLocIdItem> toLocIdItemList = new ArrayList<>();
        list.forEach(soAllocation ->
        {
            // 获取目标库位id
            Long warehouseId = soAllocation.getWarehouseId();
            Long toLocId = warehouseIdMap.get(warehouseId);
            // 组装更新数据
            SoAllocationToLocIdItem toLocIdItem = new SoAllocationToLocIdItem();
            toLocIdItem.setToLocId(toLocId);
            toLocIdItem.setId(soAllocation.getId());
            toLocIdItemList.add(toLocIdItem);
        });
        // 更新目标库位
        sopickingServiceAllocation.batchUpdateItemToSqlExecution(toLocIdItemList);
    }

    /**
     * 检查拣货单状态
     *
     * @param updateStatusItem 更新状态项目
     * @param soPicking 所以选
     */
    private void checkPickingStatus(SoPickingUpdateStatusItem updateStatusItem, SoPicking soPicking)
    {
        // 校验拣货单状态
        String status = soPicking.getStatus();
        String updateStatus = updateStatusItem.getStatus();
        if (SoPickingStatusEnum.CANCELLED.getCode().equals(status)) {
            throw new ServiceException(SoExcepitonEnum.PICKING_CANCEL);
        }

        // 取消拣货单操作
        if (SoPickingStatusEnum.CANCELLED.getCode().equals(updateStatus)) {
            // 拣货单开始拣货
            if (SoPickingStatusEnum.PARTIAL_PICKING.getCode().equals(status)
                    || SoPickingStatusEnum.COMPLETE_PICKING.getCode().equals(status)) {
                throw new ServiceException(SoExcepitonEnum.PICKING_CANCEL_ERROR_ON_PICKING);
            }
        }

        // 拣货确认
        if (SoPickingStatusEnum.PARTIAL_PICKING.getCode().equals(updateStatus)
                || SoPickingStatusEnum.COMPLETE_PICKING.getCode().equals(updateStatus)) {
            // 拣货单已完全拣货
            if (SoPickingStatusEnum.COMPLETE_PICKING.getCode().equals(status)) {
                throw new ServiceException(SoExcepitonEnum.PICKING_COMPLETE_PICKING);
            }
        }
    }


    /*============================== PDA接口方法 ===========================*/

    /**
     * PDA拣货管理分页
     *
     * @param condition 条件
     * @return {@link PageResult }<{@link SoPickingQuery }>
     */
    public PageResult<SoPickingQuery> pdaPage(SoPickingPageCondition condition) {
        SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();
        Long userId = sessionUserInfo.getUserId();
        if (!sessionUserInfo.isSuperAdmin()) {
            condition.setPickingOpId(userId);
        }
        return dao.pdaPage(condition);
    }

    /**
     * PDA拣货任务分页
     *
     * @param condition 条件
     * @return {@link PageResult }<{@link SoAllocationQuery }>
     */
    public PageResult<SoAllocationQuery> getByIdPda(SoPickingPageCondition condition) {
        SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();
        Long userId = sessionUserInfo.getUserId();
        if (!sessionUserInfo.isSuperAdmin()) {
            condition.setPickingOpId(userId);
        }
        PageResult<SoAllocationQuery> byIdPda = dao.getByIdPda(condition);
        Set<Long> collect = byIdPda.getRows().stream().map(SoAllocationQuery::getPackageId).collect(Collectors.toSet());
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn("packageId",collect);
        List<PackageUnit> packageUnits = packageUnitDao.find(conditionRule);
        Map<Long, List<PackageUnit>> collect1 = packageUnits.stream().collect(Collectors.groupingBy(PackageUnit::getPackageId));

        byIdPda.getRows().forEach(e->{
            Long packageId = e.getPackageId();
            List<PackageUnit> packageUnits1 = collect1.get(packageId);
            Collections.sort(packageUnits1,(a,b)->b.getSequencesNo()-a.getSequencesNo());
            List<String> qtys = new ArrayList<>();
            List<String> picks = new ArrayList<>();
            BigDecimal qtyEa = e.getQtyEa();
            BigDecimal pickingEa = e.getPickingEa();
            for (int i = 0; i < packageUnits1.size(); i++) {
                PackageUnit packageUnit = packageUnits1.get(i);
                BigDecimal minQuantity = new BigDecimal(packageUnit.getMinQuantity());
                if (minQuantity.compareTo(e.getQtyEa())<=0){
                    BigDecimal[] bigDecimals = e.getQtyEa().divideAndRemainder(minQuantity);
                    qtys.add(bigDecimals[0].toBigInteger().toString()+packageUnit.getName());
                    e.setQtyEa(bigDecimals[1]);
                }
                if (ObjUtil.isNotNull(e.getPickingEa())&&minQuantity.compareTo(e.getPickingEa())<=0){
                    BigDecimal[] bigDecimals = e.getPickingEa().divideAndRemainder(minQuantity);
                    picks.add(bigDecimals[0].toBigInteger().toString()+packageUnit.getName());
                    e.setPickingEa(bigDecimals[1]);
                }
            }
            e.setQtyEaStr(StrUtil.join(RegexStrings.ADD,qtys));
            e.setQtyEa(qtyEa);
            e.setPickingEaStr(StrUtil.join(RegexStrings.ADD,picks));
            e.setPickingEa(pickingEa);
        });
        return byIdPda;
    }

    public Double findAvgTimeByWarehouseId(Long warehouseId) {
        return dao.findAvgTimeByWarehouseId(warehouseId);
    }

    /**
     * 打印拣货单
     * @param id 主键
     * @return SoPickingPrintQuery
     */
    public SoPickingPrintQuery print(Long id) {
        SoPicking soPicking = dao.findById(id);
        if(ObjUtil.isNull(soPicking)){
            throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "拣货", id);
        }
        SoPickingPrintQuery query = new SoPickingPrintQuery();
        query.setPickingNo(ObjUtil.defaultIfNull(soPicking.getPickingNo(), StrUtil.EMPTY));
        // 出库单
        if(ObjUtil.isNotNull(soPicking.getSoId())){
            SoHeader soHeader = soHeaderService.findById(soPicking.getSoId());
            if(ObjUtil.isNull(soHeader)){
                throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "出库", id);
            }
            Warehouse warehouse = warehouseService.findById(soHeader.getWarehouseId());
            if(ObjUtil.isNull(warehouse)){
                throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "仓库", soHeader.getWarehouseId());
            }
            Owner owner = ownerService.findById(soHeader.getOwnerId());
            if(ObjUtil.isNull(owner)){
                throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "货主", soHeader.getOwnerId());
            }
            query.setOrderTime(soHeader.getOrderTime());
            query.setWarehouseName(ObjUtil.defaultIfNull(warehouse.getWarehouseName(), StrUtil.EMPTY));
            query.setOwnerName(ObjUtil.defaultIfNull(owner.getOwnerName(), StrUtil.EMPTY));
        }
        // 波次单
        if(ObjUtil.isNotNull(soPicking.getWvId())){
            WvHeader wvHeader = wvHeaderService.findById(soPicking.getWvId());
            if(ObjUtil.isNull(wvHeader)){
                throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "波次", soPicking.getWvId());
            }
            query.setWvNo(ObjUtil.defaultIfNull(wvHeader.getWvNo(), StrUtil.EMPTY));
            // 获取波次表单明细
            List<WvDetail> wvDetailList = wvDetailService.find(new ConditionRule().andEqual("wvId", wvHeader.getId()));
            if(CollectionUtil.isNotEmpty(wvDetailList)){
                // 获取出库订单IdList
                List<Long> soIdList = wvDetailList.stream().map(WvDetail::getSoId).toList();
                if(CollectionUtil.isNotEmpty(soIdList)){
                    // 获取出库订单列表
                    List<SoHeader> soHeaderList = soHeaderService.find(new ConditionRule().andIn("id", soIdList));
                    // 赋值最早的出库单时间
                    Optional<Date> minDate = soHeaderList.stream().map(SoHeader::getOrderTime).min(Date::compareTo);
                    minDate.ifPresent(query::setOrderTime);
                    if(CollectionUtil.isNotEmpty(soHeaderList)){
                        // 获取仓库IdList
                        List<Long> warehouseIdList = soHeaderList.stream().map(SoHeader::getWarehouseId).toList();
                        if (CollectionUtil.isNotEmpty(warehouseIdList)) {
                            // 获取仓库列表
                            List<Warehouse> warehouseList = warehouseService.find(new ConditionRule().andIn("id", warehouseIdList));
                            if(CollectionUtil.isNotEmpty(warehouseList)){
                                String warehouseNames = warehouseList.stream().map(Warehouse::getWarehouseName).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
                                query.setWarehouseName(ObjUtil.defaultIfNull(warehouseNames, StrUtil.EMPTY));
                            }
                        }
                        // 获取货主IdList
                        List<Long> ownerIdList = soHeaderList.stream().map(SoHeader::getOwnerId).toList();
                        if (CollectionUtil.isNotEmpty(ownerIdList)) {
                            // 获取货主列表
                            List<Owner> ownerList = ownerService.find(new ConditionRule().andIn("id", ownerIdList));
                            if(CollectionUtil.isNotEmpty(ownerList)){
                                String ownerNames = ownerList.stream().map(Owner::getOwnerName).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
                                query.setOwnerName(ObjUtil.defaultIfNull(ownerNames, StrUtil.EMPTY));
                            }
                        }
                    }
                }
            }
        }
        // 赋值拣货员信息
        List<SoAllocation> soAllocationList = soAllocationService.find(new ConditionRule().andEqual("pickingNo", soPicking.getPickingNo()));
        if(CollectionUtil.isNotEmpty(soAllocationList)){
            List<Long> allocationIdList = soAllocationList.stream().map(SoAllocation::getId).toList();
            ConditionRule conditionRule = ConditionRule.getInstance();
            conditionRule.andIn("allocationId", allocationIdList);
            conditionRule.andEqual("currentPickingOp", YesNoEnum.YES.getCode());
            List<SoAllocationPickingOp> opList = soAllocationPickingOpService.find(conditionRule);
            if(CollectionUtil.isNotEmpty(opList)){
                String opNames = opList.stream().map(SoAllocationPickingOp::getPickingOpName).filter(StrUtil::isNotBlank).distinct().collect(Collectors.joining(","));
                query.setPickingOpName(ObjUtil.defaultIfNull(opNames, StrUtil.EMPTY));
            }
        }
        getAllocationList(query, soAllocationList);
        return query;
    }

    /**
     * 打印——赋值分配列表信息
     * @param query 拣货单打印实体
     */
    public void getAllocationList(SoPickingPrintQuery query, List<SoAllocation> soAllocationList) {
        if(CollectionUtil.isNotEmpty(soAllocationList)){
            Map<Long, Sku> skuMap = skuService.findAll().stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (k1, k2) -> k1));
            Map<Long, WarehouseLoc> warehouseLocMap = warehouseLocService.findAll().stream().collect(Collectors.toMap(WarehouseLoc::getId, Function.identity(), (k1, k2) -> k1));
            // 包装单位查询
            List<Long> packageIdList = soAllocationList.stream().map(SoAllocation::getPackageId).filter(ObjUtil::isNotNull).distinct().toList();
            List<PackageUnit> packageUnitList = packageUnitService.listByPackageIds(packageIdList);
            Map<Long, List<PackageUnit>> packageUnitIdMap = packageUnitList.stream().collect(Collectors.groupingBy(PackageUnit::getPackageId));
            for (SoAllocation soAllocation : soAllocationList) {
                SoAllocationPrintQuery allocationQuery = new SoAllocationPrintQuery();
                allocationQuery.setPickingTaskNo(ObjUtil.defaultIfNull(soAllocation.getPickingTaskNo(), StrUtil.EMPTY));
                allocationQuery.setQtyEa(ObjUtil.defaultIfNull(soAllocation.getQtyEa(), BigDecimal.ZERO));
                allocationQuery.setLotNum(ObjUtil.defaultIfNull(soAllocation.getLotNum(), StrUtil.EMPTY));
                allocationQuery.setPalletId(ObjUtil.defaultIfNull(soAllocation.getPalletId(), StrUtil.EMPTY));
                Sku sku = MapUtil.get(skuMap, soAllocation.getSkuId(), Sku.class);
                ConditionRule conditionRule = ConditionRule.getInstance();
                conditionRule.andEqual("allocationId", soAllocation.getId());
                conditionRule.andEqual("currentPickingOp", YesNoEnum.YES.getCode());
                List<SoAllocationPickingOp> opList = soAllocationPickingOpService.find(conditionRule);
                if(CollectionUtil.isNotEmpty(opList)){
                    SoAllocationPickingOp soAllocationPickingOp = opList.getFirst();
                    allocationQuery.setPickingOpName(ObjUtil.defaultIfNull(soAllocationPickingOp.getPickingOpName(), StrUtil.EMPTY));
                }
                if(ObjUtil.isNotNull(sku)){
                    allocationQuery.setSkuCode(ObjUtil.defaultIfNull(sku.getSkuCode(), StrUtil.EMPTY));
                    allocationQuery.setSkuName(ObjUtil.defaultIfNull(sku.getSkuName(), StrUtil.EMPTY));
                }
                WarehouseLoc warehouseLoc = MapUtil.get(warehouseLocMap, soAllocation.getLocId(), WarehouseLoc.class);
                if(ObjUtil.isNotNull(warehouseLoc)){
                    allocationQuery.setLocCode(warehouseLoc.getLocCode());
                }
                query.getAllocationList().add(allocationQuery);
                // 包装单位转换
                if (EmptyUtil.isNotEmpty(soAllocation.getPackageId())) {
                    BigDecimal qtyEa = soAllocation.getQtyEa();
                    List<PackageUnit> packageList = packageUnitIdMap.get(soAllocation.getPackageId());
                    Map<Integer, PackageUnit> unitMap = packageList.stream().collect(Collectors.toMap(PackageUnit::getSequencesNo, p -> p));
                    packageList.stream()
                        .filter(packageUnit -> qtyEa.compareTo(new BigDecimal(packageUnit.getMinQuantity())) >= 0)
                        .max(Comparator.comparing(PackageUnit::getSequencesNo)).ifPresent(packageUnit ->
                        {
                            StringBuilder qtyPack = new StringBuilder();
                            SoAllocationPickingService.setQtyPack(packageUnit, qtyEa.intValue(), unitMap, qtyPack);
                            allocationQuery.setQtyPack(qtyPack.toString());
                        });
                }
            }
            // 赋值应拣货数信息
        }
    }

}
