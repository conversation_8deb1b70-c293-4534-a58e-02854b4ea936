package com.chinaservices.wms.module.process.combinations.model;


import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 组合件明细表
 * cs_combinations_details
 */
@Data
@Entity
@Table(name = "cs_combinations_details")
public class CombinationsDetails extends ModuleBaseModel {

    /**
     * 组合编号
     */
    private String combinationsNo;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 商品编号
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 包装规格id
     */
    private Long packageId;

    /**
     * 包装规格名称
     */
    private String packageName;

    /**
     * 包装单位id
     */
    private Long packageUnitId;

    /**
     * 包装单位名称
     */
    private String packageUnitName;

    /**
     * 数量
     */
    private Long count;

    /**
     * 数量ea
     */
    private Long countEa;

    /**
     * 子件类型:1-普通;2-辅料
     * @see com.chinaservices.wms.common.enums.process.ProcessCombinationsDetailsTypeEnum
     */
    private String detailsType;

    /**
     * 备注
     */
    private String remark;

}