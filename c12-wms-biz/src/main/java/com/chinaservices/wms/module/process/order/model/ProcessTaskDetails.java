package com.chinaservices.wms.module.process.order.model;

import java.math.BigDecimal;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 加工拆分任务明细表
 * cs_process_task_details
 */
@Data
@Entity
@Table(name = "cs_process_task_details")
public class ProcessTaskDetails extends ModuleBaseModel {


    /**
     * 加工单编号
     */
    private String processOrderNo;

    /**
     * 加工任务编号
     */
    private String processTaskNo;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 商品编号
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 包装规格id
     */
    private Long packageId;

    /**
     * 包装规格名称
     */
    private String packageName;

    /**
     * 包装单位id
     */
    private Long packageUnitId;

    /**
     * 包装单位名称
     */
    private String packageUnitName;

    /**
     * 商品源数量
     */
    private BigDecimal skuQuantity;

    /**
     * 待加工数量
     */
    private BigDecimal detailsProcessQuantity;

    /**
     * 待加工数量ea
     */
    private BigDecimal detailsProcessQuantityEa;

    /**
     * 实际加工数量
     */
    private BigDecimal detailsActualProcessQuantity;

    /**
     * 实际加工数量ea
     */
    private BigDecimal detailsActualProcessQuantityEa;

    /**
     * 分配数ea
     */
    private BigDecimal qtyAllocationEa;

    /**
     * 分配状态，数据字典：alloc_status
     */
    private String allocStatus;

    /**
     * 拣货数ea
     */
    private BigDecimal qtyPickingEa;

    /**
     * 拣货状态，数据字典：picking_status
     */
    private String pickingStatus;

}