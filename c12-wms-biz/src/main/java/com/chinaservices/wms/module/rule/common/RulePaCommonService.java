package com.chinaservices.wms.module.rule.common;

import com.chinaservices.core.enums.StatusEnum;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.enums.rule.PaTaskStatusEnum;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.module.asn.receive.model.AsnReceive;
import com.chinaservices.wms.module.basic.sku.dao.SkuDao;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.rule.pa.dao.RulePaTaskDao;
import com.chinaservices.wms.module.rule.pa.dao.RulePaTaskDetailDao;
import com.chinaservices.wms.module.rule.pa.domain.RulePaTaskDetailLocItem;
import com.chinaservices.wms.module.rule.pa.domain.RulePaTaskLocSku;
import com.chinaservices.wms.module.rule.pa.model.RulePaTask;
import com.chinaservices.wms.module.stock.mutli.dao.InvLotLocDao;
import com.chinaservices.wms.module.warehouse.loc.dao.WarehouseLocDao;
import com.chinaservices.wms.module.warehouse.loc.domain.WarehouseLocPaCondition;
import com.chinaservices.wms.module.warehouse.loc.domain.WarehouseLocPaQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RulePaCommonService {
    @Autowired
    private RulePaTaskDetailDao rulePaTaskDetailDao;
    @Autowired
    private RulePaTaskDao rulePaTaskDao;
    @Autowired
    private WarehouseLocDao warehouseLocDao;
    @Autowired
    private SkuService skuService;
    @Autowired
    private InvLotLocDao invLotLocDao;
    @Autowired
    private SkuDao skuDao;


    public Long checkRulePaTaskAndGetLoc(AsnReceive asnReceive) {
        // 1. 查询商品信息
        Sku sku = skuService.getBySkuCode(asnReceive.getSkuCode());
        if (Objects.isNull(sku) || EmptyUtil.isEmpty(sku.getPaRuleId())){
            return null;
        }
        // 2. 根据id查找上架规则
        RulePaTask header = rulePaTaskDao.findFirst( new ConditionRule().andEqual(RulePaTask::getId,sku.getPaRuleId()).andEqual(RulePaTask::getStatus, StatusEnum.YES.getCode()) );
        if (EmptyUtil.isEmpty(header)) {
            return null;
        }
        // 3. 查询明细
        List<RulePaTaskDetailLocItem> detailByHeaderId = rulePaTaskDetailDao.getDetailByHeaderId(header.getId());
        if (EmptyUtil.isEmpty(detailByHeaderId)) {
            return null;
        }
        // 4.组装商品信息
        RulePaTaskLocSku rulePaTaskLocSku = new RulePaTaskLocSku();
        BeanUtil.copyProperties(sku, rulePaTaskLocSku);
        rulePaTaskLocSku.setSkuId(sku.getId());
        rulePaTaskLocSku.setOwnerId(sku.getOwnerId());
        rulePaTaskLocSku.setWeight(sku.getGrossWeight());
        rulePaTaskLocSku.setNum(asnReceive.getQtyRcvEa());
        rulePaTaskLocSku.setPalletNo(asnReceive.getRcvPallet());
        rulePaTaskLocSku.setRcvLocId(asnReceive.getRcvLocId());
        rulePaTaskLocSku.setLotCode(asnReceive.getLotNum());

        // 5.校验四大主规则，获取库位信息
        return getLocQuery(detailByHeaderId, rulePaTaskLocSku);
    }


    /**
     * 四大类主规则
     *
     * @param detailByHeaderId
     * @param sku
     * @return
     */
    public Long getLocQuery(List<RulePaTaskDetailLocItem> detailByHeaderId, RulePaTaskLocSku sku) {
        Long receiveLocId = null;
        for (RulePaTaskDetailLocItem e : detailByHeaderId) {
            // 如果query有值就不在循环进入其他校验
            if (Objects.nonNull(receiveLocId)) {
                break;
            }
            if (YesNoEnum.NO.getCode().equals(e.getStatus())) {
                continue;
            }
            log.error("开始{}:{}", e.getMasterRule(), PaTaskStatusEnum.getNameByCode(e.getMasterRule()));
            // 规则逻辑
            switch (Objects.requireNonNull(PaTaskStatusEnum.fromCode(e.getMasterRule()))) {
                // A->A01上架到上架规则指定的目标库位  --> 强制指定的库位  ->  上架规则
                case SPECIFIED_PA_TASK:
                    if (!WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode().equals(e.getToLocCode())) {
                        receiveLocId = checkRuleLoc(e, e.getToLocId(), sku);
                    }
                    log.error("主规则A01：{}",receiveLocId);
                    continue;
                //A->A02 上架到商品指定的收货库位  --> 强制指定的库位  ->  商品的收货库位
                case SPECIFIED_SKU:
                    if (EmptyUtil.isNotEmpty(sku.getLocId())) {
                        receiveLocId = checkRuleLoc(e, sku.getLocId(), sku);
                    }
                    log.error("主规则A02：{}",receiveLocId);
                    continue;
                // A->A03 商品收货库位 = 上架规则的源库位  --> 强制指定的库位  -> 上架规则
                case SPECIFIED_EQUAL:
                    if (EmptyUtil.isNotEmpty(e.getFmLocId()) && EmptyUtil.isNotEmpty(sku.getRcvLocId()) && sku.getRcvLocId().equals(e.getFmLocId())) {
                        receiveLocId = checkRuleLoc(e, e.getToLocId(), sku);
                    }
                    log.error("主规则A03：{}",receiveLocId);
                    continue;
                // B-B01在上架规则指定的<目标库区>内查找空库位    ->规则+规则的库区+商品信息+商品数量 pa_seq上架顺序 floor层 seq序号
                case NULL_LOC_PA_TASK:
                    receiveLocId = ruleLoc(e, e.getZoneId(), sku, YesNoEnum.YES.getCode());
                    log.error("主规则B01：{}",receiveLocId);
                    continue;
                // B-B02在商品指定的<上架库区>内查找空库位    ->规则+商品的库区+商品信息+商品数量
                case NULL_LOC_SKU:
                    receiveLocId = ruleLoc(e, sku.getZoneId(), sku, YesNoEnum.YES.getCode());
                    log.error("主规则B02：{}",receiveLocId);
                    continue;
                // C-C01在上架规则指定的<目标库区>内查找非空库位    ->规则+规则的库区+商品信息+商品数量 pa_seq上架顺序 floor层 seq序号
                case NOT_NULL_LOC_PA_TASK:
                    receiveLocId = ruleLoc(e, e.getZoneId(), sku, YesNoEnum.NO.getCode());
                    log.error("主规则C01：{}",receiveLocId);
                    continue;
                // C-C02在商品指定的<上架库区>内查找非空库位    ->规则+商品的库区+商品信息+商品数量
                case NOT_NULL_LOC_SKU:
                    receiveLocId = ruleLoc(e, sku.getZoneId(), sku, YesNoEnum.NO.getCode());
                    log.error("主规则C02：{}",receiveLocId);
                    continue;
                // C-C03在上架规则指定的目标库区内查找相同客户的非空库位    ->规则+相同客户的库区+商品信息+商品数量
                case NOT_NULL_LOC_OWNER:
                    receiveLocId = ruleLoc(e, sku.getZoneId(), sku, YesNoEnum.NO.getCode());
                    log.error("主规则C03：{}",receiveLocId);
                    continue;
                // D-D01 在上架规则指定的目标库区内查找存在相同商品的库位 库区+商品
                case SAME_SKU_LOC_PA_TASK:
                    receiveLocId = ruleLoc(e, e.getZoneId(), sku, null);
                    log.error("主规则D01：{}",receiveLocId);
                    continue;
                // D-D02 在商品指定的上架库区内查找存在相同商品的库位 库区+商品
                case SAME_SKU_LOC_SKU:
                    receiveLocId = ruleLoc(e, sku.getZoneId(), sku, null);
                    log.error("主规则D02：{}",receiveLocId);
                    continue;
                default:
                    // 其他状态无需操作
            }
        }
        ;
        return receiveLocId;
    }

    /**
     * 根据库区的空库位
     *
     * @param detail    上架规则信息
     * @param zoneId    要查找的库区
     * @param sku       商品信息
     * @param isNullLoc 是否是空库位 1是0否 null=D方法不校验
     * @return
     */
    public Long ruleLoc(RulePaTaskDetailLocItem detail, Long zoneId, RulePaTaskLocSku sku, String isNullLoc) {
        // 根据库区查找匹配的库位，库区不能为空
        if (Objects.isNull(zoneId)) {
            return null;
        }
        // BC不需要校验商品；D方法要查询商品 isNull = null
        boolean checkSku = isNullLoc == null;
        String nullLoc;
        if (checkSku){
            nullLoc = YesNoEnum.NO.getCode();
        }else {
            nullLoc = isNullLoc;
        }
        // 1.根据库区获取库位信息
        List<WarehouseLocPaQuery> warehouseLocByZoneList = getWarehouseLocByZone(detail, zoneId, sku, checkSku);
        if (EmptyUtil.isEmpty(warehouseLocByZoneList)) {
            return null;
        }
        log.error("根据库区获取库位信息:"+warehouseLocByZoneList);
        // 2.过滤出空/非空库位 B：空库位 CD：非空库位
        warehouseLocByZoneList = warehouseLocByZoneList.stream()
                .filter(e -> nullLoc.equals(e.getIsEmptyLoc()))
                .toList();
        if (EmptyUtil.isEmpty(warehouseLocByZoneList)) {
            return null;
        }
        log.error("是否需要过滤空/非空库位 ===> BC方法:"+warehouseLocByZoneList);
        // 3.校验库位信息（是否允许混商品，混批次，允许混放的数量）
        warehouseLocByZoneList = checkLocData(warehouseLocByZoneList, sku);
        if (EmptyUtil.isEmpty(warehouseLocByZoneList)) {
            return null;
        }
        log.error("校验库位信息（是否允许混商品，混批次，允许混放的数量）:"+warehouseLocByZoneList);

        // 4.空间限制校验
        warehouseLocByZoneList = checkCompact(detail, warehouseLocByZoneList, sku);
        if (EmptyUtil.isEmpty(warehouseLocByZoneList)) {
            return null;
        }
        log.error("空间限制校验:"+warehouseLocByZoneList);

        // 5.根据库位顺序，获取库位信息
        WarehouseLocPaQuery minWarehouseLocPaQuery = warehouseLocByZoneList.stream()
                .min(Comparator.comparingInt(e ->
                        Integer.parseInt(e.getPaSeq()) // 字符串转为整数再比较
                ))
                .orElse(null);
        if (minWarehouseLocPaQuery == null) {
            return null;
        }
        log.error("根据库位顺序，获取库位信息:"+minWarehouseLocPaQuery);

        return minWarehouseLocPaQuery.getId();
    }

    /**
     * 根据条件查询库位信息
     *
     * @param detail 上架规则的校验
     * @param zoneId 要查询的库区
     * @param sku  需要查询的商品
     * @param flag   是否需要查询商品信息D方法需要查询商品
     * @return
     */
    public List<WarehouseLocPaQuery> getWarehouseLocByZone(RulePaTaskDetailLocItem detail, Long zoneId, RulePaTaskLocSku sku, boolean flag) {
        WarehouseLocPaCondition condition = new WarehouseLocPaCondition();
        condition.setZoneId(zoneId);
        String[] hideLocList = {WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(),
                WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(),
        };
        condition.setHideLocList(hideLocList);
        // 库位使用类型限制校验
        processField(condition, detail.getLocUseType(),
                detail.getLocUseTypeExist(), WarehouseLocPaCondition::setLocUseTypeList,
                detail.getLocUseTypeAbsent(), WarehouseLocPaCondition::setHideLocUseTypeList
        );
        // 库位种类限制
        processField(condition, detail.getCategory(),
                detail.getCategoryExist(), WarehouseLocPaCondition::setCategoryList,
                detail.getCategoryAbsent(), WarehouseLocPaCondition::setHideCategoryList
        );
        // 库位ABC限制
        processField(condition, detail.getAbc(),
                detail.getAbcExist(), WarehouseLocPaCondition::setAbcList,
                detail.getAbcAbsent(), WarehouseLocPaCondition::setHideAbcList
        );
        // todo 是否需要校验商品的abc
        if (YesNoEnum.YES.getCode().equals(detail.getAbc()) && EmptyUtil.isNotEmpty(sku.getAbc())){
            // 存在ABC
            condition.setAbc(sku.getAbc());
        }
        // C03规则需要查询相同客户
        if (PaTaskStatusEnum.NOT_NULL_LOC_OWNER.getCode().equals(detail.getMasterRule())){
            condition.setOwnerId(sku.getOwnerId());
        }
        // D规则需要查询是否存在相同商品
        if (flag) {
            // 存在相同商品
            condition.setSkuId(sku.getSkuId());
        }
        return warehouseLocDao.getWarehouseLocByZone(condition);
    }


    /**
     * @param condition    需要set值的condition
     * @param flag         该限制是否启用
     * @param existStr     包含的值
     * @param existSetter  包含值需要set的字段
     * @param absentStr    不包含的值
     * @param absentSetter 不包含值需要set的字段
     */
    private void processField(WarehouseLocPaCondition condition, String flag,
                              String existStr, BiConsumer<WarehouseLocPaCondition, String[]> existSetter,
                              String absentStr, BiConsumer<WarehouseLocPaCondition, String[]> absentSetter) {

        // 1. 判断是否启用该字段逻辑
        if (!YesNoEnum.YES.getCode().equals(flag)) {
            return;
        }

        // 2. 处理存在值
        if (EmptyUtil.isNotEmpty(existStr)) {
            existSetter.accept(condition, existStr.split(","));
        }

        // 3. 处理不存在值
        if (EmptyUtil.isNotEmpty(absentStr)) {
            absentSetter.accept(condition, absentStr.split(","));
        }
    }

    /**
     * 空间限制校验
     */
    private List<WarehouseLocPaQuery> checkCompact(RulePaTaskDetailLocItem detail, List<WarehouseLocPaQuery> warehouseLocByZoneList, RulePaTaskLocSku sku) {
        // 1:如果没有开启库位空间限制，则无需校验
        if (!YesNoEnum.YES.getCode().equals(detail.getCompact())) {
            return warehouseLocByZoneList;
        }
        // 2：缓存常用配置参数
        boolean checkVolume = YesNoEnum.YES.getCode().equals(detail.getVolumetric());
        boolean checkWeight = YesNoEnum.YES.getCode().equals(detail.getWeightRestraint());
        boolean checkPallet = YesNoEnum.YES.getCode().equals(detail.getPallet());

        BigDecimal num = sku.getNum();
        // 使用流式处理构建新列表（保持原有顺序）
        return warehouseLocByZoneList.stream()
                .filter(e -> {
                    BigDecimal sumVolume = BigDecimal.ZERO;
                    BigDecimal sumWeight = BigDecimal.ZERO;
                    if (YesNoEnum.NO.getCode().equals(e.getIsEmptyLoc())){
                        RulePaTaskLocSku ruleDataBySkuIdAndLoc = invLotLocDao.getRuleDataBySkuIdAndLoc(e.getLocCode());
                        sumVolume = ruleDataBySkuIdAndLoc.getSumVolume();
                        sumWeight = ruleDataBySkuIdAndLoc.getSumWeight();
                        // 不为空库位时，检查容器量限制: 库位的最大容器量 最大容器数
                        if (checkPallet && e.getMaxPl() != null) {
                            if (e.getPalletNum() == null) {
                                e.setPalletNum(BigDecimal.ZERO); // 防止空指针
                            }
                            BigDecimal palletNum = e.getPalletNum().add(BigDecimal.ONE);
                            if (palletNum.compareTo(BigDecimal.valueOf(e.getMaxPl())) > 0) {
                                log.info("palletNum:"+palletNum);
                                return false; // 不通过检查，保留到排除列表
                            }
                        }
                    }
                    // 检查体积限制: 库位的体积是否超出 限制体积
                    if (checkVolume && sku.getVolume() != null && e.getMaxCubic() != null) {
                        BigDecimal volume = sku.getVolume().multiply(num).add(sumVolume);
                        if (volume.compareTo(e.getMaxCubic()) > 0) {
                            log.info("sumVolume:"+sumVolume);
                            return false; // 不通过检查，保留到排除列表
                        }
                    }
                    // 检查重量限制: 库位的重量是否超出 限制重量
                    if (checkWeight && sku.getWeight() != null && e.getMaxWeight() != null) {
                        BigDecimal weight = sku.getWeight().multiply(num).add(sumWeight);
                        if (weight.compareTo(e.getMaxWeight()) > 0) {
                            log.info("sumWeight:"+sumWeight);
                            return false; // 不通过检查，保留到排除列表
                        }
                    }
                    return true; // 通过检查，保留到结果列表
                })
                .collect(Collectors.toList());

    }

    /**
     * 校验库位规则
     * @param warehouseLocByZoneList 需要校验的库位数据
     * @param sku 商品信息
     * @return 可用库位
     */
    public List<WarehouseLocPaQuery> checkLocData(List<WarehouseLocPaQuery> warehouseLocByZoneList, RulePaTaskLocSku sku) {

        return warehouseLocByZoneList.stream()
                .filter(e -> {
                    if (YesNoEnum.YES.getCode().equals(e.getIsEmptyLoc())){
                        return true;
                    }
                    // 检查商品匹配规则
                    boolean isMixSku = YesNoEnum.YES.getCode().equals(e.getIsMixSku());
                    if (isMixSku) {
                        if (e.getSkuNum() != null && e.getMaxMixSku() != null
                                && e.getSkuNum().compareTo(e.getMaxMixSku()) >= 0) {
                            // 库存数量达到阈值，需查询是否存在该商品
                            WarehouseLocPaCondition condition = new WarehouseLocPaCondition();
                            condition.setLocCode(e.getLocCode());
                            condition.setSkuId(e.getSkuId());
                            return !EmptyUtil.isEmpty(warehouseLocDao.getWarehouseLocByZone(condition));
                        }
                    } else {
                        // 非混合模式，必须商品ID严格匹配
                        if (e.getSkuNum().compareTo(BigDecimal.ZERO) == 0) {
                            return true;
                        }
                        return sku.getSkuId().equals(e.getSkuId());
                    }

                    // 检查批次匹配规则
                    boolean isMixLot = YesNoEnum.YES.getCode().equals(e.getIsMixLot());
                    if (isMixLot) {
                        if (e.getLotNum() != null && e.getMaxMixLot() != null
                                && e.getLotNum().compareTo(BigDecimal.valueOf(e.getMaxMixLot())) >= 0) {
                            // 库存批次号数量达到阈值，需查询是否存在该批次
                            WarehouseLocPaCondition condition = new WarehouseLocPaCondition();
                            condition.setLocCode(e.getLocCode());
                            return !EmptyUtil.isEmpty(warehouseLocDao.getWarehouseLocByZone(condition));
                        }
                    } else {
                        // 非混合模式，必须批次ID严格匹配
                        // todo 商品批次属性是否必填
                        if (sku.getLotCode() == null){
                            return true;
                        }
                        return sku.getLotCode().equals(e.getLotCode());
                    }
                    return true;
                })
                .collect(Collectors.toList());
    }


    /**
     * 指定库位的校验
     */
    public Long checkRuleLoc(RulePaTaskDetailLocItem detail, Long locId, RulePaTaskLocSku sku) {
        WarehouseLocPaCondition condition = new WarehouseLocPaCondition();
        String[] hideLocList = {WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(),
                WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(),
        };
        condition.setHideLocList(hideLocList);
        condition.setId(locId);
        // 1. 库位数据查询
        List<WarehouseLocPaQuery> warehouseLocByZoneList = warehouseLocDao.getWarehouseLocByZone(condition);
        if (EmptyUtil.isEmpty(warehouseLocByZoneList)) {
            return null;
        }
        // 2.空间限制校验
        warehouseLocByZoneList = checkCompact(detail, warehouseLocByZoneList, sku);
        if (EmptyUtil.isEmpty(warehouseLocByZoneList)) {
            return null;
        }
        // 3.校验库位信息（是否允许混商品，混批次，允许混放的数量）
        warehouseLocByZoneList = checkLocData(warehouseLocByZoneList, sku);
        if (EmptyUtil.isEmpty(warehouseLocByZoneList)) {
            return null;
        }
        return warehouseLocByZoneList.getFirst().getId();
    }

}
