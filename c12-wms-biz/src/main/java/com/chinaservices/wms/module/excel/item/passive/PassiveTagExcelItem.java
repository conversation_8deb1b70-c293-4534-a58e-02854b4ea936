package com.chinaservices.wms.module.excel.item.passive;


import cn.idev.excel.annotation.ExcelProperty;
import com.chinaservices.excel.annotation.ExcelDictFormat;
import com.chinaservices.excel.convert.ExcelDictConvert;
import com.chinaservices.sdk.validator.annotation.DictValid;
import jakarta.validation.constraints.NotBlank;

public class PassiveTagExcelItem {

	@NotBlank(message = "标签号不能为空")
	@ExcelProperty(value = "标签号", index = 0)
	private String tagNo;

	@NotBlank(message = "标签类型不能为空")
	@ExcelProperty(value = "标签类型", index = 1, converter = ExcelDictConvert.class)
	@ExcelDictFormat(dictType = "passive_tag_type", mismatchBehavior = ExcelDictFormat.MismatchBehavior.KEEP_ORIGINAL)
	@DictValid(dictType = "passive_tag_type", message = "标签类型不符合要求", allowNull = true)
	private String tagType;

	@NotBlank(message = "归属仓库不能为空")
	@ExcelProperty(value = "归属仓库", index = 2)
	private String warehouseName;

	public String getTagNo() {
		return tagNo;
	}

	public void setTagNo(String tagNo) {
		this.tagNo = tagNo;
	}

	public String getTagType() {
		return tagType;
	}

	public void setTagType(String tagType) {
		this.tagType = tagType;
	}

	public String getWarehouseName() {
		return warehouseName;
	}

	public void setWarehouseName(String warehouseName) {
		this.warehouseName = warehouseName;
	}
}