package com.chinaservices.wms.module.asn.receive.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.session.SessionContext;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.enums.AuditStatusEnum;
import com.chinaservices.wms.common.enums.ReceiveStatusEnum;
import com.chinaservices.wms.common.enums.archive.common.YesOrNoEnum;
import com.chinaservices.wms.common.enums.passive.PassiveTagStatusEnum;
import com.chinaservices.wms.common.enums.so.PackShippingStatusEnum;
import com.chinaservices.wms.common.exception.AsnExceptionEnum;
import com.chinaservices.wms.common.exception.PassiveExceptionEnum;
import com.chinaservices.wms.common.properties.ExecutorThreadProperties;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailDao;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailSnDao;
import com.chinaservices.wms.module.asn.asn.dao.AsnHeaderDao;
import com.chinaservices.wms.module.asn.asn.model.AsnDetail;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.receive.domain.AsnReceiptDetailItem;
import com.chinaservices.wms.module.basic.cspackage.dao.PackageUnitDao;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.lot.dao.WarehouseLotDetailDao;
import com.chinaservices.wms.module.basic.lot.model.WarehouseLotDetail;
import com.chinaservices.wms.module.basic.sku.dao.SkuDao;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.passive.tag.model.PassiveTag;
import com.chinaservices.wms.module.passive.tag.model.PassiveTagRecognition;
import com.chinaservices.wms.module.passive.tag.service.PassiveTagRecognitionService;
import com.chinaservices.wms.module.passive.tag.service.PassiveTagService;
import com.chinaservices.wms.module.so.pack.domain.ShippedExceptionQuery;
import com.chinaservices.wms.module.so.pack.domain.ShippedSkuCheckQtyQuery;
import com.chinaservices.wms.module.so.pack.model.PackHeader;
import com.chinaservices.wms.module.so.pack.service.PackHeaderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AsnReceivePassiveService {

    @Autowired
    private AsnHeaderDao asnHeaderDao;

    @Autowired
    private AsnReceiveService asnReceiveService;
    @Autowired
    private AsnDetailDao asnDetailDao;
    @Autowired
    private SkuDao skuDao;
    @Autowired
    private WarehouseLotDetailDao warehouseLotDetailDao;
    private AsnDetailSnDao asnDetailSnDao;
    @Autowired
    private PackageUnitDao packageUnitDao;

    @Autowired
    @Qualifier(value = ExecutorThreadProperties.TRANSACTION_THREAD_EXECUTOR)
    private ExecutorService executorService;

    @Autowired
    private PackHeaderService packHeaderService;

    @Autowired
    private PassiveTagRecognitionService passiveTagRecognitionService;

    @Autowired
    private PassiveTagService passiveTagService;


    private static final String LOG = "【无源出入库】";
    /**
     * 无源出入库方法入口
     * @param tagNoList 标签号
     */
    @Transactional
    public void createAsnOrderOrSoOrderByListTag(List<String> tagNoList){
        List<CompletableFuture<Boolean>> futures = new ArrayList<>();
        for (String tagNo : tagNoList) {
            log.info("{}方法入口线程 开始执行： tagNo:{}",LOG,tagNoList);
            futures.add(CompletableFuture.supplyAsync(() -> createAsnOrderOrSoOrderReturnBooleanByTag(tagNo), executorService));
        }
        // 等待所有任务完成
        StringBuilder bui = new StringBuilder();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        for (CompletableFuture<Boolean> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof ServiceException serviceException){
                    bui.append(serviceException.getMessage()).append(",");
                }else{
                    log.error(LOG + "方法入口线程执行失败,系统异常",e);
                    throw new ServiceException(AsnExceptionEnum.RECEIVE_BATCH_ERROR);
                }
            }
        }

        //验证是否存在错误信息，如果存在则直接提示报错信息
        if (EmptyUtil.isNotEmpty(bui.toString())){
            String msg = bui.deleteCharAt(bui.length() - 1).toString();
            throw new ServiceException(msg);
        }
    }

    /**
     * 无源出入库方法入口返回布尔值
     *
     * @param tagNo 标签没有
     * @return {@link Boolean }
     */
    @Transactional
    public Boolean createAsnOrderOrSoOrderReturnBooleanByTag(String tagNo) {
        this.createAsnOrderOrSoOrderByTag(tagNo);
        return Boolean.TRUE;
    }

    /**
     * 无源出入库方法入口
     * @param tagNo 标签号
     */
    @Transactional
    public String createAsnOrderOrSoOrderByTag(String tagNo) {
        //1. 先校验标签是否存在，不存在提示”{标签号}在系统中不存在，请核查“，流程结束；存在继续校验；
        PassiveTag passiveTag = passiveTagService.findFirst(new ConditionRule().andEqual(PassiveTag::getTagNo, tagNo));
        if (EmptyUtil.isEmpty(passiveTag)) {
            //不存在提示”{标签号}在系统中不存在，请核查“，流程结束；存在继续校验；
            throw new ServiceException(AsnExceptionEnum.TAG_NO_NOT_DATABASE,tagNo);
        }
        if (ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode().equals(passiveTag.getReceiveStatus())){
            throw new ServiceException(AsnExceptionEnum.PASSIVES_ALL_RECEIVE,passiveTag.getTagNo());
        }
        PassiveTagStatusEnum tagStatusEnum = EnumUtil.getBy(PassiveTagStatusEnum::getCode, passiveTag.getStatus());
        //2. 校验标签状态，标签状态如果是已失效/已拆包/已损坏，提示“{标签号}已失效/已拆包/已损坏，请核查”，流程结束；非以上状态继续校验；
        switch (tagStatusEnum) {
            case INVALID,UNPACKED,DAMAGED -> throw new ServiceException(AsnExceptionEnum.RECEIVE_TAG_NO_STATUS_ERROR,passiveTag.getTagNo(),tagStatusEnum.getName());
        }
        //3. 标签关联已入库订单并且收货状态是未收货，走自动收货逻辑，流程结束；非自动收货继续校验；
        if (ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(passiveTag.getReceiveStatus())){
            //走自动收货逻辑
            AsnReceiptDetailItem detailItem = initialAutoReceiveObject(passiveTag);
            asnReceiveService.receiptConfirmation(detailItem);
            log.info("{} 方法初始化完毕,生成收货数据:{}, 开始调用收货方法",LOG,JSONUtil.toJsonStr(detailItem));
            passiveTag.setReceiveStatus(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
            passiveTagService.preSave(passiveTag);
            passiveTagService.update(passiveTag);
            return null;
        }
        // 3. 走自动收货逻辑，流程结束；非自动收货继续校验；
        // 4. 标签关联发运单号并且发运状态是待派车OR待发运，走自动发运逻辑，非自动发运，给出提示“无法判断{标签号}是自动收货还是自动发运，请核查该标签是否绑定非货物信息，或已发运”
        if (PackShippingStatusEnum.PENDING_SHIPMENT.getCode().equals(passiveTag.getShippingStatus()) || PackShippingStatusEnum.PENDING_DISPATCH.getCode().equals(passiveTag.getShippingStatus())) {
            ShippedExceptionQuery query = initialAutoDispatchLong(passiveTag);
            log.info("{} 方法初始化完毕", LOG);
            if (EmptyUtil.isNotEmpty(query) && EmptyUtil.isNotEmpty(query.getId())) {
                // 走自动发运逻辑
                log.info("获取发运单Id:{}, 开始调用发运方法", query.getId());
                packHeaderService.confirm(query.getId());
                // 标签状态更新为已失效
                passiveTag.setShippingStatus(PackShippingStatusEnum.IN_TRANSIT.getCode());
                passiveTag.setStatus(PassiveTagStatusEnum.INVALID.getCode());
                passiveTagService.preSave(passiveTag);
                passiveTagService.update(passiveTag);
            }
            return query.getErrorReason();
        }
        // 非自动发运，给出提示“无法判断{标签号}是自动收货还是自动发运，请核查该标签是否绑定非货物信息，或已发运”
        throw new ServiceException(PassiveExceptionEnum.PASSIVE_NOT_TAG_DISPATCH, passiveTag.getTagNo());
    }

    /**
     * 初始化收货对象
     * @param passiveTag 标签对象
     * @return {@link AsnReceiptDetailItem}
     */
    private AsnReceiptDetailItem initialAutoReceiveObject(PassiveTag passiveTag){
        //1. 先标签关联入库订单是否已审核，是流程继续，
        AsnHeader asnHeader = asnHeaderDao.findFirst(new ConditionRule().andEqual(AsnHeader::getAsnNo, passiveTag.getAsnNo()));
        if (EmptyUtil.isEmpty(asnHeader.getReceiverId())){
            //设置系统管理员
            asnHeader.setReceiverId(SessionContext.getSessionUserInfo().getUserId());
            asnHeader.setReceiverOp(SessionContext.getSessionUserInfo().getRealName());
        }
        if (AuditStatusEnum.UNREVIEWED.getCode().equals(asnHeader.getAuditStatus())) {
            //否提示“{标签号}绑定的{入库订单号}未审核，请核查”
            throw new ServiceException(AsnExceptionEnum.RECEIVE_TAG_NO_BUILDER_ORDER_AUDIT_ERROR,passiveTag.getTagNo(),asnHeader.getAsnNo());
        }
        //2. 判断标签绑定商品编号，标签关联入库订单商品编号是否有，
        List<AsnDetail> asnDetailList = asnDetailDao.find(new ConditionRule().andEqual(AsnDetail::getAsnNo, passiveTag.getAsnNo())
                .andEqual(AsnDetail::getSkuId, passiveTag.getSkuId()));
        if (EmptyUtil.isEmpty(asnDetailList)){
            //没有，提示“{标签号}商品编号与入库订单商品编号不一致”，流程结束
            throw new ServiceException(AsnExceptionEnum.RECEIVE_TAG_NO_NOT_EQ_SKU_CODE,passiveTag.getTagNo());
        }
        //找出第一个未收货的数据进行收货
        AsnDetail asnDetail = new AsnDetail();
        for (AsnDetail item : asnDetailList) {
            //预收数>已收数
            if (item.getQtyRcvEa() == null){
                item.setQtyRcvEa(BigDecimal.ZERO);
            }
            if (item.getQtyPlanEa().compareTo(item.getQtyRcvEa()) > 0) {
                //非完全收货
                asnDetail = item;
                break;
            }
        }
        //校验是否序列商品，不是，自动收货；
        Sku sku = skuDao.findById(asnDetail.getSkuId());
        List<WarehouseLotDetail> detailList = warehouseLotDetailDao.find(new ConditionRule().andEqual(WarehouseLotDetail::getLotId, sku.getLotId())
                .andEqual(WarehouseLotDetail::getInputControl,YesOrNoEnum.YES.getCode()));
        //验证批次属性
        validateLotAtt(detailList,asnDetail,passiveTag.getTagNo());
        //生成最终自动收货对象
        AsnReceiptDetailItem item = new AsnReceiptDetailItem();
        item.setAsnNo(passiveTag.getAsnNo());
        item.setId(asnDetail.getId());
        item.setQtyPlanEa(asnDetail.getQtyPlanEa());
        item.setSkuCode(asnDetail.getSkuCode());
        item.setRcvLocId(asnDetail.getPlanToLocId());
        item.setRcvLocName(asnDetail.getPlanToLocName());
        item.setTagNo(passiveTag.getTagNo());
        copyAtt(asnDetail,item);
        //设置序列号和收货数EA
        initialReceiveSnNoListAndEa(sku,passiveTag,item);
        return item;
    }

    /**
     * 初始化SN列表和EA数
     * @param sku 商品
     * @param passiveTag 标签
     * @param item 收货对象
     */
    private void initialReceiveSnNoListAndEa(Sku sku, PassiveTag passiveTag,AsnReceiptDetailItem item) {
        //验证是否存在序列号
        if (YesOrNoEnum.YES.getCode().equals(sku.getWhetherSerialController())){
            //是序列号控制
            if (EmptyUtil.isNotEmpty(passiveTag.getBoxCode())){
                List<AsnDetailSn> asnDetailSns = asnDetailSnDao.find(new ConditionRule().andEqual(AsnDetailSn::getBoxCode, passiveTag.getBoxCode()));
                List<String> list = asnDetailSns.stream().map(AsnDetailSn::getSnNo).toList();
                item.setSkuSn(list);
                item.setQtyRcvEa(new BigDecimal(list.size()));
            }else{
                item.setSkuSn(Arrays.asList(passiveTag.getSnNo()));
                item.setQtyRcvEa(BigDecimal.ONE);
            }
        }else{
            //通过标签绑定的单位 去转换EA数
            PackageUnit packageUnit = packageUnitDao.findById(passiveTag.getPackageUnitId());
            item.setQtyRcvEa(Convert.toBigDecimal(packageUnit.getMinQuantity()));
        }
    }

    /**
     * 复制12个批次属性
     * @param source 源
     * @param target 目标
     */
    private void copyAtt(Object source, Object target){
        for (int i = 1; i <= 12; i++) {
            String num = "";
            if (i >= 10){
                num = i + "";
            }else {
                num = "0" + i;
            }
            String get = "getLotAtt" + num;
            String set = "setLotAtt" + num;
            //执行get
            Object invoke = ReflectUtil.invoke(source, get);
            if (EmptyUtil.isEmpty(invoke))continue;
            //执行set
            ReflectUtil.invoke(target, set, invoke);
        }
    }

    /**
     * 验证必填项批次属性
     * @param detailList 必填属性列表
     * @param asnDetail 需验证的属性对象
     * @param tagNo 标签号
     */
    private void validateLotAtt(List<WarehouseLotDetail> detailList,AsnDetail asnDetail,String tagNo){
        detailList.forEach(detail -> {
            String substring = detail.getLotAtt().substring(detail.getLotAtt().length() - 2, detail.getLotAtt().length());
            String param = "getLotAtt" + substring;
            Object invoke = ReflectUtil.invoke(asnDetail, param);
            if (EmptyUtil.isEmpty(invoke)) {
                //{标签号}绑定{入库订单}批次属性未填写，请在入库单编辑处进行编辑或手动收货
                throw new ServiceException(AsnExceptionEnum.RECEIVE_TAG_BUILDER_ORDER_NOT_ATT_ERROR,tagNo,asnDetail.getAsnNo());
            }
        });
    }

    /**
     * 初始自动发运
     *
     * @param passiveTag 标签
     * @return {@link Long }
     */
    public ShippedExceptionQuery initialAutoDispatchLong(PassiveTag passiveTag) {
        ShippedExceptionQuery query = new ShippedExceptionQuery();

        // 1. 校验标签号是否有收货，没有查标签号绑定的关联标签是否有收货，没有提示“{标签号}关联{发运单}，没有收货信息，请核查”，不论结果继续校验；
        if (EmptyUtil.isEmpty(passiveTag.getReceiveStatus()) ||
                !ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode().equals(passiveTag.getReceiveStatus())) {
            // 提示“{标签号}关联{发运单}，没有收货信息，请核查”
            query.setErrorReason(String.format(PassiveExceptionEnum.PASSIVE_TAG_DISPATCH_NOT_RECEIVE.getMsg(), passiveTag.getTagNo(), passiveTag.getDispatchNo()));
        }

        // 2. 判断标签绑定商品编号，标签关联发运单商品编号是否一致；
        //   1. 是，校验发运单标签是否已全部识别；
        //   2. 否，提示“{标签号}绑定{商品名称}与关联{运单号}商品信息不一致，请核查，”；该标签不记录识别数
        String dispatchNo = passiveTag.getDispatchNo();
        int exist = packHeaderService.isExistByDispatchNoAndSkuId(dispatchNo, passiveTag.getSkuId());
        if (exist == 0) {
            //   2. 否，提示“{标签号}绑定{商品名称}与关联{运单号}商品信息不一致，请核查，”；该标签不记录识别数
            throw new ServiceException(PassiveExceptionEnum.PASSIVE_TAG_SKU_DISPATCH_SKU_INCONFORMITY, passiveTag.getTagNo(), passiveTag.getSkuName(), dispatchNo);
        }

        // 3. 校验发运单标签是否已全部识别
        //   1. 是，校验识别标签关联商品EA数与发运单EA数是否一致，一致自动发运，将状态更新为运输中，标签状态更新为已失效，不一致提示；“数量信息不一致，请核查”；
        //   2. 否，流程结束；
        List<PassiveTag> tagList = passiveTagService.find(new ConditionRule().andEqual(PassiveTag::getDispatchNo, dispatchNo));
        List<PassiveTagRecognition> recognitionList = passiveTagRecognitionService.find(new ConditionRule().andEqual(PassiveTagRecognition::getDispatchNo, dispatchNo));
        if ((tagList.size() - 1) != recognitionList.size()) {
            //   2. 否，流程结束；
            return null;
        }
        //   1. 是，校验识别标签关联商品EA数与发运单EA数是否一致，一致自动发运，将状态更新为运输中，标签状态更新为已失效，不一致提示；“数量信息不一致，请核查”；
        List<ShippedSkuCheckQtyQuery> shippedSkuEa = packHeaderService.findShippedSkuEa(dispatchNo);
        Map<Long, BigDecimal> qtyMap = shippedSkuEa.stream().collect(Collectors.toMap(ShippedSkuCheckQtyQuery::getSkuId, ShippedSkuCheckQtyQuery::getQtyCheckEa));
        BigDecimal checkQty = qtyMap.get(passiveTag.getSkuId());
        if (checkQty.compareTo(new BigDecimal(passiveTag.getMinEa())) != 0) {
            // 数量信息不一致，请核查
            throw new ServiceException(PassiveExceptionEnum.PASSIVE_SKU_QTY_INCONFORMITY);
        }

        // 4. 获取发运id、识别异常信息
        PackHeader packHeader = packHeaderService.findFirst(new ConditionRule().andEqual(PackHeader::getDispatchNo, dispatchNo));
        query.setId(packHeader.getId());
        return query;
    }
}
