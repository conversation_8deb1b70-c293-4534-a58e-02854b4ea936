package com.chinaservices.wms.module.dashboard.service;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.constant.ControlsConstant;
import com.chinaservices.wms.common.domain.WmsBasePageCondition;
import com.chinaservices.wms.common.enums.asn.PaStatusEnum;
import com.chinaservices.wms.common.enums.so.PackShippingStatusEnum;
import com.chinaservices.wms.common.enums.so.ReceiveStatusEnum;
import com.chinaservices.wms.common.enums.so.SoPickingStatusEnum;
import com.chinaservices.wms.common.enums.so.SoRecheckStatusEnum;
import com.chinaservices.wms.common.service.CommonService;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailDao;
import com.chinaservices.wms.module.asn.asn.dao.AsnHeaderDao;
import com.chinaservices.wms.module.asn.asn.domain.AsnDetailCondition;
import com.chinaservices.wms.module.asn.asn.model.AsnDetail;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.pa.dao.CsPaTaskDao;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskInfoQuery;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskPageCondition;
import com.chinaservices.wms.module.dashboard.domain.*;
import com.chinaservices.wms.module.so.check.dao.SoRecheckHeaderDao;
import com.chinaservices.wms.module.so.check.model.SoRecheckHeader;
import com.chinaservices.wms.module.so.pack.dao.PackBoxDetailDao;
import com.chinaservices.wms.module.so.pack.domain.PackBoxDetailInfo;
import com.chinaservices.wms.module.so.picking.dao.SoPickingDao;
import com.chinaservices.wms.module.so.picking.domain.SoPickingCondition;
import com.chinaservices.wms.module.so.picking.model.SoPicking;
import com.chinaservices.wms.module.so.so.dao.SoHeaderDao;
import com.chinaservices.wms.module.so.so.model.SoHeader;
import com.chinaservices.wms.module.stock.multi.domain.StorageLocationStatisticsCondition;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocService;
import com.chinaservices.wms.module.stock.warning.domain.StockWarningPageCondition;
import com.chinaservices.wms.module.stock.warning.domain.StockWarningPageQuery;
import com.chinaservices.wms.module.stock.warning.dao.StockWarningDao;
import com.chinaservices.wms.module.warehouse.loc.dao.WarehouseLocDao;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.warehouse.dao.WarehouseDao;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DashboardService {
    @Autowired
    private AsnHeaderDao asnHeaderDao;

    @Autowired
    private SoHeaderDao soHeaderDao;

    @Autowired
    private AsnDetailDao asnDetailDao;

    @Autowired
    private CsPaTaskDao csPaTaskDao;

    @Autowired
    private SoPickingDao soPickingDao;

    @Autowired
    private PackBoxDetailDao packBoxDetailDao;

    @Autowired
    private StockWarningDao stockWarningDao;

    @Autowired
    private CommonService commonService;

    @Autowired
    private WarehouseDao warehouseDao;

    @Autowired
    private SoRecheckHeaderDao soRecheckHeaderDao;

    @Autowired
    private InvLotLocService invLotLocService;
    @Autowired
    private WarehouseLocDao warehouseLocDao;

    /**
     * 库位利用率
     * @return
     */
    public StorageLocationUtilization storageLocationUtilization(StatisticsCondition condition) {
        //计算当月
        StorageLocationUtilization storageLocationUtilization = new StorageLocationUtilization();
        //获取当月开始日期和当前日期
        DateTime beginOfMonth = DateUtil.beginOfMonth(new Date());
        DateTime date = DateUtil.endOfMonth(new Date());
        //计算总库位数
        BigDecimal zb = getUtilizationRate(beginOfMonth, date,condition.getWarehouseId());
        StorageLocationUtilization.OccupancyRate rate = new StorageLocationUtilization.OccupancyRate();

        rate.setUtilizationRate(NumberUtil.decimalFormat(ControlsConstant.PERCENTAGE_CONVERSION,zb));

        //计算上个月的数据
        DateTime lastMonth = DateUtil.lastMonth();
        DateTime beginOfMonthUpper = DateUtil.beginOfMonth(lastMonth);
        DateTime dateUpper = DateUtil.endOfMonth(lastMonth);

        //计算总库位数
        BigDecimal zbUpper = getUtilizationRate(beginOfMonthUpper, dateUpper,condition.getWarehouseId());
        BigDecimal sub;
        if (zbUpper.compareTo(zb) > 0){
            //下降
            sub = NumberUtil.div(zb, zbUpper);
            rate.setUpOrDown("1");
        }else if (zbUpper.compareTo(zb) < 0){
            //上升
            sub = NumberUtil.div(zbUpper, zb);
            rate.setUpOrDown("2");
        }else{
            //平等
            sub = BigDecimal.ZERO;
            rate.setUpOrDown("0");
        }

        rate.setRingRate(NumberUtil.decimalFormat(ControlsConstant.PERCENTAGE_CONVERSION,sub));
        storageLocationUtilization.setOccupancyRate(rate);
        //计算占用库位数
        Long occupyLoc = getOccupyLoc(beginOfMonth, date, condition.getWarehouseId());
        StorageLocationUtilization.OccupancyNumber occupancyNumber = new StorageLocationUtilization.OccupancyNumber();
        occupancyNumber.setLocNumber(occupyLoc);
        Long occupyLocUpper = getOccupyLoc(beginOfMonthUpper, dateUpper, condition.getWarehouseId());
        BigDecimal occupyLocSub;
        if (occupyLocUpper.compareTo(occupyLoc) > 0){
            //下降
            occupyLocSub = NumberUtil.div(occupyLoc, occupyLocUpper);
            occupancyNumber.setUpOrDown("1");
        }else if (zbUpper.compareTo(zb) < 0){
            //上升
            occupyLocSub = NumberUtil.div(occupyLocUpper, occupyLoc);
            occupancyNumber.setUpOrDown("2");
        }else{
            //平等
            occupyLocSub = BigDecimal.ZERO;
            occupancyNumber.setUpOrDown("0");
        }
        occupancyNumber.setRingRate(NumberUtil.decimalFormat(ControlsConstant.PERCENTAGE_CONVERSION,occupyLocSub));
        storageLocationUtilization.setOccupancyNumber(occupancyNumber);
        //计算空库位数
        StorageLocationUtilization.EmptyLoc emptyLoc = new StorageLocationUtilization.EmptyLoc();
        emptyLoc.setLocNumber(getEmptyLoc(beginOfMonth,date,condition.getWarehouseId()));
        Long emptyLocUpper = getEmptyLoc(beginOfMonthUpper, dateUpper, condition.getWarehouseId());
        BigDecimal emptyLocSub;
        if (ObjUtil.isNotNull(emptyLoc.getLocNumber()) && emptyLocUpper.compareTo(emptyLoc.getLocNumber()) > 0){
            //下降
            emptyLocSub = NumberUtil.div(emptyLoc.getLocNumber(), emptyLocUpper);
            emptyLoc.setUpOrDown("1");
        }else if (zbUpper.compareTo(zb) < 0 && NumberUtil.equals(BigDecimal.ZERO, emptyLoc.getLocNumber())){
            //上升
            emptyLocSub = NumberUtil.div(emptyLocUpper, emptyLoc.getLocNumber());
            emptyLoc.setUpOrDown("2");
        }else{
            //平等
            emptyLocSub = BigDecimal.ZERO;
            emptyLoc.setUpOrDown("0");
        }
        emptyLoc.setRingRate(NumberUtil.decimalFormat(ControlsConstant.PERCENTAGE_CONVERSION,emptyLocSub));
        storageLocationUtilization.setEmptyLoc(emptyLoc);
        //计算总库位数
        storageLocationUtilization.setLocSum(getSumLoc(beginOfMonth,date,condition.getWarehouseId()));
        // 占有库位环比
        BigDecimal locSub = BigDecimal.ZERO;
        if (ObjectUtil.isNotEmpty(storageLocationUtilization.getLocSum()) && NumberUtil.equals(BigDecimal.ZERO, storageLocationUtilization.getLocSum())){
            locSub = NumberUtil.div(occupancyNumber.getLocNumber(),storageLocationUtilization.getLocSum()).multiply(new BigDecimal("100"));
        }
        storageLocationUtilization.setLocRing(locSub);
        return storageLocationUtilization;
    }

    /**
     * 统计库位利用率实际方法
     * @param begin 开始时间
     * @param end 结束时间
     * @return
     */
    private BigDecimal getUtilizationRate(DateTime begin, DateTime end,Long warehouseId) {
        Long sumLoc = getSumLoc(begin,end,warehouseId);
        //计算占用库位
        Long occupyLoc = getOccupyLoc(begin,end,warehouseId);
        BigDecimal zb = BigDecimal.ZERO;
        if (ObjectUtil.isNotEmpty(sumLoc) && sumLoc != 0L){
            zb = NumberUtil.div(occupyLoc,sumLoc);
        }
        return zb;
    }

    /**
     * 获取总库位数
     * @param begin 开始时间
     * @param end 结束时间
     * @param warehouseId 仓库ID
     * @return
     */
    public Long getSumLoc(DateTime begin, DateTime end,Long warehouseId){
        return warehouseLocDao.count(new ConditionRule().andGreaterEqual(WarehouseLoc::getCreateTime, begin)
                .andLessEqual(InvLotLoc::getCreateTime, end).andEqual(WarehouseLoc::getWarehouseId, warehouseId));
    }

    /**
     * 获取占用库位
     * @param begin 开始时间
     * @param end 结束时间
     * @param warehouseId 仓库ID
     * @return
     */
    private Long getOccupyLoc(DateTime begin, DateTime end,Long warehouseId){
        return invLotLocService.findOccupyLocByWarehouseId(DateUtil.format(begin,DatePattern.NORM_DATETIME_PATTERN),
                DateUtil.format(end,DatePattern.NORM_DATETIME_PATTERN),
                warehouseId).getResult();
       /* return invLotLocService.count(new ConditionRule().andGreaterEqual(InvLotLoc::getCreateTime, begin)
                .andLessEqual(InvLotLoc::getCreateTime, end).andGreaterThan(InvLotLoc::getQty, BigDecimal.ZERO).andEqual(InvLotLoc::getWarehouseId, warehouseId));*/
    }

    /**
     * 获取空库位
     * @param begin 开始时间
     * @param end 结束时间
     * @param warehouseId 仓库ID
     * @return
     */
    private Long getEmptyLoc(DateTime begin, DateTime end,Long warehouseId){
        return invLotLocService.findEmptyLocByWarehouseId(DateUtil.format(begin,DatePattern.NORM_DATETIME_PATTERN),
                DateUtil.format(end,DatePattern.NORM_DATETIME_PATTERN),warehouseId).getResult();
    }

    /**
     * 库位统计
     * @return
     */
    public StorageLocationStatistics storageLocationStatistics(StatisticsCondition condition) {
        StorageLocationStatisticsCondition statisticsCondition = new StorageLocationStatisticsCondition();
        statisticsCondition.setCreateTime(DateUtil.offsetDay(DateUtil.parseDate(DateUtil.formatDate(DateUtil.date())),-6));
        statisticsCondition.setWarehouseId(condition.getWarehouseId());
        List<InvLotLoc> locList = invLotLocService.storageLocationStatistics(statisticsCondition);
        List<DateTime> rangeToList = DateUtil.rangeToList(DateUtil.offsetDay(DateUtil.date(), -6),DateUtil.date(), DateField.DAY_OF_YEAR);
        List<String> dateList = rangeToList.stream().map(date -> DateUtil.format(date, DatePattern.NORM_DATE_PATTERN)).toList();

        List<List<Object>> brokenLineList = new ArrayList<>();
        if (CollUtil.isNotEmpty(locList)){
            //key=统计日期，value=库存数量
            Map<String, List<InvLotLoc>> map = locList.stream().collect(Collectors.groupingBy(item -> DateUtil.format(item.getCreateTime(), DatePattern.NORM_DATE_PATTERN)));
            dateList.forEach(item -> {
                List<InvLotLoc> lotLocs = map.get(item);
                Map<String, Integer> rangeMap = getTimeRangeMap();
                if (CollUtil.isNotEmpty(lotLocs)){
                    lotLocs.forEach(lotLoc -> {
                        int hour = DateUtil.hour(lotLoc.getCreateTime(), Boolean.TRUE);
                        for (String keys : rangeMap.keySet()) {
                            String[] split = keys.split("~");
                            Integer begin = Convert.toInt(split[0]);
                            Integer end = Convert.toInt(split[1]);
                            if (hour >= begin && hour <= end){
                                rangeMap.put(keys,Convert.toInt(lotLoc.getQty().add(Convert.toBigDecimal(rangeMap.get(keys)))));
                            }
                        }
                    });
                }
                initValue(item, rangeMap, brokenLineList);
            });
        }else{
            dateList.forEach(item -> {
                Map<String, Integer> rangeMap = getTimeRangeMap();
                initValue(item, rangeMap, brokenLineList);
            });
        }

        List<String> nodeDates = new ArrayList<>();
        dateList.forEach(item -> {
            DateTime dateTime = DateUtil.parseDate(item);
            //如果dateTime日期和今天的日期一致，则取当前时间
            if (DateUtil.isSameDay(dateTime, DateUtil.date())){
                //取0点到当前小时的中间时间
                DateTime date = DateUtil.date();
                //当前小时
                int hour = DateUtil.hour(date, Boolean.TRUE);
                //计算数字hour的中间值
                int middleHour = (hour + 1) / 2;
                if (middleHour >= 10){
                    nodeDates.add(DateUtil.formatDate(date) + " " +middleHour+":00:00");
                }else{
                    nodeDates.add(DateUtil.formatDateTime(DateUtil.offsetHour(date, -middleHour)));
                }
            }else{
                nodeDates.add(item + " 12:00:00");
            }
        });

        StorageLocationStatistics rest = new StorageLocationStatistics();
        rest.setBrokenLineList(brokenLineList);
        rest.setNodeDates(nodeDates);
        List<StorageLocationStatistics.Ranking> rankingList = invLotLocService.storageLocationRanking(statisticsCondition);
        if (CollUtil.isNotEmpty(rankingList)){
            BigDecimal sum = rankingList.stream().map(StorageLocationStatistics.Ranking::getNumber)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            for (int i = 0; i < rankingList.size(); i++) {
                StorageLocationStatistics.Ranking ranking = rankingList.get(i);
                BigDecimal div = NumberUtil.div(ranking.getNumber(), sum);
                ranking.setSerialNumber(i+1);
                ranking.setProportion(NumberUtil.decimalFormat(ControlsConstant.PERCENTAGE_CONVERSION,div));
            }
        }
        rest.setRankingList(rankingList);
        return rest;
    }

    private static void initValue(String item, Map<String, Integer> rangeMap, List<List<Object>> brokenLineList) {
        for (String keys : rangeMap.keySet()) {
            List<Object> list = new ArrayList<>();
            String[] split = keys.split("~");
            list.add(item +" "+split[0] +":00:00");
            list.add((rangeMap.get(keys)));
            brokenLineList.add(list);
        }
        // 每次进行排序
        if (CollectionUtil.isNotEmpty(brokenLineList)){
            brokenLineList.sort((o1, o2) -> {
                Date date1 = DateUtil.parse(String.valueOf(o1.getFirst()), DatePattern.NORM_DATETIME_PATTERN);
                Date date2 = DateUtil.parse(String.valueOf(o2.getFirst()), DatePattern.NORM_DATETIME_PATTERN);
                return date1.compareTo(date2); // 升序
            });
        }
    }

    private static @NotNull Map<String, Integer> getTimeRangeMap() {
        Map<String,Integer> rangeMap = new HashMap<>();
        rangeMap.put("01~04",0);
        rangeMap.put("05~08",0);
        rangeMap.put("09~12",0);
        rangeMap.put("13~16",0);
        rangeMap.put("17~20",0);
        rangeMap.put("21~00",0);
        return rangeMap;
    }

    /**
     * 当前用户下仓库权限
     * @return List<Warehouse>
     */
    public List<Warehouse> getPermissionWarehouse(){
        List<Warehouse> warehouses;
        WmsBasePageCondition basePageCondition = new WmsBasePageCondition();
        if (ObjectUtil.isEmpty(basePageCondition.getWarehouseIds()) || basePageCondition.getWarehouseIds().getFirst() == -1 ){
            warehouses = warehouseDao.findAll();
        }else {
            warehouses = warehouseDao.findByIds(basePageCondition.getWarehouseIds().toArray(new Long[0]));
        }
        return warehouses;
    };


    /**
     * 获取预警消息
     * @param dashboardCondition 入参
     * @return  PageResult<StockItem> g
     */
    public PageResult<StockItem> getStockItem(@RequestBody DashboardCondition dashboardCondition){
        //预警
        StockWarningPageCondition stockWarningPageCondition = new StockWarningPageCondition();
        stockWarningPageCondition.setWarehouseIds(Collections.singletonList(dashboardCondition.getWarehouseId()));
        stockWarningPageCondition.setPageSize(dashboardCondition.getStockWarningPageSize());
        stockWarningPageCondition.setPageNumber(dashboardCondition.getStockWarningPageNumber());
        return getWaringInfo(stockWarningPageCondition);
    }

    /**
     * 工作台实时数据
     * @param dashboardCondition 入参
     * @return 回参
     */
    public DashboardQuery getRealInfo(DashboardCondition dashboardCondition){
        DashboardQuery dashboardQuery = new DashboardQuery();
        Long warehouseId = dashboardCondition.getWarehouseId();
        // 当日开始日期
        LocalDateTime now = LocalDateTime.now();
        // 当天的0时0分0秒
        LocalDateTime curLocalDataTime = now.withHour(0).withMinute(0).withSecond(0);
        Date curDataTime = Date.from(curLocalDataTime.atZone(ZoneId.systemDefault()).toInstant());
        dashboardQuery.setNowDate(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));

        // 入库订单
        ConditionRule asnHeaderConditionRule = new ConditionRule();
        asnHeaderConditionRule.andEqual("warehouseId",warehouseId);
        List<AsnHeader> asnHeaders = asnHeaderDao.find(AsnHeader.class,asnHeaderConditionRule);

        // 出库订单
        ConditionRule soHeaderConditionRule = new ConditionRule();
        soHeaderConditionRule.andEqual("warehouseId",warehouseId);
        List<SoHeader> soHeaders = soHeaderDao.find(SoHeader.class,soHeaderConditionRule);

        // 订单数
        OrderItem orderItem = getOrderInfo(asnHeaders,soHeaders,now);
        dashboardQuery.setOrderItem(orderItem);

        // 货物数量
        List<String> asnHeaderNos = asnHeaders.stream().filter(item -> item.getCreateTime().compareTo(curDataTime) >= 0).map(AsnHeader::getAsnNo).toList();
        List<String> soHeaderNos = soHeaders.stream().filter(item -> item.getCreateTime().compareTo(curDataTime) >= 0).map(SoHeader::getSoNo).toList();
        CargoItem cargoItem = getCargoInfo(asnHeaderNos,soHeaderNos);
        dashboardQuery.setCargoItem(cargoItem);
        //效益统计
        dashboardQuery.setBenefitStatisticsItem(getBenefitStatistics(soHeaders,now));

        return dashboardQuery;
    }


    /**
     * 订单数量
     * @param asnHeaders 入库单
     * @param soHeaders 出库单
     * @return OrderItem
     */
    public OrderItem getOrderInfo(List<AsnHeader> asnHeaders, List<SoHeader> soHeaders,LocalDateTime now){
        OrderItem orderItem = new OrderItem();
        String risk = "rise";
        String fall = "fall";
        // 转换当前日期
        LocalDateTime curLocalDataTime = now.withHour(0).withMinute(0).withSecond(0);
        Date curDataTime = Date.from(curLocalDataTime.atZone(ZoneId.systemDefault()).toInstant());
        // 获取昨天的开始时间（00:00:00）
        LocalDateTime yesterdayStart = now.minusDays(1).withHour(0).withMinute(0).withSecond(0);
        // 获取昨天的结束时间（23:59:59）
        LocalDateTime yesterdayEnd = now.minusDays(1).withHour(23).withMinute(59).withSecond(59);
        // 转换为 Date 类型
        Date yesterdayStartDate = Date.from(yesterdayStart.atZone(ZoneId.systemDefault()).toInstant());
        Date yesterdayEndDate = Date.from(yesterdayEnd.atZone(ZoneId.systemDefault()).toInstant());

        // 当前时间往前2天的日期
        LocalDateTime localDateTimeAfterTwoDays = now.toLocalDate().plusDays(-2).atTime(now.toLocalTime());
        Date dateTimeAfterTwoDays = Date.from(localDateTimeAfterTwoDays.atZone(ZoneId.systemDefault()).toInstant());

        // 今日入库数量
        List<AsnHeader> curDayAsnHeaders = asnHeaders.stream().filter(item -> item.getCreateTime().compareTo(curDataTime) >= 0).toList();
        // 昨日入库数量
        List<AsnHeader> yesterdayAsnHeaders = asnHeaders.stream()
                .filter(item -> item.getCreateTime().compareTo(yesterdayStartDate) >= 0 && item.getCreateTime().compareTo(yesterdayEndDate) <= 0)
                .toList();
        // 计算公式
        String asnHeaderIncrease =  fall;
        String asnHeaderPercentage = CollectionUtil.isEmpty(curDayAsnHeaders) ? "0%" : "100%";
        if (CollectionUtil.isNotEmpty(yesterdayAsnHeaders)){
            double v = ((double) (curDayAsnHeaders.size() - yesterdayAsnHeaders.size()) / yesterdayAsnHeaders.size()) * 100;
            double result = Math.round(v * 100.0) / 100.0; // 保留两位小数
            asnHeaderPercentage = (result >= 0 ? result : -result) + "%";
            asnHeaderIncrease = result > 0 ? risk : asnHeaderIncrease;
        }

        // 今日出库数量
        List<SoHeader> curDaySoHeaders = soHeaders.stream().filter(item -> item.getCreateTime().compareTo(curDataTime) >= 0).toList();
        // 昨日出库数量
        List<SoHeader> yesterdaysHeaders  = soHeaders.stream()
                .filter(item -> item.getCreateTime().compareTo(yesterdayStartDate) >= 0 && item.getCreateTime().compareTo(yesterdayEndDate) <= 0)
                .toList();

        String soHeaderPercentage = CollectionUtil.isEmpty(curDaySoHeaders) ? "0%" :  "100%";
        String soHeaderIncrease = fall;
        if (CollectionUtil.isNotEmpty(yesterdaysHeaders)){
            double v = ((double) (curDaySoHeaders.size() - yesterdaysHeaders.size()) / yesterdaysHeaders.size()) * 100;
            double result = Math.round(v * 100.0) / 100.0; // 保留两位小数
            soHeaderPercentage = (result >= 0 ? result : -result) + "%";
            soHeaderIncrease = result > 0 ? risk : soHeaderIncrease;
        }

        // 获取两日天的库存量降序排序 ; 2小时为一个维度 获取出日期节点
        List<AsnHeader> afterTwoAsnHeaders = asnHeaders.stream()
                .filter(item -> item.getCreateTime().compareTo(dateTimeAfterTwoDays) >= 0)
                .sorted(Comparator.comparing(AsnHeader::getCreateTime,Comparator.nullsLast(Comparator.naturalOrder())).reversed())
                .toList();

        List<SoHeader> afterTwoSoHeaders = soHeaders.stream()
                .filter(item -> item.getCreateTime().compareTo(dateTimeAfterTwoDays) >= 0)
                .sorted(Comparator.comparing(SoHeader::getCreateTime,Comparator.nullsLast(Comparator.naturalOrder())).reversed())
                .toList();

        List<List<Object>> asnHeaderDateNodes = new ArrayList<>();
        List<List<Object>> soHeaderDateNodes = new ArrayList<>();
        // 将时间按照2小时进行分割
        List<Date> nodeDates = new ArrayList<>();
        LocalDateTime splitLocalDateTime = localDateTimeAfterTwoDays;
        while (splitLocalDateTime.isBefore(now)) {
            // add 2 hours
            LocalDateTime plusHourLocalTimeDateTime = splitLocalDateTime.plusHours(2);
            // localDateTime -> Date
            Date splitDateTime = Date.from(splitLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
            Date plusHourDateTime = Date.from(plusHourLocalTimeDateTime.atZone(ZoneId.systemDefault()).toInstant());

            // 判断入库存在当前区间的数量
            List<Object> asnHeaderDateNode = new ArrayList<>();
            long count = afterTwoAsnHeaders.stream()
                    .filter(item -> item.getCreateTime().compareTo(splitDateTime) >= 0 && item.getCreateTime().compareTo(plusHourDateTime) <= 0)
                    .count();
            asnHeaderDateNode.add(plusHourDateTime);
            asnHeaderDateNode.add((int)count);
            asnHeaderDateNodes.add(asnHeaderDateNode);

            // 判断出库存在当前区间数量
            List<Object> soHeaderDateNode = new ArrayList<>();
            //OrderItem.DateNode soHeaderDateNode = new OrderItem.DateNode();
            long soHeaderCount = afterTwoSoHeaders.stream()
                    .filter(item -> item.getCreateTime().compareTo(splitDateTime) >= 0 && item.getCreateTime().compareTo(plusHourDateTime) <= 0)
                    .count();
            soHeaderDateNode.add(plusHourDateTime);
            soHeaderDateNode.add((int)soHeaderCount);
            soHeaderDateNodes.add(soHeaderDateNode);
//            // 统计图节点时间
//            if ((CollectionUtil.isEmpty(nodeDates) || !nodeDates.getLast().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().equals(plusHourLocalTimeDateTime.toLocalDate()))){
//                nodeDates.add(plusHourDateTime);
//            }
//            // 最后时间点取最大
//            if (plusHourLocalTimeDateTime.toLocalDate().equals(now.toLocalDate()) && plusHourDateTime.compareTo(nodeDates.getLast()) > 0){
//                nodeDates.set((nodeDates.size() - 1),plusHourDateTime);
//            }
            splitLocalDateTime = plusHourLocalTimeDateTime;
        }

        //  纵轴时间节点数
        if (CollectionUtil.isNotEmpty(asnHeaderDateNodes)){
            nodeDates.add((Date) asnHeaderDateNodes.getFirst().getFirst());
            nodeDates.add((Date) asnHeaderDateNodes.get(asnHeaderDateNodes.size() / 2).getFirst());
            nodeDates.add((Date) asnHeaderDateNodes.getLast().getFirst());
        }
        orderItem.setNodeDates(nodeDates);
        orderItem.setAsnHeaderDateNode(asnHeaderDateNodes);
        orderItem.setSoHeaderDateNode(soHeaderDateNodes);
        orderItem.setAsnHeaderIncrease(asnHeaderIncrease);
        orderItem.setSoHeaderIncrease(soHeaderIncrease);
        orderItem.setAsnHeaderCount(curDayAsnHeaders.size());
        orderItem.setSoHeaderCount(curDaySoHeaders.size());
        orderItem.setAsnHeaderPercentage(asnHeaderPercentage);
        orderItem.setSoHeaderPercentage(soHeaderPercentage);
        return orderItem;
    }

    /**
     * 获取上架货物数量
     * @return 回参
     */
    private CargoItem getCargoInfo(List<String> asnHeaderNos,List<String> soHeaderNos){
        CargoItem cargoItem = new CargoItem();
        int qtyRcvCount = 0;
        int treatQtyCount = 0;
        if (CollectionUtil.isNotEmpty(asnHeaderNos)){
            // 查询入库单详情
            AsnDetailCondition asnDetailCondition = new AsnDetailCondition();
            asnDetailCondition.setAsnNos(asnHeaderNos.toArray(String[]::new));
            // 发货数
            List<AsnDetail> asnDetails = asnDetailDao.findByCondition(asnDetailCondition);
            if (CollectionUtil.isNotEmpty(asnDetails)){
                qtyRcvCount = (int)asnDetails.stream()
                        .filter(item -> !ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(item.getStatus()))
                        .map(AsnDetail::getAsnNo)
                        .distinct()
                        .count();
            }

            // 待上架
            PaTaskPageCondition paTaskPageCondition = new PaTaskPageCondition();
            paTaskPageCondition.setOrderNos(asnHeaderNos);
            List<PaTaskInfoQuery> paTaskInfoQueries = csPaTaskDao.findList(paTaskPageCondition);
            if (CollectionUtil.isNotEmpty(paTaskInfoQueries)){
                treatQtyCount = (int)paTaskInfoQueries.stream()
                        .filter(item -> PaStatusEnum.SHELVESING.getCode().equals(item.getStatus()))
                        .map(PaTaskInfoQuery::getAsnNo)
                        .distinct()
                        .count();
            }
        }
        cargoItem.setQtyRcvCount(qtyRcvCount);
        cargoItem.setTreatQtyCount(treatQtyCount);

        int pickingCount = 0;
        int treatPickingCount = 0;
        int qtyPickedCount = 0;
        int shippedCount = 0;
        if (CollectionUtil.isNotEmpty(soHeaderNos)){
            //拣货
            SoPickingCondition soPickingCondition = new SoPickingCondition();
            soPickingCondition.setSoNos(soHeaderNos);
            soPickingCondition.setExcludeStatuses(Arrays.asList(SoPickingStatusEnum.NOT_GENERATE.getCode(),SoPickingStatusEnum.CANCELLED.getCode()));
            List<SoPicking> soPickings = soPickingDao.findByCondition(soPickingCondition);
            if (CollectionUtil.isNotEmpty(soPickings)){
                // 未拣货
                treatPickingCount = (int)soPickings.stream()
                        .filter(item -> SoPickingStatusEnum.UNPICKED.getCode().equals(item.getStatus()))
                        .map(SoPicking::getSoNo)
                        .distinct()
                        .count();

                // 已拣货
                pickingCount = (int)soPickings.stream()
                        .filter(item -> Arrays.asList(SoPickingStatusEnum.PARTIAL_PICKING.getCode(),SoPickingStatusEnum.COMPLETE_PICKING.getCode()).contains(item.getStatus()))
                        .map(SoPicking::getSoNo)
                        .distinct()
                        .count();

            }

            // 待复核
            ConditionRule soRecheckHeaderConditionRule = new ConditionRule();
            soRecheckHeaderConditionRule.andIn("so_no",soHeaderNos);
            qtyPickedCount = (int)Optional.ofNullable(soRecheckHeaderDao.find(SoRecheckHeader.class,soRecheckHeaderConditionRule))
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(item -> SoRecheckStatusEnum.un_recheck.getCode().equals(item.getCheckStatus()))
                    .map(SoRecheckHeader::getSoNo)
                    .distinct()
                    .count();

            // 查询发运
            List<PackBoxDetailInfo> packBoxDetailBySoNo = packBoxDetailDao.getPackBoxDetailBySoNo(soHeaderNos, PackShippingStatusEnum.SHIPMENT_COMPLETE.getCode());
            if (CollectionUtil.isNotEmpty(packBoxDetailBySoNo)){
                shippedCount = (int)packBoxDetailBySoNo
                        .stream()
                        .map(PackBoxDetailInfo::getSoNo)
                        .distinct()
                        .count();

            }
        }
        cargoItem.setTreatPickingCount(treatPickingCount);
        cargoItem.setPickingCount(pickingCount);
        cargoItem.setQtyPickedCount(qtyPickedCount);
        cargoItem.setShippedCount(shippedCount);
        return cargoItem;
    }

    /**
     * 预警消息
     * @return StockItem
     */
    private PageResult<StockItem> getWaringInfo(StockWarningPageCondition condition){
        Integer one = 1;
        Function<StockWarningPageQuery,String> stringFunction = (stockWarning)->{
            String message = "";
            // todo 滞库预警，阿浅
//            if (ObjectUtil.isNotEmpty(stockWarning.getWarningType())){
//                if (one.equals(stockWarning.getWarningType())){
//                    message = String.format("商品%s已经滞库%d天，滞库量%s。",stockWarning.getSkuName(),stockWarning.getStockDays(),stockWarning.getStockNum().toString());
//                }else {
//                    message = String.format("商品%s即将滞库，滞库量%s。",stockWarning.getSkuName(),stockWarning.getStockNum().toString());
//
//                }
//            }
            return message;
        };

        PageResult<StockItem> stockItemPageResult = new PageResult<>();
        PageResult<StockWarningPageQuery> queryPageResult = stockWarningDao.page(condition);
        List<StockItem> stockItems = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(queryPageResult) && CollectionUtil.isNotEmpty(queryPageResult.getRows())){
            for (StockWarningPageQuery row : queryPageResult.getRows()) {
                StockItem stockItem = new StockItem();
                stockItem.setId(row.getId());
                stockItem.setAsnTime(row.getAsnTime());
                stockItem.setStockDays(row.getStockDays());
                stockItem.setStockMessage(stringFunction.apply(row));
                stockItem.setCreateTime(row.getCreateTime());
                // todo 滞库预警 阿浅
//                if (ObjectUtil.isNotEmpty(row.getWarningType())){
//                    String stockName = one.equals(row.getWarningType()) ? "滞库预警" : "即将滞库";
//                    stockItem.setStockName(stockName);
//                }
                stockItems.add(stockItem);
            }
        }
        stockItemPageResult.setRows(stockItems.stream().sorted(Comparator.comparing(StockItem::getCreateTime,Comparator.nullsLast(Comparator.naturalOrder())).reversed()).toList());
        stockItemPageResult.setCount(queryPageResult.getCount());
        stockItemPageResult.setLastPage(queryPageResult.isLastPage());
        initStockDate(stockItemPageResult); // 初始化数据
        return stockItemPageResult;
    }

    public void initStockDate(PageResult<StockItem> stockItemPageResult){
        List<StockItem> stockItems = new ArrayList<>();

        StockItem stockItem = new StockItem();
        stockItem.setStockMessage("商品编号WX-115部分商品已超过【90】天未出库");
        stockItem.setStockName("滞库预警");
        stockItem.setCreateTime(DateUtil.parse("2025-02-15 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItem.setAsnTime(DateUtil.parse("2025-02-15 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItems.add(stockItem);

        StockItem stockItemTwo = new StockItem();
        stockItemTwo.setStockMessage("商品编号XY-098商品库存量远超库存水平");
        stockItemTwo.setStockName("滞库预警");
        stockItemTwo.setCreateTime(DateUtil.parse("2025-02-14 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItemTwo.setAsnTime(DateUtil.parse("2025-02-14 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItems.add(stockItemTwo);

        StockItem stockItemThree = new StockItem();
        stockItemThree.setStockMessage("商品编号SH-006销量低于预期，导致库存积压");
        stockItemThree.setStockName("滞库预警");
        stockItemThree.setCreateTime(DateUtil.parse("2025-02-14 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItemThree.setAsnTime(DateUtil.parse("2025-02-14 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItems.add(stockItemThree);

        StockItem stockItemFour = new StockItem();
        stockItemFour.setStockMessage("商品编号SZ-003商品库存量远超库存水平");
        stockItemFour.setStockName("滞库预警");
        stockItemFour.setCreateTime(DateUtil.parse("2025-02-13 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItemFour.setAsnTime(DateUtil.parse("2025-02-13 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItems.add(stockItemFour);

        StockItem stockItem2 = new StockItem();
        stockItem2.setStockMessage("商品编号OI-115部分商品已超过【20】天未出库");
        stockItem2.setStockName("滞库预警");
        stockItem2.setCreateTime(DateUtil.parse("2025-02-12 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItem2.setAsnTime(DateUtil.parse("2025-02-12 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItems.add(stockItem2);

        StockItem stockItem3 = new StockItem();
        stockItem3.setStockMessage("商品编号PO-13部分商品已超过【12】天未出库");
        stockItem3.setStockName("滞库预警");
        stockItem3.setCreateTime(DateUtil.parse("2025-02-12 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItem3.setAsnTime(DateUtil.parse("2025-02-12 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItems.add(stockItem3);

        StockItem stockItem4 = new StockItem();
        stockItem4.setStockMessage("商品编号UI-XO销量低于预期，导致库存积压");
        stockItem4.setStockName("滞库预警");
        stockItem4.setCreateTime(DateUtil.parse("2025-02-11 13:23:55", DatePattern.NORM_DATETIME_PATTERN));
        stockItem4.setAsnTime(DateUtil.parse("2025-02-11 13:23:55",DatePattern.NORM_DATETIME_PATTERN));
        stockItems.add(stockItem4);

        stockItemPageResult.setRows(stockItems);
        stockItemPageResult.setCount(stockItems.size());
        stockItemPageResult.setLastPage(true);
    }


    /**
     * 效益统计
     * @param soHeaders 入参
     * @return BenefitStatisticsItem
     */
    private BenefitStatisticsItem getBenefitStatistics(List<SoHeader> soHeaders,LocalDateTime now){
        BiFunction<Map<String,List<PackBoxDetailInfo>>,List<SoHeader>,Long> addShippingTime = (Map<String,List<PackBoxDetailInfo>> packHeaderMap,List<SoHeader> headers)-> {
            long resultTime = 0;
            for (SoHeader header : headers) {
                if (packHeaderMap.containsKey(header.getSoNo()) && ObjectUtil.isNotEmpty(header.getCreateTime())){
                    Optional<PackBoxDetailInfo> min = packHeaderMap.get(header.getSoNo()).stream().min(Comparator.comparing(PackBoxDetailInfo::getShippingTime));
                    if (min.isPresent()){
                        // 最早发运单
                        PackBoxDetailInfo packBoxDetailInfo = min.get();
                        // 计算时间差 进行相加
                        long longTime = packBoxDetailInfo.getShippingTime().getTime() - header.getCreateTime().getTime();
                        resultTime += longTime;
                    }
                }
            }
            return resultTime;
        };

        BenefitStatisticsItem benefitStatisticsItem =new BenefitStatisticsItem();
        long hours = 0;
        long minutes = 0;
        String compareMonth = "fall";
        if (CollectionUtil.isNotEmpty(soHeaders)){
            // 查询发运
            Map<String,List<PackBoxDetailInfo>> packBoxDetailInfoMap = Optional.ofNullable(packBoxDetailDao.getPackBoxDetailBySoNo(soHeaders.stream().map(SoHeader::getSoNo).toList(), PackShippingStatusEnum.SHIPMENT_COMPLETE.getCode()))
                   .orElse(new ArrayList<>())
                   .stream()
                   .collect(Collectors.groupingBy(PackBoxDetailInfo::getSoNo));

            // 初始化时间
            Date nowDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
            // 当月开始时间
            LocalDateTime nowMonthStart  = now.withHour(0).withMinute(0).withSecond(0);
            Date nowMonthStartDate = Date.from(nowMonthStart.atZone(ZoneId.systemDefault()).toInstant());
            // 当前时间往后一个月时间
            LocalDateTime oneMonthLater = now.plusMonths(-1);
            Date oneMonthLaterDate = Date.from(nowMonthStart.atZone(ZoneId.systemDefault()).toInstant());
            // 上月开始时间
            LocalDateTime oneMonthLaterStart = oneMonthLater.withHour(0).withMinute(0).withSecond(0);
            Date oneMonthLaterStartDate = Date.from(oneMonthLaterStart.atZone(ZoneId.systemDefault()).toInstant());

            // 当月时间
            List<SoHeader> nowMonthSoHeaders =  soHeaders.stream().filter(item -> item.getCreateTime().compareTo(nowMonthStartDate) >= 0 && item.getCreateTime().compareTo(nowDate) <= 0 ).toList();
            // 取上月
            List<SoHeader> oneMonthLaterSoHeaders =  soHeaders.stream().filter(item -> item.getCreateTime().compareTo(oneMonthLaterStartDate) >= 0 && item.getCreateTime().compareTo(oneMonthLaterDate) <= 0 ).toList();

            // 当月的时长、上月时长 -> 计算环比
            long nowMonthTotalTime = addShippingTime.apply(packBoxDetailInfoMap,nowMonthSoHeaders);
            long oneMonthLaterTotalTime = addShippingTime.apply(packBoxDetailInfoMap,oneMonthLaterSoHeaders);
            compareMonth = nowMonthTotalTime > oneMonthLaterTotalTime ? "rise" : "fall";

            // 该仓库下所有 -> 统计平均值
            long totalTime = addShippingTime.apply(packBoxDetailInfoMap,soHeaders);
            long milliseconds = (totalTime / soHeaders.size());
            hours = milliseconds / (1000 * 60 * 60);
            minutes = (milliseconds % (1000 * 60 * 60)) / (1000 * 60);
        }
        benefitStatisticsItem.setPerformanceDate(String.format("%02d时%02d分钟",hours,minutes));
        benefitStatisticsItem.setCompareMonth(compareMonth);
        return  benefitStatisticsItem;
    }
}
