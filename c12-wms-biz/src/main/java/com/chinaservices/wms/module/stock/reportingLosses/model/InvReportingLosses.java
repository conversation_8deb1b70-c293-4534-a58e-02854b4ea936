package com.chinaservices.wms.module.stock.reportingLosses.model;

import java.util.Date;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 库存报损信息表
 * cs_inv_reporting_losses
 */
@Data
@Entity
@Table(name = "cs_inv_reporting_losses")
public class InvReportingLosses extends ModuleBaseModel {


    /**
     * 报损编号
     */
    private String lossesNo;

    /**
     * 所属仓库ID
     */
    private Long warehouseId;

    /**
     * 所属仓库名称
     */
    private String warehouseName;

    /**
     * 所属货主ID
     */
    private Long ownerId;

    /**
     * 所属货主名称
     */
    private String ownerName;

    /**
     * 报损类型：1-物料损坏；2-物料过期；3-物料丢失
     */
    private String lossesType;

    /**
     * 状态:1-创建;2-已审核;3-已报损
     */
    private String lossesStatus;

    /**
     * 报损日期
     */
    private Date lossesTime;

    /**
     * 报损原因
     */
    private String lossesReason;

    /**
     * 创建人名称
     */
    private String creatorName;

}