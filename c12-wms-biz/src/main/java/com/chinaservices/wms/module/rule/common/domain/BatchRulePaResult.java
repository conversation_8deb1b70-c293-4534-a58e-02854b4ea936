package com.chinaservices.wms.module.rule.common.domain;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 批量上架规则处理结果
 * 
 * <AUTHOR> Assistant
 */
@Data
public class BatchRulePaResult {
    
    /**
     * 成功处理的结果列表
     */
    private List<RulePaProcessResult> successResults = new ArrayList<>();
    
    /**
     * 失败处理的结果列表
     */
    private List<RulePaProcessResult> failureResults = new ArrayList<>();
    
    /**
     * 总处理数量
     */
    private int totalCount;
    
    /**
     * 成功数量
     */
    private int successCount;
    
    /**
     * 失败数量
     */
    private int failureCount;
    
    /**
     * 处理开始时间
     */
    private long startTime;
    
    /**
     * 处理结束时间
     */
    private long endTime;
    
    /**
     * 处理耗时（毫秒）
     */
    private long processingTime;
    
    /**
     * 是否全部成功
     */
    public boolean isAllSuccess() {
        return failureCount == 0;
    }
    
    /**
     * 添加成功结果
     */
    public void addSuccessResult(RulePaProcessResult result) {
        successResults.add(result);
        successCount++;
    }
    
    /**
     * 添加失败结果
     */
    public void addFailureResult(RulePaProcessResult result) {
        failureResults.add(result);
        failureCount++;
    }
    
    /**
     * 计算处理时间
     */
    public void calculateProcessingTime() {
        this.processingTime = endTime - startTime;
    }
    
    /**
     * 获取失败原因摘要
     */
    public String getFailureSummary() {
        if (failureResults.isEmpty()) {
            return "无失败记录";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("失败数量: ").append(failureCount).append("\n");
        
        for (RulePaProcessResult failure : failureResults) {
            summary.append("- 收货明细ID: ").append(failure.getAsnReceiveId())
                   .append(", 商品编码: ").append(failure.getSkuCode())
                   .append(", 错误: ").append(failure.getErrorMessage())
                   .append("\n");
        }
        
        return summary.toString();
    }
}
