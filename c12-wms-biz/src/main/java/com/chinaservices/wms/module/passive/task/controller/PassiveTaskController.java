package com.chinaservices.wms.module.passive.task.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;

import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.passive.task.domain.PassiveTaskItem;
import com.chinaservices.wms.module.passive.take.dto.PassiveTaskMockCondition;
import com.chinaservices.wms.module.passive.task.domain.PassiveTaskPageCondition;
import com.chinaservices.wms.module.passive.task.domain.PassiveTaskQuery;
import com.chinaservices.wms.module.passive.task.service.PassiveTaskService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 无源任务管理控制器
* <AUTHOR>
* @date  2025-07-23
**/
@RestController
@RequestMapping("/api/passive/task")
public class PassiveTaskController {

    @Autowired
    private PassiveTaskService passiveTaskService;

    /**
    * 列表分页
    * <AUTHOR>
    * @date  2025-07-23
    * @param condition 查询参数
    * @return ResponseData
    **/
    @PostMapping("/page")
    @SaCheckPermission("passiveTask:passiveTaskList:headBtn:page")
    public ResponseData<PageResult<PassiveTaskQuery>> page(@RequestBody PassiveTaskPageCondition condition) {
        PageResult<PassiveTaskQuery> pageResult = passiveTaskService.page(condition);
        return ResponseData.success(pageResult);
    }
    /**
     * 自动查询数据
     * <AUTHOR>
     * @date  2025-07-23
     * @return ResponseData
     **/
    @PostMapping("/getTaskInfo")
    @SaCheckPermission("passiveTask:passiveTaskList:headBtn:page")
    public ResponseData<Void> getTaskInfo() {
         passiveTaskService.getTaskInfo(null);
        return ResponseData.success();
    }

    /**
    * 结束
    * <AUTHOR>
    * @date  2025-07-23
    * @param id id
    * @return ResponseData
    **/
    @PostMapping("/end/{id}")
    @SaCheckPermission("passiveTask:passiveTaskList:table:end")
    public ResponseData<Void> getById(@PathVariable(value = "id") Long id) {
         passiveTaskService.end(id,false);
        return ResponseData.success();
    }
    /**
     * 自动查询数据mock接口
     * <AUTHOR>
     * @date  2025-07-23
     * @return ResponseData
     **/
    @PostMapping("/getTaskInfoByMock")
    public ResponseData<Void> getTaskInfoByMock(@RequestBody List<PassiveTaskMockCondition> condition) {
        passiveTaskService.getTaskInfo(condition);
        return ResponseData.success();
    }
    /**
     * 结束mock接口
     * <AUTHOR>
     * @date  2025-07-23
     * @param id id
     * @return ResponseData
     **/
    @PostMapping("/endByMock/{id}")
    public ResponseData<Void> getByIdByMock(@PathVariable(value = "id") Long id) {
        passiveTaskService.end(id,true);
        return ResponseData.success();
    }

    /**
    * 新增/更新PassiveTask信息
    * <AUTHOR>
    * @date  2025-07-23
    * @param item 数据
    * @return ResponseData
    **/

    @PostMapping("/save")
    @SaCheckPermission("passiveTask:passiveTaskList:headBtn:save")
    public ResponseData<Void> save(@RequestBody @Valid PassiveTaskItem item) {
        passiveTaskService.save(item);
        return ResponseData.success();
    }


}
