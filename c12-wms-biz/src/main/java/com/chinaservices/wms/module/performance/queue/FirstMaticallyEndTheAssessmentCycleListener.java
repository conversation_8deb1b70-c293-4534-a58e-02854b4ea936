package com.chinaservices.wms.module.performance.queue;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.redis.delay.RedisDelayQueueProcessor;
import com.chinaservices.core.redis.util.RedisKeyGenerator;
import com.chinaservices.wms.common.constant.RedisKeys;
import com.chinaservices.wms.module.performance.model.KpiCycle;
import com.chinaservices.wms.module.performance.service.KpiAllocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 徐健
 * @Description: 手动设置周期后到达开始时间延迟队列执行器
 * Date: 2025/5/22 15:39
 */
@Component
@Slf4j
public class FirstMaticallyEndTheAssessmentCycleListener implements RedisDelayQueueProcessor {

    @Autowired
    private KpiAllocationService kpiAllocationService;

    @Override
    public void execute(String t) {
        if (StrUtil.isNotEmpty(t)){
            KpiCycle bean = JSONUtil.toBean(t, KpiCycle.class);
            kpiAllocationService.updateHeaderStatusByRedisDelay(bean);
        }
    }

    @Override
    public String delayKey() {
        return RedisKeyGenerator.gen(RedisKeys.KPI_CYCLE_QUEUE_FIRST);
    }
}
