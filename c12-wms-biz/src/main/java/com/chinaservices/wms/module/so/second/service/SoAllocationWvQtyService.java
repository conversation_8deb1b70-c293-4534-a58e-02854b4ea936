package com.chinaservices.wms.module.so.second.service;

import com.alibaba.fastjson.JSONObject;
import com.chinaservices.auth.module.controlparam.domain.ControlParamCondition;
import com.chinaservices.auth.module.controlparam.feign.RemoteControlParamService;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.enums.common.SoEnum;
import com.chinaservices.wms.common.enums.so.SecondarySortingStatusEnum;
import com.chinaservices.wms.common.enums.so.SoPickingStatusEnum;
import com.chinaservices.wms.common.enums.so.TaskStatusEnum;
import com.chinaservices.wms.common.exception.SoExcepitonEnum;
import com.chinaservices.wms.module.so.check.service.SoCheckService;
import com.chinaservices.wms.module.so.picking.model.SoPicking;
import com.chinaservices.wms.module.so.picking.service.SoPickingService;
import com.chinaservices.wms.module.so.so.dao.SoAllocationDao;
import com.chinaservices.wms.module.so.so.dao.SoAllocationWvQtyDao;
import com.chinaservices.wms.module.so.so.domain.SoAllocationSortingItem;
import com.chinaservices.wms.module.so.so.model.SoAllocation;
import com.chinaservices.wms.module.so.so.model.SoAllocationPallet;
import com.chinaservices.wms.module.so.so.model.SoAllocationWvQty;
import com.chinaservices.wms.module.so.so.service.SoAllocationPalletService;
import com.chinaservices.wms.module.so.wv.dao.WvDetailDao;
import com.chinaservices.wms.module.so.wv.domain.*;
import com.chinaservices.wms.module.so.wv.model.WvDetail;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.transaction.model.StockTransaction;
import com.chinaservices.wms.module.stock.transaction.service.StockTransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 波次单分配明细出库单占有数量服务
 *
 * <AUTHOR>
 * @date 2025/01/20
 */
@Service
public class SoAllocationWvQtyService extends ModuleBaseServiceSupport<SoAllocationWvQtyDao, SoAllocationWvQty, Long>
{

    @Autowired
    private SoAllocationDao soAllocationDao;
    @Autowired
    private SoPickingService soPickingService;
    @Autowired
    private SoCheckService soCheckService;
    @Autowired
    private WvDetailDao wvDetailDao;
    @Autowired
    private RemoteControlParamService remoteControlParamService;
    @Autowired
    private StockService stockService;
    @Autowired
    private SoAllocationPalletService soAllocationPalletService;
    @Autowired
    private StockTransactionService stockTransactionService;

    /**
     * 承运商授权控制代码
     */
    public static final String CARRIER_AUTHORITY_CONTROL_CODE = "is_carrier";

    /**
     * 使用范围-公司
     */
    public static final String USE_RANGE_COMPANY = "company";

    /**
     * 分拣客户查询列表
     *
     * @param condition 查询条件
     * @return {@link List }<{@link SortingCustomerQuery }>
     */
    public List<SortingCustomerQuery> sortingCustomerQueryListByWvNo(SortingCustomerCondition condition)
    {
        if (EmptyUtil.isEmpty(condition.getWvNo()) && EmptyUtil.isEmpty(condition.getPickingNo()) ) {
            return new ArrayList<>();
        }
        List<SortingCustomerQuery> customerQueryList = dao.sortingCustomerQueryListByWvNo(condition);
        if (EmptyUtil.isEmpty(customerQueryList)) {
            return new ArrayList<>();
        }
        // 按钮权限
        customerQueryList.forEach(customerQuery ->
        {
            if (customerQuery.getSortingEa() == null || BigDecimal.ZERO.compareTo(customerQuery.getSortingEa()) == 0) {
                customerQuery.setPallet(true);
            }
        });

        return customerQueryList;
    }


    /**
     * 客户商品分拣信息查询
     *
     * @param condition 条件
     * @return {@link List }<{@link SortingCustomerSkuQuery }>
     */
    public List<SortingCustomerSkuQuery> customerSortingSkuList(SortingCustomerSkuCondition condition)
    {
        if (EmptyUtil.isNotEmpty(condition.getSoNo())) {
            // 入库单
            String[] soNos = condition.getSoNo().split(",");
            condition.setSoNos(soNos);
        }
        List<SortingCustomerSkuQuery> customerQueryList = dao.findListBySoNo(condition);
        return customerQueryList;
    }


    /**
     * 捆绑分拣容器号
     *
     * @param condition 条件
     */
    public void bindingSortingPalletNo(SortingPalletNoBindingCondition condition)
    {
        SortingPalletNoBindingItem item = new SortingPalletNoBindingItem();
        item.setSortingPalletNo(condition.getSortingPalletNo());
        // 更新分拣容器号
        updateAllocationSortingPalletNo(item, condition);
    }


    /**
     * 解绑分拣容器号
     *
     * @param condition 条件
     */
    public void unbindSortingPalletNo(SortingCustomerSkuCondition condition)
    {
        SortingPalletNoBindingItem item = new SortingPalletNoBindingItem();
        item.setSortingPalletNo(null);
        // 更新分拣容器号
        updateAllocationSortingPalletNo(item, condition);

    }

    /**
     * 更新二次分拣容器号
     *
     * @param item 项目
     * @param condition 条件
     */
    private void updateAllocationSortingPalletNo(SortingPalletNoBindingItem item, SortingCustomerSkuCondition condition)
    {
        // 入库单
        String[] soNos = condition.getSoNo().split(",");
        // 查询soDetailId
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(WvDetail::getSoNo, (Object[]) soNos);
        conditionRule.andEqual(WvDetail::getWvNo, condition.getWvNo());
        List<WvDetail> wvDetailList = wvDetailDao.find(conditionRule);
        List<Long> list = wvDetailList.stream().map(WvDetail::getSoDetailId).toList();

        ConditionRule rule = new ConditionRule().andIn(SoAllocation::getSoDetailId, list);
        if (EmptyUtil.isNotEmpty(condition.getPickingNo())) {
            rule.andEqual(SoAllocation::getPickingNo, condition.getPickingNo());
        }
        preSave(item);
        soAllocationDao.updateByCondition(item, rule);
    }


    /**
     * 确认分拣
     *
     * @param condition 条件
     */
    @Transactional(rollbackFor = Exception.class)
    public void confirmSorting(SortingCondition condition)
    {
        // 校验分拣信息
        List<SortingCustomerQuery> customerSkuList = condition.getCustomerSkuList();
        if (EmptyUtil.isEmpty(customerSkuList)) {
            throw new ServiceException(SoExcepitonEnum.SECOND_EMPTY);
        }
        // 主键和分拣数量校验
        boolean sortingIdIsNotExist =
                customerSkuList.stream().anyMatch(query -> EmptyUtil.isEmpty(query.getSortingId()));
        boolean sortingEaIsNotExist =
                customerSkuList.stream().anyMatch(query -> EmptyUtil.isEmpty(query.getSortingEa()));
        if (sortingIdIsNotExist || sortingEaIsNotExist) {
            throw new ServiceException(SoExcepitonEnum.SECOND_EMPTY);
        }

        // 查询分拣信息
        Map<Long, SortingCustomerQuery> sortingMap =
                customerSkuList.stream().collect(Collectors.toMap(SortingCustomerQuery::getSortingId, e -> e, (e1, e2) -> e1));

        // 拣货单分拣信息更新
        SortingTaskSkuCondition allocationSkuCondition = new SortingTaskSkuCondition();
        allocationSkuCondition.setPickingTaskNo(condition.getPickingTaskNo());
        SoAllocation first = soAllocationDao.findFirst(allocationSkuCondition);
        if (EmptyUtil.isEmpty(first)) {
            throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, SoEnum.PICKING_TASK.getName(), condition.getPickingTaskNo());
        }
        SortingCustomerQuery query = sortingMap.get(first.getId());

        // 分拣数累加计算和判断
        // 已分拣数量
        BigDecimal sortingEa = first.getSortingEa() == null ? BigDecimal.ZERO : first.getSortingEa();
        // 本次分拣数量
        BigDecimal newSortingRa = query.getSortingEa();
        if (newSortingRa == null || BigDecimal.ZERO.equals(newSortingRa)) {
            throw new ServiceException(SoExcepitonEnum.SECOND_SORTING_NOT_EMPTY_ZERO);
        }
        // 已拣货数量|待分拣数量
        BigDecimal qtyEa = first.getPickingEa() == null ? BigDecimal.ZERO : first.getPickingEa();
        // 累计分拣数量
        BigDecimal totalSortingEa = sortingEa.add(newSortingRa);
        if (totalSortingEa.compareTo(qtyEa) > 0) {
            throw new ServiceException(SoExcepitonEnum.SECOND_SORTING_LT_PICKING, qtyEa.longValue());
        }
        // 拣货任务分拣信息更新数据
        SoAllocationSortingItem item = new SoAllocationSortingItem();
        if (totalSortingEa.compareTo(qtyEa) == 0) {
            item.setSecondarySortingStatus(SecondarySortingStatusEnum.complete_sorting.getCode());
            item.setSortingTaskStatus(TaskStatusEnum.COMPLETED.getCode());
        } else {
            item.setSecondarySortingStatus(SecondarySortingStatusEnum.partial_sorting.getCode());
            item.setSortingTaskStatus(TaskStatusEnum.IN_PROGRESS.getCode());
        }
        item.setSortingEa(totalSortingEa);
        item.setSortingTime(new Date());
        SessionUserInfo userInfo = SessionContext.get();
        item.setSortingOpId(userInfo.getUserId());
        item.setSortingOpName(userInfo.getRealName());
        soAllocationDao.updateByCondition(item, allocationSkuCondition);

        String pickingNo = first.getPickingNo();
        SoPicking soPicking = soPickingService.findFirst(new ConditionRule().andEqual("pickingNo", pickingNo));
        // 更新二次分拣库存
        String wvNo = soPicking.getWvNo();
        this.updateInvLotLoc(first, newSortingRa, wvNo);

        // 二次分拣完成，生成出库复核
        List<SoAllocation> list = soAllocationDao.find(new ConditionRule().andEqual("pickingNo", pickingNo)
                .andNotEqual(SoAllocation::getPickingStatus, SoPickingStatusEnum.CANCELLED.getCode()));
        boolean allMatch = list.stream().allMatch(soAllocation -> SecondarySortingStatusEnum.complete_sorting.getCode().equals(soAllocation.getSecondarySortingStatus()));
        if (allMatch) {
            soCheckService.saveRecheckByWvNo(soPicking, list);
        }
    }

    /**
     * 更新库存
     *
     * @param first        第一个
     * @param newSortingRa 新排序规则
     */
    private void updateInvLotLoc(SoAllocation first, BigDecimal newSortingRa, String wvNo) {
        // 查询二次分拣库存交易记录
        StockTransaction transaction = new StockTransaction();
        transaction.setOrderNo(wvNo);
        transaction.setFmSkuId(first.getSkuId());
        List<StockTransaction> transactionList = this.findStockTransaction(transaction);
        // 库存交易容器号为空数据处理
        transactionList.forEach(stockTransaction -> {
            if (EmptyUtil.isEmpty(stockTransaction.getFmPalletNum())) {
                stockTransaction.setFmPalletNum("null_pallet");
            }
        });
        Map<String, List<StockTransaction>> map = transactionList.stream().collect(Collectors.groupingBy(StockTransaction::getFmPalletNum));

        // 拣货操作信息
        List<SoAllocationPallet> pallets = soAllocationPalletService.find(new ConditionRule().andEqual(SoAllocationPallet::getPickingTaskNo, first.getPickingTaskNo()));
        // 拣货容器号为空数据处理
        pallets.forEach(pallet -> {
            if (EmptyUtil.isEmpty(pallet.getToPalletNum())) {
                pallet.setToPalletNum("null_pallet");
            }
        });
        Map<String, List<SoAllocationPallet>> listMap = pallets.stream().collect(Collectors.groupingBy(SoAllocationPallet::getToPalletNum));
        // 计算已分拣数量
        BigDecimal subtracted = newSortingRa;
        for (Map.Entry<String, List<SoAllocationPallet>> entry : listMap.entrySet()) {
            String palletNum = entry.getKey();
            // 同一个容器号的拣货数量
            List<SoAllocationPallet> palletList = entry.getValue();
            BigDecimal ored = palletList.stream().map(SoAllocationPallet::getPickingEa).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            BigDecimal decimal;
            // 同一个拣货容器的已分拣数量
            if (EmptyUtil.isNotEmpty(map)) {
                List<StockTransaction> stockTransactions = map.get(palletNum);
                if (EmptyUtil.isNotEmpty(stockTransactions)) {
                    decimal = stockTransactions.stream().map(StockTransaction::getFmQtyEaOp).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    if (ored.compareTo(decimal) <= 0) {
                        continue;
                    }
                } else {
                    decimal = BigDecimal.ZERO;
                }
            }else {
                decimal = BigDecimal.ZERO;
            }

            // 拣货数量减去已分拣数量=未分拣数量
            BigDecimal subSecondary = ored.subtract(decimal);
            // 本次分拣数量-未分拣数量=剩余分拣数量
            if (subtracted.compareTo(subSecondary) > 0) {
                this.updateInvLotLocQty(first, subSecondary, wvNo, "null_pallet".equals(palletNum) ? null : palletNum);
                subtracted = subtracted.subtract(subSecondary);
            } else {
                this.updateInvLotLocQty(first, subtracted, wvNo, "null_pallet".equals(palletNum) ? null : palletNum);
                break;
            }
        }
    }

    /**
     * 更新库存数量
     *
     * @param allocation 分配
     * @param pickingEa  拣货EA
     */
    private void updateInvLotLocQty(SoAllocation allocation, BigDecimal pickingEa, String wvNo, String toPalletNum) {
        // 库存更新
        InvLotLocQtyBO invLotLocQtyBO = new InvLotLocQtyBO()
                .setLotNum(allocation.getLotNum())
                .setOrderNo(wvNo)
                .setUpdateNum(pickingEa)
                .setWarehouseId(allocation.getWarehouseId())
                .setSkuId(allocation.getSkuId())
                .setOwnerId(allocation.getOwnerId())
                .setTransactionType(TransactionType.TRAN_S_PK)
                .setLocId(allocation.getToLocId())
                .setToLocId(allocation.getToLocId())
                .setPalletNum(toPalletNum)
                .setToPallet(allocation.getSortingPalletNo());
        logger.info("二次分拣库存更新：{}", JSONObject.toJSONString(invLotLocQtyBO));
        stockService.exec(invLotLocQtyBO);
    }

    /**
     * 查找库存交易
     *
     * @param condition 条件
     * @return {@link List }<{@link StockTransaction }>
     */
    public List<StockTransaction> findStockTransaction(StockTransaction condition) {
        List<StockTransaction> stockTransactions = stockTransactionService.find(new ConditionRule()
                .andEqual(StockTransaction::getOrderNo, condition.getOrderNo())
                .andEqual(StockTransaction::getTransactionType, TransactionType.TRAN_S_PK)
                .andEqual(StockTransaction::getFmSkuId, condition.getFmSkuId())
        );
        return stockTransactions;
    }

    /**
     * 订单商品列表
     *
     * @param condition 条件
     * @return {@link List }<{@link SortingCustomerSkuQuery }>
     */
    public List<SortingOrderSkuQuery> orderSkuList(SortingCustomerCondition condition)
    {
        if (EmptyUtil.isEmpty(condition.getWvNo()) && EmptyUtil.isEmpty(condition.getPickingNo()) ) {
            return new ArrayList<>();
        }
        List<SortingOrderSkuQuery> orderSkuQueryList = new ArrayList<>();
        List<SortingCustomerSkuQuery> skuQueryList = new ArrayList<>();

        String wvNo = condition.getWvNo();
        String pickingNo = condition.getPickingNo();

        // 根据波次单查询
        if (EmptyUtil.isNotEmpty(wvNo)) {
            // 查询出库单拣货商品
            SortingCustomerSkuCondition sortingCondition = new SortingCustomerSkuCondition();
            sortingCondition.setWvNo(wvNo);
            skuQueryList = this.customerSortingSkuList(sortingCondition);
            if (EmptyUtil.isEmpty(skuQueryList)) {
                throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, SoEnum.WV_HEADER.getName(), wvNo);
            }
        }

        // 根据拣货单查询
        if (EmptyUtil.isNotEmpty(pickingNo)) {
            SoPicking soPicking = soPickingService.findFirst(new ConditionRule().andEqual(SoPicking::getPickingNo, pickingNo));
            if (EmptyUtil.isEmpty(soPicking)) {
                throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, SoEnum.PICKING.getName(), pickingNo);
            }
            // 查询拣货任务
            List<SoAllocation> queryList = soAllocationDao
                    .find(new ConditionRule().andEqual(SoAllocation::getPickingNo, pickingNo));
            if (EmptyUtil.isEmpty(queryList)) {
                throw new ServiceException(SoExcepitonEnum.PICKING_NOT_ALLOCATION, pickingNo);
            }
            List<String> pickingTaskNoList = queryList.stream().map(SoAllocation::getPickingTaskNo).toList();

            // 查询出库单拣货商品
            SortingCustomerSkuCondition sortingCondition = new SortingCustomerSkuCondition();
            sortingCondition.setPickingTaskNoList(pickingTaskNoList);
            sortingCondition.setWvNo(soPicking.getWvNo());
            skuQueryList = this.customerSortingSkuList(sortingCondition);
        }

        if (EmptyUtil.isEmpty(skuQueryList)) {
            return new ArrayList<>();
        }
        // 组装返回数据
        Map<String, List<SortingCustomerSkuQuery>> listMap = skuQueryList.stream().collect(Collectors.groupingBy(SortingCustomerSkuQuery::getSoNo));
        listMap.forEach((key, value) ->
        {
            SortingOrderSkuQuery skuQuery = new SortingOrderSkuQuery();
            skuQuery.setSoNo(key);
            skuQuery.setSkuList(value);
            orderSkuQueryList.add(skuQuery);
        });
        return orderSkuQueryList;
    }

    /**
     * 承运商权限控制
     *
     * @return {@link String }
     */
    public String carrierAuthorityControl() {
        // 查询承运商控制器
        ControlParamCondition condition = new ControlParamCondition();
        condition.setCpCode(CARRIER_AUTHORITY_CONTROL_CODE);
        condition.setUseRange(USE_RANGE_COMPANY);
        ResponseData<String> controlParam = remoteControlParamService.getControlParam(condition);
        return controlParam.getData();
    }


    /**
     * 承运商列表
     *
     * @param condition 条件
     * @return {@link List }<{@link SortingCarrierQuery }>
     */
    public List<SortingCarrierQuery> carrierList(SortingCustomerCondition condition)
    {
        if (EmptyUtil.isEmpty(condition.getWvNo()) && EmptyUtil.isEmpty(condition.getPickingNo()) ) {
            return new ArrayList<>();
        }
        List<SortingCarrierQuery> carrierQueryList = dao.sortingCarrierQueryListByWvNo(condition);
        if (EmptyUtil.isEmpty(carrierQueryList)) {
            return new ArrayList<>();
        }
        // 按钮权限
        carrierQueryList.forEach(customerQuery ->
        {
            if (customerQuery.getSortingEa() == null || BigDecimal.ZERO.compareTo(customerQuery.getSortingEa()) == 0) {
                customerQuery.setPallet(true);
            }
        });

        return carrierQueryList;
    }


}
