package com.chinaservices.wms.module.rule.wv.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.core.enums.StatusEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.util.StringUtil;
import com.chinaservices.wms.common.base.BaseStatusItem;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constant.StatusConstant;
import com.chinaservices.wms.common.enums.common.DataOperationValidatorEnum;
import com.chinaservices.wms.common.enums.rule.RuleWvStatusEnum;
import com.chinaservices.wms.common.enums.so.SoTypeEnum;
import com.chinaservices.wms.common.exception.RuleExceptionEnum;
import com.chinaservices.wms.common.service.CommonService;
import com.chinaservices.wms.module.rule.wv.dao.CsRuleWvDao;
import com.chinaservices.wms.module.rule.wv.domain.*;
import com.chinaservices.wms.module.rule.wv.model.CsRuleWv;
import com.chinaservices.wms.module.rule.wv.model.CsRuleWvGroup;
import com.chinaservices.wms.module.validator.DataOperationValidationEvent;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description RuleWv
* @date  2024-12-26
**/
@Service
public class CsRuleWvService extends ModuleBaseServiceSupport<CsRuleWvDao, CsRuleWv,Long> {

    @Value("${rulewv.detail.setting:}")
    private Integer setting;

    @Autowired
    private CsRuleWvEffectService csRuleWvEffectService;
    @Autowired
    private CsRuleWvGroupService csRuleWvGroupService;
    @Autowired
    private CsRuleWvCondService csRuleWvCondService;


    @Autowired
    private NumberGenerator numberGenerator;
    @Autowired
    private CommonService commonService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 分页查询
     *
     * @param condition
     * @return PageResult<CsRuleWvQuery>
     */
    public PageResult<CsRuleWvQuery> page(CsRuleWvPageCondition condition) {
        PageResult<CsRuleWvQuery> page = dao.page(condition);
        if (CollectionUtil.isNotEmpty(page.getRows())){
            List<CsRuleWvQuery> rows = page.getRows();
            List<Long> ids = rows.stream().map(CsRuleWvQuery::getId).collect(Collectors.toList());
            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andIn(CsRuleWvGroup::getRuleWvId,ids);
            List<CsRuleWvGroupQuery> csRuleWvGroups = csRuleWvGroupService.find(CsRuleWvGroupQuery.class,conditionRule);
            Long[] longs = rows.stream().map(CsRuleWvQuery::getCreator).collect(Collectors.toList()).toArray(Long[]::new);
            Map<Long, UserForOuterQuery> userNameByIds = commonService.getUserNameByIds(longs);
            page.getRows().forEach(i -> {
                if (Objects.equals(i.getStatus(), StatusEnum.NO.getCode())){
                    i.setIsDelete(true);
                }
                if (StrUtil.isNotEmpty(i.getOrderType())){
                    List<String> stringList = Arrays.asList(i.getOrderType().split(","));
                    i.setOrderType(stringList.stream().map(SoTypeEnum::getNameByCode).collect(Collectors.joining(",")));
                }
                if (CollectionUtil.isNotEmpty(csRuleWvGroups)){
                    List<CsRuleWvGroupQuery> csRuleWvGroupItems = csRuleWvGroups.stream().filter(j -> Objects.equals(i.getId(),j.getRuleWvId())).collect(Collectors.toList());
                    i.setGroupQueries(csRuleWvGroupItems);
                }
                if (!userNameByIds.isEmpty() && userNameByIds.containsKey(i.getCreator())){
                    i.setCreatorName(userNameByIds.get(i.getCreator()).getRealName());
                }
            });
        }
        return page;
    }

    /**
     * 根据id查询
     *
     * @param id id
     * @return CsRuleWvQuery
     */
    public CsRuleWvQuery getById(Long id) {
        CsRuleWv csRuleWv = dao.findById(id);
        if (Objects.isNull(csRuleWv)){
            return null;
        }
        CsRuleWvQuery csRuleWvQuery = new CsRuleWvQuery();
        BeanUtil.copyProperties(csRuleWv, csRuleWvQuery);
        if (StrUtil.isNotEmpty(csRuleWv.getOrderType())){
            csRuleWvQuery.setOrderTypes(Arrays.asList(csRuleWv.getOrderType().split(",")));
        }
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(CsRuleWvGroup::getRuleWvId,id);
        List<CsRuleWvGroupQuery> csRuleWvGroups = csRuleWvGroupService.find(CsRuleWvGroupQuery.class,conditionRule);
        //行号排序
        csRuleWvGroups = csRuleWvGroups.stream().sorted(Comparator.comparingInt(CsRuleWvGroupQuery::getLineNo)).collect(Collectors.toList());
        csRuleWvQuery.setGroupQueries(csRuleWvGroups);
        return csRuleWvQuery;
    }


    /**
     * 校验是否重复
     *
     * @return
     */
    public ResponseData isDuplicate(CsRuleWvItem ruleWvItem) {
        CsRuleWv csRuleWv = new CsRuleWv();
        BeanUtil.copyProperties(ruleWvItem, csRuleWv);
        if (dao.isDuplicate(csRuleWv, "id", "ruleCode")) {
            return ResponseData.error("[规则代码]不能重复");
        }
        if (dao.isDuplicate(csRuleWv, "id", "warehouseId", "priority")) {
            return ResponseData.error("同一仓库[优先级]不能重复");
        }
        return ResponseData.success();
    }


    /**
     * 根据Ids批量启用
     *
     * @param ids
     * @return
     */
    public boolean enableByIds(Long[] ids) {
        List<CsRuleWv> byIds = findByIds(ids);
        BaseStatusItem statusItem = new BaseStatusItem(StatusConstant.ENABLE);
        preSave(statusItem);
        return dao.updateByIds(statusItem, ids) > 0;
    }

    /**
     * 根据Ids批量停用
     *
     * @param ids
     * @return
     */
    public boolean disableByIds(Long[] ids) {
        BaseStatusItem statusItem = new BaseStatusItem(StatusConstant.DISABLE);
        preSave(statusItem);
        return dao.updateByIds(statusItem, ids) > 0;
    }

    /**
     * 根据Ids批量删除
     *
     * @param ids
     * @return
     */
    public boolean deleteAndSubByIds(Long[] ids) {
        DataOperationValidationEvent dataOperationValidationEvent = new DataOperationValidationEvent(this, DataOperationValidatorEnum.WV_RULE.getDeleteObj(), CsRuleWv.class, Arrays.asList(ids), DataOperationValidationEvent.OperationType.DELETE);
        eventPublisher.publishEvent(dataOperationValidationEvent);
        List<CsRuleWv> byIds = findByIds(ids);
        List<CsRuleWv> check = byIds.stream().filter(item -> item.getStatus().equals(StatusConstant.ENABLE)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(check)) {
            throw new ServiceException(RuleExceptionEnum.CAN_NOT_DELETE);
        }
        // 删除子表
//        csRuleWvEffectService.deleteByRuleWvIds(ids);
        csRuleWvGroupService.deleteByRuleWvIds(ids);
//        csRuleWvCondService.deleteByRuleWvIds(ids);
        // 删除主表
        return dao.delete(ids) > 0;
    }

    /**
     * 保存
     * @param item
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(CsRuleWvItem item){
        CsRuleWv csRuleWv = new CsRuleWv();
        BeanUtils.copyProperties(item, csRuleWv);
        if (CollectionUtil.isNotEmpty(item.getOrderTypes())){
            csRuleWv.setOrderType(String.join(",",item.getOrderTypes()));
        }
        if (Objects.isNull(csRuleWv.getId())){
            csRuleWv.setStatus(RuleWvStatusEnum.ENABLE.getCode());
            csRuleWv.setRuleCode(numberGenerator.nextValue(IdRuleConstant.RULE_NO));
            preSave(csRuleWv);
            dao.saveOrUpdate(csRuleWv);
        }else {
            //创建时间丢失问题
            CsRuleWv ruleWv = dao.findById(csRuleWv.getId());
            csRuleWv.setCreateTime(ruleWv.getCreateTime());
            dao.update(csRuleWv);
        }
        if (CollectionUtil.isNotEmpty(item.getGroupItems())) {
            if (item.getGroupItems().size() > setting){
                throw new ServiceException(RuleExceptionEnum.CAN_ONLY_ONE,setting);
            }
            List<CsRuleWvGroup> saveGroups = new ArrayList<>();
            Integer lineNo = 1;
            for (CsRuleWvGroupItem groupItem : item.getGroupItems()) {
                //校验必填
                if (StringUtil.isEmpty(groupItem.getConditionName())) {
                    throw new ServiceException(RuleExceptionEnum.NAME_IS_NOT_NULL);
                }
                if (Objects.isNull(groupItem.getGroupOrderQty())) {
                    throw new ServiceException(RuleExceptionEnum.NUM_IS_NOT_NULL);
                }
                CsRuleWvGroup csRuleWvGroup = new CsRuleWvGroup();
                BeanUtils.copyProperties(groupItem, csRuleWvGroup);
                csRuleWvGroup.setLineNo(lineNo);
                csRuleWvGroup.setId(null);
                csRuleWvGroup.setRuleWvId(csRuleWv.getId());
                csRuleWvGroupService.preSave(csRuleWvGroup);
                saveGroups.add(csRuleWvGroup);
                lineNo++;
            }
            //名称是否重复
            item.getGroupItems().stream().collect(Collectors.groupingBy(CsRuleWvGroupItem::getConditionName)).forEach((k, v) -> {
                if (v.size() > 1) {
                    throw new ServiceException(RuleExceptionEnum.NAME_IS_REPEAT);
                }
            });
            if (Objects.nonNull(item.getId())){
                ConditionRule conditionRule = new ConditionRule();
                conditionRule.andEqual(CsRuleWvGroup::getRuleWvId, item.getId());
                csRuleWvGroupService.delete(conditionRule);
            }
            csRuleWvGroupService.batchInsert(saveGroups);
        }
        return true;
    }
}
