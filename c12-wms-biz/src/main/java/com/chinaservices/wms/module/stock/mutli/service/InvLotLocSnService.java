package com.chinaservices.wms.module.stock.mutli.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.useragent.Platform;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.jpa.support.rule.OrderRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.domain.LotAttribute;
import com.chinaservices.wms.common.domain.LotDetailItem;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.common.util.EasyExcelExportUtil;
import com.chinaservices.wms.common.util.LotUtil;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailDao;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailSnDao;
import com.chinaservices.wms.module.asn.asn.model.AsnDetail;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.basic.sku.dao.SkuDao;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.excel.item.stock.InvLotLocSnExcelItem;
import com.chinaservices.wms.module.stock.adjust.domain.AdjustLocSnDetailCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocSnDetailCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocSnItem;
import com.chinaservices.wms.module.stock.multi.domain.*;
import com.chinaservices.wms.module.stock.mutli.dao.InvLotLocSnDao;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLocSn;
import com.chinaservices.wms.module.stock.transaction.dao.StockTransactionDao;
import com.google.common.collect.Lists;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class InvLotLocSnService extends ModuleBaseServiceSupport<InvLotLocSnDao, InvLotLocSn, Long> {


    @Autowired
    private EasyExcelExportUtil easyExcelExportUtil;

    @Autowired
    private AsnDetailSnDao asnDetailSnDao;

    @Autowired
    private AsnDetailDao asnDetailDao;

    @Autowired
    private LotUtil lotUtil;

    @Autowired
    private SkuDao skuDao;

    @Autowired
    private StockTransactionDao stockTransactionDao;

    private static final String SORTATION = "SORTATION";

    public PageResult<InvLotLocSnQuery> snStockPage(InvLotLocSnPageCondition condition) {
        PageResult<InvLotLocSnQuery> invLotLocSnQueryPageResult = dao.snStockPage(condition);
        List<String> snNos = invLotLocSnQueryPageResult.getRows().stream().map(InvLotLocSnQuery::getSnNo).collect(Collectors.toList());
        List<String> skuCodes = invLotLocSnQueryPageResult.getRows().stream().map(InvLotLocSnQuery::getSkuCode).collect(Collectors.toList());
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(AsnDetailSn::getSnNo,snNos);
        OrderRule orderRule = new OrderRule();
        orderRule.addDescOrder("create_time");
        List<AsnDetailSn> asnDetailSns = asnDetailSnDao.find(conditionRule,orderRule);

        conditionRule = new ConditionRule();
        conditionRule.andIn(Sku::getSkuCode,skuCodes);
        List<Sku> skus = skuDao.find(conditionRule);
        Map<String, List<Sku>> skuMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(skus)){
            skuMap = skus.stream().collect(Collectors.groupingBy(Sku::getSkuCode));
        }
        Map<String, List<AsnDetailSn>> snMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(asnDetailSns)){
            snMap = asnDetailSns.stream().collect(Collectors.groupingBy(AsnDetailSn::getSnNo));
        }
        //抽取入库单明细id
        List<Long> detailsIdList = asnDetailSns.stream().map(AsnDetailSn::getAsnDetailId).distinct().collect(Collectors.toList());
        //查询入库单明细
        List<AsnDetail> asnDetailList = asnDetailDao.findByIds(detailsIdList.toArray(new Long[0]));
        Map<Long, AsnDetail> asnDetailMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(asnDetailList)){
            asnDetailMap = asnDetailList.stream().collect(Collectors.toMap(AsnDetail::getId, e -> e));
        }
        Map<String, List<AsnDetailSn>> finalSnMap = snMap;
        Map<String, List<Sku>> finalSkuMap = skuMap;
        Map<Long, AsnDetail> finalAsnDetailMap = asnDetailMap;
        invLotLocSnQueryPageResult.getRows().forEach(e->{
            List<AsnDetailSn> asnDetailSns1 = finalSnMap.get(e.getSnNo());
            if(CollectionUtil.isNotEmpty(asnDetailSns1)){
                e.setBoxNo(asnDetailSns1.get(0).getBoxCode());
                AsnDetail detail = finalAsnDetailMap.get(asnDetailSns1.get(0).getAsnDetailId());
                if (ObjectUtil.isNotNull(detail)){
                    e.setAsnNo(detail.getAsnNo());
                }
            }
            List<Sku> currentSkuList = finalSkuMap.get(e.getSkuCode());
            if(CollectionUtil.isNotEmpty(currentSkuList)){
                Sku firstSku = currentSkuList.get(0);
                if(ObjectUtil.isNotNull(firstSku) && ObjectUtil.isNotNull(firstSku.getLotId())){
                    List<LotDetailItem> lotDetailItemsByLotId = lotUtil.getLotDetailItemsByLotId(firstSku.getLotId());
                    LotAttribute lotAttribute = new LotAttribute();
                    BeanUtil.copyProperties(e,lotAttribute);
                    e.setLot(lotUtil.lotAttributeToStr(lotDetailItemsByLotId,lotAttribute));
                }

            }

        });
        return invLotLocSnQueryPageResult;
    }

    public void exportSnStock(InvLotLocSnPageCondition condition,HttpServletResponse response) {
        List<InvLotLocSnQuery> invLotLocs = dao.exportSnStock(condition);
        List<InvLotLocSnExcelItem> list = new ArrayList<>();
        List<String> snNos = invLotLocs.stream().map(InvLotLocSnQuery::getSnNo).collect(Collectors.toList());
        List<String> skuCodes = invLotLocs.stream().map(InvLotLocSnQuery::getSkuCode).collect(Collectors.toList());
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(AsnDetailSn::getSnNo,snNos);
        OrderRule orderRule = new OrderRule();
        orderRule.addDescOrder("create_time");
        List<AsnDetailSn> asnDetailSns = asnDetailSnDao.find(conditionRule,orderRule);

        conditionRule = new ConditionRule();
        conditionRule.andIn(Sku::getSkuCode,skuCodes);
        List<Sku> skus = skuDao.find(conditionRule);
        Map<String, List<Sku>> skuMap = skus.stream().collect(Collectors.groupingBy(Sku::getSkuCode));
        Map<String, List<AsnDetailSn>> snMap = asnDetailSns.stream().collect(Collectors.groupingBy(AsnDetailSn::getSnNo));

        //抽取入库单明细id
        List<Long> detailsIdList = asnDetailSns.stream().map(AsnDetailSn::getAsnDetailId).distinct().collect(Collectors.toList());
        //查询入库单明细
        List<AsnDetail> asnDetailList = asnDetailDao.findByIds(detailsIdList.toArray(new Long[0]));
        Map<Long, AsnDetail> asnDetailMap = asnDetailList.stream().collect(Collectors.toMap(AsnDetail::getId, e -> e));
        invLotLocs.forEach(e->{
            List<AsnDetailSn> asnDetailSns1 = snMap.get(e.getSnNo());
            AsnDetail detail = asnDetailMap.get(asnDetailSns1.get(0).getAsnDetailId());
            e.setBoxNo(asnDetailSns1.get(0).getBoxCode());
            e.setAsnNo(detail.getAsnNo());
            List<LotDetailItem> lotDetailItemsByLotId = lotUtil.getLotDetailItemsByLotId(skuMap.get(e.getSkuCode()).get(0).getLotId());
            LotAttribute lotAttribute = new LotAttribute();
            BeanUtil.copyProperties(e,lotAttribute);
            e.setLot(lotUtil.lotAttributeToStr(lotDetailItemsByLotId,lotAttribute));
            InvLotLocSnExcelItem invLotLocSnExcelItem = new InvLotLocSnExcelItem();
            BeanUtil.copyProperties(e, invLotLocSnExcelItem);
            list.add(invLotLocSnExcelItem);
        });
        try {
            easyExcelExportUtil.export("序列号库存",list, InvLotLocSnExcelItem.class,response);
        } catch (Exception e) {
            log.error("导出序列号库存失败：{}",e);
            throw new RuntimeException(e);
        }
    }

    /**
     *@Description 根据商品集合和库位集合查询序列号库存
     *@Param skuIdList
     *@param locIdList
     *@param lotNumList
     *@param filterFrozen 是否过滤已解冻数据  1是；0否
     *@Return * {@link List< InvLotLocSn> }
     *@Date 2025/3/20 15:40
     *<AUTHOR>
     **/
    public List<InvLotLocSn> getByLocIdListAndSkuIdListAndLotNumList(List<Long> skuIdList, List<Long> locIdList,List<String> lotNumList,List<String> palletNumList, String filterFrozen) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InvLotLocSn::getLocId,locIdList);
        conditionRule.andIn(InvLotLocSn::getSkuId,skuIdList);
        conditionRule.andIn(InvLotLocSn::getLotNum,lotNumList);
        if(CollectionUtil.isNotEmpty(palletNumList)){
            palletNumList = palletNumList.stream().filter(e -> StringUtils.isNotBlank(e)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(palletNumList)){
                conditionRule.andIn(InvLotLocSn::getPalletNum,palletNumList);
            }
        }
        if (YesNoEnum.YES.getCode().equals(filterFrozen)) {
            conditionRule.andEqual(InvLotLocSn::getIsFrozen, filterFrozen);
        }
        conditionRule.andGreaterThan(InvLotLocSn::getQty, 0);
        return dao.find(conditionRule);
    }

    /**
     * 箱码-序列码
     *
     * @param condition 条件
     * @return {@link List }<{@link InvLotLocSnBoxQuery }>
     */
    public List<InvLotLocSnBoxQuery> snBoxCode(InvLotLocSnPageCondition condition)
    {
        // 查询库存序列号
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(InvLotLocSn::getLotNum, condition.getLotNum());
        conditionRule.andNotEqual(InvLotLocSn::getLocCode, SORTATION);
        conditionRule.andEqual(InvLotLocSn::getLocId, condition.getLocId());
        if (EmptyUtil.isNotEmpty(condition.getPalletNum())) {
            conditionRule.andEqual(InvLotLocSn::getPalletNum, condition.getPalletNum());
        }
        List<InvLotLocSn> locSnList = dao.find(conditionRule);
        if (locSnList.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> list = locSnList.stream().map(InvLotLocSn::getSkuSn).toList();

        // 查询序列号箱码
        ConditionRule rule = new ConditionRule();
        rule.andIn(AsnDetailSn::getSnNo, list);
        List<AsnDetailSn> detailSnList = asnDetailSnDao.find(rule);
        if (detailSnList.isEmpty()) {
            return new ArrayList<>();
        }
        // 无箱码处理
        detailSnList.forEach(e ->
        {
            if (EmptyUtil.isEmpty(e.getBoxCode())) {
                e.setBoxCode("无箱码");
            }
        });

        // 组装数据
        Map<String, List<AsnDetailSn>> listMap = detailSnList.stream().collect(Collectors.groupingBy(AsnDetailSn::getBoxCode));
        List<InvLotLocSnBoxQuery> invLotLocSnBoxQueryList = new ArrayList<>();
        if (!listMap.isEmpty()) {
            listMap.forEach((k, v) ->
            {
                InvLotLocSnBoxQuery query = new InvLotLocSnBoxQuery();
                query.setBoxNo(k);
                List<String> list1 = v.stream().map(AsnDetailSn::getSnNo).toList();
                query.setSoNoList(list1);
                invLotLocSnBoxQueryList.add(query);
            });
        }
        return invLotLocSnBoxQueryList;
    }

    /**
     *@Description 根据snCodeList查询序列号库存
     *@Param snCodeList
     *@Return * {@link List< InvLotLocSn> }
     *@Date 2025/3/24 14:07
     *<AUTHOR>
     **/
    public List<InvLotLocSn> findBySnCodeList(List<String> snCodeList) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(InvLotLocSn::getSkuSn,snCodeList);
        conditionRule.andIn(InvLotLocSn::getIsFrozen,YesNoEnum.NO.getCode());
        return dao.find(conditionRule);
    }

    /**
     *@Description 根据snCodeList查询序列号库存
     *@Param snCodeList
     *@Return * {@link List< InvLotLocSn> }
     *@Date 2025/3/24 14:07
     **/
    public List<InvLotLocSn> findBySnCodeList(List<String> snCodeList, AdjustLocSnDetailCondition condition) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(InvLotLocSn::getSkuSn,snCodeList);
        conditionRule.andEqual(InvLotLocSn::getIsFrozen,condition.getIsFrozen());
        conditionRule.andEqual(InvLotLocSn::getLocId,condition.getLocId());
        return dao.find(conditionRule);
    }

    /**
     *@Description 根据箱码或序列码查询
     *@Param key
     *@Return * {@link List< InvLotLocSn> }
     *@Date 2025/3/24 17:28
     *<AUTHOR>
     **/
    public List<LocSnItem> findListByKey(LocSnDetailCondition condition) {
        return dao.findListByKey(condition);
    }

    /**
     *@Description 库存报损序列号商品分页查询
     *@Param condition
     *@Return * {@link PageResult< SnSkuPageQuery> }
     *@Date 2025/7/15 14:38
     *<AUTHOR>
     **/
    public PageResult<SnSkuPageQuery> getSnSkuPage(SnSkuPageCondition condition) {
        return dao.getSnSkuPage(condition);
    }

    /**
     *@Description 根据仓库ID，货主ID、商品ID查询序列号库存
     *@Param condition
     *@Return * {@link Map< String, List< String>> }
     *@Date 2025/7/15 16:14
     *<AUTHOR>
     **/
    public Map<String, List<String>> getSnMap(SnSkuPageCondition condition) {
        Map<String, List<String>> snMap = new HashMap<>();
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(InvLotLocSn::getWarehouseId, condition.getWarehouseId());
        conditionRule.andEqual(InvLotLocSn::getOwnerId, condition.getOwnerId());
        conditionRule.andEqual(InvLotLocSn::getSkuId, condition.getSkuId());
        conditionRule.andGreaterThan(InvLotLocSn::getQty, BigDecimal.ZERO);
        conditionRule.andEqual(InvLotLocSn::getIsLosses, YesNoEnum.NO.getCode());
        List<InvLotLocSn> snList = dao.find(conditionRule);
        if(CollectionUtil.isNotEmpty(snList)){
            //抽取序列号
            List<String> list = snList.stream().map(InvLotLocSn::getSkuSn).distinct().toList();
            //根据序列号查询入库序列号
            List<AsnDetailSn> asnDetailSnList = asnDetailSnDao.find(ConditionRule.getInstance().andIn(AsnDetailSn::getSnNo, list));
            if(CollectionUtil.isNotEmpty(asnDetailSnList)){
                //无箱码的序列号
                List<AsnDetailSn> notBoxNo = asnDetailSnList.stream().filter(asnDetailSn -> StringUtils.isBlank(asnDetailSn.getBoxCode())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(notBoxNo)){
                    snMap.put("无箱码",  notBoxNo.stream().map(AsnDetailSn::getSnNo).distinct().collect(Collectors.toList()));
                }
                //有箱码序列号
                List<AsnDetailSn> hasBoxNo = asnDetailSnList.stream().filter(asnDetailSn -> StringUtils.isNotBlank(asnDetailSn.getBoxCode())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(hasBoxNo)){
                    //根据箱码分组
                    Map<String, List<String>> boxMap = hasBoxNo.stream().collect(Collectors.groupingBy(AsnDetailSn::getBoxCode,Collectors.mapping(AsnDetailSn::getSnNo,Collectors.toList())));
                    snMap.putAll(boxMap);
                }
            }
        }
        return snMap;
    }

    /**
     *@Description 通过序列号进行查询
     *@Param snList
     *@Return * {@link List< InvLotLocSn> }
     *@Date 2025/7/17 17:28
     *<AUTHOR>
     **/
    public List<InvLotLocSn> findBySnList(List<String> snList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InvLotLocSn::getSkuSn,snList);
        conditionRule.andNotIn(InvLotLoc::getLocCode, Lists.newArrayList(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), WarehouseLocUseTypeEnum.TALLYING_STATION.getCode()));
        //库存数大于0
        conditionRule.andGreaterThan(InvLotLocSn::getQty, BigDecimal.ZERO);
        //未冻结
        conditionRule.andEqual(InvLotLocSn::getIsFrozen, YesNoEnum.NO.getCode());
        //未报损
        conditionRule.andEqual(InvLotLocSn::getIsLosses, YesNoEnum.NO.getCode());
        return dao.find(conditionRule);
    }
}
