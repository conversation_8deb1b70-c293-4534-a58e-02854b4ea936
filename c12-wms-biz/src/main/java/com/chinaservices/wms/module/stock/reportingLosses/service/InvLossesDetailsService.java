package com.chinaservices.wms.module.stock.reportingLosses.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.wms.module.stock.reportingLosses.dao.InvLossesDetailsDao;
import com.chinaservices.wms.module.stock.reportingLosses.domain.InvLossesDetailsQuery;
import com.chinaservices.wms.module.stock.reportingLosses.domain.InvLossesDetailsSaveItem;
import com.chinaservices.wms.module.stock.reportingLosses.model.InvLossesDetails;
import com.chinaservices.wms.module.stock.reportingLosses.model.InvLossesDetailsSn;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName InvLossesDetailsService
 * <AUTHOR>
 * @Date 2025/7/11 16:33
 * @Description
 * @Version 1.0
 */
@Service
public class InvLossesDetailsService extends ModuleBaseServiceSupport<InvLossesDetailsDao, InvLossesDetails, Long> {

    @Autowired
    private InvLossesDetailsSnService invLossesDetailsSnService;

    /***
     *@Description 报损单号查询报损单详情
     *@Param list
     *@Return * {@link List< InvLossesDetailsQuery> }
     *@Date 2025/7/11 17:15
     *<AUTHOR>
     **/
    public List<InvLossesDetailsQuery> findByLossesNoList(List<String> list) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InvLossesDetails::getLossesNo, list);
        List<InvLossesDetailsQuery> queryList = dao.find(InvLossesDetailsQuery.class, conditionRule);
        if(CollectionUtil.isNotEmpty(queryList)){
            //根据编号查询序列号信息
            List<InvLossesDetailsSn> snList = invLossesDetailsSnService.findByLossesNoList(list);
            if(CollectionUtil.isNotEmpty(snList)){
                //根据详情id分组
                Map<Long, List<InvLossesDetailsSn>> snMap = snList.stream().collect(Collectors.groupingBy(InvLossesDetailsSn::getDetailsId));
                queryList.forEach(query->{
                    List<InvLossesDetailsSn> snListByDetailsId = snMap.get(query.getId());
                    if(CollectionUtil.isNotEmpty(snListByDetailsId)){
                        query.setSnNoList(getSnMap(snListByDetailsId));
                    }
                });
            }
        }
        return queryList;
    }

    /**
     *@Description 将序列号信息转换成map
     *@Param snListByDetailsId
     *@Return * {@link Map< String, List< String>> }
     *@Date 2025/7/11 17:21
     *<AUTHOR>
     **/
    private Map<String, List<String>> getSnMap(List<InvLossesDetailsSn> snListByDetailsId) {
        Map<String, List<String>> resultMap = new HashMap<>();
        //无箱码的
        List<InvLossesDetailsSn> notBoxCodeList = snListByDetailsId.stream().filter(sn -> StringUtils.isBlank(sn.getBoxCode())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(notBoxCodeList)){
            resultMap.put("无箱码", notBoxCodeList.stream().map(InvLossesDetailsSn::getSnNo).collect(Collectors.toList()));
        }
        //有箱码的
        List<InvLossesDetailsSn> hasBoxCodeList = snListByDetailsId.stream().filter(sn -> StringUtils.isNotBlank(sn.getBoxCode())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(hasBoxCodeList)){
            Map<String, List<InvLossesDetailsSn>> boxCodeMap = hasBoxCodeList.stream().collect(Collectors.groupingBy(InvLossesDetailsSn::getBoxCode));
            boxCodeMap.forEach((key,value)->{
                resultMap.put(key, value.stream().map(InvLossesDetailsSn::getSnNo).collect(Collectors.toList()));
            });
        }
        return resultMap;
    }

    /**
     *@Description 根据编号集合删除对应明细和序列号
     *@Param lossesNoList
     *@Return Void
     *@Date 2025/7/11 17:50
     *<AUTHOR>
     **/
    public void batchDel(List<String> lossesNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InvLossesDetails::getLossesNo, lossesNoList);
        dao.delete(conditionRule);
        invLossesDetailsSnService.deleteByLossesNoList(lossesNoList);
    }

    /**
     *@Description 根据编号集合查询序列号
     *@Param collect
     *@Return * {@link List< InvLossesDetailsSn> }
     *@Date 2025/7/11 17:57
     *<AUTHOR>
     **/
    public List<InvLossesDetailsSn> listSnByLossesNoList(List<String> collect) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(InvLossesDetailsSn::getLossesNo, collect);
        return invLossesDetailsSnService.find(conditionRule);
    }

    /**
     *@Description 批量保存
     *@Param detailsSaveItemList
     *@Return Void
     *@Date 2025/7/14 10:09
     *<AUTHOR>
     **/
    public void batchSaveItem(List<InvLossesDetails>detailsList,List<InvLossesDetailsSaveItem> detailsSaveItemList) {
        dao.batchInsert(detailsList);
        //根据商品id、库位id、批次号、转map
        Map<String,InvLossesDetailsSaveItem> detailsSaveItemMap =
                detailsSaveItemList.stream().collect(Collectors.toMap(detailsSaveItem -> detailsSaveItem.getSkuId() +"_"+detailsSaveItem.getLocId() +"_"+detailsSaveItem.getLotNum(), Function.identity(), (old, newItem)->old));
        List<InvLossesDetailsSn> snList = new ArrayList<>();
        detailsList.forEach(details->{
            InvLossesDetailsSaveItem item = detailsSaveItemMap.get(details.getSkuId() +"_"+details.getLocId() +"_"+details.getLotNum());
            if(ObjectUtil.isNotNull(item)){
                Map<String, List<String>> snMap = item.getSnMap();
                if(CollectionUtil.isNotEmpty(snMap)){
                    snMap.forEach((key,value)->{
                        value.forEach(sn->{
                            InvLossesDetailsSn detailsSn = new InvLossesDetailsSn();
                            detailsSn.setDetailsId(details.getId());
                            //无箱码用 -1 表示
                            if(!"-1".equals(key)){
                                detailsSn.setBoxCode(key);
                            }
                            detailsSn.setLossesNo(details.getLossesNo());
                            detailsSn.setSnNo(sn);
                            invLossesDetailsSnService.preSave(detailsSn);
                            snList.add(detailsSn);
                        });
                    });
                }
            }
        });
        if(CollectionUtil.isNotEmpty(snList)){
            invLossesDetailsSnService.batchInsert(snList);
        }
    }
}
