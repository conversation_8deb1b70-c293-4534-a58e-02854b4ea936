package com.chinaservices.wms.module.so.pack.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.chinaservices.auth.module.dict.domain.DictDataCondition;
import com.chinaservices.auth.module.dict.domain.DictDataQuery;
import com.chinaservices.auth.module.dict.feign.RemoteDictDataService;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.core.enums.StatusEnum;
import com.chinaservices.core.enums.WmsPushStatusEnum;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.core.file.util.CustomFile;
import com.chinaservices.edi.module.asn.domain.enums.ActionCode;
import com.chinaservices.edi.module.tms.domain.ThirdCancelDispatchRequest;
import com.chinaservices.edi.module.tms.domain.ThirdPushDispatchRequest;
import com.chinaservices.edi.module.tms.feign.RemoteDispatchService;
import com.chinaservices.file.FileObject;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.constants.DictDataTypeConstants;
import com.chinaservices.wms.common.enums.DocumentTypeEnum;
import com.chinaservices.wms.common.enums.PackStatusEnum;
import com.chinaservices.wms.common.enums.common.FileTypeEnum;
import com.chinaservices.wms.common.enums.order.OrderStatusEnum;
import com.chinaservices.wms.common.enums.so.*;
import com.chinaservices.wms.common.enums.stock.ExpiryWarningSaveStageEnum;
import com.chinaservices.wms.common.enums.stock.TraceBackBusinessTypeEnum;
import com.chinaservices.wms.common.exception.SoExcepitonEnum;
import com.chinaservices.wms.common.service.CommonService;
import com.chinaservices.wms.module.archive.archives.service.LogisticsFileAutoGenerateService;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailSnDao;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.pa.dao.CsPaTaskAssociationDao;
import com.chinaservices.wms.module.asn.pa.dao.CsPaTaskDao;
import com.chinaservices.wms.module.asn.pa.model.CsPaTask;
import com.chinaservices.wms.module.asn.pa.model.CsPaTaskAssociation;
import com.chinaservices.wms.module.basic.customer.model.Customer;
import com.chinaservices.wms.module.basic.customer.service.CustomerService;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.folder.logistics.domain.LogisticsFolderDetailLinkCondition;
import com.chinaservices.wms.module.order.model.Order;
import com.chinaservices.wms.module.order.model.OrderRelation;
import com.chinaservices.wms.module.order.model.OrderSku;
import com.chinaservices.wms.module.order.service.OrderRelationService;
import com.chinaservices.wms.module.order.service.OrderService;
import com.chinaservices.wms.module.order.service.OrderSkuService;
import com.chinaservices.wms.module.passive.tag.model.PassiveTag;
import com.chinaservices.wms.module.passive.tag.model.PassiveTagRecognition;
import com.chinaservices.wms.module.passive.tag.service.PassiveTagRecognitionService;
import com.chinaservices.wms.module.passive.tag.service.PassiveTagService;
import com.chinaservices.wms.module.so.check.dao.SoRecheckDetailDao;
import com.chinaservices.wms.module.so.check.dao.SoRecheckHeaderDao;
import com.chinaservices.wms.module.so.check.model.SoRecheckDetail;
import com.chinaservices.wms.module.so.check.model.SoRecheckHeader;
import com.chinaservices.wms.module.so.check.service.SoCheckService;
import com.chinaservices.wms.module.so.pack.dao.PackBoxDetailDao;
import com.chinaservices.wms.module.so.pack.dao.PackDetailDao;
import com.chinaservices.wms.module.so.pack.dao.PackDetailFileDao;
import com.chinaservices.wms.module.so.pack.dao.PackHeaderDao;
import com.chinaservices.wms.module.so.pack.domain.*;
import com.chinaservices.wms.module.so.pack.model.PackBoxDetail;
import com.chinaservices.wms.module.so.pack.model.PackDetail;
import com.chinaservices.wms.module.so.pack.model.PackDetailFile;
import com.chinaservices.wms.module.so.pack.model.PackHeader;
import com.chinaservices.wms.module.so.picking.domain.TagNoQuery;
import com.chinaservices.wms.module.so.so.dao.SoDetailDao;
import com.chinaservices.wms.module.so.so.dao.SoHeaderDao;
import com.chinaservices.wms.module.so.so.domain.SoDetailDateSyncItem;
import com.chinaservices.wms.module.so.so.domain.SoDetailInfo;
import com.chinaservices.wms.module.so.so.domain.SoDetailsCondition;
import com.chinaservices.wms.module.so.so.model.SoAllocation;
import com.chinaservices.wms.module.so.so.model.SoDetail;
import com.chinaservices.wms.module.so.so.model.SoHeader;
import com.chinaservices.wms.module.so.so.service.SoAllocationPickingService;
import com.chinaservices.wms.module.so.so.service.SoAllocationService;
import com.chinaservices.wms.module.so.so.service.SoDetailService;
import com.chinaservices.wms.module.stock.back.model.TraceBackRecordItem;
import com.chinaservices.wms.module.stock.back.service.TraceBackRecordService;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.expirywarning.service.ExpiryWarningService;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import com.chinaservices.wms.module.warning.service.InvWarningService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: c12-wms
 * @ClassName PackHeaderService
 * @description:
 * @author: Goukou Liu
 * @create: 2025-01-21 09:12
 **/
@Slf4j
@Service
public class PackHeaderService extends ModuleBaseServiceSupport<PackHeaderDao, PackHeader, Long> {

    @Autowired
    private SoDetailDao soDetailDao;
    @Autowired
    private NumberGenerator numberGenerator;
    @Autowired
    private CustomFile customFile;
    @Autowired
    private PackDetailDao packDetailDao;
    @Autowired
    private PackBoxDetailDao packBoxDetailDao;
    @Autowired
    private PackDetailFileDao packDetailFileDao;
    @Autowired
    private SoHeaderDao soHeaderDao;
    @Autowired
    private StockService stockService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SoRecheckDetailDao soRecheckDetailDao;
    @Autowired
    private AsnDetailSnDao asnDetailSnDao;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private RemoteDispatchService remoteDispatchService;
    @Autowired
    private SoAllocationPickingService soAllocationPickingService;
    @Autowired
    private ExpiryWarningService expiryWarningService;

    @Autowired
    private SoRecheckHeaderDao soRecheckHeaderDao;
    @Autowired
    private SoCheckService soRecheckHeaderService;

    @Autowired
    @Lazy
    private SoDetailService soDetailService;

    @Autowired
    @Lazy
    private OrderService orderService;

    @Autowired
    @Lazy
    private OrderRelationService orderRelationService;

    @Autowired
    @Lazy
    private OrderSkuService orderSkuService;

    @Autowired
    private TraceBackRecordService traceBackRecordService;

    @Autowired
    private SoAllocationService soAllocationService;
    @Autowired
    private OwnerService ownerService;

    @Autowired
    private TaskExecutor taskExecutor;
    @Autowired
    private LogisticsFileAutoGenerateService logisticsFileAutoGenerateService;
    @Autowired
    private PackHeaderDao packHeaderDao;
    @Autowired
    private CsPaTaskAssociationDao csPaTaskAssociationDao;
    @Autowired
    private CsPaTaskDao csPaTaskDao;

    @Autowired
    private RemoteDictDataService remoteDictDataService;
    @Autowired
    private PassiveTagRecognitionService passiveTagRecognitionService;
    @Autowired
    private PassiveTagService passiveTagService;
    /**
     * 发运管理分页查询
     * @param condition 查询条件
     * @return PageResult<ShippedQuery>
     */
    public PageResult<ShippedQuery> pageShipped(ShippedPageCondition condition) {
        // 分配承运商、发运确认按钮校验
        List<String> pushLegal = Arrays.asList(WmsPushStatusEnum.NOT_PUSHED.getCode(), WmsPushStatusEnum.PUSH_FAILED.getCode());
        // 取消按钮校验
        List<String> cancelLegal = Arrays.asList(PackShippingStatusEnum.PENDING_DISPATCH.getCode(), PackShippingStatusEnum.PENDING_SHIPMENT.getCode());
        PageResult<ShippedQuery> page = dao.pageShipped(condition);

        // 标签查询
        List<String> packNoList = page.getRows().stream().map(ShippedQuery::getPackNo).toList();
        List<PackNoTagNoQuery> tagNoQueries = dao.pageShippedTagNo(packNoList);
        if (EmptyUtil.isNotEmpty(tagNoQueries)) {
            tagNoQueries.forEach(tagNoQuery -> {
                String tagNo = tagNoQuery.getTagNo();
                if (EmptyUtil.isNotEmpty(tagNo)) {
                    String[] split = tagNo.split(",");
                    tagNoQuery.setTagNoList(Arrays.asList(split));
                }
            });
        }
        Map<String, PackNoTagNoQuery> packNoListMap = tagNoQueries.stream().collect(Collectors.toMap(PackNoTagNoQuery::getPackNo, Function.identity()));
        // 标签号
        List<String> tagNoAllList = tagNoQueries.stream()
                .filter(query -> EmptyUtil.isNotEmpty(query.getTagNoList()))
                .flatMap(query -> query.getTagNoList().stream())
                .toList();
        List<PassiveTag> tagList = passiveTagService.find(new ConditionRule().andIn(PassiveTag::getTagNo, tagNoAllList));
        Map<String, Long> tagNoMap = tagList.stream().collect(Collectors.toMap(PassiveTag::getTagNo, PassiveTag::getId));

        // 发运信息
        Map<String, List<PassiveTagRecognition>> listMap = new HashMap<>();
        List<String> dispatchNoList = page.getRows().stream().map(ShippedQuery::getDispatchNo)
                .filter(EmptyUtil::isNotEmpty).toList();
        if (EmptyUtil.isNotEmpty(dispatchNoList)) {
            List<PassiveTagRecognition> recognitionList = passiveTagRecognitionService.find(new ConditionRule().andIn(PassiveTagRecognition::getDispatchNo, dispatchNoList));
            listMap = recognitionList.stream().collect(Collectors.groupingBy(PassiveTagRecognition::getDispatchNo));
        }

        // 数据处理
        for (ShippedQuery row : page.getRows()) {
            // 未推送 推送失败
            if(pushLegal.contains(row.getPushStatus())){
                // 待派车状态 开放推送、取消、分配承运商按钮
                if(StrUtil.equals(PackShippingStatusEnum.PENDING_DISPATCH.getCode(), row.getShippingStatus())){
                    row.setPushBtn(true);
                    row.setCancelBtn(true);
                    row.setAllocCarrierBtn(true);
                }
                // 待发运状态 开放取消、编辑承运商、确认发运按钮
                if(StrUtil.equals(PackShippingStatusEnum.PENDING_SHIPMENT.getCode(), row.getShippingStatus())){
                    row.setCancelBtn(true);
                    row.setEditCarrierBtn(true);
                    row.setDispatchBtn(true);
                }
                // 运输中状态 开放运输完成按钮
                if(StrUtil.equals(PackShippingStatusEnum.IN_TRANSIT.getCode(), row.getShippingStatus())){
                    row.setCompleteBtn(true);
                }
            // 已推送
            } else {
                // 当状态是待派车、待发车时 开放取消按钮
                if(cancelLegal.contains(row.getShippingStatus())){
                    row.setCancelBtn(true);
                }
            }

            // 上架任务号集合
            row.setPaNoNum(0L);
            String paNo = row.getPaNo();
            if (EmptyUtil.isNotEmpty(paNo)) {
                String[] split = paNo.split(",");
                List<String> paNoList = Arrays.stream(split).toList();
                row.setPaNoNum((long) paNoList.size());
                row.setPaNoList(paNoList);
            }

            // 标签信息
            if (EmptyUtil.isNotEmpty(packNoListMap)) {
                // 获取标签信息
                PackNoTagNoQuery packNoTagNoQuery = packNoListMap.get(row.getPackNo());
                if (EmptyUtil.isNotEmpty(packNoTagNoQuery.getTagNoList())) {
                    List<TagNoQuery> list = packNoTagNoQuery.getTagNoList().stream().map(tagNo -> {
                        Long tagId = tagNoMap.get(tagNo);
                        TagNoQuery tagNoQuery = new TagNoQuery();
                        tagNoQuery.setTagNo(tagNo);
                        tagNoQuery.setTagId(tagId);
                        return tagNoQuery;
                    }).toList();
                    row.setTagNoList(list);
                    row.setTagNoQty((long) list.size());
                    if (list.size() == 1) {
                        row.setTagNo(packNoTagNoQuery.getTagNo());
                    }
                }
            }
            // 标签识别信息
            if (EmptyUtil.isNotEmpty(listMap)) {
                // 获取标签识别信息
                List<PassiveTagRecognition> list = listMap.get(row.getDispatchNo());
                List<TagNoRecognitionQuery> queryList = list.stream().map(recognition -> {
                    TagNoRecognitionQuery query = new TagNoRecognitionQuery();
                    query.setTagId(recognition.getId());
                    query.setTagNo(recognition.getTagNo());
                    query.setRecognitionTime(recognition.getRecognitionTime());
                    query.setRecognition(recognition.getErrorReason());
                    return query;
                }).toList();
                row.setTagNoRecognitionList(queryList);
                row.setTagNoRecognitionQty((long) queryList.size());
            }

            // 标签识别状态
            if (EmptyUtil.isNotEmpty(row.getTagNoQty()) && EmptyUtil.isEmpty(row.getTagNoRecognitionQty())) {
                row.setRecognitionStatus(PackRecognitionStatusEnum.UN_RECOGNITION.getCode());
            } else if (EmptyUtil.isNotEmpty(row.getTagNoQty()) && EmptyUtil.isNotEmpty(row.getTagNoRecognitionQty())
                    && row.getTagNoQty().equals(row.getTagNoRecognitionQty())) {
                row.setRecognitionStatus(PackRecognitionStatusEnum.COMPLETE_RECOGNITION.getCode());
            } else if (EmptyUtil.isNotEmpty(row.getTagNoQty()) && EmptyUtil.isNotEmpty(row.getTagNoRecognitionQty())
                    && row.getTagNoQty().compareTo(row.getTagNoRecognitionQty()) > 0) {
                row.setRecognitionStatus(PackRecognitionStatusEnum.PARTIAL_RECOGNITION.getCode());
            }
        }
        return page;
    }

    /**
     * 获取打包任务信息
     * @param id 主键
     * @return PackHeader
     */
    public PackHeader getById(Long id) {
        return dao.findById(id);
    }

    /**
     * 分配承运商
     * @param packHeaderItem 分配条件
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(PackHeaderItem packHeaderItem) {
        // 承运商为空
        if(ObjUtil.isNull(packHeaderItem.getCarrierId())){
            packHeaderItem.setCarrierName(null);
            packHeaderItem.setShippingStatus(PackShippingStatusEnum.PENDING_DISPATCH.getCode());
        }else{
            packHeaderItem.setShippingStatus(PackShippingStatusEnum.PENDING_SHIPMENT.getCode());
        }
        dao.saveOrUpdate(packHeaderItem);
    }

    /**
     * 发运确认
     * @param id 主键
     */
    @Transactional(rollbackFor = Exception.class)
    public void confirm(Long id) {
        // 1.校验出库打包任务表是否存在
        PackHeader byId = dao.findById(id);
        if (ObjUtil.isNull(byId.getCarrierId())|| StrUtil.isBlank(byId.getShippingOrderNo())){
            throw new ServiceException(SoExcepitonEnum.PACK_NO_CARRIER_TRACKING_ASSIGNED);
        }
        // 2.根据打包任务号查询数据SoNoItem：出库单号+出库复核单号
        List<SoNoItem> soNoByPackNo = dao.findSoNoByPackNo(byId.getPackNo());
        // 3.获取出库复核单号\运输单号
        List<String> collect = soNoByPackNo.stream().map(SoNoItem::getRecheckNo).filter(EmptyUtil::isNotEmpty).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)){
            throw new ServiceException("未查询到出库复核单号");
        }
        String orderNo = soNoByPackNo.stream().map(SoNoItem::getShippingOrderNo).filter(EmptyUtil::isNotEmpty).distinct().findFirst().orElse(null);
        // 4.根据出库复核单号查询库存变化信息
        List<InvLotInfo> invLotInfos = dao.findInvLotInfoByPackNo(collect);
        log.error("<库存变化信息>{}<库存变化信息>", JSON.toJSONString(invLotInfos));
        List<String> arrayList = new ArrayList<>();
        for (InvLotInfo invLotInfo : invLotInfos) {
            InvLotLocQtyBO invLotLocQtyBO = new InvLotLocQtyBO();
            invLotLocQtyBO.setOwnerId(invLotInfo.getOwnerId());
            invLotLocQtyBO.setWarehouseId(invLotInfo.getWarehouseId());
            invLotLocQtyBO.setSkuId(invLotInfo.getSkuId());
            invLotLocQtyBO.setTransactionType(TransactionType.TRAN_SP);
            invLotLocQtyBO.setUpdateNum(invLotInfo.getQtyShippedEa());
            invLotLocQtyBO.setLotNum(invLotInfo.getLotNum());
            invLotLocQtyBO.setToPallet(invLotInfo.getPalletNo());
            invLotLocQtyBO.setPalletNum(invLotInfo.getPalletNum());
            invLotLocQtyBO.setOrderNo(orderNo);
            invLotLocQtyBO.setLocId(invLotInfo.getLocId());
            if (EmptyUtil.isNotEmpty(invLotInfo.getPickingSnNoStr())) {
                List<String> soNoList = Arrays.stream(invLotInfo.getPickingSnNoStr().split(",")).toList();
                arrayList.addAll(soNoList);
                invLotLocQtyBO.setSkuSn(soNoList);
            }
            stockService.exec(invLotLocQtyBO);
            String soDetailNo = invLotInfo.getSoDetailNo();
            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andEqual(SoDetail::getSoDetailNo, soDetailNo);
            SoDetail first = soDetailDao.findFirst(conditionRule);
            BigDecimal bigDecimal = ObjUtil.isNotNull(first.getQtyShippedEa()) ? first.getQtyShippedEa() : BigDecimal.ZERO;
            first.setQtyShippedEa(bigDecimal.add(invLotInfo.getQtyShippedEa()));
            soDetailDao.update(first);

            String soNo = first.getSoNo();
            SoHeader soHeader = soHeaderDao.findFirst(new ConditionRule().andEqual(AsnHeader::getSoNo, soNo));
            SoAllocation allocation = soAllocationPickingService.findById(invLotInfo.getAllocationId());
            // wms推送的上游单库存更新
            if (OrderSourceTypeEnum.WMSPUSH.getCode().equals(soHeader.getOrderSource()) && EmptyUtil.isNotEmpty(soHeader.getLogisticNo())) {
                // 调用更新wms库存
                soAllocationPickingService.invokingUpdateWmsInventory(allocation, soHeader, ActionCode.SHIPPING, invLotInfo.getQtyShippedEa());
            }
            if (EmptyUtil.isNotEmpty(invLotLocQtyBO.getSkuSn())) {
                // 序列号列表保存追溯记录
                this.snNoLitSaveExtracted(invLotLocQtyBO.getSkuSn(), byId.getPackNo(), first.getSoNo(), TraceBackBusinessTypeEnum.SHIPPING_CONFIRM, allocation.getOwnerId(), allocation.getWarehouseId());
            }
            expiryWarningService.addExpiryWarning(ExpiryWarningSaveStageEnum.OUTBOUND.getCode(), invLotInfo.getLotNum(), invLotInfo.getOwnerId(), invLotInfo.getWarehouseId(), invLotInfo.getSkuId());
        }
        byId.setShippingTime(new Date());
        byId.setShippingStatus(PackShippingStatusEnum.IN_TRANSIT.getCode());
        dao.saveOrUpdate(byId);

        //修改关联订单状态
        modifyRelationOrderStatus(soNoByPackNo.stream().map(e -> e.getSoNo()).distinct().collect(Collectors.toList()));
        pushShippingOrder(id);
        if (EmptyUtil.isEmpty(arrayList)){
            return;
        }
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(AsnDetailSn::getSnNo,arrayList);
        List<AsnDetailSn> asnDetailSns = asnDetailSnDao.find(conditionRule);
        if (EmptyUtil.isEmpty(asnDetailSns)){
            return;
        }
        asnDetailSns.forEach(e -> e.setStatus(SnStatusEnum.SHIPPING.getCode()));
        asnDetailSnDao.batchUpdate(asnDetailSns);
    }

    public void modifyRelationOrderStatus(List<String> soNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(SoHeader::getSoNo, soNoList);
        List<SoHeader> soHeaderList = soHeaderDao.find(conditionRule);
        if(CollectionUtil.isEmpty(soHeaderList)){
            return;
        }
        List<SoHeader> relationAsnHeaderList = soHeaderList.stream().filter(x -> YesNoEnum.YES.getCode().equals(x.getRelationOrder())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(relationAsnHeaderList)){
            return;
        }
        List<String> businessNoList = relationAsnHeaderList.stream().map(SoHeader::getSoNo).distinct().collect(Collectors.toList());
        // 根据出库单号查询关联订单信息
        List<OrderRelation> orderRelationList = orderRelationService.findByBusinessNo(businessNoList);
        if (CollectionUtil.isEmpty(orderRelationList)){
            return;
        }
        //根据出库单集合查询对应明细数据
        List<SoDetail> soDetailList = soDetailService.findBySoNoList(businessNoList);
        //抽取订单号
        List<String> orderNoList = orderRelationList.stream().map(OrderRelation::getOrderNo).distinct().collect(Collectors.toList());
        //根据订单号集合查询订单对应的所有的关联数据
        List<OrderRelation> allOrderRelationList = orderRelationService.findByOrderNoList(orderNoList);
        //转map
        Map<String,List<OrderRelation>> allOrderRelationMap = allOrderRelationList.stream().collect(Collectors.groupingBy(OrderRelation::getOrderNo));
        //抽取出库单号
        List<String> asnNoList = allOrderRelationList.stream().map(OrderRelation::getBusinessNo).distinct().collect(Collectors.toList());
        //查询出库单
        List<SoHeader> allSoHeaderList = soHeaderDao.find(ConditionRule.getInstance().andIn(SoHeader::getSoNo,asnNoList));
        //查询入库单详情
        List<SoDetail> AllSoDetailList = soDetailService.find(ConditionRule.getInstance().andIn(SoHeader::getSoNo,asnNoList));
        Map<String,List<SoDetail>> allSoDetailMap = AllSoDetailList.stream().collect(Collectors.groupingBy(SoDetail::getSoNo));
        //查询订单
        List<Order> allOrderList = orderService.findByOrderNoList(orderNoList);
        Map<String,Order> allOrderMap = allOrderList.stream().collect(Collectors.toMap(Order::getOrderNo,x->x));
        //查询订单关联商品
        List<OrderSku> orderSkuList = orderSkuService.listByOrderNoList(orderNoList);
        Map<String,List<OrderSku>> orderSkuMap = orderSkuList.stream().collect(Collectors.groupingBy(OrderSku::getOrderNo));
        List<Order> updateList = new ArrayList<>();
        allOrderRelationMap.forEach((orderNo,currentOrderRelationList) ->{
            boolean soFlag = false;
            List<String> currentSoNoList = currentOrderRelationList.stream().map(OrderRelation::getBusinessNo).distinct().collect(Collectors.toList());
            List<SoDetail> currentSoDetailList = new ArrayList<>();
            allSoDetailMap.forEach((soNo,currentSoDetail) ->{
                if(currentSoNoList.contains(soNo)){
                    currentSoDetailList.addAll(currentSoDetail);
                }
            });
            List<SoHeader> currentHeadList = allSoHeaderList.stream().filter(x -> currentSoNoList.contains(x.getSoNo())).collect(Collectors.toList());
            boolean statusFlag = Boolean.FALSE;
            if(CollectionUtil.isNotEmpty(currentHeadList)){
                statusFlag = currentHeadList.stream().allMatch(x -> SoOrderStatusEnum.CLOSE.getCode().equals(x.getStatus()));
            }
            if (CollectionUtil.isNotEmpty(currentSoDetailList)){
                //所有发运量都等于分配量 证明已完全分配
                soFlag = currentSoDetailList.stream().allMatch(x -> ObjectUtil.isNotNull(x.getQtyShippedEa()) && x.getQtyShippedEa().compareTo(x.getQtyAllocationEa()) == 0);
            }
            List<OrderSku> currentOrderSkuList = orderSkuMap.get(orderNo);
            boolean osFlag = false;
            //采购单对应商品的剩余量是否都为0
            if(CollectionUtil.isNotEmpty(currentOrderSkuList)){
                osFlag = currentOrderSkuList.stream().allMatch(x -> x.getRemainCount().compareTo(BigDecimal.ZERO) == 0);
            }
            Order order = allOrderMap.get(orderNo);
            if(soFlag && osFlag && statusFlag){
                //将销售单状态修改为已完成
                order.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
                orderService.preSave(order);
                updateList.add(order);
            }
        });
        if(CollectionUtil.isNotEmpty(updateList)){
            orderService.batchUpdate(updateList);
        }
    }

    /**
     * 打包保存
     * @param packBaseItem 打包更新类
     */
    public void save(PackBaseItem packBaseItem){
        dao.saveOrUpdate(packBaseItem);
    }

    /**
     * 出库打包分页查询
     * @param condition 查询条件
     * @return PageResult<PackBaseQuery>
     */
    public PageResult<PackBaseQuery> page(PackPageCondition condition){
        PageResult<PackBaseQuery> pageResult;
        if(ObjUtil.equal(condition.getPackStatus(),PackStatusEnum.UNPACK.getCode()) || ObjUtil.equal(condition.getPackStatus(),PackStatusEnum.CLOSE.getCode())){
            pageResult = dao.unPageIsPack(condition);
        }else{
            pageResult = dao.pageIsPack(condition);
        }

            List<String> collect = pageResult.getRows().stream().map(e -> e.getPackNo()).collect(Collectors.toList());
            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andIn("packNo",collect);
            List<PackDetail> packDetails = packDetailDao.find(conditionRule);
            Long[] array = packDetails.stream().map(PackDetail::getCreator).toArray(Long[]::new);
            Map<Long, UserForOuterQuery> userNameByIds = commonService.getUserNameByIds(array);
            Map<String, List<PackDetail>> map = packDetails.stream().collect(Collectors.groupingBy(PackDetail::getPackNo));
            pageResult.getRows().forEach(e->{
                e.convertJsonToSoNos();
                List<PackDetail> packDetails1 = map.get(e.getPackNo());
                if (CollUtil.isNotEmpty(packDetails1)){
                    List<PackDetailQuery> packDetailQueries = new ArrayList<>();
                    for (PackDetail packDetail : packDetails1) {
                        PackDetailQuery packDetailQuery = new PackDetailQuery();
                        packDetailQuery.setPackId(packDetail.getCreator());
                        packDetailQuery.setPackName(userNameByIds.get(packDetail.getCreator()).getUserName());
                        packDetailQuery.setPackTime(packDetail.getCreateTime());
                        packDetailQuery.setBoxNo(packDetail.getBoxNo());
                        packDetailQueries.add(packDetailQuery);
                    }
                    e.setPackDetailQueries(packDetailQueries);
                }

                // 上架任务号集合
                e.setPaNoNum(0L);
                String paNo = e.getPaNo();
                if (EmptyUtil.isNotEmpty(paNo)) {
                    String[] split = paNo.split(",");
                    List<String> paNoList = Arrays.stream(split).toList();
                    e.setPaNoNum((long) paNoList.size());
                    e.setPaNoList(paNoList);
                }
            });

            return pageResult;

    }


    /**
     * 根据打包任务号查询打包单信息-单据预览数据查询
     * @param packNo
     * @return
     */
    public SoPackPreviewQuery getPackPreviewQuery(String packNo){
        SoPackPreviewQuery result = new SoPackPreviewQuery();
        PackPreviewQuery packPreviewQuery = dao.getPackPreviewQueryByPackNo(packNo);
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual("packNo", packNo);
        List<PackDetail> packDetails = packDetailDao.find(conditionRule);
        Long[] array = packDetails.stream().map(PackDetail::getCreator).toArray(Long[]::new);
        Map<Long, UserForOuterQuery> userNameByIds = commonService.getUserNameByIds(array);
        List<PackPreviewQuery> queryList = new ArrayList<>();
        if (CollUtil.isNotEmpty(packDetails)){
            for (PackDetail packDetail : packDetails) {
                PackPreviewQuery query = new PackPreviewQuery();
                BeanUtil.copyProperties(packPreviewQuery, query);
                query.setBoxNo(packDetail.getBoxNo());
                query.setPackTime(packDetail.getCreateTime());
                query.setPackTime(packDetail.getCreateTime());
                if(EmptyUtil.isNotEmpty(userNameByIds) && userNameByIds.containsKey(packDetail.getCreator())){
                    query.setPackName(userNameByIds.get(packDetail.getCreator()).getUserName());
                }
                queryList.add(query);
            }
        }
        result.setPackPreviewQueryList(queryList);
        return result;
    }

    /**
     * 根据打包任务号查询发运单信息-单据预览数据查询
     * @param packNo
     */
    public ShippingPreviewQuery getShippingPreviewQueryByPackNo(String packNo) {
        ShippingPreviewQuery result = new ShippingPreviewQuery();
        ShippedQuery shippedQuery = dao.getShippingPreviewQueryByPackNo(packNo);
        if(EmptyUtil.isEmpty(shippedQuery)) {
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPED_NOT_EXIST, packNo);
        }
        BeanUtil.copyProperties(shippedQuery, result);
        // 查询数据字典 组装发运状态
        if(EmptyUtil.isNotEmpty(shippedQuery.getShippingStatus())) {
            DictDataCondition dictDataCondition = new DictDataCondition();
            dictDataCondition.setType(DictDataTypeConstants.PACK_SHIPPING_STATUS);
            ResponseData<List<DictDataQuery>> responseData = remoteDictDataService.getDictDataList(dictDataCondition);
            if(responseData.getSuccess()){
                List<DictDataQuery> data = responseData.getData();
                Map<String, String> dictMap = data.stream().collect(Collectors.toMap(DictDataQuery::getCode, DictDataQuery::getValue, (key1, key2) -> key1));
                if(EmptyUtil.isNotEmpty(dictMap) && dictMap.containsKey(shippedQuery.getShippingStatus())) {
                    shippedQuery.setShippingStatusStr(dictMap.get(shippedQuery.getShippingStatus()));
                }
            }
        }
        result.setShippedQueryList(List.of(shippedQuery));
        return result;
    }

    /**
     * 打包
     * @param ids 主键数组
     * @return PackInfo
     */
    public PackInfo pack(Long[] ids){
        List<PackBase> byIds = soHeaderDao.findPackInfoByIds(ids);
        Set<String> collect = byIds.stream().map(e -> e.getCustomerId() +
                (StrUtil.isEmpty(e.getProvinceName())? CharSequenceUtil.EMPTY:e.getProvinceName()) +
                (StrUtil.isEmpty(e.getCityName())?CharSequenceUtil.EMPTY:e.getCityName()) +
                (StrUtil.isEmpty(e.getCountyName())?CharSequenceUtil.EMPTY:e.getCountyName())
        ).collect(Collectors.toSet());
        if (CollUtil.isEmpty(collect)||collect.size()>1) {
            throw new ServiceException(SoExcepitonEnum.PACK_CUSTOMER_ADDRESS_DIFF.getMsg());
        }
        Long[] array = byIds.stream().map(PackBase::getId).toArray(Long[]::new);
        SoDetailsCondition soDetailsCondition = new SoDetailsCondition();
        soDetailsCondition.setIds(array);
        List<SoDetailInfo> soDetails = soDetailDao.findFullBySoId(soDetailsCondition);
        Map<String, List<SoDetailInfo>> groupBySoNo = soDetails.stream().collect(Collectors.groupingBy(SoDetailInfo::getRecheckNo));
        List<PackBaseInfo> packBaseInfos = new ArrayList<>();
        for (PackBase byId : byIds) {
            PackBaseInfo packInfo = new PackBaseInfo();
            packInfo.setSoNo(byId.getSoNo());
            packInfo.setRecheckNo(byId.getRecheckNo());
            packInfo.setCustomerId(byId.getCustomerId());
            packInfo.setCustomerName(byId.getCustomerName());
            packInfo.setSoDetailInfos(groupBySoNo.get(byId.getRecheckNo()));
            packBaseInfos.add(packInfo);
        }
        String packNo = numberGenerator.nextValue(IdRuleConstant.PACK_NO);

        PackInfo res = new PackInfo();
        res.setPackBaseInfos(packBaseInfos);
        res.setPackNo(packNo);
        return res;
    }
    /**
     * 获取商品数据
     * @param packNo 主键数组
     * @return PackInfo
     */
    public PackInfo packByPackNo(String packNo){
        List<PackBase> byIds = soHeaderDao.findPackInfoByPackNo(packNo);
        if (CollUtil.isEmpty(byIds)){
            throw new ServiceException(SoExcepitonEnum.PACK_CUSTOMER_ADDRESS_NOT_FIND);
        }

        Long[] array = byIds.stream().map(PackBase::getId).toArray(Long[]::new);
        SoDetailsCondition soDetailsCondition = new SoDetailsCondition();
        soDetailsCondition.setIds(array);
        List<SoDetailInfo> soDetails = soDetailDao.findFullBySoId(soDetailsCondition);
        Map<String, List<SoDetailInfo>> groupBySoNo = soDetails.stream().collect(Collectors.groupingBy(SoDetailInfo::getRecheckNo));
        List<PackBaseInfo> packBaseInfos = new ArrayList<>();
        for (PackBase byId : byIds) {
            PackBaseInfo packInfo = new PackBaseInfo();
            packInfo.setSoNo(byId.getSoNo());
            packInfo.setRecheckNo(byId.getRecheckNo());
            packInfo.setCustomerId(byId.getCustomerId());
            packInfo.setCustomerName(byId.getCustomerName());
            packInfo.setSoDetailInfos(groupBySoNo.get(byId.getRecheckNo()));
            packBaseInfos.add(packInfo);
        }

        PackInfo res = new PackInfo();
        res.setPackBaseInfos(packBaseInfos);
        res.setPackNo(packNo);
        return res;
    }
    public PackBoxInfo getBoxNo() {
        String boxNo = numberGenerator.nextValue(IdRuleConstant.BOX_NO);
        FileObject fileObject = null;
        try {
            fileObject = customFile.barcodeGenerate(boxNo);
        } catch (Exception e) {
            throw new ServiceException(GlobalExceptionEnum.BARCODE_GENERATION_FAILED.getMsg());
        }
        PackDetailFileItem packDetailFile = new PackDetailFileItem();
        packDetailFile.setBusinessId(boxNo);
        packDetailFile.setFileId(fileObject.getUuid());
        packDetailFile.setFileName(fileObject.getOrigFileName());
        packDetailFile.setFileType(FileTypeEnum.png.getCode());
        packDetailFile.setFilePath(fileObject.getUrl());
        packDetailFile.setOriginalName(fileObject.getOrigFileName());
        packDetailFile.setFileSize(Convert.toStr(fileObject.getFileSize()));
        PackBoxInfo res = new PackBoxInfo();
        res.setBoxNo(boxNo);
        res.setPackBoxDetailInfos(new ArrayList<>());
        res.setPackDetailFileItem(packDetailFile);
        return res;
    }

    /**
     * 保存打包信息
     * @param packHeaderInfo 打包信息更新类
     */
    @Transactional(rollbackFor = Exception.class)
    public void savePack(PackHeaderInfo packHeaderInfo) {
        String packNo = packHeaderInfo.getPackNo();
        ConditionRule conditionRuleFirst = new ConditionRule();
        conditionRuleFirst.andEqual("packNo",packNo);
        PackHeader first = dao.findFirst(conditionRuleFirst);
        Set<String> recheckNos = new HashSet<>();
        Map<String,BigDecimal> pack = new HashMap<>();
        String snNo = null;
        for (PackBoxInfo packBoxInfo : packHeaderInfo.getPackBoxInfos()) {
            for (PackBoxDetailInfo packBoxDetailInfo : packBoxInfo.getPackBoxDetailInfos()) {
                recheckNos.add(packBoxDetailInfo.getRecheckNo());
                String lotNum = packBoxDetailInfo.getLotNum();
                String recheckNo = packBoxDetailInfo.getRecheckNo();
                BigDecimal qtyCheckEa = packBoxDetailInfo.getQtyCheckEa();
                BigDecimal orDefault = pack.getOrDefault(lotNum + recheckNo, BigDecimal.ZERO);
                pack.put(lotNum+recheckNo,orDefault.add(qtyCheckEa));
                snNo = packBoxDetailInfo.getSoNo();
            }
        }
        SoHeader soHeader = soHeaderDao.findFirst(new ConditionRule().andEqual(AsnHeader::getSoNo, snNo));
        Integer firstNotCheck = soDetailDao.findFirstNotCheck(recheckNos);
        if (ObjUtil.isNull(firstNotCheck)||firstNotCheck!=recheckNos.size()){
            throw new ServiceException(SoExcepitonEnum.EXIST_NOT_CHECK);
        }
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn("recheckNo",recheckNos);
        PackBoxDetail first1 = packBoxDetailDao.findFirst(conditionRule);
        if (ObjUtil.isNotNull(first1)){
            throw new ServiceException(SoExcepitonEnum.EXIST_IS_PACK);
        }
        List<SoRecheckDetail> soRecheckDetails = soRecheckDetailDao.find(conditionRule);
        Map<String, BigDecimal> old = new HashMap<>();
        soRecheckDetails.forEach(e->{
            String lotNum = e.getLotNum();
            String recheckNo = e.getRecheckNo();
            BigDecimal qtyCheckEa = e.getQtyCheckEa();
            BigDecimal orDefault = old.getOrDefault(lotNum + recheckNo, BigDecimal.ZERO);
            old.put(lotNum+recheckNo,orDefault.add(qtyCheckEa));
        });
        if (pack.size() != old.size()) {
            throw new ServiceException(SoExcepitonEnum.THE_ITEM_IS_NOT_FULLY_PACKED);
        }
        for (Map.Entry<String, BigDecimal> entry : pack.entrySet()) {
            String key = entry.getKey();
            BigDecimal value1 = entry.getValue();
            BigDecimal value2 = old.get(key);
            if (value2 == null || value1.compareTo(value2)!=0) {
                throw new ServiceException(SoExcepitonEnum.THE_ITEM_IS_NOT_FULLY_PACKED);
            }
        }
        //打包任务状态扭转
        if(ObjUtil.isNull(soHeader.getCarrierId())){
            packHeaderInfo.setShippingStatus(PackShippingStatusEnum.PENDING_DISPATCH.getCode());
        }else{
            packHeaderInfo.setShippingStatus(PackShippingStatusEnum.PENDING_SHIPMENT.getCode());
        }
        Warehouse warehouse = soDetailDao.findWarehouseBySoDetailNo(soRecheckDetails.getFirst().getSoDetailNo());
        Long warehouseId = warehouse.getId();
        packHeaderInfo.setWarehouseId(warehouse.getId());
        packHeaderInfo.setWarehouseCode(warehouse.getWarehouseCode());
        packHeaderInfo.setWarehouseName(warehouse.getWarehouseName());
        packHeaderInfo.setPushStatus(WmsPushStatusEnum.NOT_PUSHED.getCode());
        packHeaderInfo.setCarrierName(soHeader.getCarrierName());
        packHeaderInfo.setCarrierId(soHeader.getCarrierId());
        packHeaderInfo.setPackStatus(PackStatusEnum.PACK.getCode());
        packHeaderInfo.setId(first.getId());
        dao.saveOrUpdate(packHeaderInfo);
        //删除原有新增的打包任务详情
        ConditionRule deleteRule = new ConditionRule();
        deleteRule.andEqual("packNo",packNo);
        packDetailDao.delete(deleteRule);

        List<Map<String, String>> mapList = new ArrayList<>();
        // 详情
        Set<String> soNoSet = new HashSet<>();
        List<PackBoxInfo> packBoxInfos = packHeaderInfo.getPackBoxInfos();
        packBoxInfos.stream().forEach(e->{
            // 箱码
            String boxNo = e.getBoxNo();
            List<PackBoxDetailInfo> packBoxDetailInfos = e.getPackBoxDetailInfos();
            PackDetailFileItem packDetailFileItem = e.getPackDetailFileItem();
            packDetailFileDao.saveOrUpdate(packDetailFileItem);
            // 商品
            packBoxDetailInfos.stream().forEach(item->{
                item.setId(null);
                item.setBoxNo(boxNo);
                soNoSet.add(item.getSoNo());
                packBoxDetailDao.saveOrUpdate(item);

                // 序列号追溯记录参数
                Map<String, String> map = new HashMap<>();
                map.put("packNo", packNo);
                map.put("skuId", item.getSkuId().toString());
                map.put("soNo", item.getSoNo());
                map.put("recheckNo", item.getRecheckNo());
                mapList.add(map);
            });
            e.setPackNo(packNo);
            packDetailDao.saveOrUpdate(e);
        });

        // 序列号追溯记录保存 -- skuSnList为空不执行
        List<Map<String, String>> list = mapList.stream().distinct().toList();
        for (Map<String, String> stringMap : list) {
            String recheckNo = stringMap.get("recheckNo");
            String skuId = stringMap.get("skuId");
            SoRecheckDetail detail = soRecheckDetailDao.findFirst(new ConditionRule().andEqual(SoRecheckDetail::getRecheckNo, recheckNo).andEqual(SoRecheckDetail::getSkuId, skuId));
            String pickingTaskNo = detail.getPickingTaskNo();
            // 查询序列号列表
            SoAllocation allocation = soAllocationService.findFirst(new ConditionRule().andEqual(SoAllocation::getPickingTaskNo, pickingTaskNo));
            String pickingSnNoStr = allocation.getPickingSnNoStr();
            if (EmptyUtil.isNotEmpty(pickingSnNoStr)) {
                String[] split = pickingSnNoStr.split(",");
                List<String> snNoList = Arrays.asList(split);
                // 记录保存
                this.snNoLitSaveExtracted(snNoList, stringMap.get("packNo"), stringMap.get("soNo"), TraceBackBusinessTypeEnum.PACKING, allocation.getOwnerId(), allocation.getWarehouseId());
            }
        }
        // 异步任务
        taskExecutor.execute(() -> {
            SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();
            List<LogisticsFolderDetailLinkCondition> asnOrderCondition  = new ArrayList<>();
            //打包单集合
            for (String soNo : soNoSet) {
                LogisticsFolderDetailLinkCondition logisticsFolderDetailLinkCondition = new LogisticsFolderDetailLinkCondition();
                logisticsFolderDetailLinkCondition.setUpDocumentNo(soNo);
                logisticsFolderDetailLinkCondition.setDocumentNos(List.of(packNo));
                asnOrderCondition.add(logisticsFolderDetailLinkCondition);
            }

            //这⾥第三个参数，为上级的单号，这⾥需替换成出库单号号码
            logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                    DocumentTypeEnum.SHIPPING,asnOrderCondition, sessionUserInfo.getCompanyId(), sessionUserInfo.getTenancy(), warehouseId);
            //这⾥第三个参数，为上级的单号，这⾥需替换成出库单号号码
            logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                    DocumentTypeEnum.SO_SHIPPING,asnOrderCondition, sessionUserInfo.getCompanyId(), sessionUserInfo.getTenancy(), warehouseId);
        });

    }

    public PackHeaderInfo getPackInfoById(Long id) {
        PackHeader byId = dao.findById(id);
        PackHeaderInfo res = new PackHeaderInfo();
        res.setPackNo(byId.getPackNo());
        List<PackDetail> packDetailByPackNo = packDetailDao.getPackDetailByPackNo(byId.getPackNo());
        List<PackBoxInfo> packBoxInfos = new ArrayList<>();
        for (PackDetail packDetail : packDetailByPackNo) {
            PackBoxInfo packBoxInfo = new PackBoxInfo();
            BeanUtil.copyProperties(packDetail,packBoxInfo);
            PackDetailFile packDetailFile = packDetailFileDao.getPackDetailFileByBoxNo(packDetail.getBoxNo());
            PackDetailFileItem packDetailFileItem = new PackDetailFileItem();
            BeanUtil.copyProperties(packDetailFile,packDetailFileItem);
            packBoxInfo.setPackDetailFileItem(packDetailFileItem);
            List<PackBoxDetailInfo> packBoxDetailInfos = packBoxDetailDao.getPackBoxDetailByBoxNos(CollUtil.toList(packDetail.getBoxNo()));
            packBoxInfo.setPackBoxDetailInfos(packBoxDetailInfos);
            packBoxInfos.add(packBoxInfo);
        }
        res.setPackBoxInfos(packBoxInfos);
        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deletePack(Long[] ids) {
        if (EmptyUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        List<PackHeader> byIds = dao.findByIds(ids);
        List<String> illegalStatus = Arrays.asList(PackShippingStatusEnum.IN_TRANSIT.getCode(), PackShippingStatusEnum.SHIPMENT_COMPLETE.getCode());
        if (byIds.stream().anyMatch(e-> illegalStatus.contains(e.getShippingStatus()))) {
            throw new ServiceException(SoExcepitonEnum.SHIPPED_NOT_DELETE);
        }
        // 删除数据
        List<String> collect = byIds.stream().map(PackHeader::getPackNo).collect(Collectors.toList());
        List<PackDetail> packDetailByPackNo = packDetailDao.getPackDetailByPackNos(collect);

        // 删除序列号追溯记录
        List<Long> idList = packDetailByPackNo.stream().map(PackDetail::getId).toList();
        List<PackBoxDetailDTO> packNoSkuCode = packBoxDetailDao.findPackNoSkuCode(idList);
        Map<String, List<PackBoxDetailDTO>> listMap = packNoSkuCode.stream().collect(Collectors.groupingBy(PackBoxDetailDTO::getPackNo));
        for (Map.Entry<String, List<PackBoxDetailDTO>> entry : listMap.entrySet()) {
            String key = entry.getKey();
            List<PackBoxDetailDTO> value = entry.getValue();
            List<String> skuCodeList = value.stream().map(PackBoxDetailDTO::getSkuCode).toList();
            // 序列号追溯记录删除
            TraceBackRecordItem item = new TraceBackRecordItem();
            item.setBusinessNo(key);
            item.setBusinessType(TraceBackBusinessTypeEnum.PACKING);
            item.setSkuCodeList(skuCodeList);
            log.error("删除序列号追溯记录参数: {}", JSON.toJSONString(item));
            traceBackRecordService.executeDel(item);
        }

        if (CollUtil.isNotEmpty(packDetailByPackNo)){
            List<String> boxNos = packDetailByPackNo.stream().map(PackDetail::getBoxNo).collect(Collectors.toList());
            packBoxDetailDao.deletePackBoxDetailByBoxNos(boxNos);
            packDetailFileDao.deletePackDetailFileByBoxNos(boxNos);
            packDetailDao.deletePackDetailByPackNos(collect);
            packDetailDao.createPackDetailByPackNos(collect);
        }
        byIds.forEach(packHeader -> {
            packHeader.setPackStatus(PackStatusEnum.UNPACK.getCode());
            preSave(packHeader);
        });
        dao.batchUpdate(byIds);
    }

    /*
     * 取消打包任务
     * */
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long[] ids) {
        if (EmptyUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        List<PackHeader> byIds = dao.findByIds(ids);
        List<String> illegalStatus = Arrays.asList(PackStatusEnum.PACK.getCode(), PackStatusEnum.CLOSE.getCode());
        if (byIds.stream().anyMatch(e-> illegalStatus.contains(e.getPackStatus()))) {
            throw new ServiceException(SoExcepitonEnum.PACKAGING_HEADER_STATUS_NOT_ALLOW_MODIFY);
        }
        List<String> collect = byIds.stream().map(PackHeader::getPackNo).collect(Collectors.toList());
//        List<PackDetail> packDetailByPackNo = packDetailDao.getPackDetailByPackNos(collect);
//        if (CollUtil.isNotEmpty(packDetailByPackNo)){
//            packDetailDao.deletePackDetailByPackNos(collect);
//        }

        List<SoRecheckHeader> soRecheckDetailByPackNos = soRecheckHeaderDao.getSoRecheckHeaderByPackNos(collect);
        if (CollUtil.isNotEmpty(soRecheckDetailByPackNos)){
            soRecheckDetailByPackNos.stream().forEach(item->{
                item.setPackNo(null);
                //设置为未生成打包任务
                item.setIsGeneratePackTask(StatusEnum.NO.getCode());
                soRecheckHeaderService.preSave(item);
                soRecheckHeaderDao.saveOrUpdate(item);
            });
        }
        byIds.stream().forEach(packHeader -> {
            packHeader.setPackStatus(PackStatusEnum.CLOSE.getCode());
            preSave(packHeader);
        });
        dao.batchUpdate(byIds);
    }

    public PackHeaderInfoQuery printPack(String packNo){
        if (StrUtil.isEmpty(packNo)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        PackHeaderInfoQuery packHeader = dao.getPrintPackList(packNo);
        if (ObjUtil.isEmpty(packHeader)) {
            throw new ServiceException(SoExcepitonEnum.PACKAGING_HEADER_DATA_NULL);
        }
        // 获取所有packNo
//        List<String> collect = printPackList.stream().map(PackHeaderInfoQuery::getPackNo).collect(Collectors.toList());
        // 获取打包任务详情
        List<PackDetail> packDetailByPackNos = packDetailDao.getPackDetailByPackNo(packHeader.getPackNo());
        //获取箱码
        List<String> boxNoList = packDetailByPackNos.stream().map(PackDetail::getBoxNo).collect(Collectors.toList());

        List<PackBoxDetailInfo> packBoxDetailByBoxNos = packBoxDetailDao.getPackBoxDetailByBoxNos(boxNoList);
        //分组
        Map<String, List<PackBoxDetailInfo>> packBoxDetailMap = packBoxDetailByBoxNos.stream()
                .collect(Collectors.groupingBy(PackBoxDetailInfo::getPackNo));

        // 将分组后的数据设置到printPackList中对应的对象的packDetailQueries字段
            List<PackBoxDetailInfo> packBoxDetails = packBoxDetailMap.getOrDefault(packHeader.getPackNo(), new ArrayList<>());

            // 统计 qtyCheckEa 的总和
            BigDecimal totalQty = packBoxDetails.stream()
                    .map(PackBoxDetailInfo::getQtyCheckEa)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 将统计结果设置到 packHeader 的 count 字段
            packHeader.setCount(totalQty);
            packHeader.setTables(packBoxDetails);
            Integer idCount = 1;
        for (PackBoxDetailInfo pack: packHeader.getTables()) {
            pack.setIndex(idCount++);
        }
         return packHeader;
    }
    public PackHeaderDnInfoQuery printDnPack(String packNo){
        if (StrUtil.isEmpty(packNo)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        PackHeaderDnInfoQuery packHeaderDnInfoQuery = dao.getPrintDNPackList(packNo);
        if (ObjUtil.isEmpty(packHeaderDnInfoQuery)) {
            throw new ServiceException(SoExcepitonEnum.PACKAGING_HEADER_DATA_NULL);
        }
        SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();
            packHeaderDnInfoQuery.setUserName(sessionUserInfo.getUserName());
            packHeaderDnInfoQuery.setTables(convertJsonToPackSkuDnInfo(packHeaderDnInfoQuery.getPackDetailQueries()));
        Integer idCount = 1;
        for (PackSkuDnInfo table: packHeaderDnInfoQuery.getTables()) {
            table.setId(idCount++);
        }


        return packHeaderDnInfoQuery;
    }

    /**
     * 将JSON字符串转换为PackSkuDnInfo对象列表
     */
    private List<PackSkuDnInfo> convertJsonToPackSkuDnInfo(String json) {
        if (json == null || json.trim().isEmpty()) {
            return Collections.emptyList();
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(json, new TypeReference<List<PackSkuDnInfo>>() {});
        } catch (Exception e) {
            // 处理异常，记录日志或返回空列表
            // 实际项目中应使用日志框架记录错误
            System.err.println("JSON转换失败: " + e.getMessage());
            return Collections.emptyList();
        }
    }

    public void batchSave(PackHeaderItem packHeaderItem) {
        List<Long> ids = packHeaderItem.getIds();
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(PackHeader::getId,ids);
        List<PackHeader> packHeaderList = dao.find(conditionRule);
        if(CollectionUtil.isNotEmpty(packHeaderList)) {
            for (PackHeader packHeader : packHeaderList) {
                packHeader.setCarrierId(packHeaderItem.getCarrierId());
                packHeader.setCarrierName(packHeaderItem.getCarrierName());
                preSave(packHeader);
            }
            dao.batchUpdate(packHeaderList);
        }
    }

    /**
     * 推送运单
     * @param id 主键
     * @return Boolean
     */
    public Boolean push(Long id){
        PackHeader packHeader = dao.findById(id);
        if(ObjUtil.isNull(packHeader)) {
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPED_NOT_EXIST, id);
        }
        if(StrUtil.equals(WmsPushStatusEnum.ALREADY_PUSHED.getCode(), packHeader.getPushStatus())) {
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPED_ALREADY_PUSHED, id);
        }
        List<String> illegalStatusList = Arrays.asList(
                PackShippingStatusEnum.PENDING_SHIPMENT.getCode(),
                PackShippingStatusEnum.IN_TRANSIT.getCode(),
                PackShippingStatusEnum.SHIPMENT_COMPLETE.getCode(),
                PackShippingStatusEnum.CANCELED.getCode()
        );
        if(illegalStatusList.contains(packHeader.getPushStatus())) {
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPED_PUSH_ILLEGAL_STATUS, id);
        }
        ThirdPushDispatchRequest request = new ThirdPushDispatchRequest();
        // 赋值唯一标识符
        request.setTaskNo(String.valueOf(packHeader.getId()));
        // 赋值客户信息
        Customer customer = customerService.getById(packHeader.getCustomerId());
        request.setCustomerCode(customer.getCustomerCode());
        request.setCustomerName(customer.getCustomerName());
        request.setReceiveProvinceCode(customer.getProvinceCode());
        request.setReceiveProvinceName(customer.getProvinceName());
        request.setReceiveCityCode(customer.getCityCode());
        request.setReceiveCityName(customer.getCityName());
        request.setReceiveCountyCode(customer.getCountyCode());
        request.setReceiveCountyName(customer.getCountyName());
        request.setReceiveAddress(customer.getAddress());
        // 赋值仓库信息
        Warehouse warehouse = warehouseService.getById(packHeader.getWarehouseId());
        request.setWarehouseCode(warehouse.getWarehouseCode());
        request.setWarehouseName(warehouse.getWarehouseName());
        request.setWarehouseProvinceCode(warehouse.getProvinceCode());
        request.setWarehouseProvinceName(warehouse.getProvinceName());
        request.setWarehouseCityCode(warehouse.getCityCode());
        request.setWarehouseCityName(warehouse.getCityName());
        request.setWarehouseCountyCode(warehouse.getCountyCode());
        request.setWarehouseCountyName(warehouse.getCountyName());
        request.setWarehouseAddress(warehouse.getAddress());
        // 赋值额外信息
        request.setTransportStatus(ThirdDispatchTransportStatusEnum.WAITING_DISPATCH.getCode());
        request.setSource(ThirdDispatchSourceEnum.CM_WMS.getCode());
        String response = remoteDispatchService.generateThirdDispatch(request);
        ResponseData responseData = JSONUtil.toBean(response, ResponseData.class);
        if(BooleanUtil.isTrue((Boolean) responseData.getData())) {
            packHeader.setPushStatus(WmsPushStatusEnum.ALREADY_PUSHED.getCode());
            packHeader.setShippingStatus(PackShippingStatusEnum.PENDING_DISPATCH.getCode());
            preSave(packHeader);
            return dao.update(packHeader);
        }else{
            packHeader.setPushStatus(WmsPushStatusEnum.PUSH_FAILED.getCode());
            packHeader.setShippingStatus(null);
            preSave(packHeader);
            return dao.update(packHeader);
        }
    }

    /**
     * 取消运单
     * @param id 主键
     * @return Boolean
     */
    public Boolean cancel(Long id){
        PackHeader packHeader = dao.findById(id);
        if(ObjUtil.isNull(packHeader)) {
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPED_NOT_EXIST, id);
        }
        List<String> illegalStatusList = Arrays.asList(
                PackShippingStatusEnum.IN_TRANSIT.getCode(),
                PackShippingStatusEnum.SHIPMENT_COMPLETE.getCode(),
                PackShippingStatusEnum.CANCELED.getCode()
        );
        // 已发运 不可取消
        if(StrUtil.equals(WmsPushStatusEnum.ALREADY_PUSHED.getCode(), packHeader.getPushStatus()) && illegalStatusList.contains(packHeader.getShippingStatus())) {
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPED_TRANSIT_CANNOT_CANCEL);
        }
        // 已推送运单额外请求取消三方的运单
        if(StrUtil.equals(WmsPushStatusEnum.ALREADY_PUSHED.getCode(), packHeader.getPushStatus())) {
            ThirdCancelDispatchRequest request = new ThirdCancelDispatchRequest();
            request.setTaskNo(String.valueOf(packHeader.getId()));
            String response = remoteDispatchService.cancelThirdDispatch(request);
            ResponseData responseData = JSONUtil.toBean(response, ResponseData.class);
            if(BooleanUtil.isFalse((Boolean) responseData.getData())) {
                return Boolean.FALSE;
            }
        }
        packHeader.setShippingStatus(PackShippingStatusEnum.CANCELED.getCode());
        preSave(packHeader);
        return dao.update(packHeader);
    }

    /**
     * 三方推送发车时间/卸货时间
     * @param request 发运变更状态请求体
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean pushShippedTime(ShippedTimeRequest request) {
        log.info("TMS推送WMS-发车时间/卸货时间，request：{}", request);
        List<String> legalTypeList = Arrays.stream(ThirdShippedTimeEnum.values()).map(ThirdShippedTimeEnum::getCode).toList();
        if(BooleanUtil.isFalse(legalTypeList.contains(request.getTimeType()))) {
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPING_TIME_TYPE_ERROR);
        }
        PackHeader packHeader = dao.findById(request.getId());
        log.info("TMS推送WMS-发车时间/卸货时间，移动发运单修改前：{}", JSONUtil.toJsonStr(packHeader));
        if(ObjUtil.isNull(packHeader)) {
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPED_NOT_EXIST, request.getId());
        }
        if(StrUtil.equals(ThirdShippedTimeEnum.DISPATCH_TIME.getCode(), request.getTimeType())) {
            packHeader.setShippingTime(request.getShippedTime());
            packHeader.setShippingStatus(PackShippingStatusEnum.IN_TRANSIT.getCode());
        }else{
            packHeader.setUnloadingTime(request.getShippedTime());
            packHeader.setShippingStatus(PackShippingStatusEnum.SHIPMENT_COMPLETE.getCode());
        }
        // 获取租户信息并赋值
        SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();
        log.info("TMS推送WMS-赋值前用户Session信息: {}", JSONUtil.toJsonStr(sessionUserInfo));
        sessionUserInfo.setCompanyId(packHeader.getCompanyId());
        sessionUserInfo.setTenancy(packHeader.getTenancy());
        SessionContext.put(sessionUserInfo);
        log.info("TMS推送WMS-赋值后用户Session信息: {}", JSONUtil.toJsonStr(SessionContext.getSessionUserInfo()));
        preSave(packHeader);
        boolean pushResult = dao.saveOrUpdate(packHeader);
        log.info("TMS推送WMS-发车时间/卸货时间，移动发运单修改后：{}", JSONUtil.toJsonStr(packHeader));
        // 如果是发车后 则扣除库存信息
        if(StrUtil.equals(PackShippingStatusEnum.IN_TRANSIT.getCode(), packHeader.getShippingStatus())) {
            // 1.根据打包任务号查询数据SoNoItem：出库单号+出库复核单号
            List<SoNoItem> soNoByPackNo = dao.findSoNoByPackNo(packHeader.getPackNo());
            // 2.获取出库复核单号
            List<String> collect = soNoByPackNo.stream().map(SoNoItem::getRecheckNo).collect(Collectors.toList());
            // 3.根据出库复核单号查询库存变化信息
            List<InvLotInfo> invLotInfos = dao.findInvLotInfoByPackNo(collect);
            List<String> arrayList = new ArrayList<>();
            for (InvLotInfo invLotInfo : invLotInfos) {
                InvLotLocQtyBO invLotLocQtyBO = new InvLotLocQtyBO();
                invLotLocQtyBO.setOwnerId(invLotInfo.getOwnerId());
                invLotLocQtyBO.setWarehouseId(invLotInfo.getWarehouseId());
                invLotLocQtyBO.setSkuId(invLotInfo.getSkuId());
                invLotLocQtyBO.setTransactionType(TransactionType.TRAN_SP);
                invLotLocQtyBO.setUpdateNum(invLotInfo.getQtyShippedEa());
                invLotLocQtyBO.setLotNum(invLotInfo.getLotNum());
                invLotLocQtyBO.setToPallet(invLotInfo.getPalletNo());
                invLotLocQtyBO.setPalletNum(invLotInfo.getPalletNum());
                invLotLocQtyBO.setOrderNo(packHeader.getPackNo());
                invLotLocQtyBO.setLocId(invLotInfo.getLocId());
                if (EmptyUtil.isNotEmpty(invLotInfo.getPickingSnNoStr())){
                    List<String> soNoList = Arrays.stream(invLotInfo.getPickingSnNoStr().split(",")).toList();
                    arrayList.addAll(soNoList);
                    invLotLocQtyBO.setSkuSn(soNoList);
                }
                stockService.exec(invLotLocQtyBO);
                String soDetailNo = invLotInfo.getSoDetailNo();
                ConditionRule conditionRule = new ConditionRule();
                conditionRule.andEqual(SoDetail::getSoDetailNo,soDetailNo);
                SoDetail first = soDetailDao.findFirst(conditionRule);
                BigDecimal bigDecimal = ObjUtil.isNotNull(first.getQtyShippedEa())?first.getQtyShippedEa():BigDecimal.ZERO;
                first.setQtyShippedEa(bigDecimal.add(invLotInfo.getQtyShippedEa()));
                soDetailDao.update(first);
            }
            if (EmptyUtil.isEmpty(arrayList)){
                return pushResult;
            }
            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andIn(AsnDetailSn::getSnNo,arrayList);
            List<AsnDetailSn> asnDetailSns = asnDetailSnDao.find(conditionRule);
            if (EmptyUtil.isEmpty(asnDetailSns)){
                return pushResult;
            }
            asnDetailSns.forEach(e -> e.setStatus(SnStatusEnum.SHIPPING.getCode()));
            asnDetailSnDao.batchUpdate(asnDetailSns);
        }
        return pushResult;
    }

    /**
     * 三方推送承运商信息
     * @param request 请求体
     * @return Boolean
     */
    public Boolean pushShipCarrier(ShippedCarrierRequest request){
        log.info("TMS推送WMS-承运商信息，request：{}", request);
        if(ObjUtil.isNull(request.getId())){
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPED_NOT_EXIST, request.getId());
        }
        PackHeader packHeader = dao.findById(request.getId());
        log.info("TMS推送WMS-承运商信息，移动发运单修改前：{}", JSONUtil.toJsonStr(packHeader));
        if(ObjUtil.isNull(packHeader)) {
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPED_NOT_EXIST, request.getId());
        }
        packHeader.setCarrierName(request.getCarrierName());
        packHeader.setShippingOrderNo(request.getShippingOrderNo());
        packHeader.setDriverName(request.getDriverName());
        packHeader.setDriverPhone(request.getDriverPhone());
        packHeader.setLicensePlate(request.getLicensePlate());
        packHeader.setShippingStatus(PackShippingStatusEnum.PENDING_SHIPMENT.getCode());
        // 获取租户信息并赋值
        SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();
        log.info("TMS推送WMS-赋值前用户UserSession信息: {}", JSONUtil.toJsonStr(sessionUserInfo));
        sessionUserInfo.setCompanyId(packHeader.getCompanyId());
        sessionUserInfo.setTenancy(packHeader.getTenancy());
        SessionContext.put(sessionUserInfo);
        log.info("TMS推送WMS-赋值后用户UserSession信息: {}", JSONUtil.toJsonStr(SessionContext.getSessionUserInfo()));
        preSave(packHeader);
        boolean pushResult = dao.update(packHeader);
        log.info("TMS推送WMS-承运商信息，移动发运单修改后：{}", JSONUtil.toJsonStr(packHeader));
        return pushResult;
    }

    /**
     * 运单运输完成
     * @param id 主键
     * @return Boolean
     */
    public Boolean shippedComplete(Long id) {
        PackHeader packHeader = dao.findById(id);
        if(ObjUtil.isNull(packHeader)) {
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPED_NOT_EXIST, id);
        }
        List<String> legalStatus = Arrays.asList(WmsPushStatusEnum.NOT_PUSHED.getCode(), WmsPushStatusEnum.PUSH_FAILED.getCode());
        if(legalStatus.contains(packHeader.getPushStatus()) && StrUtil.equals(PackShippingStatusEnum.IN_TRANSIT.getCode(), packHeader.getShippingStatus())) {
            packHeader.setShippingStatus(PackShippingStatusEnum.SHIPMENT_COMPLETE.getCode());
            preSave(packHeader);
            return dao.update(packHeader);
        } else {
            throw new ServiceException(SoExcepitonEnum.PACK_SHIPPED_PUSHED_OR_NOT_IN_TRANSIT);
        }
    }

    /**
     * 根据出库单号获取运输单号（逗号连接）
     * @param SoNoList 出库单号列表
     * @return Map<String, String> 出库单号对应的运输单号（逗号分隔）
     */
    public Map<String, String> getShipOrderNoBySoNoList(List<String> SoNoList){
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn("soNo", SoNoList);
        List<PackBoxDetail> packBoxDetailList = packBoxDetailDao.find(conditionRule);
        if(CollectionUtil.isNotEmpty(packBoxDetailList)) {
            // 1. 按 SoNo 分组 PackBoxDetail
            Map<String, List<PackBoxDetail>> soNoToBoxDetailListMap = packBoxDetailList.stream().collect(Collectors.groupingBy(PackBoxDetail::getSoNo));
            // 2. 查询出所有PackDetail 并建立 boxNo → PackDetail 的映射
            List<PackDetail> packDetailList = packDetailDao.findAll();
            Map<String, PackDetail> boxNoToPackDetailMap = packDetailList.stream().collect(Collectors.toMap(
                PackDetail::getBoxNo, Function.identity(), (k1, k2) -> k1
            ));
            // 3. 构建 SoNo → List<PackDetail> 的 Map
            Map<String, List<PackDetail>> soNoToPackDetailListMap = new HashMap<>();
            soNoToBoxDetailListMap.forEach((soNo, packBoxDetails) -> {
                List<PackDetail> details = packBoxDetails.stream()
                    .map(packBoxDetail -> MapUtil.get(boxNoToPackDetailMap, packBoxDetail.getBoxNo(), PackDetail.class))  // 通过 boxNo 找到 PackDetail
                    .filter(Objects::nonNull)  // 过滤掉 null（避免 PackBoxDetail 的 boxNo 在 PackDetail 中不存在的情况）
                    .collect(Collectors.toList());
                soNoToPackDetailListMap.put(soNo, details);
            });
            // 4.查询出所有PackHeader 并建立 packNo → PackHeader 的映射
            Map<String, PackHeader> packNoToPackHeaderMap = dao.findAll().stream().collect(Collectors.toMap(
                PackHeader::getPackNo, Function.identity(), (k1, k2) -> k1)
            );
            // 5. 构造 SoNo → List<PackHeader> 的 Map
            Map<String, List<PackHeader>> soNoToPackHeaderListMap = new HashMap<>();
            soNoToPackDetailListMap.forEach((soNo, packBoxDetails) -> {
                List<PackHeader> packHeaderList = packBoxDetails.stream()
                    // 1. 通过 boxNo 找到 PackDetail
                    .map(packBoxDetail -> MapUtil.get(boxNoToPackDetailMap, packBoxDetail.getBoxNo(), PackDetail.class))
                    .filter(Objects::nonNull)  // 过滤掉不存在的 PackDetail
                    // 2. 通过 PackDetail 的 packNo 找到 PackHeader
                    .map(packDetail -> packNoToPackHeaderMap.get(packDetail.getPackNo()))
                    .filter(Objects::nonNull)  // 过滤掉不存在的 PackHeader
                    .distinct()  // 去重（避免同一个 PackHeader 多次出现）
                    .toList();
                soNoToPackHeaderListMap.put(soNo, packHeaderList);
            });
            // 6. 构造 SoNo → String（PackHeaderList中所有的运输单号 以逗号连接） 的 Map
            Map<String, String> soNoToShipOrderNoMap = new HashMap<>();
            soNoToPackHeaderListMap.forEach((soNo, packHeaderList) -> {
                String shipOrderNo = packHeaderList.stream().map(PackHeader::getShippingOrderNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.joining(","));
                soNoToShipOrderNoMap.put(soNo, shipOrderNo);
            });
            return soNoToShipOrderNoMap;
        }
        return null;
    }

    /**
     * 根据id推送运输单号至WMS出库订单
     * @param id 主键
     */
    private void pushShippingOrder(Long id){
        // 获取发运单
        PackHeader packHeader = dao.findById(id);
        if(StrUtil.isNotBlank(packHeader.getShippingOrderNo())) {
            // 通过PackNo获取发运详情列表
            ConditionRule conditionRule = ConditionRule.getInstance();
            conditionRule.andEqual("packNo", packHeader.getPackNo());
            List<PackDetail> packDetailList = packDetailDao.find(conditionRule);
            if(CollectionUtil.isNotEmpty(packDetailList)) {
                // 过滤出发运详情中的装箱号BoxNo 获取打包装箱列表
                List<String> boxNoList = packDetailList.stream().map(PackDetail::getBoxNo).toList();
                conditionRule = ConditionRule.getInstance();
                conditionRule.andIn("boxNo", boxNoList);
                List<PackBoxDetail> packBoxDetailList = packBoxDetailDao.find(conditionRule);
                // 过滤出所有出库订单
                List<String> soNoList = packBoxDetailList.stream().map(PackBoxDetail::getSoNo).distinct().toList();
                conditionRule = ConditionRule.getInstance();
                conditionRule.andIn("soNo", soNoList);
                List<SoHeader> soHeaderList = soHeaderDao.find(conditionRule);
                if(CollectionUtil.isNotEmpty(soHeaderList)) {
                    for (SoHeader soHeader : soHeaderList) {
                        // 将出库订单结果拆分为列表 test001,test002 -> [test001, test002]
                        List<String> shippingOrderNoList = StrUtil.split(soHeader.getShippingOrderNo(), ",");
                        // 列表加入目前发运单的运输单号
                        shippingOrderNoList.add(packHeader.getShippingOrderNo());
                        // 去重并再次转换为逗号连接的格式
                        String shippingOrderNo = shippingOrderNoList.stream().filter(StrUtil::isNotBlank).distinct().collect(Collectors.joining(","));
                        // 预更新出库订单
                        soHeader.setShippingOrderNo(shippingOrderNo);
                    }
                    soHeaderDao.preSaveList(soHeaderList);
                }
                soHeaderDao.batchUpdate(soHeaderList);
            }
        }
    }

    /**
     * 分配拣货员
     * @param packerAssignItem 打包拣货员分配信息
     * @return ResponseData<PackHeaderInfo>
     */
    public Boolean assignPacker(PackerAssignItem packerAssignItem) {
        List<Long> ids = packerAssignItem.getIds();
        if (CollectionUtil.isEmpty(ids)){
            throw new ServiceException(SoExcepitonEnum.RECHECK_ID_EMPTY);
        }
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(PackHeader::getId,ids);
        List<PackHeader> packHeaders = dao.find(conditionRule);
        if (CollectionUtil.isEmpty(packHeaders) || packHeaders.size() != ids.size()){
            throw new ServiceException(SoExcepitonEnum.THE_SELECTED_OUTBOUND_PACKING_TASKS_CONTAIN_INVALID_OR_DELETED_ITEMS);
        }
        packHeaders.forEach(header -> {
            header.setPackagerId(packerAssignItem.getPackagerId());
            header.setPackagerName(packerAssignItem.getPackagerName());
            preSave(header);
        });
        dao.batchUpdate(packHeaders);
        return Boolean.TRUE;
    }

    /**
     * 序列号列表保存追溯记录
     *
     * @param skuSnList Sku sn列表
     * @param packNo    包没有
     * @param soNo      所以没有
     */
    private void snNoLitSaveExtracted(List<String> skuSnList, String packNo, String soNo, TraceBackBusinessTypeEnum typeEnum, Long ownerId, Long warehouseId) {
        if (EmptyUtil.isNotEmpty(skuSnList)) {
            TraceBackRecordItem item = new TraceBackRecordItem();
            // 货主名称
            Owner owner = ownerService.findById(ownerId);
            item.setOwnerName(owner.getOwnerName());
            // 仓库名称
            Warehouse warehouse = warehouseService.findById(warehouseId);
            item.setWarehouseName(warehouse.getWarehouseName());
            // 业务单号=打包出库
            item.setBusinessNo(packNo);
            // 业务类型=复核
            item.setBusinessType(typeEnum);
            // 来源单号=出库单号
            item.setSourceNo(soNo);
            // 序列号列表
            item.setSnNoList(skuSnList);
            traceBackRecordService.execute(item);
        }
    }

    /**
     * 根据出库单号和商品id逆向查询出对应的运输单号集合
     * @param soDetailNo 出库单详情单号
     * @param skuId 商品id
     * @param resultMap 结果集，线程执行的最终结果 key = soNo,skuId  value = 运输单号集合
     */
    @Async
    public CompletableFuture<String> syncShippedOrderNoBySoNoAndSkuId(String soDetailNo, Long skuId, Map<String, SoDetailDateSyncItem> resultMap) {
        String key = soDetailNo + "," + skuId;
        SoDetailDateSyncItem dateSyncItem = resultMap.get(key);
        dateSyncItem.setWayBillNoList(new ArrayList<>());
        //根据so + skuid 查询出对应的box_no
        List<SoAllocation> allocationList = soAllocationService.find(new ConditionRule().andEqual(SoAllocation::getSoDetailNo, soDetailNo));
        if (EmptyUtil.isEmpty(allocationList)) {
            return CompletableFuture.completedFuture(key);
        }
        List<String> pickingTaskNoList = allocationList.stream().map(SoAllocation::getPickingTaskNo).toList();
        if (EmptyUtil.isEmpty(pickingTaskNoList)) {
            return CompletableFuture.completedFuture(key);
        }
        //中途执行查询任务出库上架数EA
        List<CsPaTaskAssociation> associationList = csPaTaskAssociationDao.find(new ConditionRule().andIn(CsPaTaskAssociation::getPickingTaskNo, pickingTaskNoList));
        if (EmptyUtil.isNotEmpty(associationList)) {
            List<String> paListNo = associationList.stream().map(CsPaTaskAssociation::getPaNo).toList();
            List<CsPaTask> paTasks = csPaTaskDao.find(new ConditionRule().andIn(CsPaTaskAssociation::getPaNo, paListNo));
            BigDecimal reduce = paTasks.stream().map(CsPaTask::getQtyPlanEa).reduce(BigDecimal.ZERO, BigDecimal::add);
            dateSyncItem.setQtyPaEa(reduce);
        }

        List<SoRecheckDetail> recheckDetailList = soRecheckDetailDao.find(new ConditionRule().andIn(SoRecheckDetail::getPickingTaskNo, pickingTaskNoList));
        if (EmptyUtil.isEmpty(recheckDetailList)) {
            return CompletableFuture.completedFuture(key);
        }
        List<String> recheckNoList = recheckDetailList.stream().map(SoRecheckDetail::getRecheckNo).distinct().toList();
        if (EmptyUtil.isEmpty(recheckNoList)) {
            return CompletableFuture.completedFuture(key);
        }
        List<PackBoxDetail> packBoxDetailList = packBoxDetailDao.find(new ConditionRule().andIn(PackBoxDetail::getRecheckNo, recheckNoList));
        if (EmptyUtil.isEmpty(packBoxDetailList)) {
            return CompletableFuture.completedFuture(key);
        }
        List<String> boxNoList = packBoxDetailList.stream().map(PackBoxDetail::getBoxNo).distinct().toList();
        if (EmptyUtil.isEmpty(boxNoList)) {
            return CompletableFuture.completedFuture(key);
        }
        List<PackDetail> packDetailList = packDetailDao.find(new ConditionRule().andIn(PackDetail::getBoxNo, boxNoList));
        if (EmptyUtil.isEmpty(packDetailList)) {
            return CompletableFuture.completedFuture(key);
        }
        List<String> packNoList = packDetailList.stream().map(PackDetail::getPackNo).distinct().toList();
        if (EmptyUtil.isEmpty(packNoList)) {
            return CompletableFuture.completedFuture(key);
        }
        List<PackHeader> packHeaderList = packHeaderDao.find(new ConditionRule().andIn(PackHeader::getPackNo, packNoList));
        if (EmptyUtil.isEmpty(packHeaderList)) {
            return CompletableFuture.completedFuture(key);
        }
        List<String> shippingOrderNoList = packHeaderList.stream().map(PackHeader::getShippingOrderNo).filter(Objects::nonNull).toList();
        dateSyncItem.setWayBillNoList(shippingOrderNoList);
        return CompletableFuture.completedFuture(key);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deletePack(Collection<String> packNos){
        List<PackHeader> headerList = dao.find(new ConditionRule().andIn(PackHeader::getPackNo, packNos));
        if (EmptyUtil.isEmpty(headerList)){
            throw new ServiceException(SoExcepitonEnum.PACKAGING_HEADER_DATA_NULL);
        }
        deletePack(headerList.stream().map(PackHeader::getId).toArray(Long[]::new));
        cancel(headerList.stream().map(PackHeader::getId).toArray(Long[]::new));
    }

    /**
     * 发运单和商品Id是否存在
     *
     * @param dispatchNo 发运单
     * @param skuId      商品Id
     * @return int
     */
    public int isExistByDispatchNoAndSkuId(String dispatchNo, Long skuId) {
        return dao.isExistByDispatchNoAndSkuId(dispatchNo, skuId);
    }

    /**
     * 发运商品EA数量
     *
     * @param dispatchNo 发运单
     * @return int
     */
    public List<ShippedSkuCheckQtyQuery> findShippedSkuEa(String dispatchNo) {
        return dao.findShippedSkuEa(dispatchNo);
    }
}
