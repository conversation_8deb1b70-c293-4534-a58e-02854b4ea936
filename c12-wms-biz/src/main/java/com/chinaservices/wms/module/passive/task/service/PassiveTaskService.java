package com.chinaservices.wms.module.passive.task.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.core.third.constant.ThirdResponse;
import com.chinaservices.core.third.porch.domain.*;
import com.chinaservices.core.third.porch.service.ThirdInoutService;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.wms.common.enums.passive.PassiveTaskStatusEnum;
import com.chinaservices.wms.common.exception.PassiveExceptionEnum;
import com.chinaservices.wms.module.asn.receive.service.AsnReceivePassiveService;
import com.chinaservices.wms.module.passive.device.service.PassiveDeviceService;
import com.chinaservices.wms.module.passive.tag.service.PassiveTagRecognitionService;
import com.chinaservices.wms.module.passive.take.dto.PassiveTaskMockCondition;
import com.chinaservices.wms.module.passive.task.dao.PassiveTaskDao;
import com.chinaservices.wms.module.passive.task.domain.*;
import com.chinaservices.wms.module.passive.task.model.PassiveTask;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.sdk.pojo.PageResult;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
* task
* <AUTHOR> generate
* @date  2025-07-23
**/
@Service
public class PassiveTaskService extends ModuleBaseServiceSupport<PassiveTaskDao,PassiveTask,Long> {

    @Autowired
    private ThirdInoutService thirdInoutService;
    @Autowired
    private AsnReceivePassiveService asnReceivePassiveService;
    @Autowired
    private PassiveDeviceService passiveDeviceService;
    @Autowired
    private PassiveTagRecognitionService passiveTagRecognitionService;

    /**
    * 分页查询
    * @param condition
    * @return PageResult<PassiveTaskQuery>
    */
    public PageResult<PassiveTaskQuery> page(PassiveTaskPageCondition condition) {
        return dao.page(condition);
    }
    /**
    * 自动查询数据
    * @return PageResult<PassiveTaskQuery>
    */
    public void getTaskInfo(List<PassiveTaskMockCondition> condition) {
        List<PassiveTask> passiveTasks = new ArrayList<>();
        if (CollUtil.isEmpty(condition)){
            ConditionRule conditionRuleOne = new ConditionRule();
            conditionRuleOne.andEqual(PassiveTask::getStatus, PassiveTaskStatusEnum.START.getCode());
            passiveTasks = dao.find(conditionRuleOne);
        }else{
            //mock数据
            for (PassiveTaskMockCondition con:condition) {
                PassiveTask byId = dao.findById(con.getId());
                PassiveTask passiveTask1 = BeanUtil.copyProperties(byId, PassiveTask.class);
                passiveTasks.add(passiveTask1);
            }
        }

        List<PassiveTaskSaveLogCondition> passiveTaskSaveLogConditions = new ArrayList<>();
        //执行中的数据
        for (PassiveTask passiveTask : passiveTasks) {
            // 为当前任务创建 StringBuilder
            StringBuilder epcIdsBuilder = new StringBuilder();

            PorchQueryRequest porchQueryRequest = new PorchQueryRequest();
            porchQueryRequest.setTaskId(Integer.valueOf(passiveTask.getTaskId()));
            porchQueryRequest.setInoutStatus(Integer.valueOf(passiveTask.getType()));
            porchQueryRequest.setCurrent(1);
            porchQueryRequest.setLimit(1000);

            List<PorchRecordResponse> records = new ArrayList<>();
            PorchResultResponse responseData = null;
            //mock接口取传入的参数
            if (ObjectUtil.isNotNull(condition)){
                // 查找condition中taskId匹配的项
                Optional<PassiveTaskMockCondition> matchedCondition = condition.stream()
                        .filter(con -> passiveTask.getTaskId().equals(con.getTaskId()))
                        .findFirst();

                // 如果找到匹配的条件，将其epcIds赋值给records
                if (matchedCondition.isPresent() && matchedCondition.get().getEpcList() != null) {
                    records = matchedCondition.get().getEpcList();
                }
            }else{
                // 查询进出门详情
                ThirdResponse thirdResponse = thirdInoutService.queryInoutInfo(porchQueryRequest);
                if (!ObjectUtil.equal(thirdResponse.getCode(), 200)) {
                    continue;
                }
                responseData = (PorchResultResponse) thirdResponse.getData();
                records = responseData.getPage().getRecords();
            }


            // 处理每个记录
            for (int i = 0; i < records.size(); i++) {
                PorchRecordResponse porchRecordResponse = records.get(i);
                PassiveTaskSaveLogCondition passiveTaskSaveLogCondition = new PassiveTaskSaveLogCondition();
                String errorMessage = null;

                try {
                    // 自动收货
                    errorMessage = asnReceivePassiveService.createAsnOrderOrSoOrderByTag(porchRecordResponse.getEpc());
                } catch (Exception e) {
                    errorMessage = e.getMessage();
                } finally {

                    //不包含才添加到日志
                    if(ObjectUtil.isNull(passiveTask.getEpcIds()) || !passiveTask.getEpcIds().contains(porchRecordResponse.getEpc())){
                        // 添加到 EPC 列表
                        if (epcIdsBuilder.length() > 0) {
                            epcIdsBuilder.append(",");
                        }
                        epcIdsBuilder.append(porchRecordResponse.getEpc());
                        // 设置日志条件
                        passiveTaskSaveLogCondition.setEpc(porchRecordResponse.getEpc());
                        passiveTaskSaveLogCondition.setInventoryId(String.valueOf(porchRecordResponse.getId()));
                        passiveTaskSaveLogCondition.setErrorMessage(errorMessage);
                        passiveTaskSaveLogCondition.setDatetime(porchRecordResponse.getDatetime());
                        passiveTaskSaveLogCondition.setTaskId(porchRecordResponse.getTaskid());
                        passiveTaskSaveLogConditions.add(passiveTaskSaveLogCondition);
                    }

                }
            }
            //保存查询过的标签号
            if(StrUtil.isNotEmpty(passiveTask.getEpcIds())){
                passiveTask.setEpcIds(passiveTask.getEpcIds() + "," + epcIdsBuilder);
            }else{
                passiveTask.setEpcIds(epcIdsBuilder.toString());
            }
            //mock接口直接完成
            if (ObjectUtil.isNotNull(condition)){
                passiveTask.setStatus(PassiveTaskStatusEnum.COMPLETED.getCode());
                passiveTask.setEndTime(new Date());
            }else if(!ObjectUtil.equal(responseData.getTaskinfo().getStatus(),PassiveTaskStatusEnum.START.getCode())){
                passiveTask.setStatus(responseData.getTaskinfo().getStatus());
                passiveTask.setEndTime(new Date());
            }

              //保存标签集合
            dao.update(passiveTask);
        }
        passiveTagRecognitionService.save(passiveTaskSaveLogConditions);
    }


    /**
    * 结束
    * @param id id
    * @return PassiveTaskQuery
    */
    public void end(Long id,Boolean mock) {
        PassiveTask passiveTask = dao.findById(id);
        if (StrUtil.isEmpty(passiveTask.getTaskId())) {
            throw new ServiceException(PassiveExceptionEnum.PASSIVE_DEVICE_TASK_ID_NOT);
        }
        if (mock){
            passiveTask.setStatus(PassiveTaskStatusEnum.COMPLETED.getCode());
            passiveTask.setEndTime(new Date());
            dao.update(passiveTask);
            return;
        }
        PorchStopRequest porchStopRequest = new PorchStopRequest();
        porchStopRequest.setTaskId(Integer.valueOf(passiveTask.getTaskId()));
        ThirdResponse thirdResponse = thirdInoutService.stopInout(porchStopRequest);
        if (!ObjectUtil.equal(thirdResponse.getCode(), 200)){
            throw new ServiceException(PassiveExceptionEnum.PASSIVE_DEVICE_PORT_IN_INOUT, thirdResponse.getMessage());
        }
        passiveTask.setStatus(PassiveTaskStatusEnum.COMPLETED.getCode());
        passiveTask.setEndTime(new Date());
        dao.update(passiveTask);
    }
    /**
    * 新增
    * @param item
    * @return PassiveTaskQuery
    */
    public void save(PassiveTaskItem item) {
        //判断库区下是否存在自动化类型设备
        passiveDeviceService.checkPassiveDeviceExists(item.getWarehouseZoneId());

        ConditionRule conditionRuleOne = new ConditionRule();
        conditionRuleOne.andEqual(PassiveTask::getWarehouseZoneId,item.getWarehouseZoneId());
        PassiveTask first = dao.findFirst(conditionRuleOne);
        if (ObjectUtil.isNotNull(first) && ObjectUtil.equal( first.getStatus(), PassiveTaskStatusEnum.START.getCode())){
            throw new ServiceException(PassiveExceptionEnum.PASSIVE_DEVICE_PORT_IN_WZ_INPUT, first.getLocationName());
        }

        //走mock接口
        if (BooleanUtils.isTrue(item.getMock())){
            Random random = new Random();
            item.setLocationName(item.getLocationName());
            item.setTaskId(String.valueOf(random.nextInt(10000)));
            item.setBeginTime(new Date());
            dao.saveOrUpdate(item);
            return;
        }
            // 1：启动自动门进门
            PorchStartRequest porchStartRequest = new PorchStartRequest();
            porchStartRequest.setTaskname(item.getTaskNo());
            porchStartRequest.setLocation(item.getLocationName()+"自动门");
            porchStartRequest.setType(item.getType());
            ThirdResponse thirdResponse = thirdInoutService.ableInout(porchStartRequest);
            if (!ObjectUtil.equal(thirdResponse.getCode(), 200)){
                throw new ServiceException(PassiveExceptionEnum.PASSIVE_DEVICE_PORT_IN_INOUT, thirdResponse.getMessage());
            }
            PorchStopRequest responseData = (PorchStopRequest) thirdResponse.getData();
            item.setTaskId(String.valueOf(responseData.getTaskId()));
            item.setBeginTime(new Date());
            dao.saveOrUpdate(item);
    }




}
