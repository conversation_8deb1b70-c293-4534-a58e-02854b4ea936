package com.chinaservices.wms.module.performance.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "cs_kpi_index")
public class KpiIndex extends ModuleBaseModel {

    /**
     *指标编号
     */
    private String indexNo;
    /**
     *指标名称
     */
    private String indexName;
    /**
     *指标类型
     */
    private String indexType;
    /**
     *平分标准
     */
    private String scoreType;
    /**
     *数据来源
     */
    private String dataSource;
    /**
     *指标状态
     */
    private String status;
    /**
     *备注
     */
    private String notes;

    /**
     * 是否是系统创建
     */
    private Integer isSystem;
}
