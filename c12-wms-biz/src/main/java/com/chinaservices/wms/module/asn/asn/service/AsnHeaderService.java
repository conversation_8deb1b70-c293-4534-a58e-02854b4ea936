package com.chinaservices.wms.module.asn.asn.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.auth.module.dict.domain.DictDataCondition;
import com.chinaservices.auth.module.dict.domain.DictDataQuery;
import com.chinaservices.auth.module.dict.domain.SelectItem;
import com.chinaservices.auth.module.dict.feign.RemoteDictDataService;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.edi.module.asn.domain.StatusUpdateCondition;
import com.chinaservices.edi.module.asn.domain.enums.OperationTypeEnum;
import com.chinaservices.edi.module.asn.feign.RemoteAsnStatusPushToCrzFegin;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseModel;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ErrorResponseData;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.base.BaseStatusItem;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constant.MsgConstant;
import com.chinaservices.wms.common.constants.DictDataTypeConstants;
import com.chinaservices.wms.common.domain.LotAttribute;
import com.chinaservices.wms.common.domain.LotDetailItem;
import com.chinaservices.wms.common.enums.DocumentTypeEnum;
import com.chinaservices.wms.common.enums.asn.*;
import com.chinaservices.wms.common.enums.common.AsnEnum;
import com.chinaservices.wms.common.enums.common.AuditStatusEnum;
import com.chinaservices.wms.common.enums.order.OrderStatusEnum;
import com.chinaservices.wms.common.enums.so.ReceiveStatusEnum;
import com.chinaservices.wms.common.enums.so.SnStatusEnum;
import com.chinaservices.wms.common.enums.stock.TraceBackBusinessTypeEnum;
import com.chinaservices.wms.common.exception.AsnExceptionEnum;
import com.chinaservices.wms.common.service.CommonService;
import com.chinaservices.wms.common.util.LotUtil;
import com.chinaservices.wms.module.archive.archives.service.LogisticsFileAutoGenerateService;
import com.chinaservices.wms.module.asn.asn.dao.AsnDetailSnDao;
import com.chinaservices.wms.module.asn.asn.dao.AsnHeaderDao;
import com.chinaservices.wms.module.asn.asn.domain.*;
import com.chinaservices.wms.module.asn.asn.eventbus.AsnServiceEventBusPush;
import com.chinaservices.wms.module.asn.asn.model.AsnDetail;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.merge.dao.AsnMergeReceiveDao;
import com.chinaservices.wms.module.asn.merge.model.AsnMergeReceive;
import com.chinaservices.wms.module.asn.receive.domain.AsnReceiveCondition;
import com.chinaservices.wms.module.asn.receive.domain.AsnReceiveWhetherShelfUpdateItem;
import com.chinaservices.wms.module.asn.receive.model.AsnReceive;
import com.chinaservices.wms.module.asn.receive.service.AsnReceiveService;
import com.chinaservices.wms.module.attachment.domain.CsAttachmentCondition;
import com.chinaservices.wms.module.attachment.domain.CsAttachmentQuery;
import com.chinaservices.wms.module.basic.carrier.CarrierCondition;
import com.chinaservices.wms.module.basic.carrier.dao.CarrierDao;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.basic.cspackage.dao.PackageDao;
import com.chinaservices.wms.module.basic.cspackage.dao.PackageUnitDao;
import com.chinaservices.wms.module.basic.cspackage.model.Package;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.customer.dao.CustomerDao;
import com.chinaservices.wms.module.basic.customer.domain.CustomerCondition;
import com.chinaservices.wms.module.basic.lot.dao.WarehouseLotDetailDao;
import com.chinaservices.wms.module.basic.lot.domain.WarehouseLotDetailCondition;
import com.chinaservices.wms.module.basic.lot.domain.WarehouseLotDetailItem;
import com.chinaservices.wms.module.basic.owner.dao.OwnerDao;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.pallet.dao.PalletDao;
import com.chinaservices.wms.module.basic.pallet.model.Pallet;
import com.chinaservices.wms.module.basic.sku.dao.SkuDao;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.basic.owner.dao.OwnerDao;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.pallet.dao.PalletDao;
import com.chinaservices.wms.module.basic.pallet.model.Pallet;
import com.chinaservices.wms.module.basic.sku.dao.SkuDao;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.supplier.dao.SupplierDao;
import com.chinaservices.wms.module.basic.supplier.model.Supplier;
import com.chinaservices.wms.module.excel.item.AsnHeaderExcelItem;
import com.chinaservices.wms.module.folder.logistics.domain.LogisticsFolderDetailLinkCondition;
import com.chinaservices.wms.module.order.domain.OrderSkuQuery;
import com.chinaservices.wms.module.order.model.Order;
import com.chinaservices.wms.module.order.model.OrderRelation;
import com.chinaservices.wms.module.order.model.OrderSku;
import com.chinaservices.wms.module.order.service.OrderRelationService;
import com.chinaservices.wms.module.order.service.OrderService;
import com.chinaservices.wms.module.order.service.OrderSkuService;
import com.chinaservices.wms.module.passive.tag.dao.PassiveTagDao;
import com.chinaservices.wms.module.passive.tag.dao.PassiveTagLogDao;
import com.chinaservices.wms.module.passive.tag.dao.PassiveTagRecognitionDao;
import com.chinaservices.wms.module.passive.tag.domain.PassiveTagLogQuery;
import com.chinaservices.wms.module.passive.tag.domain.PassiveTagQuery;
import com.chinaservices.wms.module.passive.tag.domain.PassiveTagRecognitionQuery;
import com.chinaservices.wms.module.passive.tag.model.PassiveTag;
import com.chinaservices.wms.module.passive.tag.model.PassiveTagLog;
import com.chinaservices.wms.module.passive.tag.model.PassiveTagRecognition;
import com.chinaservices.wms.module.warehouse.loc.dao.WarehouseLocDao;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.stock.back.model.TraceBackRecordItem;
import com.chinaservices.wms.module.stock.back.service.TraceBackRecordService;
import com.chinaservices.wms.module.warehouse.warehouse.dao.WarehouseDao;
import com.chinaservices.wms.module.warehouse.warehouse.domain.WarehouseCondition;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 预到货通知单 Service
 */
@Service
public class AsnHeaderService extends ModuleBaseServiceSupport<AsnHeaderDao, AsnHeader, Long> {

    @Autowired
    private WarehouseLotDetailDao warehouseLotDetailDao;

    @Autowired
    private WarehouseDao warehouseDao;

    @Autowired
    private CarrierDao carrierDao;

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private AsnDetailService asnDetailService;

    @Autowired
    private AsnReceiveService asnReceiveService;

    @Autowired
    private NumberGenerator numberGenerator;

    @Autowired
    private RemoteDictDataService remoteDictDataService;
    @Autowired
    private AsnDetailSnDao asnDetailSnDao;
    @Autowired
    private CommonService commonService;
    @Autowired
    private CsAsnHeaderFileService csAsnHeaderFileService;
    @Autowired
    private SupplierDao supplierDao;
    @Autowired
    private LotUtil lotUtil;
    @Autowired
    private RemoteAsnStatusPushToCrzFegin remoteAsnStatusPushToCrzFegin;
    @Autowired
    private OrderRelationService orderRelationService;
    @Autowired
    private OrderSkuService orderSkuService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private PackageUnitService packageUnitService;
    @Autowired
    private OwnerDao ownerDao;
    @Autowired
    private SkuDao skuDao;
    @Autowired
    private PackageDao packageDao;
    @Autowired
    private PackageUnitDao packageUnitDao;
    @Autowired
    private WarehouseLocDao warehouseLocDao;
    @Autowired
    private PalletDao palletDao;
    @Autowired
    private AsnMergeReceiveDao asnMergeReceiveDao;
    @Autowired
    private AsnServiceEventBusPush asnServiceEventBusPush;

    @Autowired
    private TraceBackRecordService traceBackRecordService;
    @Autowired
    private OwnerService ownerService;
    @Autowired
    private TaskExecutor taskExecutor;
    @Autowired
    private LogisticsFileAutoGenerateService logisticsFileAutoGenerateService;
    @Autowired
    private PassiveTagDao  passiveTagDao;
    @Autowired
    private PassiveTagRecognitionDao passiveTagRecognitionDao;

    /**
     * 入库计划分页查询
     *
     * @param condition 分页条件
     * @return {@link PageResult }<{@link AsnHeaderQuery }>
     */
    public PageResult<AsnHeaderQuery> page(AsnHeaderPageCondition condition) {
        //设置数据权限
        PageResult<AsnHeaderQuery> page = dao.page(condition);
        List<AsnHeaderQuery> rows = page.getRows();
        if (CollUtil.isEmpty(rows)){
            return page;
        }
        Set<String> asnNos = rows.stream().map(AsnHeaderQuery::getAsnNo).collect(Collectors.toSet());
        // 查询全部入库订单状态
        AsnDetailByReceiveICondition cond = new AsnDetailByReceiveICondition();
        cond.setAsnNos(asnNos);
        List<AsnDetailByReceiveItem> detailListByAsnNos = asnDetailService.selectListByAsnNo(cond);
        Map<String, List<AsnDetailByReceiveItem>> asnIdGroupDetailList =
                detailListByAsnNos.stream().collect(Collectors.groupingBy(AsnDetailByReceiveItem::getAsnNo));
        //根据asnNo查询标签号
        List<PassiveTag> passiveTags = passiveTagDao.find(new ConditionRule().andIn(PassiveTag::getAsnNo, asnNos));
        Map<String,List<PassiveTag>> passiveTagMap = CollUtil.isEmpty(passiveTags) ? new HashMap<>() : passiveTags.stream().collect(Collectors.groupingBy(PassiveTag::getAsnNo));
        Map<String,List<PassiveTagRecognitionQuery>> passiveTagLogMap = new HashMap<>();
        if (CollUtil.isNotEmpty(passiveTags)){
            List<String> list = passiveTags.stream().map(PassiveTag::getTagNo).toList();
            List<PassiveTagRecognitionQuery> tagLogList = passiveTagRecognitionDao.find(PassiveTagRecognitionQuery.class,new ConditionRule().andIn(PassiveTagRecognition::getTagNo, list));
           if (EmptyUtil.isEmpty(tagLogList)){
               passiveTagLogMap = tagLogList.stream().collect(Collectors.groupingBy(PassiveTagRecognitionQuery::getTagNo));
           }
        }
        // 判断按钮是否符合展示状态
        Map<String, List<PassiveTagRecognitionQuery>> finalPassiveTagLogMap = passiveTagLogMap;
        rows.forEach(query ->
        {
            // 订单状态
            String status = query.getStatus();
            // 审核状态
            String auditStatus = query.getAuditStatus();
            // 入库单号
            String asnNo = query.getAsnNo();
            List<AsnDetailByReceiveItem> detailList = asnIdGroupDetailList.get(asnNo);
            if (EmptyUtil.isEmpty(detailList)) {
                return;
            }
            // 入库订单商品详情
            query.setDetails(detailList);

            // 入库商品明细全部完全收货 -> 入库单完全收货
            boolean isExistAllReceipt =
                    detailList.stream().allMatch(detail -> ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode().equals(detail.getStatus()));
            // 入库商品明细全部未收货 -> 入库单未收货
            boolean isExistNotReceipt =
                    detailList.stream().allMatch(detail ->
                            ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(detail.getStatus()));
            // 收货状态
            String receiveStatusCode = isExistAllReceipt ? ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode() :
                    isExistNotReceipt ? ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode() :
                            ReceiveStatusEnum.PART_RECEIVED_GOODS.getCode();
            query.setReceiveStatus(receiveStatusCode);

            // 编辑按钮\审核按钮
            if (AsnStatusEnum.ASN_NEW.getCode().equals(status)
                    && AuditStatusEnum.UNREVIEWED.getCode().equals(auditStatus)) {
                query.setEdit(true);
                query.setAudit(true);
                query.setCancelOrder(true);
            }
            // 取消审核
            if (AsnStatusEnum.ASN_NEW.getCode().equals(status)
                    && AuditStatusEnum.AUDITED.getCode().equals(auditStatus)
                    && isExistNotReceipt) {
                query.setCancelAudit(true);
            }
            // 取消订单\完结订单
            if (AsnStatusEnum.ASN_NEW.getCode().equals(status)
                    || ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode().equals(status)
                    || ReceiveStatusEnum.PART_RECEIVED_GOODS.getCode().equals(status)) {
                query.setDoneOrder(true);
            }
            if (AsnStatusEnum.ASN_NEW.getCode().equals(status)&& AuditStatusEnum.AUDITED.getCode().equals(auditStatus)) {
                // 收货员按钮
                if (StrUtil.isEmpty(query.getReceiverOp())) {
                    query.setAssignReceiverBtn(Boolean.TRUE);
                } else {
                    if (ObjUtil.equals(AsnStatusEnum.ASN_NEW.getCode(), status) && ObjUtil.notEqual(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode(), receiveStatusCode)) {
                        query.setEditReceiverBtn(Boolean.TRUE);
                    }
                };
            }

            List<PassiveTag> tags = passiveTagMap.get(asnNo);
            if (EmptyUtil.isNotEmpty(tags)) {
                List<PassiveTagQuery> tagQueries = tags.stream().map(item -> {
                    PassiveTagQuery tagQuery = new PassiveTagQuery();
                    BeanUtil.copyProperties(item, tagQuery);
                    return tagQuery;
                }).toList();
                query.setPassiveTagList(tagQueries);
                List<PassiveTagRecognitionQuery> allTagLogQueryList = new ArrayList<>();
                for (PassiveTag tag : tags) {
                    List<PassiveTagRecognitionQuery> tagLogList = finalPassiveTagLogMap.get(tag.getTagNo());
                    if (EmptyUtil.isNotEmpty(tagLogList)) {
                        List<PassiveTagRecognitionQuery> tagLogQueryList = tagLogList.stream().map(log -> {
                            PassiveTagRecognitionQuery tagQuery = new PassiveTagRecognitionQuery();
                            BeanUtil.copyProperties(log, tagQuery);
                            return tagQuery;
                        }).toList();
                        allTagLogQueryList.addAll(tagLogQueryList);
                    }
                }
                query.setPassiveTagLogList(allTagLogQueryList);
                if (EmptyUtil.isNotEmpty(allTagLogQueryList)) {
                    List<PassiveTagRecognitionQuery> list = allTagLogQueryList.stream().filter(f -> EmptyUtil.isNotEmpty(f.getErrorReason())).toList();
                    query.setErrorReasonList(list);
                }
            }
        });

        return page;
    }

    /**
     * 根据Id查询入库计划
     *
     * @param id ID
     * @return {@link AsnHeader }
     */
    public AsnHeader findById(Long id) {
        return dao.findById(id);
    }


    /**
     * 新增或更新入库计划信息
     *
     * @param asnHeaderItem
     * @return
     */
    public AsnHeaderItem save(AsnHeaderItem asnHeaderItem) {
        // 1.流水号(入库单号)，新增时，系统自动生成（ASN＋年月日＋6位，状态 = 创建，审核状态 = 未审核
        if (EmptyUtil.isEmpty(asnHeaderItem.getId()) || EmptyUtil.isEmpty(asnHeaderItem.getAsnNo())) {
            addAsnHeader(asnHeaderItem);
            // 如果是退货入库，则需要校验序列号是否存在
            checkSn(asnHeaderItem);
        }else {
            if (!AsnStatusEnum.ASN_NEW.getCode().equals(asnHeaderItem.getStatus()) || AuditStatusEnum.AUDITED.getCode().equals(asnHeaderItem.getAuditStatus())){
                throw new ServiceException(AsnExceptionEnum.STATUS_NOT_CREATE_REVIEWED,asnHeaderItem.getAsnNo());
            }
            AsnHeader byId = dao.findById(asnHeaderItem.getId());
            if (!ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(byId.getReceiveStatus())){
                throw new ServiceException(AsnExceptionEnum.RECEIVE_STATUS_NOT_RECEIVED);
            }
            asnHeaderItem.setStatus(byId.getStatus());
            asnHeaderItem.setAuditStatus(byId.getAuditStatus());
            asnHeaderItem.setAsnNo(byId.getAsnNo());
            asnHeaderItem.setOrderSource(byId.getOrderSource());
            asnHeaderItem.setReceiveStatus(byId.getReceiveStatus());
            asnHeaderItem.setIsMerge(byId.getIsMerge());
            asnHeaderItem.setMergeId(byId.getMergeId());
            asnHeaderItem.setMergeNo(byId.getMergeNo());
        }
        //订单信息关联
        orderRelation(asnHeaderItem);
        dao.saveOrUpdate(asnHeaderItem);
        mergeAsnDetail(asnHeaderItem);

        // 2.入库计划信息处理
        asnDetailService.saveAsn(asnHeaderItem.getAsnDetailItemList(), asnHeaderItem.getAsnNo());
        return asnHeaderItem;
    }

    /**
     *@Description 入库单保存订单关联
     *@Param asnHeaderItem
     *@Return Void
     *@Date 2025/5/16 14:55
     *<AUTHOR>
     **/
    private void orderRelation(AsnHeaderItem asnHeaderItem) {
        if(YesNoEnum.YES.getCode().equals(asnHeaderItem.getRelationOrder())){
            // 根据入库单号查询关联信息
            List<OrderRelation> orderRelationList = orderRelationService.findByBusinessNo(Arrays.asList(asnHeaderItem.getAsnNo()));
            if(CollectionUtil.isNotEmpty(orderRelationList)){
                //修改剩余量
                orderSkuService.batchUpdateRemainingAmount(Boolean.TRUE,orderRelationList);
                //根据入库单号删除关联信息
                orderRelationService.delByBusinessNo(Arrays.asList(asnHeaderItem.getAsnNo()));
            }

            //重新保存关联信息
            if(CollectionUtil.isNotEmpty(asnHeaderItem.getAsnDetailItemList())){
                List<OrderRelation> insertList = new ArrayList<>();
                asnHeaderItem.getAsnDetailItemList().forEach(x ->{
                    OrderRelation relation = new OrderRelation();
                    relation.setBusinessNo(asnHeaderItem.getAsnNo());
                    relation.setOrderSkuId(x.getOrderSkuId());
                    relation.setSkuId(x.getSkuId());
                    relation.setPackageId(x.getPackageId());
                    relation.setPackageUnitId(x.getPackageUnitId());
                    relation.setCurrentAmount(x.getQtyPlan());
                    relation.setOrderNo(asnHeaderItem.getpOrderNo());
                    orderRelationService.preSave(relation);
                    insertList.add(relation);
                });
                if(CollectionUtil.isNotEmpty(insertList)){
                    orderRelationService.batchInsert(insertList);
                    //修改剩余量
                    orderSkuService.batchUpdateRemainingAmount(Boolean.FALSE,insertList);
                }
                orderService.updateOrderStatus(asnHeaderItem.getpOrderNo(), OrderStatusEnum.PROCESSING.getCode());
            }
        }
    }


    /**
     * 合并入库单
     * @param asnHeaderItem
     */
    public void mergeAsnDetail(AsnHeaderItem asnHeaderItem){
        List<AsnDetailItem> asnDetailItemList = asnHeaderItem.getAsnDetailItemList();
        Map<AsnDetailIAttribute,List<AsnDetailItem>> map = new HashMap<>();
        asnDetailItemList.forEach(e->{
            AsnDetailIAttribute asnDetailIAttribute = new AsnDetailIAttribute();
            BeanUtil.copyProperties(e,asnDetailIAttribute);
            List<AsnDetailItem> orDefault = map.getOrDefault(asnDetailIAttribute, new ArrayList<>());
            orDefault.add(e);
            map.put(asnDetailIAttribute,orDefault);
        });
        List<AsnDetailItem> list = new ArrayList<>();
        for (AsnDetailIAttribute asnDetailIAttribute : map.keySet()) {
            List<AsnDetailItem> asnDetailItems = map.get(asnDetailIAttribute);
            AsnDetailItem asnDetailItem = asnDetailItems.getFirst();
            List<AsnDetailSnItem> asnDetailSns = asnDetailItem.getAsnDetailSns();
            if (CollUtil.isEmpty(asnDetailSns)){
                asnDetailSns = new ArrayList<>();
            }
            for (int i = 1; i < asnDetailItems.size(); i++) {
                asnDetailItem.setQtyPlanEa(asnDetailItem.getQtyPlanEa().add(asnDetailItems.get(i).getQtyPlanEa()));
                asnDetailItem.setQtyPlan(asnDetailItem.getQtyPlan().add(asnDetailItems.get(i).getQtyPlan()));
                List<AsnDetailSnItem> asnDetailSns1 = asnDetailItems.get(i).getAsnDetailSns();
                if (CollUtil.isNotEmpty(asnDetailSns1)) {
                    asnDetailSns.addAll(asnDetailSns1);
                    asnDetailItem.setAsnDetailSns(asnDetailSns);
                }
            }
            list.add(asnDetailItem);
        }
        asnHeaderItem.setAsnDetailItemList(list);
    }

    /**
     * 复制入库计划信息
     * @param asnHeaderItem
     * @return
     */
    public AsnHeaderItem copy(AsnHeaderItem asnHeaderItem) {
        // 1.流水号(入库单号)，新增时，系统自动生成（ASN＋年月日＋6位，状态 = 创建，审核状态 = 未审核
        asnHeaderItem.setId(null);
        addAsnHeader(asnHeaderItem);
        // 如果是退货入库，则需要校验序列号是否存在
        checkSn(asnHeaderItem);
        List<AsnDetailItem> asnDetailItemList = asnHeaderItem.getAsnDetailItemList();
        asnDetailItemList.stream().forEach(e -> {
            e.setId(null);
            e.setQtyRcvEa(null);
        });
        orderRelation(asnHeaderItem);
        mergeAsnDetail(asnHeaderItem);
        // 2.入库计划信息处理
        asnDetailService.saveAsn(asnHeaderItem.getAsnDetailItemList(),asnHeaderItem.getAsnNo());
        return asnHeaderItem;
    }

    /**
     * 校验sn码是否正确
     * @param asnHeaderItem
     */
    public void checkSn(AsnHeaderItem asnHeaderItem){
        List<AsnDetailSnItem> asnDetailSns = asnHeaderItem.getAsnDetailItemList().getFirst().getAsnDetailSns();
        if (EmptyUtil.isEmpty(asnDetailSns)){
            return;
        }
        List<AsnDetailSnQuery> asnDetailSnList = asnDetailSnDao.getSnBySoNo(asnDetailSns.stream().map(AsnDetailSnItem::getSnNo).toList());
        if (EmptyUtil.isNotEmpty(asnDetailSnList)) {
            if (!AsnTypeEnum.RTN.getCode().equals(asnHeaderItem.getAsnType())){
                throw new ServiceException(AsnExceptionEnum.ASN_RTN_SHIPPING);
            }
            Map<String, AsnDetailSnQuery> snMap = asnDetailSnList.stream().collect(Collectors.toMap(AsnDetailSnQuery::getSnNo, e -> e));
            asnDetailSns.forEach(e -> {
                AsnDetailSnQuery asnDetailSnQuery = MapUtil.get(snMap, e.getSnNo(), AsnDetailSnQuery.class);
                e.setId(asnDetailSnQuery.getId());
                e.setBoxCode(asnDetailSnQuery.getBoxCode());
            });
        }else {
            if (AsnTypeEnum.RTN.getCode().equals(asnHeaderItem.getAsnType())){
                throw new ServiceException(AsnExceptionEnum.ASN_RTN_SHIPPING_ONLY);
            }
        }
    }


    public void addAsnHeader(AsnHeaderItem asnHeaderItem) {
        // 1.流水号(入库单号)，新增时，系统自动生成（ASN＋年月日＋6位，状态 = 创建，审核状态 = 未审核
        String nextValue = numberGenerator.nextValue(IdRuleConstant.ASN_NO);
        asnHeaderItem.setAsnNo(nextValue);
        asnHeaderItem.setStatus(AsnStatusEnum.ASN_NEW.getCode());
        asnHeaderItem.setAuditStatus(AuditStatusEnum.UNREVIEWED.getCode());
        if (EmptyUtil.isEmpty(asnHeaderItem.getOrderSource())) {
            asnHeaderItem.setOrderSource(AsnOrderSourceEnum.MANUAL_CREATE.getCode());
        }
        asnHeaderItem.setReceiveStatus(ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode());
        asnHeaderItem.setIsMerge(YesNoEnum.NO.getCode());
        if (EmptyUtil.isNotEmpty(asnHeaderItem.getAsnNo())) {
            // 预到货通知单附件表
            csAsnHeaderFileService.save(asnHeaderItem.getAsnNo());
        }
        dao.saveOrUpdate(asnHeaderItem);

        //异步任务
        taskExecutor.execute(() -> {
            //收货单集合
            List<LogisticsFolderDetailLinkCondition> asnOrderCondition  = new ArrayList<>();
            asnOrderCondition.add(new LogisticsFolderDetailLinkCondition().setUpDocumentNo(null).setDocumentNos(List.of(nextValue)));

            logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                    DocumentTypeEnum.ASN_ORDER,
                    asnOrderCondition,
                    SessionContext.getSessionUserInfo().getCompanyId(),
                    SessionContext.getSessionUserInfo().getTenancy(),asnHeaderItem.getWarehouseId());
            //这⾥第三个参数，为上级的单号，这⾥需替换成采购单号号码
            List<LogisticsFolderDetailLinkCondition> asnCondition  = new ArrayList<>();
            asnCondition.add(new LogisticsFolderDetailLinkCondition().setUpDocumentNo(asnHeaderItem.getpOrderNo()).setDocumentNos(List.of(nextValue)));
            logisticsFileAutoGenerateService.batchGenerateLogisticsFiles(
                    DocumentTypeEnum.ASN,
                    asnCondition,
                    SessionContext.getSessionUserInfo().getCompanyId(),
                    SessionContext.getSessionUserInfo().getTenancy(),asnHeaderItem.getWarehouseId());
        });
    }


    /**
     * 根据Ids批量删除入库计划信息
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(Long[] ids) {
        if (EmptyUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.DELETE_FAILED);
        }
        dao.delete(ids);
        // 删除入库详情
        asnDetailService.deleteByAsnIds(ids);
        // 删除收货信息
        asnReceiveService.deleteByAsnIds(ids);
    }


    /**
     * 检查ASN状态由ID集合
     *
     * @param ids         ID集合
     * @param operateType 操作类型
     */
    public void checkAsnStatusByIds(Long[] ids, String operateType) {
        if (EmptyUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        // 查询全部入库订单
        List<AsnHeader> asnHeaderList = this.findByIds(ids);
        String[] asnNos = asnHeaderList.stream().map(AsnHeader::getAsnNo)
                .toArray(String[]::new);
        // 查询全部入库订单状态
        AsnDetailCondition asnDetailCondition = new AsnDetailCondition();
        asnDetailCondition.setAsnNos(asnNos);
        List<AsnDetail> detailListByAsnNos = asnDetailService.findByCondition(asnDetailCondition);
        if (EmptyUtil.isEmpty(detailListByAsnNos)) {
            throw new ServiceException(AsnExceptionEnum.DETAIL_NOT_EMPTY);
        }
        Map<String, List<AsnDetail>> asnIdGroupDetailList =
                detailListByAsnNos.stream().collect(Collectors.groupingBy(AsnDetail::getAsnNo));

        StringBuilder errorMsg = new StringBuilder();
        StringBuilder auditAsnNo = new StringBuilder();
        StringBuilder cancelAuditAsnNo = new StringBuilder();
        StringBuilder detailsNullAsnNo = new StringBuilder();
        StringBuilder orderAsnNo = new StringBuilder();

        for (AsnHeader asnHeader : asnHeaderList) {
            if (asnHeader == null) {
                continue;
            }
            // 订单状态
            String status = asnHeader.getStatus();
            // 审核状态
            String auditStatus = asnHeader.getAuditStatus();
            // 入库单号
            String asnNo = asnHeader.getAsnNo();

                // 审核 -> 只有订单状态为创建且审核状态为未审核的订单才能执行
                if (AsnOperateTypeEnum.ASN_AUDIT.name().equals(operateType)) {
                    // 校验订单各种状态
                    if (!(AsnStatusEnum.ASN_NEW.getCode().equals(status)
                            && AuditStatusEnum.UNREVIEWED.getCode().equals(auditStatus))
                    ) {
                        auditAsnNo.append(asnNo).append("、");
                    }
                }

                // 取消审核 -> 只有订单状态创建且审核状态为已审核的订单才能执行
                if (AsnOperateTypeEnum.ASN_CANCEL_AUDIT.name().equals(operateType)) {
                    // 查询收货状态
                    String receivedStatus;
                    List<AsnDetail> detailList = asnIdGroupDetailList.get(asnNo);
                    if (EmptyUtil.isEmpty(detailList)) {
                        detailsNullAsnNo.append(asnNo).append("、");
                    }
                    // 入库商品明细全部完全收货 -> 入库单完全收货
                    boolean isExistAllReceipt =
                            detailList.stream().allMatch(detail -> ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode().equals(detail.getStatus()));
                    // 入库商品明细全部未收货 -> 入库单未收货
                    boolean isExistNotReceipt =
                            detailList.stream().allMatch(detail ->
                                    ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(detail.getStatus()));
                    // 收货状态
                    receivedStatus = isExistAllReceipt ?
                            ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode() :
                            isExistNotReceipt ?
                                    ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode() :
                                    ReceiveStatusEnum.PART_RECEIVED_GOODS.getCode();

                    // 校验订单各种状态
                    if (!(AsnStatusEnum.ASN_NEW.getCode().equals(status)
                            && AuditStatusEnum.AUDITED.getCode().equals(auditStatus)
                            && ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(receivedStatus)
                            // TODO && 未上架状态后续补充
                    )
                    ) {
                        cancelAuditAsnNo.append(asnNo).append("、");
                    }
                }

                // 取消订单/完结订单 -> 只有订单状态为(创建或部分收货或完全收货)且上架状态不为完全上架的订单才能执行
                if (AsnOperateTypeEnum.ASN_CANCEL.name().equals(operateType) || AsnOperateTypeEnum.ASN_CLOSE.name().equals(operateType)) {
                    // 校验订单各种状态
                    if (!((AsnStatusEnum.ASN_NEW.getCode().equals(status)
                            || ReceiveStatusEnum.PART_RECEIVED_GOODS.getCode().equals(status)
                            || ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode().equals(status))
                            // TODO && !完全上架状态后续补充
                    )) {
                        orderAsnNo.append(asnNo).append("、");
                    }
                }
            }

        // 判断是否有不满足操作状态的订单提示语
        if (EmptyUtil.isNotEmpty(orderAsnNo)
                || EmptyUtil.isNotEmpty(detailsNullAsnNo)
                || EmptyUtil.isNotEmpty(orderAsnNo)
                || EmptyUtil.isNotEmpty(cancelAuditAsnNo)) {
            splicingErrorMsg(errorMsg, auditAsnNo, detailsNullAsnNo,
                    cancelAuditAsnNo, orderAsnNo);
            throw new ServiceException(errorMsg.toString());
        }

        //验证是否存在合并收获数据,且对合并收获数据的收货状态进行验证
        List<AsnHeader> mergeDateList = asnHeaderList.stream().filter(f -> YesNoEnum.YES.getCode().equals(f.getIsMerge())).toList();
        if (EmptyUtil.isNotEmpty(mergeDateList) && AsnOperateTypeEnum.ASN_CANCEL_AUDIT.name().equals(operateType)) {
            //如果状态是部分收货/完全收货，不能取消审核。提示“订单收货任务{合并收货单号}正在进行中，无法取消审核”
            Map<Long, List<AsnHeader>> listMap = mergeDateList.stream().collect(Collectors.groupingBy(AsnHeader::getMergeId));
            List<AsnMergeReceive> receives = asnMergeReceiveDao.findByIds(listMap.keySet().toArray(Long[]::new));
            Set<String> errorAsnNoSet = new LinkedHashSet<>();
            if (EmptyUtil.isNotEmpty(receives)) {
                List<AsnMergeReceive> list = receives.stream().filter(f -> !ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(f.getStatus())).toList();
                if (EmptyUtil.isNotEmpty(list)) {
                    for (AsnMergeReceive receive : list) {
                        List<AsnHeader> headers = listMap.get(receive.getId());
                        errorAsnNoSet.addAll(headers.stream().map(AsnHeader::getAsnNo).toList());
                    }
                }
            }
            if (EmptyUtil.isNotEmpty(errorAsnNoSet)) {
                throw new ServiceException(AsnExceptionEnum.MERGE_ORDER_TASK_JXZ_ERROR,errorAsnNoSet.toString());
            }else{
                //可以取消任务,发布订阅消息进行异步更改
                List<AsnDetail> asnDetails = asnDetailService.find(new ConditionRule().andIn(AsnDetail::getAsnNo, mergeDateList.stream().map(AsnHeader::getAsnNo).toList()));
                Map<String, List<AsnDetail>> map = asnDetails.stream().collect(Collectors.groupingBy(AsnDetail::getAsnNo));
                receives.forEach(item -> {
                    List<AsnHeader> headerList = listMap.get(item.getId());
                    List<AsnDetail> detailList = new ArrayList<>();
                    headerList.forEach(header -> {
                        detailList.addAll(map.get(header.getAsnNo()));
                    });
                    item.setOrderCount(item.getOrderCount() - 1);
                    BigDecimal qtyPlanEa = detailList.stream().map(AsnDetail::getQtyPlanEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    item.setQtyPlanEa(item.getQtyPlanEa().subtract(qtyPlanEa));
                    asnServiceEventBusPush.updateMergeDate(item);
                });
            }
        }
    }

    /**
     * 拼接错误提示语
     *
     * @param errorMsg         错误 msg
     * @param auditAsnNo       审核ASN编号
     * @param detailsNullAsnNo 详零ASN编号
     * @param cancelAuditAsnNo 取消审核ASN编号
     * @param orderAsnNo       次序ASN编号
     */
    private void splicingErrorMsg(StringBuilder errorMsg, StringBuilder auditAsnNo, StringBuilder detailsNullAsnNo, StringBuilder cancelAuditAsnNo, StringBuilder orderAsnNo) {
        if (EmptyUtil.isNotEmpty(auditAsnNo)) {
            int length = auditAsnNo.length();
            auditAsnNo.replace(length - 1, length, "");
            errorMsg.append(String.format(AsnExceptionEnum.STATUS_NOT_CREATE_REVIEWED.getMsg(), auditAsnNo));
        }
        if (EmptyUtil.isNotEmpty(detailsNullAsnNo)) {
            int length = detailsNullAsnNo.length();
            detailsNullAsnNo.replace(length - 1, length, "");
            errorMsg.append(String.format(AsnExceptionEnum.DETAIL_EMPTY.getMsg(), detailsNullAsnNo));
        }
        if (EmptyUtil.isNotEmpty(cancelAuditAsnNo)) {
            int length = cancelAuditAsnNo.length();
            cancelAuditAsnNo.replace(length - 1, length, "");
            errorMsg.append(String.format(AsnExceptionEnum.STATUS_NOT_CREATE_UNREVIEWED.getMsg(), cancelAuditAsnNo));
        }
        if (EmptyUtil.isNotEmpty(orderAsnNo)) {
            int length = orderAsnNo.length();
            orderAsnNo.replace(length - 1, length, "");
            errorMsg.append(String.format(AsnExceptionEnum.STATUS_NOT_CREATE_PART_ALL_REVIEWED.getMsg(), orderAsnNo));
        }
    }


    /**
     * 取消审核
     *
     * @param ids ID集合
     */
    public void cancelAudit(Long[] ids) {
        // 更新审核状态为未审核，并且置空审核时间、审核人
        AsnHeaderAuditStatusUpdateItem updateItem = new AsnHeaderAuditStatusUpdateItem(AuditStatusEnum.UNREVIEWED.getCode());
        updateItem.setAuditTime(null);
        updateItem.setAuditOp(null);
        updateItem.setMergeNo(null);
        updateItem.setMergeId(null);
        updateItem.setIsMerge(YesNoEnum.NO.getCode());
        boolean b = this.updateAuditStatusByIds(ids, updateItem);
        if (!b) {
            throw new ServiceException(AsnExceptionEnum.CANCEL_AUDIT_ERROR);
        }
        //执行删除日志
        List<AsnHeader> headers = findByIds(ids);
        List<String> asnNoList = headers.stream().map(AsnHeader::getAsnNo).toList();
        List<AsnDetail> asnDetails = asnDetailService.find(new ConditionRule().andIn(AsnDetail::getAsnNo, asnNoList));
        Map<String, List<AsnDetail>> listMap = asnDetails.stream().collect(Collectors.groupingBy(AsnDetail::getAsnNo));
        headers.forEach(header -> traceBackRecordService.executeDel(new TraceBackRecordItem().setBusinessNo(header.getAsnNo())
                .setBusinessType(TraceBackBusinessTypeEnum.INBOUND_CREATE)
                .setSkuCodeList(listMap.get(header.getAsnNo()).stream().map(AsnDetail::getSkuCode).toList())));

    }

    /**
     * 更新审核状态由ID集合
     *
     * @param ids        ID集合
     * @param updateItem 更新项
     * @return boolean
     */
    public boolean updateAuditStatusByIds(Long[] ids,
                                          AsnHeaderAuditStatusUpdateItem updateItem) {
        updateItem.setIsMerge(YesNoEnum.NO.getCode());
        return dao.updateByIds(updateItem, ids) > 0;
    }

    /**
     * 取消订单由ID集合
     *
     * @param ids ID集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelByIds(Long[] ids) {
        if (EmptyUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        // 更新收货商品是否货架
        updateReceiveWhetherShelf(ids);
        // 更新入库单状态
        this.updateAsnStatusByIds(ids, AsnStatusEnum.ASN_CANCEL.getCode());
        // 删除sn码
        this.deleteAsnDetailSnByAsnIds(ids);

        // 取消订单上游单号
        List<AsnHeader> asnHeaderList = this.findByIds(ids);
        List<String> logisticNoList = asnHeaderList.stream().map(AsnHeader::getLogisticNo).filter(EmptyUtil::isNotEmpty).distinct().toList();
        if (EmptyUtil.isNotEmpty(logisticNoList)) {
            StatusUpdateCondition statusUpdateCondition = new StatusUpdateCondition();
            statusUpdateCondition.setCode(logisticNoList);
            statusUpdateCondition.setOperationType(OperationTypeEnum.CANCEL.getCode());
            ResponseData<Boolean> booleanResponseData = remoteAsnStatusPushToCrzFegin.updateAsnStatus(statusUpdateCondition);
            if (EmptyUtil.isNotEmpty(booleanResponseData) && !booleanResponseData.getSuccess()) {
                throw new ServiceException("上游" + booleanResponseData.getMsg());
            }
        }
        //采购单相关数据回退
        callbackOrderRelation(ids);
    }

    /**
     *@Description 采购单相关数据回退
     *@Param ids
     *@Return Void
     *@Date 2025/5/14 17:03
     *<AUTHOR>
     **/
    private void callbackOrderRelation(Long[] ids) {
        List<AsnHeader> asnHeaderList = dao.findByIds(ids);
        List<AsnHeader> orderRelationHeadList = asnHeaderList.stream().filter(e -> YesNoEnum.YES.getCode().equals(e.getRelationOrder())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(orderRelationHeadList)){
            //抽取入库单号
            List<String> businessNoList = orderRelationHeadList.stream().map(AsnHeader::getAsnNo).distinct().collect(Collectors.toList());
            //根据入库单号查询对应关联商品信息
            List<OrderRelation> orderRelationList = orderRelationService.findByBusinessNo(businessNoList);
            Map<String, List<OrderRelation>> orderRelationMap = orderRelationList.stream().collect(Collectors.groupingBy(OrderRelation::getBusinessNo));
            List<Long> orderSkuIdList = orderRelationList.stream().map(OrderRelation::getOrderSkuId).distinct().collect(Collectors.toList());
            List<OrderSku> orderSkuList = orderSkuService.findByIds(orderSkuIdList.toArray(new Long[0]));
            if(CollectionUtil.isNotEmpty(orderSkuList)){
                List<PackageUnit> packageUnitList = packageUnitService.findByIds(orderSkuList.stream().map(OrderSku::getPackageUnitId).distinct().collect(Collectors.toList()).toArray(new Long[0]));
                Map<Long, PackageUnit> packageUnitMap = new HashMap<>();
                if(CollectionUtil.isNotEmpty(packageUnitList)){
                    packageUnitMap = packageUnitList.stream().collect(Collectors.toMap(PackageUnit::getId, Function.identity(),(v1, v2) -> v2));
                }
                Map<Long, OrderSku> orderSkuMap = orderSkuList.stream().collect(Collectors.toMap(OrderSku::getId, Function.identity(),(v1, v2) -> v2));
                Map<Long, PackageUnit> finalPackageUnitMap = packageUnitMap;
                List<OrderSku> updateList = new ArrayList<>();
                orderRelationMap.forEach((businessNo, currentOrderRelationList) -> {
                    if(CollectionUtil.isNotEmpty(currentOrderRelationList)){
                        currentOrderRelationList.forEach(x -> {
                            OrderSku orderSku = orderSkuMap.get(x.getOrderSkuId());
                            orderSku.setRemainCount(orderSku.getRemainCount().add(x.getCurrentAmount()));
                            PackageUnit packageUnit = finalPackageUnitMap.get(orderSku.getPackageUnitId());
                            if(ObjectUtil.isNotNull(packageUnit)){
                                if(ObjectUtil.isNotNull(packageUnit.getMinQuantity())){
                                    BigDecimal minQuantity = new BigDecimal(packageUnit.getMinQuantity());
                                    orderSku.setRemainCountEa(orderSku.getRemainCount().multiply(minQuantity).setScale(2,BigDecimal.ROUND_HALF_UP));
                                }
                            }
                            orderSkuService.preSave(orderSku);
                            updateList.add(orderSku);
                        });
                    }
                });
                if(CollectionUtil.isNotEmpty(updateList)){
                    orderSkuService.batchUpdate(updateList);
                    //抽取订单号
                    List<String> orderNoList = updateList.stream().map(OrderSku::getOrderNo).distinct().collect(Collectors.toList());
                    List<Order> orderList = orderService.findByOrderNoList(orderNoList);
                    List<Order> updateOrderList = new ArrayList<>();
                    //根据订单号查询所有商品信息
                    List<OrderSku> allOrderSkuList = orderSkuService.listByOrderNoList(orderNoList);
                    //根据编号分组
                    Map<String, List<OrderSku>> allOrderSkuMap = allOrderSkuList.stream().collect(Collectors.groupingBy(OrderSku::getOrderNo));
                    orderList.forEach(x ->{
                        List<OrderSku> currentOrderSkuList = allOrderSkuMap.get(x.getOrderNo());
                        if(CollectionUtil.isNotEmpty(currentOrderSkuList)){
                            BigDecimal totalCount = currentOrderSkuList.stream().map(OrderSku::getTotalCount).reduce(BigDecimal.ZERO,BigDecimal::add);
                            BigDecimal remainCount = currentOrderSkuList.stream().map(OrderSku::getRemainCount).reduce(BigDecimal.ZERO,BigDecimal::add);
                            //总数大于 0 且 和可用数一致
                            String code;
                            if (totalCount.compareTo(BigDecimal.ZERO) > 0 && totalCount.compareTo(remainCount) == 0){
                                code = OrderStatusEnum.CREATE.getCode();
                            }else{
                                code = OrderStatusEnum.PROCESSING.getCode();
                            }
                            x.setOrderStatus(code);
                            orderService.preSave(x);
                            updateOrderList.add(x);
                        }
                    });

                    if(CollectionUtil.isNotEmpty(updateOrderList)){
                        orderService.batchUpdate(updateOrderList);
                    }
                }
            }
            asnHeaderList.forEach(x ->{
                x.setRelationOrder(YesNoEnum.NO.getCode());
                x.setPOrderNo(null);
                preSave(x);
            });
            batchUpdate(asnHeaderList);
            //删除关联信息
            orderRelationService.delByBusinessNo(businessNoList);
        }
    }

    public void deleteAsnDetailSnByAsnIds(Long[] ids) {
        List<AsnHeader> asnHeaderList = dao.findByIds(ids);
        Map<Boolean, List<AsnHeader>> partitionedMap = asnHeaderList.stream()
                .collect(Collectors.partitioningBy(e ->
                        AsnTypeEnum.RTN.getCode().equals(e.getAsnType())
                ));
        for (boolean x : partitionedMap.keySet()) {
            List<AsnHeader> asnHeaders = partitionedMap.get(x);
            this.editSn(asnHeaders,x);
        }
    }

    private void editSn(List<AsnHeader> asnHeaderList,Boolean del){
        if (EmptyUtil.isNotEmpty(asnHeaderList)){
            List<String> soNos = asnHeaderList.stream().map(AsnHeader::getAsnNo).toList();
            List<AsnDetailSnQuery> snBySoNo = asnDetailSnDao.getAsnDetailSnByAsnIds(soNos);
            Long[] idList = snBySoNo.stream()
                    .filter(e-> e.getReceiveStatus() == null ||ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(e.getReceiveStatus()))
                    .map(AsnDetailSnQuery::getId).toArray(Long[]::new);
            if (EmptyUtil.isEmpty(idList)){
                return;
            }
            if (del){
                BaseStatusItem statusItem = new BaseStatusItem(SnStatusEnum.SHIPPING.getCode());
                asnDetailSnDao.updateByIds(statusItem, idList);
            }else {
                asnDetailSnDao.delete(idList);
            }
        }
    }

    /**
     * 关闭订单（完结订单）由ID集合
     *
     * @param ids ID集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void closeByIds(Long[] ids) {
        if (EmptyUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        List<AsnHeader> asnHeaderList = this.findByIds(ids);
        // 更新收货商品是否货架
        updateReceiveWhetherShelf(ids);
        // 更新入库单状态
        this.updateAsnStatusByIds(ids, AsnStatusEnum.ASN_CLOSE.getCode());
        // 删除sn码
        this.deleteAsnDetailSnByAsnIds(ids);

        // 关闭订单上游单号
        List<String> logisticNoList = asnHeaderList.stream().map(AsnHeader::getLogisticNo).filter(EmptyUtil::isNotEmpty).distinct().toList();
        if (EmptyUtil.isNotEmpty(logisticNoList)) {
            StatusUpdateCondition statusUpdateCondition = new StatusUpdateCondition();
            statusUpdateCondition.setCode(logisticNoList);
            statusUpdateCondition.setOperationType(OperationTypeEnum.CLOSE.getCode());
            ResponseData<Boolean> booleanResponseData = remoteAsnStatusPushToCrzFegin.updateAsnStatus(statusUpdateCondition);
            if (EmptyUtil.isNotEmpty(booleanResponseData) && !booleanResponseData.getSuccess()) {
                throw new ServiceException("上游" + booleanResponseData.getMsg());
            }
        }
        //更新关联订单状态及
        modifyRelationOrderStatus(ids);

    }

    /**
     *@Description 更新关联订单状态
     *@Param ids
     *@Return Void
     *@Date 2025/5/16 15:13
     *<AUTHOR>
     **/
    public void modifyRelationOrderStatus(Long[] ids) {
        List<AsnHeader> asnHeaderList = dao.findByIds(ids);
        List<AsnHeader> relationAsnHeaderList = asnHeaderList.stream().filter(x -> YesNoEnum.YES.getCode().equals(x.getRelationOrder())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(relationAsnHeaderList)){
            List<String> businessNoList = relationAsnHeaderList.stream().map(AsnHeader::getAsnNo).distinct().collect(Collectors.toList());
            // 根据入库单号查询关联信息
            List<OrderRelation> orderRelationList = orderRelationService.findByBusinessNo(businessNoList);
            if(CollectionUtil.isNotEmpty(orderRelationList)){
                //抽取订单号
                List<String> orderNoList = orderRelationList.stream().map(OrderRelation::getOrderNo).distinct().collect(Collectors.toList());
                //根据订单号集合查询订单对应的所有的关联数据
                List<OrderRelation> allOrderRelationList = orderRelationService.findByOrderNoList(orderNoList);
                //转map
                Map<String,List<OrderRelation>> allOrderRelationMap = allOrderRelationList.stream().collect(Collectors.groupingBy(OrderRelation::getOrderNo));
                //抽取入库单号
                List<String> asnNoList = allOrderRelationList.stream().map(OrderRelation::getBusinessNo).distinct().collect(Collectors.toList());
                //查询入库单
                List<AsnHeader> allAsnHeaderList = dao.find(ConditionRule.getInstance().andIn(AsnHeader::getAsnNo,asnNoList));
                Map<String,AsnHeader> allSnHeadMap = allAsnHeaderList.stream().collect(Collectors.toMap(AsnHeader::getAsnNo,x->x));
                //查询入库明细
                List<AsnDetail> asnDetailList = asnDetailService.findByAsnNoList(asnNoList);
                //根据采购单商品明细ID进行分组
                Map<Long,List<AsnDetail>> groupByOrderSkuId = asnDetailList.stream().collect(Collectors.groupingBy(AsnDetail::getOrderSkuId));
                //查询订单
                List<Order> allOrderList = orderService.findByOrderNoList(orderNoList);
                Map<String,Order> allOrderMap = allOrderList.stream().collect(Collectors.toMap(Order::getOrderNo,x->x));
                //查询订单关联商品
                List<OrderSku> orderSkuList = orderSkuService.listByOrderNoList(orderNoList);
                Map<String,List<OrderSku>> orderSkuMap = orderSkuList.stream().collect(Collectors.groupingBy(OrderSku::getOrderNo));
                List<Long> packageUnitIdList = orderSkuList.stream().map(OrderSku::getPackageUnitId).distinct().collect(Collectors.toList());
                List<PackageUnit> packageUnitList = packageUnitService.findByIds(packageUnitIdList.toArray(new Long[]{}));
                Map<Long,PackageUnit> packageUnitMap = packageUnitList.stream().collect(Collectors.toMap(PackageUnit::getId,Function.identity(),(o1, o2) -> o1));
                List<Order> updateList = new ArrayList<>();
                List<OrderSku> updateOrderSkuList = new ArrayList<>();
                orderSkuMap.forEach((orderNo,currentOrderSkuList) ->{
                    List<OrderRelation> currentOrderRelationList = allOrderRelationMap.get(orderNo);
                    if(CollectionUtil.isNotEmpty(currentOrderRelationList)){
                        List<String> currentAsnNoList = currentOrderRelationList.stream().map(OrderRelation::getBusinessNo).distinct().collect(Collectors.toList());
                        List<AsnHeader> currentAsnHeaderList = allSnHeadMap.values().stream().filter(x -> currentAsnNoList.contains(x.getAsnNo())).distinct().collect(Collectors.toList());
                        boolean asnFlag = false;
                        //入库单是否全部订单完结
                        if(CollectionUtil.isNotEmpty(currentAsnHeaderList)){
                            asnFlag = currentAsnHeaderList.stream().allMatch(x -> AsnStatusEnum.ASN_CLOSE.getCode().equals(x.getStatus()) );
                        }
                        boolean osFlag = false;
                        if(CollectionUtil.isNotEmpty(currentOrderSkuList)){
                            currentOrderSkuList.forEach(x -> {
                                List<AsnDetail> currentAsnDetailList = groupByOrderSkuId.get(x.getId());
                                if(CollectionUtil.isNotEmpty(currentAsnDetailList)){
                                    //去除收货数为null的数据
                                    BigDecimal addAmount = currentAsnDetailList.stream().filter(detail -> businessNoList.contains(detail.getAsnNo()))
                                            .map(details -> details.getQtyPlanEa().subtract(ObjectUtil.isNull(details.getQtyRcvEa())?BigDecimal.ZERO:details.getQtyRcvEa()))
                                            .reduce(BigDecimal.ZERO,BigDecimal::add);
                                    if(addAmount.compareTo(BigDecimal.ZERO) > 0){
                                        x.setRemainCountEa(x.getRemainCountEa().add(addAmount));
                                        PackageUnit packageUnit = packageUnitMap.get(x.getPackageUnitId());
                                        if(ObjectUtil.isNotNull(packageUnit) && ObjectUtil.isNotNull(packageUnit.getMinQuantity())){
                                            x.setRemainCount(x.getRemainCountEa().divide(new BigDecimal(packageUnit.getMinQuantity()),2,RoundingMode.HALF_UP));
                                        }
                                        orderSkuService.preSave(x);
                                        updateOrderSkuList.add(x);
                                    }
                                }
                            });
                            //采购单对应商品的剩余量是否都为0
                            osFlag = currentOrderSkuList.stream().allMatch(x -> x.getRemainCount().compareTo(BigDecimal.ZERO) == 0);

                        }
                        Order order = allOrderMap.get(orderNo);
                        if(asnFlag && osFlag){
                            //将采购单状态修改为已完成
                            order.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
                            orderService.preSave(order);
                            updateList.add(order);
                        }
                    }
                });
                if(CollectionUtil.isNotEmpty(updateOrderSkuList)){
                    orderSkuService.batchUpdate(updateOrderSkuList);
                }
                if(CollectionUtil.isNotEmpty(updateList)){
                    orderService.batchUpdate(updateList);
                }
            }
        }
    }

    /**
     * 更新入库单状态根据ID集合
     *
     * @param ids       ID集合
     * @param asnStatus ASN状态
     */
    public void updateAsnStatusByIds(Long[] ids, String asnStatus) {
        BaseStatusItem statusItem = new BaseStatusItem(asnStatus);
        dao.updateByIds(statusItem, ids);
    }

    /**
     * 更新收货商品是否货架
     *
     * @param ids ID集合
     */
    public void updateReceiveWhetherShelf(Long[] ids) {
        List<AsnHeader> asnHeaderList = this.findByIds(ids);
        String[] asnNos = asnHeaderList.stream().map(AsnHeader::getAsnNo).toArray(String[]::new);
        //根据asnNo获取商品明细
        AsnReceiveCondition condition = new AsnReceiveCondition();
        condition.setAsnCodes(asnNos);
        List<AsnReceive> receiveList = asnReceiveService.findByCondition(condition);
        List<Long> receiveIdList = new ArrayList<>();
        for (AsnReceive receive : receiveList) {
            String receiveStatus = receive.getReceiveStatus();
            if (ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(receiveStatus)) {
                receiveIdList.add(receive.getId());
            }
        }
        // 是否上架
        AsnReceiveWhetherShelfUpdateItem item = new AsnReceiveWhetherShelfUpdateItem();
        item.setWhetherShelf(YesNoEnum.NO.getName());
        asnReceiveService.updateByIds(item, receiveIdList.toArray(new Long[0]));
    }

    /**
     * 审核由ID集合
     *
     * @param ids ID集合
     */
    public void auditByIds(Long[] ids) {
        if (EmptyUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        List<AsnHeader> asnHeaderList = this.findByIds(ids);
        String[] asnNos = asnHeaderList.stream().map(AsnHeader::getAsnNo).toArray(String[]::new);
        //根据asnNo获取商品明细
        AsnDetailCondition condition = new AsnDetailCondition();
        condition.setAsnNos(asnNos);
        List<AsnDetail> asnDetails = asnDetailService.findByCondition(condition);

        if (EmptyUtil.isNotEmpty(asnDetails)) {
            // 更新审核状态为审核，并且置空审核时间、审核人
            AsnHeaderAuditStatusUpdateItem updateItem =
                    new AsnHeaderAuditStatusUpdateItem(AuditStatusEnum.AUDITED.getCode());
            updateItem.setAuditTime(new Date());
            if (EmptyUtil.isEmpty(SessionContext.getSessionUserInfo())) {
                updateItem.setAuditOp("-1");
            } else {
                updateItem.setAuditOp(SessionContext.getSessionUserInfo().getUserId().toString());
            }
            this.updateAuditStatusByIds(ids, updateItem);
            this.saveRecordLogs(asnDetails, asnHeaderList);
        } else {
            throw new ServiceException(AsnExceptionEnum.DETAIL_NOT_EMPTY);
        }

        // 审核上游单号
        List<String> logisticNoList = asnHeaderList.stream().map(AsnHeader::getLogisticNo).filter(EmptyUtil::isNotEmpty).distinct().toList();
        if (EmptyUtil.isNotEmpty(logisticNoList)) {
            StatusUpdateCondition statusUpdateCondition = new StatusUpdateCondition();
            statusUpdateCondition.setCode(logisticNoList);
            statusUpdateCondition.setOperationType(OperationTypeEnum.EXAMINE.getCode());
            ResponseData<Boolean> booleanResponseData = remoteAsnStatusPushToCrzFegin.updateAsnStatus(statusUpdateCondition);
            if (EmptyUtil.isNotEmpty(booleanResponseData) && !booleanResponseData.getSuccess()) {
                throw new ServiceException("上游" + booleanResponseData.getMsg());
            }
        }
    }

    /**
     * 记录序列号操作日志
     * @param asnDetails
     * @param asnHeaderList
     */
    private void saveRecordLogs(List<AsnDetail> asnDetails, List<AsnHeader> asnHeaderList) {
        //循环执行 asnHeaderList 数据
        Map<String, List<AsnDetail>> detailMapToAsnNo = asnDetails.stream().collect(Collectors.groupingBy(AsnDetail::getAsnNo));
        List<Long> ownerIds = asnHeaderList.stream().map(AsnHeader::getOwnerId).toList();
        List<Long> warehouseIds = asnHeaderList.stream().map(AsnHeader::getWarehouseId).toList();
        List<Owner> ownerList = ownerService.findByIds(ownerIds.toArray(Long[]::new));
        List<Warehouse> warehouses = warehouseDao.findByIds(warehouseIds.toArray(Long[]::new));
        Map<Long, Owner> ownerMap = ownerList.stream().collect(Collectors.toMap(ModuleBaseModel::getId, Function.identity()));
        Map<Long, Warehouse> warehousesMap = warehouses.stream().collect(Collectors.toMap(ModuleBaseModel::getId, Function.identity()));
        for (AsnHeader asnHeader : asnHeaderList) {
            List<AsnDetail> detailList = detailMapToAsnNo.get(asnHeader.getAsnNo());
            if (CollUtil.isEmpty(detailList)){
                continue;
            }
            List<Long> detailIdList = detailList.stream().map(ModuleBaseModel::getId).toList();
            List<AsnDetailSn> detailSns = asnDetailSnDao.find(new ConditionRule().andIn(AsnDetailSn::getAsnDetailId, detailIdList));
            if (CollUtil.isEmpty(detailSns)){
                continue;
            }
            Map<Long, List<AsnDetailSn>> snGroyupByDetailIdMap = detailSns.stream().collect(Collectors.groupingBy(AsnDetailSn::getAsnDetailId));
            //要求每一个不同商品执行一次数据
            for (AsnDetail asnDetail : detailList) {
                List<String> asnDetailSns = snGroyupByDetailIdMap.get(asnDetail.getId()).stream().map(AsnDetailSn::getSnNo).toList();
                Owner owner = ownerMap.get(asnHeader.getOwnerId());
                Warehouse warehouse = warehousesMap.get(asnHeader.getWarehouseId());
                traceBackRecordService.execute(new TraceBackRecordItem()
                        .setBusinessNo(asnHeader.getAsnNo())
                        .setOwnerName(ObjUtil.isEmpty(owner) ? null : owner.getOwnerName())
                        .setWarehouseName(ObjUtil.isEmpty(warehouse) ? null : warehouse.getWarehouseName())
                        .setSourceNo(asnHeader.getAsnNo())
                        .setBusinessType(TraceBackBusinessTypeEnum.INBOUND_CREATE)
                        .setSnNoList(asnDetailSns)
                );
            }
        }
    }

    /**
     * 取消审核订单,保存预到货通知单信息
     *
     * @param asnHeader
     */
    public void save(AsnHeader asnHeader) {
        dao.update(asnHeader);
    }

    /**
     * 根据lotId获取导出数据
     *
     * @param id
     * @return
     */
    public List<Map<String, Object>> exportHandleRrece(Long id) {
        WarehouseLotDetailCondition condition = new WarehouseLotDetailCondition();
        condition.setLotId(id);
        return warehouseLotDetailDao.findLotDetailByLotId(condition);
    }

    /**
     * 导入处理
     *
     * @param excelItemList
     * @return
     */
    public ResponseData<AsnHeader> excelDataHandler(List<AsnHeaderExcelItem> excelItemList) {
        ResponseData<AsnHeader> responseData = new ResponseData<>();
        //定义插入数据集合
        List<AsnHeader> anHeaderList = new ArrayList<>();
        if (null == excelItemList || excelItemList.size() == 0) {
            return ResponseData.error(MsgConstant.ASN_DATE_NOT_NULL);
        }

        for (AsnHeaderExcelItem excelItem : excelItemList) {
            AsnHeader asnHeader = new AsnHeader();
            if (EmptyUtil.isEmpty(excelItem.getAsnType())) {
                return ResponseData.error(MsgConstant.ASN_TYPE_NOT_NULL, excelItem.getRowNum());
            }
            if (EmptyUtil.isEmpty(excelItem.getOwnerName())) {
                return ResponseData.error(MsgConstant.ASN_OWNER_NAME_NOT_NULL, excelItem.getRowNum());
            }
            if (EmptyUtil.isEmpty(excelItem.getOrderTime())) {
                return ResponseData.error(MsgConstant.ASN_ORDER_TIME_NOT_NULL, excelItem.getRowNum());
            }
            if (EmptyUtil.isEmpty(excelItem.getWarehouseName())) {
                return ResponseData.error(MsgConstant.ASN_WAREHOUSE_NAME_NOT_NULL, excelItem.getRowNum());
            }

            if (EmptyUtil.isEmpty(excelItem.getTransportType())) {
                return ResponseData.error(MsgConstant.ASN_TRANSPORT_TYPE_NOT_NULL, excelItem.getRowNum());
            }

            if (EmptyUtil.isEmpty(excelItem.getClientName())) {
                return ResponseData.error(MsgConstant.ASN_CLIENT_NAME_NOT_NULL, excelItem.getRowNum());
            }


            //查找入库单类型信息
            String asnType = null;
            asnType = this.findByAsnType(excelItem.getAsnType());
            if (EmptyUtil.isEmpty(asnType)) {
                return ResponseData.error(MsgConstant.ASN_TYPE_DATE_IMPORT, excelItem.getRowNum(), excelItem.getAsnType(), "");
            }

            //查找货主信息
            Long ownerId = null;
            ownerId = this.findByOwnerName(excelItem.getOwnerName());
            if (EmptyUtil.isEmpty(ownerId)) {
                return ResponseData.error(MsgConstant.ASN_OWNER_NAME_DATE_IMPORT, excelItem.getRowNum(), excelItem.getOwnerName(), "");
            }
            //查找承运商信息


            //查找仓库信息
            Long warehouseId = null;
            warehouseId = this.findByWarehouseName(excelItem.getWarehouseName());
            if (EmptyUtil.isEmpty(warehouseId)) {
                return ResponseData.error(MsgConstant.ASN_WAREHOUSE_NAME_DATE_IMPORT, excelItem.getRowNum(), excelItem.getWarehouseName(), "");
            }

            //查找运输类型
            String transportType = null;
            transportType = this.findByTransportType(excelItem.getTransportType());
            if (EmptyUtil.isEmpty(transportType)) {
                return ResponseData.error(MsgConstant.ASN_TRANSPORT_TYPE_DATE_IMPORT, excelItem.getRowNum(), excelItem.getTransportType(), "");
            }


            //查找客户id
            Long clientId = null;
            clientId = this.findByClientName(excelItem.getClientName());
            if (EmptyUtil.isEmpty(clientId)) {
                return ResponseData.error(MsgConstant.ASN_CLIENT_NAME_DATE_IMPORT, excelItem.getRowNum(), excelItem.getClientName(), "");
            }

            //查找运输方式
            String transportation = null;
            if (EmptyUtil.isNotEmpty(excelItem.getTransportation())) {
                transportation = this.findByTransportation(excelItem.getTransportation());
                if (EmptyUtil.isEmpty(transportation)) {
                    return ResponseData.error(MsgConstant.ASN_TRANSPORTATION, excelItem.getRowNum(), excelItem.getTransportation(), "");
                }
            }

            //查找承运商
            String carriersId = null;
            if (EmptyUtil.isNotEmpty(excelItem.getCarriers())) {
                carriersId = this.findByCarriers(excelItem.getCarriers());
                if (EmptyUtil.isEmpty(carriersId)) {
                    return ResponseData.error(MsgConstant.ASN_CARRIERS_DATE_IMPORT, excelItem.getRowNum(), excelItem.getCarriers(), "");
                }

            }

            //查找优先级
            String priority = null;
            if (EmptyUtil.isNotEmpty(excelItem.getPriority())) {
                priority = this.findByPriority(excelItem.getPriority());
                if (EmptyUtil.isEmpty(priority)) {
                    return ResponseData.error(MsgConstant.ASN_PRIORITY_DATE_IMPORT, excelItem.getRowNum(), excelItem.getPriority(), "");
                }

            }

            BeanUtil.copyPropertiesIsNotNull(excelItem, asnHeader);
            asnHeader.setOwnerId(ownerId);
            asnHeader.setWarehouseId(warehouseId);
            asnHeader.setAsnType(asnType);
            asnHeader.setPriority(priority);
//            asnHeader.setTransportType(transportType);
//            asnHeader.setClientId(clientId);
//            asnHeader.setClientName(excelItem.getClientName());
//            asnHeader.setTransportation(transportation);
//            asnHeader.setCarriersId(carriersId);
//            asnHeader.setCarriers(excelItem.getCarriers());
            //流水号(入库单号)
            asnHeader.setAsnNo(numberGenerator.nextValue(IdRuleConstant.ASN_NO));
            asnHeader.setStatus("00");
            asnHeader.setAuditStatus("00");
            preSave(asnHeader);
            anHeaderList.add(asnHeader);
        }
        if (anHeaderList.size() > 0) {
            int count = dao.batchInsert(anHeaderList);
            ErrorResponseData<String> errorDate = ResponseData.error(MsgConstant.ASN_IMPORT_SUCCESSFULLY, count + "");
            responseData.setMsg(errorDate.getMsg());
            responseData.setSuccess(true);
        }
        return responseData;

    }

    /**
     * 根据名称查询货主编码
     *
     * @param name
     * @return
     */
    public Long findByOwnerName(String name) {
        CustomerCondition condition = new CustomerCondition();
        condition.setCustomerName(name);
        return customerDao.findLongByClientName(condition);
    }


    /**
     * 查找仓库信息
     *
     * @param name
     * @return
     */
    public Long findByWarehouseName(String name) {
        WarehouseCondition condition = new WarehouseCondition();
        condition.setWarehouseName(name);
        return warehouseDao.findByWarehouseName(condition);
    }

    /**
     * 查找运输类型code
     *
     * @param transportType
     * @return
     */
    public String findByTransportType(String transportType) {
        DictDataCondition condition = new DictDataCondition();
        condition.setValue(transportType);
        ResponseData<DictDataQuery> dictData = remoteDictDataService.getDictData(condition);
        if (dictData.getSuccess()) {
            DictDataQuery data = dictData.getData();
            return data.getCode();
        }
        return null;
    }

    /**
     * 查找客户id
     *
     * @param clientName
     * @return
     */
    public Long findByClientName(String clientName) {
        CustomerCondition condition = new CustomerCondition();
        condition.setCustomerName(clientName);
        return customerDao.findLongByClientName(condition);
    }

    /**
     * 查找运输方式code
     *
     * @param transportation
     * @return
     */
    public String findByTransportation(String transportation) {
        DictDataCondition condition = new DictDataCondition();
        condition.setValue(transportation);
        ResponseData<DictDataQuery> dictData = remoteDictDataService.getDictData(condition);
        if (dictData.getSuccess()) {
            DictDataQuery data = dictData.getData();
            return data.getCode();
        }
        return null;
    }

    /**
     * 查找承运商id
     *
     * @param carriers
     * @return
     */
    public String findByCarriers(String carriers) {
        CarrierCondition condition = new CarrierCondition();
        condition.setCarrierName(carriers);
        return carrierDao.findByCarrierName(condition);
    }

    /**
     * 查找入库单类型信息code
     *
     * @param asnType
     * @return
     */
    public String findByAsnType(String asnType) {
        DictDataCondition condition = new DictDataCondition();
        condition.setValue(asnType);
        ResponseData<DictDataQuery> dictData = remoteDictDataService.getDictData(condition);
        if (dictData.getSuccess()) {
            DictDataQuery data = dictData.getData();
            return data.getCode();
        }
        return null;
    }

    /**
     * 查找优先级code
     *
     * @param priority
     * @return
     */
    public String findByPriority(String priority) {
        DictDataCondition condition = new DictDataCondition();
        condition.setValue(priority);
        ResponseData<DictDataQuery> dictData = remoteDictDataService.getDictData(condition);
        if (dictData.getSuccess()) {
            DictDataQuery data = dictData.getData();
            return data.getCode();
        }
        return null;
    }

    /**
     * 获取入库文件列表
     *
     * @param csAttachmentCondition
     * @return
     */
    public PageResult<CsAttachmentQuery> pageAtt(CsAttachmentCondition csAttachmentCondition) {

        if (EmptyUtil.isEmpty(csAttachmentCondition.getAsnId()) || csAttachmentCondition.getAsnId() == -1) {
            csAttachmentCondition.setContractId("-1");
        } else {
            AsnHeader asnHeader = this.findById(csAttachmentCondition.getAsnId().longValue());
            csAttachmentCondition.setContractId(asnHeader.getAsnNo());
        }
        return dao.pageCsAttachment(csAttachmentCondition);
    }

    /**
     * 查询入库订单信息
     *
     * @param id
     * @return
     */

    public AsnHeaderInfo findAsnInfoById(Long id) {
        if (EmptyUtil.isEmpty(id)) {
            throw new ServiceException(GlobalExceptionEnum.ID_NOT_EMPTY);
        }
        AsnHeaderInfo asnInfoById = dao.findAsnInfoById(id);
        if (StrUtil.isNotBlank(asnInfoById.getAuditOp())) {
            Long opId = Long.valueOf(asnInfoById.getAuditOp());
            Map<Long, UserForOuterQuery> userNameByIds = commonService.getUserNameByIds(new Long[]{opId});
            if (userNameByIds.containsKey(opId)) {
                asnInfoById.setAuditOp(userNameByIds.get(opId).getRealName());
            }
        }
        List<AsnDetailInfo> asnDetailInfoByAsnId = integrationAsnDetailInfoList(Lists.newArrayList(asnInfoById.getAsnNo()));
        asnInfoById.setAsnDetailItemList(asnDetailInfoByAsnId);
        return asnInfoById;
    }


    public List<AsnDetailInfo> integrationAsnDetailInfoList(List<String> asnNos) {
        List<AsnDetailInfo> asnDetailInfoByAsnId = asnDetailService.getAsnDetailInfoByAsnNos(asnNos);
        for (AsnDetailInfo asnDetailInfo : asnDetailInfoByAsnId) {
            Long lotId = asnDetailInfo.getLotId();
            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andEqual("lotId", lotId);
            List<WarehouseLotDetailItem> warehouseLotDetailItems = warehouseLotDetailDao.find(WarehouseLotDetailItem.class, conditionRule);
            asnDetailInfo.setWarehouseLotDetailItems(warehouseLotDetailItems);
            ConditionRule conditionRule1 = new ConditionRule();
            conditionRule1.andEqual("asnDetailId",asnDetailInfo.getId());
            List<AsnDetailSnItem> asnDetailSns = asnDetailSnDao.find(AsnDetailSnItem.class,conditionRule1);
            asnDetailInfo.setAsnDetailSns(asnDetailSns);
            LotAttribute lotAttribute = new LotAttribute();
            BeanUtil.copyProperties(asnDetailInfo,lotAttribute);
            List<LotDetailItem> lotDetailItems = new ArrayList<>();
            warehouseLotDetailItems.forEach(e->{
                LotDetailItem lotDetailItem = new LotDetailItem();
                BeanUtil.copyProperties(e,lotDetailItem);
                lotDetailItems.add(lotDetailItem);
            });
            asnDetailInfo.setAttribute(lotUtil.lotAttributeToStr(lotDetailItems,lotAttribute));
            if (ObjUtil.isNotNull(lotAttribute.getSupplierId())) {
                asnDetailInfo.setSupplierId(lotAttribute.getSupplierId());
                asnDetailInfo.setSupplierName(lotAttribute.getSupplierName());
            }
            //验证每个商品是否存在收货信息
            asnReceiveService.verificationExistenceReceive(asnDetailInfo);
        }
        return asnDetailInfoByAsnId;
    }


    /**
     * 根据asnNo查询入库订单信息
     * @param asnNo
     * @return
     */
    public AsnHeaderInfo findAsnInfoByAsnNo(String asnNo) {
        AsnHeaderInfo asnInfo = dao.findAsnInfoByAsnNo(asnNo);
        if (StrUtil.isNotBlank(asnInfo.getAuditOp())) {
            Long opId = Long.valueOf(asnInfo.getAuditOp());
            Map<Long, UserForOuterQuery> userNameByIds = commonService.getUserNameByIds(new Long[]{opId});
            if (userNameByIds.containsKey(opId)) {
                asnInfo.setAuditOp(userNameByIds.get(opId).getRealName());
            }
        }
        //查询数据字典 组装入库单类型
        if(EmptyUtil.isNotEmpty(asnInfo.getAsnType())) {
            DictDataCondition dictDataCondition = new DictDataCondition();
            dictDataCondition.setType(DictDataTypeConstants.ASN_TYPE);
            ResponseData<List<DictDataQuery>> responseData = remoteDictDataService.getDictDataList(dictDataCondition);
            if(responseData.getSuccess()){
                List<DictDataQuery> data = responseData.getData();
                Map<String, String> dictMap = data.stream().collect(Collectors.toMap(DictDataQuery::getCode, DictDataQuery::getValue, (key1, key2) -> key1));
                if(EmptyUtil.isNotEmpty(dictMap) && dictMap.containsKey(asnInfo.getAsnType())) {
                    asnInfo.setAsnTypeStr(dictMap.get(asnInfo.getAsnType()));
                }
            }
        }
        //查询数据字典 组装优先级
        if(EmptyUtil.isNotEmpty(asnInfo.getPriority())) {
            DictDataCondition dictDataCondition = new DictDataCondition();
            dictDataCondition.setType(DictDataTypeConstants.PRIORITY);
            ResponseData<List<DictDataQuery>> responseData = remoteDictDataService.getDictDataList(dictDataCondition);
            if(responseData.getSuccess()){
                List<DictDataQuery> data = responseData.getData();
                Map<String, String> dictMap = data.stream().collect(Collectors.toMap(DictDataQuery::getCode, DictDataQuery::getValue, (key1, key2) -> key1));
                if(EmptyUtil.isNotEmpty(dictMap) && dictMap.containsKey(asnInfo.getAsnType())) {
                    asnInfo.setPriorityStr(dictMap.get(asnInfo.getAsnType()));
                }
            }
        }
        List<AsnDetailInfo> asnDetailInfoByAsnId = asnDetailService.getAsnDetailInfoByAsnNo(asnInfo.getAsnNo());
        for (AsnDetailInfo asnDetailInfo : asnDetailInfoByAsnId) {
            Long lotId = asnDetailInfo.getLotId();
            ConditionRule conditionRule = new ConditionRule();
            conditionRule.andEqual("lotId", lotId);
            List<WarehouseLotDetailItem> warehouseLotDetailItems = warehouseLotDetailDao.find(WarehouseLotDetailItem.class, conditionRule);
            asnDetailInfo.setWarehouseLotDetailItems(warehouseLotDetailItems);
            ConditionRule conditionRule1 = new ConditionRule();
            conditionRule1.andEqual("asnDetailId",asnDetailInfo.getId());
            List<AsnDetailSnItem> asnDetailSns = asnDetailSnDao.find(AsnDetailSnItem.class,conditionRule1);
            asnDetailInfo.setAsnDetailSns(asnDetailSns);
            LotAttribute lotAttribute = new LotAttribute();
            BeanUtil.copyProperties(asnDetailInfo,lotAttribute);
            List<LotDetailItem> lotDetailItems = new ArrayList<>();
            warehouseLotDetailItems.forEach(e->{
                LotDetailItem lotDetailItem = new LotDetailItem();
                BeanUtil.copyProperties(e,lotDetailItem);
                lotDetailItems.add(lotDetailItem);
            });
            asnDetailInfo.setAttribute(lotUtil.lotAttributeToStr(lotDetailItems,lotAttribute));
            if (ObjUtil.isNotNull(lotAttribute.getSupplierId())) {
                asnDetailInfo.setSupplierId(lotAttribute.getSupplierId());
                asnDetailInfo.setSupplierName(lotAttribute.getSupplierName());
            }
            //验证每个商品是否存在收货信息
            asnReceiveService.verificationExistenceReceive(asnDetailInfo);
        }
        asnInfo.setAsnDetailItemList(asnDetailInfoByAsnId);
        return asnInfo;
    }

    public List<AsnHeader> findByAsnNos(Set<String> asnNoSet) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(AsnHeader::getAsnNo, asnNoSet);
        return this.find(conditionRule);
    }

    @Transactional(rollbackFor = Exception.class)
    public void check(AsnHeaderItem asnHeaderItem) {
        if (!((ObjectUtil.isEmpty(asnHeaderItem.getStartTime()) && ObjectUtil.isEmpty(asnHeaderItem.getEndTime()))
                || (ObjectUtil.isNotEmpty(asnHeaderItem.getStartTime()) && ObjectUtil.isNotEmpty(asnHeaderItem.getEndTime()) && asnHeaderItem.getEndTime().compareTo(asnHeaderItem.getStartTime()) > 0))) {
            throw new ServiceException(GlobalExceptionEnum.START_TIME_LT_END_TIME);
        }
        if (StrUtil.isNotBlank(asnHeaderItem.getLogisticNo()) && Validator.hasChinese(asnHeaderItem.getLogisticNo())) {
            throw new ServiceException(AsnExceptionEnum.LOGISTIC_NO_GENERAL_WITH_CHINESE.getMsg());
        }
        List<AsnDetailItem> asnDetailItemList = asnHeaderItem.getAsnDetailItemList();
        // 数据判空
        if (asnDetailItemList == null || asnDetailItemList.isEmpty()) {
            throw new ServiceException(AsnExceptionEnum.DETAIL_NOT_EMPTY);
        }
        for (AsnDetailItem asnDetailItem : asnDetailItemList) {
            List<AsnDetailSnItem> asnDetailSns = asnDetailItem.getAsnDetailSns();
            if (CollUtil.isNotEmpty(asnDetailSns)) {
                //去重
                Set<String> collect = asnDetailSns.stream().map(AsnDetailSnItem::getSnNo).collect(Collectors.toSet());
                if (collect.size() != asnDetailSns.size()) {
                    throw new ServiceException(AsnExceptionEnum.SN_DUPLICATED);
                }
                if (new BigDecimal(asnDetailSns.size()).compareTo(asnDetailItem.getQtyPlanEa()) != 0) {
                    throw new ServiceException(AsnExceptionEnum.SN_NOT_EQUALS_EA);
                }
            }
        }
        if (EmptyUtil.isNotEmpty(asnHeaderItem.getId())) {
            AsnHeader asnHeader = findById(asnHeaderItem.getId().longValue());
            asnHeaderItem.setCompanyId(asnHeader.getCompanyId());
        }
        if (AsnTypeEnum.OS.getCode().equals(asnHeaderItem.getAsnType()) && StrUtil.isBlank(asnHeaderItem.getSoNo())) {
            throw new ServiceException(AsnExceptionEnum.SO_NO_NOT_EMPTY);
        }
        if (AsnAddType.COPY.getCode().equals(asnHeaderItem.getAddType())) {
            copy(asnHeaderItem);
        } else {
            save(asnHeaderItem);
        }
    }

    public Map<String, List<AsnDetailSn>> getSnNoByAsnDetailId(Long id) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual("asnDetailId",id);
        List<AsnDetailSn> asnDetailSns = asnDetailSnDao.find(AsnDetailSn.class, conditionRule);
        asnDetailSns.stream().filter(item -> StrUtil.isEmpty(item.getBoxCode())).forEach(item -> item.setBoxCode("无箱码"));
        Map<String, List<AsnDetailSn>> collect = asnDetailSns.stream().collect(Collectors.groupingBy(AsnDetailSn::getBoxCode));
        return collect;

    }

    public void updateStatusBySnNos(List<String> snNos,String status){
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn("snNo",snNos);
        List<AsnDetailSn> asnDetailSns = asnDetailSnDao.find(conditionRule);
        asnDetailSns.forEach(e->e.setReceiveStatus(status));
    }

    public void assignReceiveUser(AsnHeaderReceiveItem asnHeaderReceiveItem) {
        if (EmptyUtil.isEmpty(asnHeaderReceiveItem.getId())) {
            throw new ServiceException(GlobalExceptionEnum.ID_NOT_EMPTY);
        }
        AsnHeader header = this.findById(asnHeaderReceiveItem.getId());
        if (EmptyUtil.isEmpty(header)){
            throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, AsnEnum.ASN_HEADER,asnHeaderReceiveItem.getAsnNo());
        }
        // 更新收货人
        header.setReceiverId(asnHeaderReceiveItem.getReceiverId());
        header.setReceiverOp(asnHeaderReceiveItem.getReceiverOp());
        dao.update(header);
    }

    public Boolean checkReceiverOp(Long[] ids) {
        if (EmptyUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.ID_NOT_EMPTY);
        }
        List<AsnHeader> asnHeaderList = dao.findByIds(ids);
        boolean exist = asnHeaderList.stream().anyMatch(e -> StrUtil.isBlank(e.getReceiverOp()));
        if (exist) {
            return false;
        }
        return true;
    }

    /**
     *@Description 根据编号集合进行查询
     *@Param businessNoList
     *@Return * {@link List< AsnHeader> }
     *@Date 2025/5/27 15:13
     *<AUTHOR>
     **/
    public List<AsnHeader> findByNoList(List<String> businessNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(AsnHeader::getAsnNo, businessNoList);
        return dao.find(conditionRule);
    }

    /**
     *@Description 根据上游单号集合查询
     *@Param orderNoList
     *@Return * {@link List< AsnHeader> }
     *@Date 2025/5/27 17:11
     *<AUTHOR>
     **/
    public List<AsnHeader> findByPOrderNoList(List<String> orderNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(AsnHeader::getPOrderNo, orderNoList);
        return dao.find(conditionRule);
    }

    /**
     * 打印入库订单
     * @param id 主键
     * @return AsnHeaderPrintQuery
     */
    public AsnHeaderPrintQuery print(Long id) {
        AsnHeader asnHeader = dao.findById(id);
        if(ObjUtil.isNull(asnHeader)){
            throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "入库", id);
        }
        Warehouse warehouse = warehouseDao.findById(asnHeader.getWarehouseId());
        if(ObjUtil.isNull(warehouse)){
            throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "仓库", asnHeader.getWarehouseId());
        }
        Owner owner = ownerDao.findById(asnHeader.getOwnerId());
        if(ObjUtil.isNull(owner)){
            throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "货主", asnHeader.getOwnerId());
        }
        // 获取数据字典真实数值
        ResponseData<Map<String, List<SelectItem>>> responseData = remoteDictDataService.getDictDataAll();
        if(responseData.getSuccess()){
            Map<String, List<SelectItem>> dictDataAll = responseData.getData();
            List<SelectItem> asnTypeList = dictDataAll.get("asn_type");
            List<SelectItem> priorLevelList = dictDataAll.get("prior_level");
            AsnHeaderPrintQuery query = new AsnHeaderPrintQuery();
            // 赋值入库订单信息
            query.setAsnNo(ObjUtil.defaultIfNull(asnHeader.getAsnNo(), StrUtil.EMPTY));
            query.setWarehouseName(ObjUtil.defaultIfNull(warehouse.getWarehouseName(), StrUtil.EMPTY));
            query.setOwnerName(ObjUtil.defaultIfNull(owner.getOwnerName(), StrUtil.EMPTY));
            if(StrUtil.isNotBlank(asnHeader.getAsnType())){
                Optional<SelectItem> selectItem = asnTypeList.stream().filter(e -> StrUtil.equals(e.getValue(), asnHeader.getAsnType())).findFirst();
                selectItem.ifPresent(item -> query.setAsnType(item.getLabel()));
            }
            if(ObjUtil.isNotNull(asnHeader.getSupplierId())){
                Supplier supplier = supplierDao.findById(asnHeader.getSupplierId());
                if(ObjUtil.isNull(supplier)){
                    throw new ServiceException(GlobalExceptionEnum.NO_NOT_EXIST, "供应商", asnHeader.getSupplierId());
                }
                query.setSupplierName(ObjUtil.defaultIfNull(supplier.getSupplierName(), StrUtil.EMPTY));
            }
            if(StrUtil.isNotBlank(asnHeader.getPriority())){
                Optional<SelectItem> selectItem = priorLevelList.stream().filter(e -> StrUtil.equals(e.getValue(), asnHeader.getPriority())).findFirst();
                selectItem.ifPresent(item -> query.setPriority(item.getLabel()));
            }
            if(ObjUtil.isNotNull(asnHeader.getStartTime()) && ObjUtil.isNotNull(asnHeader.getEndTime())){
                query.setStartTime(asnHeader.getStartTime());
                query.setEndTime(asnHeader.getEndTime());
            }
            // 赋值入库订单明细信息
            Map<Long, Sku>  skuMap = skuDao.findAll().stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (k1, k2) -> k1));;
            Map<Long, Package> packageMap  = packageDao.findAll().stream().collect(Collectors.toMap(Package::getId, Function.identity(), (k1, k2) -> k1));;
            Map<Long, PackageUnit> packageUnitMap = packageUnitDao.findAll().stream().collect(Collectors.toMap(PackageUnit::getId, Function.identity(), (k1, k2) -> k1));;
            Map<Long, WarehouseLoc> warehouseLocMap = warehouseLocDao.findAll().stream().collect(Collectors.toMap(WarehouseLoc::getId, Function.identity(), (k1, k2) -> k1));
            Map<Long, Pallet> palletMap = palletDao.findAll().stream().collect(Collectors.toMap(Pallet::getId, Function.identity(), (k1, k2) -> k1));
            List<AsnDetail> asnDetailList = asnDetailService.find(new ConditionRule().andEqual("asnNo", asnHeader.getAsnNo()));
            if(CollectionUtil.isNotEmpty(asnDetailList)){
                for (AsnDetail asnDetail : asnDetailList) {
                    AsnDetailPrintQuery detailQuery = new AsnDetailPrintQuery();
                    Sku sku = MapUtil.get(skuMap, asnDetail.getSkuId(), Sku.class);
                    if(ObjUtil.isNotNull(sku)){
                        detailQuery.setSkuCode(ObjUtil.defaultIfNull(sku.getSkuCode(), StrUtil.EMPTY));
                        detailQuery.setSkuName(ObjUtil.defaultIfNull(sku.getSkuName(), StrUtil.EMPTY));
                    }
                    Package pack = MapUtil.get(packageMap, asnDetail.getPackageId(), Package.class);
                    if(ObjUtil.isNotNull(pack)){
                        detailQuery.setPackageName(ObjUtil.defaultIfNull(pack.getName(), StrUtil.EMPTY));
                    }
                    PackageUnit packageUnit = MapUtil.get(packageUnitMap, asnDetail.getPackageUnitId(), PackageUnit.class);
                    if(ObjUtil.isNotNull(packageUnit)) {
                        detailQuery.setPackageUnitName(ObjUtil.defaultIfNull(packageUnit.getName(), StrUtil.EMPTY));
                        detailQuery.setQtyPlanEa(ObjUtil.defaultIfNull(asnDetail.getQtyPlanEa(), BigDecimal.ZERO));
                    }
                    WarehouseLoc warehouseLoc = MapUtil.get(warehouseLocMap, asnDetail.getPlanToLocId(), WarehouseLoc.class);
                    if(ObjUtil.isNotNull(warehouseLoc)){
                        detailQuery.setPlanToLocCode(ObjUtil.defaultIfNull(warehouseLoc.getLocCode(), StrUtil.EMPTY));
                    }
                    Pallet pallet = MapUtil.get(palletMap, asnDetail.getPlanToPalletId(), Pallet.class);
                    if(ObjUtil.isNotNull(pallet)){
                        detailQuery.setPlanToPallet(ObjUtil.defaultIfNull(pallet.getPalletNo(), StrUtil.EMPTY));
                    }
                    query.getDetailList().add(detailQuery);
                }
            }
            return query;
        }else{
            return new AsnHeaderPrintQuery();
        }
    }
}
