package com.chinaservices.wms.module.stock.take.model;


import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.module.base.ModuleBaseModel;
import com.chinaservices.wms.common.enums.stock.StocktakeTaskStatusEnum;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.util.Date;

/**
 * cs_inventory_stocktake_task
 * <AUTHOR>
@Entity
@Table(name = "cs_inventory_stocktake_task")
public class InventoryStocktakeTask extends ModuleBaseModel {

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 盘点单号
     */
    private String stocktakeNo;

    /**
     * 盘点任务号
     */
    private String taskNo;

    /**
     * 任务状态（1-创建；2-盘点中；5-关闭；4-取消）
     * @see StocktakeTaskStatusEnum
     */
    private String taskStatus;

    /**
     * 盘点人id
     */
    private Long personnelId;

    /**
     * 盘点人名称
     */
    private String personnelName;

    /**
     * 任务是否已分配（1-是；0-否）
     * @see YesNoEnum
     */
    private String taskAllocation;

    /**
     * 任务开始时间
     */
    private Date taskStartTime;

    /**
     * 任务结束时间
     */
    private Date taskEndTime;

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getStocktakeNo() {
        return stocktakeNo;
    }

    public void setStocktakeNo(String stocktakeNo) {
        this.stocktakeNo = stocktakeNo;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Long getPersonnelId() {
        return personnelId;
    }

    public void setPersonnelId(Long personnelId) {
        this.personnelId = personnelId;
    }

    public String getPersonnelName() {
        return personnelName;
    }

    public void setPersonnelName(String personnelName) {
        this.personnelName = personnelName;
    }

    public String getTaskAllocation() {
        return taskAllocation;
    }

    public void setTaskAllocation(String taskAllocation) {
        this.taskAllocation = taskAllocation;
    }

    public Date getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(Date taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    public Date getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(Date taskEndTime) {
        this.taskEndTime = taskEndTime;
    }
}