package com.chinaservices.wms.module.stock.expirywarning.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.stock.expirywarning.domain.ExpiryWarningPageCondition;
import com.chinaservices.wms.module.stock.expirywarning.domain.ExpiryWarningPageQuery;
import com.chinaservices.wms.module.stock.expirywarning.service.ExpiryWarningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 效期预警控制器
 * <AUTHOR>
 * @date 2025/6/17
 */
@RestController
@RequestMapping("/api/expiry/warning")
public class ExpiryWarningController {

    @Autowired
    private ExpiryWarningService expiryWarningService;

    /**
     * 分页查询效期预警
     * @param condition 查询条件
     * @return ResponseData<PageResult<ExpiryWarningPageQuery>>
     */
    @PostMapping("/page")
    @SaCheckPermission("expiryWarning:expiryWarningList:headBtn:page")
    public ResponseData<PageResult<ExpiryWarningPageQuery>> page(@RequestBody ExpiryWarningPageCondition condition){
        return ResponseData.success(expiryWarningService.page(condition));
    }

    /**
     * xxl-job定时更新效期状态（每天）
     * @return ResponseData<Boolean>
     */
    @PostMapping("/updateStatusByTimer")
    public ResponseData<Boolean> updateStatusByTimer(){
        return ResponseData.success(expiryWarningService.updateStatusByTimer());
    }
}
