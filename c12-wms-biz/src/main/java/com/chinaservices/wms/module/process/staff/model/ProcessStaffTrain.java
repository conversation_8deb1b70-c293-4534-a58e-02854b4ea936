package com.chinaservices.wms.module.process.staff.model;


import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 培训记录
 */
@Data
@Entity
@Table(name = "cs_process_staff_train")
@EqualsAndHashCode(callSuper = true)
public class ProcessStaffTrain extends ModuleBaseModel {
  /**
   * 加工人员id
   */
  private long staffId;
  /**
   * 培训名称
   */
  private String trainName;
  /**
   * 培训时间
   */
  private Date trainTime;
  /**
   * 培训成绩
   */
  private String trainScore;
}
