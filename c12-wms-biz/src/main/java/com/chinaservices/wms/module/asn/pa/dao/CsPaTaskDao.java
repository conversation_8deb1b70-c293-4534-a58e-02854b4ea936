package com.chinaservices.wms.module.asn.pa.dao;

import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.wms.common.sqlid.asn.pa.PaTaskSqlId;
import com.chinaservices.wms.module.asn.pa.domain.*;
import com.chinaservices.wms.module.asn.pa.model.CsPaTask;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description: 上架任务 Dao
 */
@Repository
public class CsPaTaskDao extends ModuleBaseDaoSupport<CsPaTask, Long> {

    /**
     * 上架任务（ASN）分页查询
     * @param condition
     * @return
     */
    public PageResult<PaTaskQuery> inPage(PaTaskPageCondition condition){
        return sqlExecutor.page(PaTaskQuery.class, PaTaskSqlId.PATASK_QUERY_ASN_LIST, condition);
    }

    /**
     * 上架任务（SO）分页查询
     * @param condition
     * @return
     */
    public PageResult<PaTaskQuery> outPage(PaTaskPageCondition condition){
        return sqlExecutor.page(PaTaskQuery.class, PaTaskSqlId.PATASK_QUERY_SO_LIST, condition);
    }

    /**
     * 根据asnId查询创建状态的上架任务
     * @param condition
     * @return
     */
    public List<CsPaTask> findByAsnId(PaTaskCondition condition){
        return sqlExecutor.find(CsPaTask.class, PaTaskSqlId.PATASK_QUERY_BY_ASN_ID, condition);
    }

    /**
     * 根据rcvId查询上架任务
     * @param condition
     * @return
     */
    public List<CsPaTask> findByRcvId(PaTaskCondition condition) {
        return sqlExecutor.find(CsPaTask.class, PaTaskSqlId.PATASK_QUERY_BY_ASN_ID, condition);
    }

    /**
     * 通过上架任务号paNo，获得最新任务序号
     * @param condition
     * @return
     */
    public Integer findByPaNo(PaTaskCondition condition) {
        return sqlExecutor.findInt(PaTaskSqlId.PATASK_QUERY_MAX_LINENO_BY_PA_NO, condition);
    }

    /**
     * 汇总上架ID的上架数
     * @param condition
     * @return
     */
    public Long findSumByPaNo(PaTaskCondition condition) {
        return sqlExecutor.findLong(PaTaskSqlId.PATASK_QUERY_PA_QTY_BY_PA_NO, condition);
    }

    /**
     * 根据条件获取上架任务列表
     * @param condition
     * @return
     */
    public List<PaTaskInfoQuery> findList(PaTaskPageCondition condition) {
        return sqlExecutor.find(PaTaskInfoQuery.class, PaTaskSqlId.PATASK_QUERY_ASN_LIST, condition);
    }

    public PaTaskQuery findQueryById(Long id) {
        return sqlExecutor.findFirst(PaTaskQuery.class, PaTaskSqlId.PA_TASK_QUERY_PA_QTY_BY_ID, "id",id);
    }

    /**
     * 计算仓库上架平均时效
     * @param warehouseId
     * @return
     */
    public Double findAvgTimeByWarehouseId(Long warehouseId) {
        return sqlExecutor.findDouble(PaTaskSqlId.PATASK_QUERY_AVG_TIME_BY_WAREHOUSE_ID, "warehouseId", warehouseId);
    }

    /**
     * 根据上架任务号查询上架任务信息-单据预览数据查询
     * @param paNo
     * @return
     */
    public PaTaskQuery getPaTaskPreviewQueryByPaNo(String paNo) {
        return sqlExecutor.findFirst(PaTaskQuery.class, PaTaskSqlId.PATASK_QUERY_ASN_BY_PA_NO, "paNo", paNo);
    }

    public List<PaTaskAssociationQuery> searchTaskByPackNoList(Set<String> keySet) {
        return sqlExecutor.find(PaTaskAssociationQuery.class, PaTaskSqlId.PATASK_QUERY_LIST_BY_PACK_NO, "packNos", keySet);
    }
}
