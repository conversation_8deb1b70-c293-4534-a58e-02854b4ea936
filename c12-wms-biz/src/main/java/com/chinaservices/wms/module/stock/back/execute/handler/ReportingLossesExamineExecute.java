package com.chinaservices.wms.module.stock.back.execute.handler;


import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.enums.stock.TraceBackBusinessTypeEnum;
import com.chinaservices.wms.module.stock.back.execute.TraceBackExecute;
import com.chinaservices.wms.module.stock.back.interfaces.TraceBackHandler;
import com.chinaservices.wms.module.stock.back.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 报损审核
 */
@Slf4j
@TraceBackHandler(TraceBackBusinessTypeEnum.REPORTING_LOSSES_EXAMINE)
@Service
public class ReportingLossesExamineExecute extends TraceBackExecute {

    @Override
    public void execute(TraceBackRecordItem item, long threadId) {
        log.info("SIGN:{} 开始执行SN序列号回溯功能 :{}",threadId, JSONUtil.toJsonStr(item));
        if (item == null || item.getSnNoList() == null) {
            log.warn("Invalid trace back record item or empty SN list");
            return;
        }
        // 1. 获取基础数据
        RecodeSkuItem skuInfo = getSkuInfo(item.getSnNoList());
        List<RecordSnItem> currentSnItems = getRecordSnItemList(item.getSnNoList());

        // 2. 验证并处理回溯记录
        CsSnTraceBackRecord backRecord = super.verificationDoesTheDataExist(item, skuInfo);
        if (ObjUtil.isNotNull(backRecord)) {
            handleExistingRecord(backRecord, currentSnItems,item);
            setBusinessInformation(backRecord,item);
        } else {
            backRecord = createNewRecord(item, skuInfo, currentSnItems);
        }

        // 3. 提交数据
        super.sumbit(backRecord,threadId);
        log.info("SIGN:{} SN序列号回溯功能执行结束 :{}",threadId,JSONUtil.toJsonStr(backRecord));
    }

    @Override
    protected void setBusinessInformation(CsSnTraceBackRecord backRecord,TraceBackRecordItem item) {
        ThreadTokenItem tokenItem = super.getThreadToken(item.getThreadId());
        SessionUserInfo userInfo = tokenItem.getSessionUserInfo();
        if (EmptyUtil.isEmpty(backRecord.getBusinessInfo())){
            backRecord.setBusinessInfo(userInfo.getRealName());
        }else{
            Set<String> businessInfoSet = new HashSet<>(Arrays.asList(backRecord.getBusinessInfo().split(",")));
            businessInfoSet.add(userInfo.getRealName());
            backRecord.setBusinessInfo(String.join(",",businessInfoSet));
        }
    }
}
