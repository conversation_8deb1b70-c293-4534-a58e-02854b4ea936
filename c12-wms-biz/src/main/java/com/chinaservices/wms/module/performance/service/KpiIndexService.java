package com.chinaservices.wms.module.performance.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.jpa.support.rule.OrderRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.session.SessionContext;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.enums.performance.PerformanceDataSourceEnum;
import com.chinaservices.wms.common.enums.performance.PerformanceIndexTypeEnum;
import com.chinaservices.wms.common.enums.performance.PerformanceSourceTypeEnum;
import com.chinaservices.wms.common.exception.KpiExceptionEnum;
import com.chinaservices.wms.module.fee.fee.domain.IdsItem;
import com.chinaservices.wms.module.performance.dao.KpiIndexDao;
import com.chinaservices.wms.module.performance.domain.KpiIndexIntervalItem;
import com.chinaservices.wms.module.performance.domain.KpiIndexItem;
import com.chinaservices.wms.module.performance.domain.KpiIndexPageCondition;
import com.chinaservices.wms.module.performance.domain.KpiIndexQuery;
import com.chinaservices.wms.module.performance.model.KpiIndex;
import com.chinaservices.wms.module.performance.model.KpiIndexInterval;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class KpiIndexService extends ModuleBaseServiceSupport<KpiIndexDao,KpiIndex, Long> {

    @Autowired
    private NumberGenerator numberGenerator;

    @Autowired
    private KpiIndexIntervalService kpiIndexIntervalService;

    /**
     * 分页查询
     * @param condition
     * @return
     */
    public PageResult<KpiIndexQuery> page(KpiIndexPageCondition condition) {
        return dao.page(condition);
    }


    public List<KpiIndexQuery> search(KpiIndexPageCondition condition) {
        return dao.search(condition);
    }

    /**
     * 新增指标
     * @param item
     * @return
     */
    @Transactional
    public Boolean add(KpiIndexItem item) {
        //数据转换
        KpiIndex kpiIndex = new KpiIndex();
        BeanUtil.copyProperties(item, kpiIndex);
        kpiIndex.setStatus(YesNoEnum.YES.getCode());
        kpiIndex.setIndexNo(numberGenerator.nextValue(IdRuleConstant.KPI_INDEX_NO));
        kpiIndex.setIndexType(PerformanceIndexTypeEnum.HAND.getCode());
        kpiIndex.setDataSource(PerformanceDataSourceEnum.ARTIFICIAL.getCode());
        kpiIndex.setScoreType(PerformanceSourceTypeEnum.NOT_INTERVAL.getCode());
        //添加到数据库
        kpiIndex.setModifyTime(new Date());
        kpiIndex.setModifier(SessionContext.getSessionUserInfo().getUserId());
        dao.insert(kpiIndex);
        if (PerformanceIndexTypeEnum.AUTO.getCode().equals(item.getIndexType()) && CollUtil.isNotEmpty(item.getIntervalItems())){
            List<KpiIndexInterval> list = new ArrayList<>();
            item.getIntervalItems().forEach(t -> {
                KpiIndexInterval kpiIndexInterval = new KpiIndexInterval();
                BeanUtil.copyProperties(t, kpiIndexInterval);
                kpiIndexInterval.setIndexNo(kpiIndex.getIndexNo());
                list.add(kpiIndexInterval);
            });
            kpiIndexIntervalService.batchInsert(list);
        }
        return Boolean.TRUE;
    }

    /**
     *
     * @param item
     * @return
     */
    public Boolean edit(KpiIndexItem item) {
        KpiIndex kpiIndex = new KpiIndex();
        BeanUtil.copyProperties(item, kpiIndex);
        dao.update(item);
        if (PerformanceIndexTypeEnum.AUTO.getCode().equals(item.getIndexType()) && CollUtil.isNotEmpty(item.getIntervalItems())){
            List<KpiIndexInterval> list = new ArrayList<>();
            item.getIntervalItems().forEach(t -> {
                KpiIndexInterval kpiIndexInterval = new KpiIndexInterval();
                BeanUtil.copyProperties(t, kpiIndexInterval);
                kpiIndexInterval.setIndexNo(kpiIndex.getIndexNo());
                list.add(kpiIndexInterval);
            });
            if (CollUtil.isNotEmpty(list)){
                List<KpiIndexInterval> insertList = list.stream().filter(t -> ObjUtil.isNull(t.getId())).toList();
                List<KpiIndexInterval> updateList = list.stream().filter(t -> ObjUtil.isNotNull(t.getId())).toList();
                if (CollUtil.isNotEmpty(insertList)){
                    kpiIndexIntervalService.batchInsert(list);
                }
                if (CollUtil.isNotEmpty(updateList)){
                    kpiIndexIntervalService.batchUpdate(list);
                }
            }
        }else{
            //修改为非自动评分后 需要删除原数据区间
            kpiIndexIntervalService.delete(new ConditionRule().andEqual(KpiIndexInterval::getIndexNo,item.getIndexNo()));
        }
        return Boolean.TRUE;
    }

    public Boolean delete(IdsItem item) {
        List<KpiIndex> indexList = dao.find(new ConditionRule().andIn("id", item.getIds()));
        if (CollUtil.isNotEmpty(indexList)){
            dao.delete(item.getIds().toArray(new Long[0]));
            kpiIndexIntervalService.delete(new ConditionRule().andIn("indexNo", indexList.stream().map(KpiIndex::getIndexNo).toList()));
        }
        return Boolean.TRUE;
    }

    public Boolean enable(IdsItem item,YesNoEnum yesOrNoEnum) {
        List<KpiIndex> indexList = dao.find(new ConditionRule().andIn("id", item.getIds()));
        if (CollUtil.isNotEmpty(indexList)){
            indexList.forEach(t -> {
                t.setStatus(yesOrNoEnum.getCode());
            });

            if (YesNoEnum.YES.equals(yesOrNoEnum)){
                List<String> list = indexList.stream().map(KpiIndex::getIndexNo).toList();
                List<KpiIndexInterval> intervals = kpiIndexIntervalService.find(new ConditionRule().andIn(KpiIndexInterval::getIndexNo, list));
                if (CollUtil.isEmpty(intervals)){
                    throw new ServiceException(KpiExceptionEnum.AT_LEAST_ONE_IS_REQUIRED_INTERVAL,indexList.stream().map(KpiIndex::getIndexName).collect(Collectors.joining("、")));
                }
                Map<String, List<KpiIndexInterval>> listMap =
                        intervals.stream().collect(Collectors.groupingBy(KpiIndexInterval::getIndexNo));
                if (list.size() > listMap.size()){
                    String string = indexList.stream().filter(t -> !listMap.containsKey(t.getIndexNo())).map(KpiIndex::getIndexName).collect(Collectors.joining("、"));
                    throw new ServiceException(KpiExceptionEnum.AT_LEAST_ONE_IS_REQUIRED_INTERVAL,string);
                }
            }
            dao.batchUpdate(indexList);
        }
        return Boolean.TRUE;
    }

    public List<KpiIndexIntervalItem> intervalById(String indexNo) {
        List<KpiIndexInterval> indexIntervalList = kpiIndexIntervalService.find(new ConditionRule().andEqual(KpiIndexInterval::getIndexNo, indexNo), new OrderRule().addDescOrder("quantity"));
        List<KpiIndexIntervalItem> rest = new ArrayList<>();
        for (KpiIndexInterval kpiIndexInterval : indexIntervalList) {
            KpiIndexIntervalItem item = new KpiIndexIntervalItem();
            BeanUtil.copyProperties(kpiIndexInterval, item);
            rest.add(item);
        }
        return rest;
    }
}
