package com.chinaservices.wms.module.asn.merge.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.auth.module.user.domain.UserForOuterCondition;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.auth.module.user.domain.UserQuery;
import com.chinaservices.auth.module.user.feign.RemoteUserService;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseModel;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.enums.AuditStatusEnum;
import com.chinaservices.wms.common.enums.ReceiveStatusEnum;
import com.chinaservices.wms.common.enums.asn.AsnStatusEnum;
import com.chinaservices.wms.common.exception.AsnExceptionEnum;
import com.chinaservices.wms.module.asn.asn.domain.AsnDetailInfo;
import com.chinaservices.wms.module.asn.asn.domain.AsnDetailInfoByPaStatusCondition;
import com.chinaservices.wms.module.asn.asn.model.AsnDetail;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.asn.service.AsnDetailService;
import com.chinaservices.wms.module.asn.asn.service.AsnHeaderService;
import com.chinaservices.wms.module.asn.merge.dao.AsnMergeReceiveDao;
import com.chinaservices.wms.module.asn.merge.domain.*;
import com.chinaservices.wms.module.asn.merge.model.AsnMergeReceive;
import com.chinaservices.wms.module.asn.receive.domain.*;
import com.chinaservices.wms.module.asn.receive.service.AsnReceiveService;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.passive.tag.dao.PassiveTagDao;
import com.chinaservices.wms.module.passive.tag.model.PassiveTag;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AsnMergeReceiveService extends ModuleBaseServiceSupport<AsnMergeReceiveDao, AsnMergeReceive, Long> {

    private final AsnHeaderService  asnHeaderService;
    private final WarehouseService warehouseService;
    private final AsnDetailService asnDetailService;
    private final NumberGenerator numberGenerator;
    private final RemoteUserService remoteUserService;
    private final OwnerService ownerService;
    private final AsnReceiveService asnReceiveService;
    private final PassiveTagDao passiveTagDao;

    /**
     * 分页查询
     * @param condition
     * @return
     */
    public PageResult<AsnMergePageQuery> page(AsnMergePageCondition condition) {
        PageResult<AsnMergePageQuery> page = dao.page(condition);
        if (CollUtil.isNotEmpty(page.getRows())){
            List<String> list = page.getRows().stream().map(AsnMergePageQuery::getMergeNo).toList();
            List<AsnHeader> headerList = asnHeaderService.find(new ConditionRule().andIn(AsnHeader::getMergeNo, list));
            Map<String, List<AsnHeader>> listMap = headerList.stream().collect(Collectors.groupingBy(AsnHeader::getMergeNo));
            List<String> asnNoList = headerList.stream().map(AsnHeader::getAsnNo).toList();
            List<AsnDetail> detailList = asnDetailService.find(new ConditionRule().andIn(AsnDetail::getAsnNo, asnNoList));
            Map<String, List<AsnDetail>> asnNoMap = detailList.stream().collect(Collectors.groupingBy(AsnDetail::getAsnNo));
            page.getRows().forEach(item -> {
                List<AsnHeader> headers = listMap.get(item.getMergeNo());
                if (CollUtil.isNotEmpty(headers)){
                    item.setAsnNoList(headers.stream().map(AsnHeader::getAsnNo).collect(Collectors.toList()));
                    BigDecimal notQtyRcvEa = BigDecimal.ZERO;
                    for (AsnHeader header : headers) {
                        List<AsnDetail> asnDetails = asnNoMap.get(header.getAsnNo());
                        BigDecimal rcvEa = BigDecimal.ZERO;
                        for (AsnDetail asnDetail : asnDetails) {
                            if (ObjUtil.isNull(asnDetail.getQtyRcvEa()) || asnDetail.getQtyRcvEa().compareTo(BigDecimal.ZERO) < 0) {
                                asnDetail.setQtyRcvEa(BigDecimal.ZERO);
                            }
                            if (asnDetail.getQtyRcvEa().compareTo(asnDetail.getQtyPlanEa()) > 0) {
                                rcvEa = rcvEa.add(asnDetail.getQtyPlanEa());
                            }else{
                                rcvEa = rcvEa.add(asnDetail.getQtyRcvEa());
                            }
                        }
                        BigDecimal planEa = asnDetails.stream().map(AsnDetail::getQtyPlanEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal decimal = planEa.subtract(rcvEa);
                        if (decimal.compareTo(BigDecimal.ZERO) > 0) {
                            notQtyRcvEa = notQtyRcvEa.add(decimal);
                        }
                    }
                    item.setNotQtyRcvEa(notQtyRcvEa);
                }
            });

            Set<Long> collect = page.getRows().stream().map(AsnMergePageQuery::getCreator).collect(Collectors.toSet());
            UserForOuterCondition userForOuterCondition = new UserForOuterCondition();
            userForOuterCondition.setIds(collect.toArray(Long[]::new));
            ResponseData<List<UserForOuterQuery>> userList = remoteUserService.getUserList(userForOuterCondition);
            if (CollUtil.isNotEmpty(userList.getData())){
                Map<Long, UserForOuterQuery> userMap = userList.getData().stream().collect(Collectors.toMap(UserForOuterQuery::getId, Function.identity()));
                page.getRows().forEach(item -> {
                    UserForOuterQuery user = userMap.get(item.getCreator());
                    if (ObjUtil.isNotEmpty(user)){
                        item.setCreateUserName(user.getRealName());
                    }
                });
            }
        }
        return page;
    }

    /**
     * 编辑合并收货单
     * @param item
     * @return
     */
    @Transactional
    public AsnMergeItem updateAsnMerge(AsnMergeItem item) {
        AsnMergeReceive receive = findFirst(new ConditionRule().andEqual(AsnMergeReceive::getMergeNo, item.getMergeNo()));
        List<AsnHeader> oldHeader = asnHeaderService.find(new ConditionRule().andEqual(AsnHeader::getMergeNo, item.getMergeNo()));
        oldHeader.forEach(header -> header.setMergeId(null).setMergeNo(null).setIsMerge(YesNoEnum.NO.getCode()));
        asnHeaderService.batchUpdate(oldHeader);

        List<AsnHeader> headerList = asnHeaderService.find(new ConditionRule().andIn(AsnHeader::getAsnNo, item.getAsnNoList()));
        validationParam(headerList);
        initMergeReceive(headerList,item,receive,Boolean.TRUE);
        preSave(receive);
        boolean insert = update(receive);
        if (!insert){
            log.error("合并失败：AsnMergeAddItem：{}，ist<AsnHeader>：{}，AsnMergeReceive：{}", JSONUtil.toJsonStr(item)
                    ,JSONUtil.toJsonStr(headerList),JSONUtil.toJsonStr(receive));
            throw new ServiceException(AsnExceptionEnum.MERGE_ERROR);
        }
        //更新 headerList 集合
        headerList.forEach(header -> header.setIsMerge(YesNoEnum.YES.getCode()).setMergeId(receive.getId()).setMergeNo(receive.getMergeNo()));
        asnHeaderService.batchUpdate(headerList);
        return item;
    }

    /**
     * 新增合并收货单
     * @param item
     * @return
     */
    @Transactional
    public AsnMergeItem addAsnMerge(AsnMergeItem item){
        List<AsnHeader> headerList = asnHeaderService.find(new ConditionRule().andIn(AsnHeader::getAsnNo, item.getAsnNoList()));
        validationParam(headerList);
        AsnMergeReceive receive = new AsnMergeReceive();
        initMergeReceive(headerList,item,receive,Boolean.TRUE);
        preSave(receive);
        boolean insert = insert(receive);
        if (!insert){
            log.error("合并失败：AsnMergeAddItem：{}，ist<AsnHeader>：{}，AsnMergeReceive：{}", JSONUtil.toJsonStr(item)
                    ,JSONUtil.toJsonStr(headerList),JSONUtil.toJsonStr(receive));
            throw new ServiceException(AsnExceptionEnum.MERGE_ERROR);
        }
        //更新 headerList 集合
        headerList.forEach(header -> header.setIsMerge(YesNoEnum.YES.getCode()).setMergeId(receive.getId()).setMergeNo(receive.getMergeNo()));
        asnHeaderService.batchUpdate(headerList);
        return item;
    }

    /**
     * 公归初始化合并收获但数据方法
     * @param headerList 需要合并的入库单
     * @param item 请求对象
     * @param receive 封装对象
     * @param isCreateNo 时候创建合并收货单单号 TRUE = 是 FASLE = 否
     */
    public void initMergeReceive(List<AsnHeader> headerList, AsnMergeItem item, AsnMergeReceive receive, Boolean isCreateNo){
        AsnHeader first = headerList.getFirst();
        Warehouse warehouse = warehouseService.findById(first.getWarehouseId());
        //生成合并收货数据
        List<AsnDetail> asnDetails = asnDetailService.find(new ConditionRule().andIn(AsnDetail::getAsnNo, item.getAsnNoList()));
        BigDecimal qtyPlanEa = asnDetails.stream().map(AsnDetail::getQtyPlanEa).reduce(BigDecimal.ZERO, BigDecimal::add);
        String mergeNo = "";
        if (isCreateNo){
            mergeNo = numberGenerator.nextValue(IdRuleConstant.MERGE_NO);
        }else{
            mergeNo = item.getMergeNo();
        }
        receive.setReceiverOp(first.getReceiverOp())
                .setReceiverId(first.getReceiverId())
                .setMergeNo(mergeNo)
                .setStatus(ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode())
                .setWarehouseId(warehouse.getId())
                .setWarehouseName(warehouse.getWarehouseName())
                .setOrderCount(headerList.size())
                .setQtyPlanEa(qtyPlanEa)
                .setQtyRcvEa(BigDecimal.ZERO);
    }

    /**
     * 新增数据时的前置校验
     * @param headerList
     */
    private void validationParam(List<AsnHeader> headerList) {
        if (CollUtil.isEmpty(headerList)){
            throw new ServiceException(AsnExceptionEnum.ASN_NO_NOT_SUCCESS);
        }
        List<String> list = headerList.stream().map(AsnHeader::getAsnNo).toList();
        List<PassiveTag> passiveTags = passiveTagDao.find(new ConditionRule().andIn(PassiveTag::getAsnNo, list));
        if (CollUtil.isNotEmpty(passiveTags)){
            String collect = passiveTags.stream().map(PassiveTag::getAsnNo).distinct().collect(Collectors.joining(","));
            throw new ServiceException(AsnExceptionEnum.ORDER_BY_PASSIVES_NOT_MERGE_RECEIVE,collect);
        }
        String validErrorAsnNo = headerList.stream().map(AsnHeader::getAsnNo).collect(Collectors.joining("、"));
        List<AsnHeader> mergeList = headerList.stream().filter(f -> YesNoEnum.YES.getCode().equals(f.getIsMerge())).toList();
        if (CollUtil.isNotEmpty(mergeList)){
            String asnNo = mergeList.stream().map(AsnHeader::getAsnNo).collect(Collectors.joining("、"));
            throw new ServiceException(AsnExceptionEnum.ASN_NO_CZ_MERGE_DATE,asnNo);
        }
        //验证是否是同一个仓库
        long warehouseCount = headerList.stream().map(AsnHeader::getWarehouseId).distinct().count();
        if (warehouseCount > 1){
            throw new ServiceException(AsnExceptionEnum.MERGE_ERROR_ASN_WAREHOUSE_INCONSISTENT,validErrorAsnNo);
        }
        //烟瘴是否是同一个售货员
        long receiverOpCOunt = headerList.stream().map(AsnHeader::getReceiverId).distinct().count();
        if (receiverOpCOunt > 1){
            throw new ServiceException(AsnExceptionEnum.MERGE_ERROR_ASN_RCV_OP_INCONSISTENT,validErrorAsnNo);
        }
        List<AsnHeader> errorNotNewList = headerList.stream().filter(f -> !AsnStatusEnum.ASN_NEW.getCode().equals(f.getStatus())).toList();
        if (CollUtil.isNotEmpty(errorNotNewList)){
            String asnNo = errorNotNewList.stream().map(AsnHeader::getAsnNo).collect(Collectors.joining("、"));
            throw new ServiceException(AsnExceptionEnum.MERGE_ERROR_ASN_STATUS_NOT_NEW,asnNo);
        }

        List<AsnHeader> errorNotAuditList = headerList.stream().filter(f -> !AuditStatusEnum.AUDITED.getCode().equals(f.getAuditStatus())).toList();
        if (CollUtil.isNotEmpty(errorNotNewList)){
            String asnNo = errorNotAuditList.stream().map(AsnHeader::getAsnNo).collect(Collectors.joining("、"));
            throw new ServiceException(AsnExceptionEnum.MERGE_ERROR_ASN_AUDIT_NOT_NEW,asnNo);
        }

        List<AsnHeader> errorNotReveiveList = headerList.stream().filter(f -> !ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(f.getReceiveStatus())).toList();
        if (CollUtil.isNotEmpty(errorNotNewList)){
            String asnNo = errorNotReveiveList.stream().map(AsnHeader::getAsnNo).collect(Collectors.joining("、"));
            throw new ServiceException(AsnExceptionEnum.MERGE_ERROR_ASN_RECEIVE,asnNo);
        }
    }

    /**
     * 控制器统一调用方法
     * 新增/编辑
     * @param item
     * @return
     */
    public AsnMergeItem saveAsnMerge(AsnMergeItem item) {
        if (StrUtil.isEmpty(item.getMergeNo())){
            return addAsnMerge(item);
        }else{
            return updateAsnMerge(item);
        }
    }

    @Transactional
    public Boolean batchDel(AsnMergeDeleteItem item) {
        List<AsnMergeReceive> receives = find(new ConditionRule().andIn(AsnMergeReceive::getMergeNo, item.getMergeNos()));
        if(CollUtil.isEmpty(receives)){
            throw new ServiceException(AsnExceptionEnum.MERGE_DELETE_NO_ERROR);
        }
        List<AsnMergeReceive> list = receives.stream().filter(f -> !ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode().equals(f.getStatus())).toList();
        if (CollUtil.isNotEmpty(list)){
            String msg = list.stream().map(AsnMergeReceive::getMergeNo).collect(Collectors.joining("、"));
            throw new ServiceException(AsnExceptionEnum.MERGE_DELETE_NO_RCV_STATUS_ERROR,msg);
        }
        delete(receives.stream().map(ModuleBaseModel::getId).toArray(Long[]::new));
        //删除关联关系
        List<AsnHeader> headerList = asnHeaderService.find(new ConditionRule().andIn(AsnHeader::getMergeNo, item.getMergeNos()));
        if (CollUtil.isNotEmpty(headerList)){
            headerList.forEach(header -> header.setIsMerge(YesNoEnum.NO.getCode())
                    .setMergeNo(null)
                    .setMergeId(null));

            asnHeaderService.batchUpdate(headerList);
        }
        return Boolean.TRUE;
    }

    /**
     * 收货单列表详细信息查询
     * @param mergeNo 收货单号
     * @return
     */
    public AsnMergeInfoQuery getInfoByNo(String mergeNo) {
        AsnMergeReceive receive = findFirst(new ConditionRule().andEqual(AsnMergeReceive::getMergeNo, mergeNo));
        AsnMergeInfoQuery result = new AsnMergeInfoQuery();
        BeanUtils.copyProperties(receive, result);
        UserForOuterCondition condition = new UserForOuterCondition();
        condition.setId(receive.getCreator());
        ResponseData<UserQuery> data = remoteUserService.getByUserId(condition);
        if (data.getSuccess()){
            result.setCreatorName(data.getData().getRealName());
        }
        return result;
    }

    public List<AsnDetailInfo> getSkuInfoList(AsnDetailInfoByPaStatusCondition condition) {
        ConditionRule conditionRule = new ConditionRule().andEqual(AsnHeader::getMergeNo, condition.getMergeNo());
        List<AsnHeader> headers = asnHeaderService.find(conditionRule);
        condition.setAsnNoList(headers.stream().map(AsnHeader::getAsnNo).collect(Collectors.toList()));
        if (CollUtil.isNotEmpty(headers)){
            List<Long> ownerIds = headers.stream().map(AsnHeader::getOwnerId).toList();
            List<Owner> ownerList = ownerService.findByIds(ownerIds.toArray(Long[]::new));
            Map<Long, Owner> ownerMap = ownerList.stream().collect(Collectors.toMap(ModuleBaseModel::getId, Function.identity()));
            Map<String, AsnHeader> headerMap = headers.stream().collect(Collectors.toMap(AsnHeader::getAsnNo, Function.identity()));
            List<AsnDetailInfo> asnDetailInfos = asnDetailService.getAsnDetailInfoByPaStatus(condition);
            for (AsnDetailInfo info : asnDetailInfos) {
                AsnHeader header = headerMap.get(info.getAsnNo());
                Owner owner = ownerMap.get(header.getOwnerId());
                info.setOwnerName(owner.getOwnerName());
                if (ObjUtil.isNull(info.getQtyRcvEa()) || info.getQtyRcvEa().compareTo(BigDecimal.ZERO) < 0) {
                    info.setQtyRcvEa(BigDecimal.ZERO);
                }

            }
            return asnDetailInfos;
        }
        return new ArrayList<>();
    }

    public void handleUpdateReceiveStatus(AsnMergeStatusUpdateEventBusItem item) {
        AsnMergeReceive receives = dao.findFirst(new ConditionRule().andEqual(AsnMergeReceive::getMergeNo, item.getMergeNo()));
        receives.setStatus(item.getStatus());
        update(receives);
    }

    public void handleUpdateReceive(AsnReceiptDetailItem item) {
        //根据单号查询出当前单时候是合并收货
        AsnHeader asnHeader = asnHeaderService.findFirst(new ConditionRule().andEqual(AsnHeader::getAsnNo, item.getAsnNo()));
        if (asnHeader == null || YesNoEnum.NO.getCode().equals(asnHeader.getIsMerge())) {
            return;
        }
        List<AsnHeader> mergeAsnHeaderList = asnHeaderService.find(new ConditionRule().andEqual(AsnHeader::getMergeNo, asnHeader.getMergeNo()));
        /*List<String> asnNoList = mergeAsnHeaderList.stream().map(AsnHeader::getAsnNo).toList();
        List<AsnDetail> asnDetails = asnDetailService.find(new ConditionRule().andIn(AsnDetail::getAsnNo, asnNoList));
        BigDecimal qtyPlanEa = asnDetails.stream().map(AsnDetail::getQtyPlanEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal qtyRcvEa = asnDetails.stream().map(AsnDetail::getQtyRcvEa).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        Long mergeId = asnHeader.getMergeId();*/
        AsnMergeReceive receive = findById(asnHeader.getMergeId());
        log.info("【单体-更新合并数据收获状态和EA数监听器】 原来数据集：{}", JSONUtil.toJsonStr(receive));
        //验证整体收货状态
        List<String> statusList = mergeAsnHeaderList.stream()
                .map(AsnHeader::getReceiveStatus)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.info("合并单 {} 的状态列表: {}", asnHeader.getMergeNo(), statusList);

        if (statusList.isEmpty()) {
            receive.setStatus(ReceiveStatusEnum.NOT_RECEIVED_GOODS.getCode());
        } else if (statusList.stream().allMatch(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode()::equals)) {
            receive.setStatus(ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode());
        } else {
            receive.setStatus(ReceiveStatusEnum.PART_RECEIVED_GOODS.getCode());
        }
        if (ObjUtil.isNull(receive.getQtyRcvEa())) {
            receive.setQtyRcvEa(BigDecimal.ZERO);
        }
        // 6. 更新合并收货记录
        receive.setQtyRcvEa(receive.getQtyRcvEa().add(item.getQtyRcvEa()));
        log.info("【单体-更新合并数据收获状态和EA数监听器】 更新数据集：{}", JSONUtil.toJsonStr(receive));
        update(receive);
        log.info("【单体-更新合并数据收获状态和EA数监听器】 更新后数据集：{}", JSONUtil.toJsonStr(receive));
    }

    /**
     * 批量收货
     * @param item
     * @return
     */
    public Boolean receiptConfirmationBatch(BatchRcvItem item) {
        //查出所有入库单单号 去除已取消和以关闭状态的收货单
        List<AsnHeader> headerList = asnHeaderService.find(new ConditionRule().andIn(AsnHeader::getMergeNo, item.getMergeNoList()).andEqual(AsnHeader::getStatus, AsnStatusEnum.ASN_NEW.getCode()));
        if (CollUtil.isNotEmpty(headerList)){
            //过滤掉完全收货数据
            List<AsnHeader> notRcvHeaderList = headerList.stream().filter(f -> !ReceiveStatusEnum.ALL_RECEIVED_GOODS.getCode().equals(f.getReceiveStatus())).toList();
            if (CollUtil.isEmpty(notRcvHeaderList)){
                throw new ServiceException(AsnExceptionEnum.RECEIVE_STATUS_ALL_RECEIVED,headerList.stream().map(AsnHeader::getAsnNo).collect(Collectors.joining("、")));
            }
            headerList = notRcvHeaderList;
            ReceiptConfirmationItem receiptConfirmationItem = new ReceiptConfirmationItem();
            List<ReceiptConfirmationItem.ReceiptDetail> list = headerList.stream().map(m -> {
                ReceiptConfirmationItem.ReceiptDetail receiptDetail = new ReceiptConfirmationItem.ReceiptDetail();
                receiptDetail.setAsnNo(m.getAsnNo());
                return  receiptDetail;
            }).toList();
            receiptConfirmationItem.setReceiptDetails(list);
            return asnReceiveService.receiptConfirmationBatch(receiptConfirmationItem);
        }
        return Boolean.TRUE;
    }

    /**
     * 收货记录
     */
    public List<AsnReceiveDetailQuery> getReceiptConfirmationInfo(String mergeNo) {
        List<AsnHeader> headerList = asnHeaderService.find(new ConditionRule().andIn(AsnHeader::getMergeNo, mergeNo));
        if (CollUtil.isNotEmpty(headerList)){
            List<String> list = headerList.stream().map(AsnHeader::getAsnNo).toList();
            List<AsnDetail> asnDetails = asnDetailService.find(new ConditionRule().andIn(AsnDetail::getAsnNo, list));
            List<Long> asnDetailIds = asnDetails.stream().map(ModuleBaseModel::getId).toList();
            AsnReceiveDetailCondition detailCondition = new AsnReceiveDetailCondition();
            detailCondition.setAsnDetailIds(asnDetailIds);
            detailCondition.setIsMerge(YesNoEnum.YES.getCode());
            return asnReceiveService.queryListByAsnNoAndSkuCode(detailCondition);
        }
        return null;
    }

    /**
     * 异步处理EventBus数据
     * @param item
     */
    public void handleUpdateMergeDate(AsnMergeReceive item) {
        if (EmptyUtil.isNotEmpty(item)){
            if (item.getOrderCount() > 0){
                update(item);
            }else{
                delete(item.getId());
            }
        }
    }
}
