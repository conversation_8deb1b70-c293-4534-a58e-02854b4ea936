package com.chinaservices.wms.module.stock.mutli.dao;

import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.module.asn.qc.domain.QcLocSnDetailCondition;
import com.chinaservices.wms.module.asn.qc.domain.SnDetailItem;
import com.chinaservices.wms.module.stock.adjust.domain.AdjustLocSnDetailCondition;
import com.chinaservices.wms.module.stock.adjust.domain.AdjustSnDetailItem;
import com.chinaservices.wms.module.stock.loc.domain.LocSnDetailCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocSnItem;
import com.chinaservices.wms.module.stock.multi.domain.InvLotLocSnPageCondition;
import com.chinaservices.wms.module.stock.multi.domain.InvLotLocSnQuery;
import com.chinaservices.wms.module.stock.multi.domain.SnSkuPageCondition;
import com.chinaservices.wms.module.stock.multi.domain.SnSkuPageQuery;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLocSn;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class InvLotLocSnDao extends ModuleBaseDaoSupport<InvLotLocSn, Long> {
    public PageResult<InvLotLocSnQuery> snStockPage(InvLotLocSnPageCondition condition) {
        return sqlExecutor.page(InvLotLocSnQuery.class, "invLocLotSn_query_snStockPage", condition);
    }

    /**
     * 序列号集合
     *
     * @param condition 条件
     * @return {@link List }<{@link InvLotLocSnQuery }>
     */
    public List<InvLotLocSnQuery> snStockList(InvLotLocSnPageCondition condition)
    {
        return sqlExecutor.find(InvLotLocSnQuery.class, "invLocLotSn_query_snStockList", condition);
    }

    public List<InvLotLocSnQuery> exportSnStock(InvLotLocSnPageCondition condition) {
        return sqlExecutor.find(InvLotLocSnQuery.class, "invLocLotSn_query_snStockPage", condition);
    }


    /**
     * 查询由仓库ID和区ID
     *
     * @param condition 条件
     * @return {@link List }<{@link InvLotLocSn }>
     */
    public List<InvLotLocSn> findByWarehouseIdAndZoneId(InvLotLocSnPageCondition condition) {
        return sqlExecutor.find(InvLotLocSn.class, "invLotLocSn_query_findByWarehouseIdAndZoneId", condition);
    }

    public List<AdjustSnDetailItem> getSnStockList(AdjustLocSnDetailCondition condition){
        return sqlExecutor.find(AdjustSnDetailItem.class, "invLocLotSn_query_snDetailList",condition);
    }

    public List<SnDetailItem> getSnStockList(QcLocSnDetailCondition condition){
        return sqlExecutor.find(SnDetailItem.class, "invLocLotSn_query_snDetailList",condition);
    }

    /**
     *@Description 根据箱码或序列号查询
     *@Param condition
     *@Return * {@link List< InvLotLocSn> }
     *@Date 2025/3/24 17:29
     *<AUTHOR>
     **/
    public List<LocSnItem> findListByKey(LocSnDetailCondition condition) {
        return sqlExecutor.find(LocSnItem.class, "invLocLotSn_query_findListByKey", condition);
    }

    /**
     *@Description 库存报损序列号商品分页查询
     *@Param condition
     *@Return * {@link PageResult< SnSkuPageQuery> }
     *@Date 2025/7/15 14:40
     *<AUTHOR>
     **/
    public PageResult<SnSkuPageQuery> getSnSkuPage(SnSkuPageCondition condition) {
        return sqlExecutor.page(SnSkuPageQuery.class, "invLocLotSn_query_snSkuPage", condition);
    }
}
