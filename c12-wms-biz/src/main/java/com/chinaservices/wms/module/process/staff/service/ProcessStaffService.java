package com.chinaservices.wms.module.process.staff.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.auth.module.user.domain.UserForOuterCondition;
import com.chinaservices.auth.module.user.domain.UserForOuterQuery;
import com.chinaservices.auth.module.user.feign.RemoteUserService;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.enums.process.ProcessStaffStatusEnum;
import com.chinaservices.wms.common.exception.ProcessExceptionEnum;
import com.chinaservices.wms.module.process.staff.dao.ProcessStaffDao;
import com.chinaservices.wms.module.process.staff.domain.*;
import com.chinaservices.wms.module.process.staff.model.ProcessStaff;
import com.chinaservices.wms.module.process.staff.model.ProcessStaffCertificate;
import com.chinaservices.wms.module.process.staff.model.ProcessStaffCertificateFile;
import com.chinaservices.wms.module.process.staff.model.ProcessStaffTrain;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Cyran.Chen
 * @Date: 2025/4/14
 * @Description: 加工人员服务层
 */
@Service
public class ProcessStaffService extends ModuleBaseServiceSupport<ProcessStaffDao, ProcessStaff, Long> {

    @Autowired
    private NumberGenerator numberGenerator;
    @Autowired
    private ProcessStaffCertificateService psCertificateService;
    @Autowired
    private ProcessStaffCertificateFileService psCertificateFileService;
    @Autowired
    private ProcessStaffTrainService psTrainService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 分页查询加工人员
     * @param pageCondition 查询条件
     * @return PageResult<ProcessStaffPageQuery>
     */
    public PageResult<ProcessStaffPageQuery> page(ProcessStaffPageCondition pageCondition) {
        PageResult<ProcessStaffPageQuery> pageResult = dao.page(pageCondition);
        // 1. 获取所有的创建人id并转换为数组
        List<Long> creatorList = pageResult.getRows().stream().map(ProcessStaffPageQuery::getCreator).toList();
        Long[] creatorIds = ArrayUtil.toArray(creatorList, Long.class);
        if(ArrayUtil.isNotEmpty(creatorIds)) {
            // 2. 通过ids远程请求获取所有用户响应
            UserForOuterCondition userForOuterCondition = new UserForOuterCondition();
            userForOuterCondition.setIds(creatorIds);
            ResponseData<List<UserForOuterQuery>> responseData = remoteUserService.getUserList(userForOuterCondition);
            if(responseData.getSuccess()){
                // 3. 获取所有的用户列表并转换为map格式
                List<UserForOuterQuery> queryList = responseData.getData();
                Map<Long, UserForOuterQuery> queryMap = queryList.stream().collect(
                        Collectors.toMap(UserForOuterQuery::getId, Function.identity())
                );
                // 4. 为所有的创建人赋值
                for (ProcessStaffPageQuery pageQuery : pageResult.getRows()) {
                    pageQuery.setCreatorName(queryMap.get(pageQuery.getCreator()).getRealName());
                }
            }
        }
        // 5. 判断是否可以删除
        for (ProcessStaffPageQuery pageQuery : pageResult.getRows()) {
            if(StrUtil.equals(ProcessStaffStatusEnum.DISABLED.getCode(), pageQuery.getStatus())){
                pageQuery.setCanDelete(true);
            }
        }
        return pageResult;
    }

    /**
     * 根据id查询加工人员
     * @param id 主键
     * @return ProcessStaffPageQuery
     */
    public ProcessStaffPageQuery getById(Long id) {
        // 1.校验、获取加工人员信息
        if(ObjUtil.isNull(id)){
            throw new ServiceException(ProcessExceptionEnum.ID_NOT_EMPTY);
        }
        ProcessStaff processStaff = dao.findById(id);
        // 2.校验加工人员信息是否真实存在
        if(ObjUtil.isNull(processStaff)){
            throw new ServiceException(ProcessExceptionEnum.NOT_EXIST, "加工人员");
        }
        ProcessStaffPageQuery pageQuery = new ProcessStaffPageQuery();
        BeanUtil.copyProperties(processStaff, pageQuery);
        // 3.获取培训记录信息
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(ProcessStaffTrain::getStaffId, processStaff.getId());
        List<ProcessStaffTrainPageQuery> psTrainList = psTrainService.find(ProcessStaffTrainPageQuery.class, conditionRule);
        pageQuery.setTrainList(psTrainList);
        // 4.获取资质证书信息
        conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(ProcessStaffCertificate::getStaffId, processStaff.getId());
        List<ProcessStaffCertificatePageQuery> psCertificateList = psCertificateService.find(ProcessStaffCertificatePageQuery.class, conditionRule);
        // 4.1 提取所有资质证书id
        List<Long> certificateIdList = psCertificateList.stream().map(ProcessStaffCertificatePageQuery::getId).toList();
        if(CollectionUtil.isNotEmpty(certificateIdList)){
            // 4.2 获取资质证书附件信息
            conditionRule = ConditionRule.getInstance();
            conditionRule.andIn(ProcessStaffCertificateFile::getBusinessId, certificateIdList);
            List<ProcessStaffCertificateFilePageQuery> psCertificateFileList = psCertificateFileService.find(ProcessStaffCertificateFilePageQuery.class, conditionRule);
            // 4.3 将附件按 BusinessId 分组
            Map<String, List<ProcessStaffCertificateFilePageQuery>> fileMap = psCertificateFileList.stream().collect(
                    Collectors.groupingBy(ProcessStaffCertificateFilePageQuery::getBusinessId)
            );
            // 4.4 关联附件到证书（只需遍历一次证书列表）
            for (ProcessStaffCertificatePageQuery certificate : psCertificateList) {
                certificate.setCertificateFileList(fileMap.getOrDefault(String.valueOf(certificate.getId()), Collections.emptyList()));
            }
        }
        pageQuery.setCertificateList(psCertificateList);
        // 5. 获取仓库名称
        Warehouse warehouse = warehouseService.getById(pageQuery.getWarehouseId());
        pageQuery.setWarehouseName(warehouse.getWarehouseName());
        return pageQuery;
    }

    /**
     * 新增或编辑加工人员
     * @param tableItem 入参
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdate(ProcessStaffTableItem tableItem){
        // 校验入参不能为空
        if(ObjUtil.isNull(tableItem)){
            throw new ServiceException(ProcessExceptionEnum.PARAMS_NOT_EMPTY, "入参");
        }
        Warehouse warehouse = warehouseService.getById(tableItem.getWarehouseId());
        if(ObjUtil.isNull(warehouse)){
            throw new ServiceException(ProcessExceptionEnum.NOT_EXIST, "所属仓库");
        }
        ProcessStaff processStaff = new ProcessStaff();
        // 新增加工人员
        if(ObjUtil.isEmpty(tableItem.getId())){
            BeanUtil.copyProperties(tableItem, processStaff);
            processStaff.setStaffNo(numberGenerator.nextValue(IdRuleConstant.PROCESS_STAFF_NO));
            processStaff.setStatus(ProcessStaffStatusEnum.ENABLED.getCode());
            preSave(processStaff);
            boolean saveResult = dao.saveOrUpdate(processStaff);
            // 新增资质证书信息
            Boolean certificateResult = psCertificateService.save(tableItem.getCertificateList(), processStaff.getId());
            // 新增培训记录信息
            Boolean trainResult = psTrainService.save(tableItem.getTrainList(), processStaff.getId());
            return BooleanUtil.and(saveResult, certificateResult, trainResult);
        }
        // 更新加工人员
        else{
            processStaff = dao.findById(tableItem.getId());
            // 校验加工人员是否真实存在
            if(ObjUtil.isNull(processStaff)){
                throw new ServiceException(ProcessExceptionEnum.NOT_EXIST, "加工人员");
            }
            processStaff.setStaffName(tableItem.getStaffName());
            processStaff.setWarehouseId(tableItem.getWarehouseId());
            processStaff.setPosition(tableItem.getPosition());
            processStaff.setPhone(tableItem.getPhone());
            processStaff.setAddress(tableItem.getAddress());
            processStaff.setEmergencyContact(tableItem.getEmergencyContact());
            processStaff.setEmergencyPhone(tableItem.getEmergencyPhone());
            processStaff.setSkillType(tableItem.getSkillType());
            preSave(processStaff);
            boolean updateResult = dao.saveOrUpdate(processStaff);
            // 更新资质证书信息
            Boolean certificateResult = psCertificateService.edit(tableItem.getCertificateList(), processStaff.getId());
            // 更新培训记录信息
            Boolean trainResult = psTrainService.edit(tableItem.getTrainList(), processStaff.getId());
            return BooleanUtil.and(updateResult, certificateResult, trainResult);
        }
    }

    /**
     * 批量删除加工人员
     * @param ids 入参
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDelete(Long[] ids){
        if(ArrayUtil.isEmpty(ids)){
            throw new ServiceException(ProcessExceptionEnum.IDS_NOT_EMPTY);
        }
        List<ProcessStaff> processStaffList = dao.findByIds(ids);
        if(ObjUtil.notEqual(processStaffList.size(), ids.length)){
            throw new ServiceException(ProcessExceptionEnum.NOT_EXIST, "加工人员");
        }
        // 校验加工人员状态是否为停用
        if(processStaffList.stream().anyMatch(item -> StrUtil.equals(item.getStatus(), ProcessStaffStatusEnum.ENABLED.getCode()))) {
            List<String> staffNoList = processStaffList.stream().filter(
                    item -> StrUtil.equals(item.getStatus(), ProcessStaffStatusEnum.ENABLED.getCode())
            ).map(ProcessStaff::getStaffNo).toList();
            throw new ServiceException(ProcessExceptionEnum.DELETE_STATUS_ERROR, staffNoList);
        }
        dao.delete(ids);
        // 1. 获取所有加工人员的id
        List<Long> staffIdList = processStaffList.stream().map(ProcessStaff::getId).toList();
        // 2. 删除资质证书相关内容
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(ProcessStaffCertificate::getStaffId, staffIdList);
        // 2.1 删除前获取所有的资质证书idList
        List<ProcessStaffCertificate> psCertificateList = psCertificateService.find(conditionRule);
        List<Long> certificateIdList = psCertificateList.stream().map(ProcessStaffCertificate::getId).toList();
        psCertificateService.delete(conditionRule);
        // 3. 删除资质证书附件相关内容
        conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(ProcessStaffCertificateFile::getBusinessId, certificateIdList);
        psCertificateFileService.delete(conditionRule);
        // 4. 删除培训记录相关内容
        conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(ProcessStaffTrain::getStaffId, staffIdList);
        psTrainService.delete(conditionRule);
        return Boolean.TRUE;
    }

    /**
     * 批量启用或禁用工作人员
     * @param ids 入参
     * @return Boolean
     */
    public Boolean updateStatusByIds(Long[] ids, String status) {
        if(ArrayUtil.isEmpty(ids)){
            throw new ServiceException(ProcessExceptionEnum.IDS_NOT_EMPTY);
        }
        List<ProcessStaff> processStaffList = dao.findByIds(ids);
        if(ObjUtil.notEqual(processStaffList.size(), ids.length)){
            throw new ServiceException(ProcessExceptionEnum.NOT_EXIST, "加工人员");
        }
        if(processStaffList.stream().anyMatch(item -> StrUtil.equals(item.getStatus(), status))) {
            List<String> staffNoList = processStaffList.stream().filter(
                    item -> StrUtil.equals(item.getStatus(), status)
            ).map(ProcessStaff::getStaffNo).toList();
            if(StrUtil.equals(ProcessStaffStatusEnum.ENABLED.getCode(), status)){
                throw new ServiceException(ProcessExceptionEnum.ENABLE_STATUS_ERROR, staffNoList);
            }
            else{
                throw new ServiceException(ProcessExceptionEnum.DISABLE_STATUS_ERROR, staffNoList);
            }
        }
        for (ProcessStaff processStaff : processStaffList) {
            processStaff.setStatus(status);
            preSave(processStaff);
        }
        return dao.batchUpdate(processStaffList) > 0;
    }
}
