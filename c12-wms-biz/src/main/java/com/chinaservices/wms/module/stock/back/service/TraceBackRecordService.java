package com.chinaservices.wms.module.stock.back.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.module.stock.back.dao.TraceBackRecordDao;
import com.chinaservices.wms.module.stock.back.domain.TraceBackRecordPageCondition;
import com.chinaservices.wms.module.stock.back.domain.TraceBackRecordQuery;
import com.chinaservices.wms.module.stock.back.execute.TraceBackExecute;
import com.chinaservices.wms.module.stock.back.factory.TraceBackStrategyFactory;
import com.chinaservices.wms.module.stock.back.model.CsSnTraceBackRecord;
import com.chinaservices.wms.module.stock.back.model.TraceBackRecordItem;
import com.chinaservices.wms.module.stock.multi.domain.InvLotLocSkuSnBoxQuery;
import com.chinaservices.wms.module.stock.multi.domain.InvLotLocSnNoBoxCodeQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> BusinessType 执行
 */
@Service
@Slf4j
public class TraceBackRecordService extends ModuleBaseServiceSupport<TraceBackRecordDao, CsSnTraceBackRecord,Long> {

    @Autowired
    private TraceBackStrategyFactory strategyFactory;

    /**
     * 执行序列号回溯日志记录公共方法
     * @param item
     */
    public void execute(TraceBackRecordItem item) {
        long threadToken = IdUtil.getSnowflakeNextId();
        item.setThreadId(threadToken);
        // 非阻塞提交任务
        strategyFactory.executeAsync(item,threadToken)
                .whenComplete((result, ex) -> {
                    TraceBackExecute strategy = strategyFactory.getStrategy(item.getBusinessType());
                    strategy.delThreadToken(threadToken);
                    if (ex != null) {
                        // 异常情况
                        log.error(StrUtil.format("处理失败: {}, 错误信息：{}",
                                item.getBusinessNo(), ex.getMessage()), ex);
                    } else {
                        log.info("处理成功: {}, 返回结果：{}", threadToken, JSONUtil.toJsonStr(item));
                    }
                });
    }

    /**
     * 执行序列号回溯日志删除公共方法
     * businessNo\businessType\skuCodeList 三个参数为必填参数
     * @param item
     */
    public void executeDel(TraceBackRecordItem item) {
        // 非阻塞提交任务
        strategyFactory.executeDelAsync(item)
                .whenComplete((result, ex) -> {
                    if (ex != null) {
                        // 异常情况
                        log.error(StrUtil.format("处理失败: {}, 错误信息：{}",
                                item.getBusinessNo(), ex.getMessage()), ex);
                    } else {
                        log.info("处理成功 返回结果：{}", JSONUtil.toJsonStr(item));
                    }
                });
    }

    /**
     * 序列号回溯日志记录分页列表查询
     *
     * @param pageCondition 分页条件
     * @return TraceBackRecordQuery
     */
    public PageResult<TraceBackRecordQuery> page(TraceBackRecordPageCondition pageCondition) {
        PageResult<TraceBackRecordQuery> page = dao.page(pageCondition);
        if (EmptyUtil.isNotEmpty(page.getRows())) {
            page.getRows().forEach(item -> {
                List<String> strings = JSONArray.parseArray(item.getBoxCode(), String.class);
                if (EmptyUtil.isNotEmpty(strings)) {
                    strings.remove("无箱码");
                }
                if (EmptyUtil.isNotEmpty(strings)) {
                    item.setBoxCode(String.join(",", strings));
                } else {
                    item.setBoxCode(null);
                }
            });
        }
        return page;
    }

    /**
     * 序列号追溯记录详情
     *
     * @param id 日志id
     * @return {@link CsSnTraceBackRecord} 追溯记录详情
     */
    public List<InvLotLocSkuSnBoxQuery> querySnById(Long id) {
        // 查询序列号追溯记录
        CsSnTraceBackRecord record = dao.findById(id);
        String skuCode = record.getSkuCode();
        String skuName = record.getSkuName();
        String snList = record.getSnList();
        List<InvLotLocSnNoBoxCodeQuery> list = JSONArray.parseArray(snList, InvLotLocSnNoBoxCodeQuery.class);
        // 组装数据
        if (EmptyUtil.isNotEmpty(list)) {
            // 列表
            List<InvLotLocSkuSnBoxQuery> result = new ArrayList<>();
            list.forEach(item -> {
                List<String> soNoList = item.getSnNoList();
                if (EmptyUtil.isNotEmpty(soNoList)) {
                    soNoList.forEach(sn -> {
                        InvLotLocSkuSnBoxQuery query = new InvLotLocSkuSnBoxQuery();
                        query.setSkuCode(skuCode);
                        query.setSkuName(skuName);
                        query.setBoxNo(item.getBoxCode());
                        query.setSnNo(sn);
                        result.add(query);
                    });
                }
            });
            return result;
        }
        return new ArrayList<>();
    }
}
