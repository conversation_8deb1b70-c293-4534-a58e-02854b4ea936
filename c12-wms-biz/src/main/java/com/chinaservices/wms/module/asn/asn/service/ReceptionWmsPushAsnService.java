package com.chinaservices.wms.module.asn.asn.service;

import com.alibaba.fastjson.JSON;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.enums.asn.AsnOrderSourceEnum;
import com.chinaservices.wms.common.enums.asn.AsnTypeEnum;
import com.chinaservices.wms.common.exception.AsnExceptionEnum;
import com.chinaservices.wms.module.asn.asn.domain.AsnDetailItem;
import com.chinaservices.wms.module.asn.asn.domain.AsnHeaderItem;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.basic.cspackage.model.Package;
import com.chinaservices.wms.module.basic.cspackage.model.PackageUnit;
import com.chinaservices.wms.module.basic.cspackage.service.PackageService;
import com.chinaservices.wms.module.basic.cspackage.service.PackageUnitService;
import com.chinaservices.wms.module.basic.owner.model.Owner;
import com.chinaservices.wms.module.basic.owner.service.OwnerService;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 接收wms推送入库单
 */
@Slf4j
@Service
public class ReceptionWmsPushAsnService {

    @Autowired
    public AsnHeaderService asnHeaderService;

    @Autowired
    public OwnerService ownerService;

    @Autowired
    public WarehouseService warehouseService;

    @Autowired
    public SkuService skuService;

    @Autowired
    public PackageService packageService;

    @Autowired
    public PackageUnitService packageUnitService;

    /**
     * 超燃子wms推送入库单接收
     *
     * @param asnHeaderItem 参数
     */
    public void pushWmsSave(AsnHeaderItem asnHeaderItem) {
        // 设置租户
        SessionUserInfo sessionUserInfo = SessionContext.getSessionUserInfo();
        sessionUserInfo.setTenancy(10086L);
        sessionUserInfo.setCompanyId(10086L);
        sessionUserInfo.addAttribute("orgIdentifier","0001");

        log.info("<接收超燃子推送入库单参数>{}", JSON.toJSONString(asnHeaderItem));
        if (EmptyUtil.isEmpty(asnHeaderItem.getLogisticNo())) {
            throw new ServiceException(AsnExceptionEnum.LOGISTIC_NO_NOT_EMPTY);
        }

        AsnHeader first = asnHeaderService.findFirst(new ConditionRule()
                .andEqual(AsnHeader::getLogisticNo, asnHeaderItem.getLogisticNo()));
        if (EmptyUtil.isNotEmpty(first)) {
            throw new ServiceException(AsnExceptionEnum.LOGISTIC_NO_PUSHED_NOT_PUSH);
        }

        // 货主参数校验
        if (EmptyUtil.isEmpty(asnHeaderItem.getOwnerCode())) {
            throw new ServiceException(AsnExceptionEnum.OWEN_ID_NOT_EMPTY);
        }
        // 仓库参数校验
        if (EmptyUtil.isEmpty(asnHeaderItem.getWarehouseCode())) {
            throw new ServiceException(AsnExceptionEnum.WAREHOUSE_ID_NOT_EMPTY);
        }
        // 商品参数校验
        List<AsnDetailItem> asnDetailItemList = asnHeaderItem.getAsnDetailItemList();
        List<String> skuCodeList = asnDetailItemList.stream().map(AsnDetailItem::getSkuCode).filter(EmptyUtil::isNotEmpty).distinct().toList();
        if (EmptyUtil.isEmpty(skuCodeList)) {
            throw new ServiceException(AsnExceptionEnum.SKU_ID_NOT_EMPTY);
        }
        // 包装参数校验
        for (AsnDetailItem detailItem : asnDetailItemList) {
            if (EmptyUtil.isEmpty(detailItem.getPackageCode())) {
                throw new ServiceException(AsnExceptionEnum.PACKAGE_ID_NOT_EMPTY);
            }
            // 包装单位参数校验
            if (EmptyUtil.isEmpty(detailItem.getPackageUnitCode())) {
                throw new ServiceException(AsnExceptionEnum.PACKAGE_UNIT_ID_NOT_EMPTY);
            }
        }

        // 货主信息校验
        Owner owner = ownerService.findFirst(new ConditionRule().andEqual(Owner::getUpstreamOwnerCode, asnHeaderItem.getOwnerCode()));
        if (EmptyUtil.isEmpty(owner)) {
            throw new ServiceException(AsnExceptionEnum.OWEN_INFO_NOT_EMPTY);
        }
        // 仓库信息校验
        Warehouse warehouse = warehouseService.findFirst(new ConditionRule().andEqual(Warehouse::getUpWarehouseCode, asnHeaderItem.getWarehouseCode()));
        if (EmptyUtil.isEmpty(warehouse)) {
            throw new ServiceException(AsnExceptionEnum.WAREHOUSE_INFO_NOT_EMPTY);
        }
        // 商品信息校验
        List<Sku> skuList = skuService.find(new ConditionRule().andIn(Sku::getUpSkuCode, skuCodeList));
        if (EmptyUtil.isEmpty(skuList) || skuList.size() < skuCodeList.size()) {
            throw new ServiceException(AsnExceptionEnum.SKU_INFO_NOT_EMPTY);
        }
        Map<String, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getUpSkuCode, v -> v));

        // 包装信息校验
        List<String> packageCodeList = asnDetailItemList.stream().map(AsnDetailItem::getPackageCode).filter(EmptyUtil::isNotEmpty).distinct().toList();
        List<Package> packageList = packageService.find(new ConditionRule().andIn(Package::getUpstreamPackageCode, packageCodeList));
        if (EmptyUtil.isEmpty(packageList) || packageList.size() < packageCodeList.size()) {
            throw new ServiceException(AsnExceptionEnum.PACKAGE_INFO_NOT_EMPTY);
        }
        List<Long> packageIdList = packageList.stream().map(Package::getId).filter(EmptyUtil::isNotEmpty).distinct().toList();
        Map<String, Package> packageMap = packageList.stream().collect(Collectors.toMap(Package::getUpstreamPackageCode, v -> v));

        // 包装单位信息校验
        List<String> packageUnitCodeList = asnDetailItemList.stream().map(AsnDetailItem::getPackageUnitCode).filter(EmptyUtil::isNotEmpty).distinct().toList();
        List<PackageUnit> unitList = packageUnitService.find(new ConditionRule().andIn(PackageUnit::getCode, packageUnitCodeList).andIn(PackageUnit::getPackageId, packageIdList));
        if (EmptyUtil.isEmpty(unitList) || unitList.size() < packageIdList.size()) {
            throw new ServiceException(AsnExceptionEnum.PACKAGE_INFO_NOT_EMPTY);
        }
        Map<String, PackageUnit> unitMap = unitList.stream().collect(Collectors.toMap(packageUnit -> packageUnit.getPackageId() + packageUnit.getCode(), v -> v));

        // 赋值
        asnHeaderItem.setOwnerId(owner.getId());
        asnHeaderItem.setOwnerCode(owner.getOwnerCode());
        asnHeaderItem.setWarehouseId(warehouse.getId());
        asnHeaderItem.setWarehouseCode(warehouse.getWarehouseCode());
        asnDetailItemList.forEach(asnDetailItem -> {
            // 商品
            Sku sku = skuMap.get(asnDetailItem.getSkuCode());
            asnDetailItem.setSkuId(sku.getId());
            asnDetailItem.setSkuCode(sku.getSkuCode());
            // 包装
            Package aPackage = packageMap.get(asnDetailItem.getPackageCode());
            asnDetailItem.setPackageId(aPackage.getId());
            asnDetailItem.setPackageCode(aPackage.getCode());
            // 包装单位
            PackageUnit packageUnit = unitMap.get(aPackage.getId() + asnDetailItem.getPackageUnitCode());
            asnDetailItem.setPackageUnitId(packageUnit.getId());
            asnDetailItem.setPackageUnitCode(packageUnit.getCode());
            asnDetailItem.setPackageUnitName(packageUnit.getName());
        });
        // 订单来源--WMS推送
        asnHeaderItem.setOrderSource(AsnOrderSourceEnum.WMS_PUSH.getCode());
        // 订单类型数据转换
        if ("CI".equals(asnHeaderItem.getAsnType())) {
            asnHeaderItem.setAsnType(AsnTypeEnum.GRN.getCode());
        } else if ("PI".equals(asnHeaderItem.getAsnType())) {
            asnHeaderItem.setAsnType(AsnTypeEnum.PR.getCode());
        } else if ("AI".equals(asnHeaderItem.getAsnType())) {
            asnHeaderItem.setAsnType(AsnTypeEnum.STO.getCode());
        } else if ("RI".equals(asnHeaderItem.getAsnType())) {
            asnHeaderItem.setAsnType(AsnTypeEnum.RTN.getCode());
        }
        asnHeaderService.check(asnHeaderItem);
    }

}
