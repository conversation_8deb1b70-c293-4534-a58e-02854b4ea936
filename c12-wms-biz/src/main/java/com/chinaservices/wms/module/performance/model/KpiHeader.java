package com.chinaservices.wms.module.performance.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 绩效考核单实体类
 */
@Data
@Entity
@Table(name = "cs_kpi_header")
public class KpiHeader extends ModuleBaseModel {

    /**
     * 绩效账号
     */
    private String userAccount;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 岗位名称
     */
    private String positionName;

    /**
     * 岗位ID
     */
    private Long positionId;

    /**
     * 考核状态
     */
    private String kpiStatus;

    /**
     * 考核指标（JSON格式）
     */
    private String kpiIndex;

    /**
     * 考核人名称
     */
    private String assessor;

    /**
     * 考核人ID
     */
    private Long assessorId;

    /**
     * 考核时间
     */
    private Date assessorTime;

    /**
     * 考核分数
     */
    private BigDecimal assessorFraction;

    /**
     * 标识
     */
    private String sign;
}
