package com.chinaservices.wms.module.passive.task.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.util.Date;


/**
* <AUTHOR> generate
* @description 无源任务管理
* @date  2025-07-23
**/
@Entity
@Table(name = "cs_passive_task")
public class PassiveTask extends ModuleBaseModel {

	/**
	* 任务位置
	*/
	private String locationName;


	/**
	* 任务号
	*/
	private String taskNo;

	/**
	* 状态
	*/
	private String status;
	/**
	* 已经处理过的标签ids
	*/
	private String epcIds;

	/**
	* 类型
	*/
	private String type;
	/**
	 * 库区id
	 */
	private Long warehouseZoneId;
	/**
	* 任务id
	*/
	private String taskId;

	/**
	* 开始时间
	*/
	private Date beginTime;

	/**
	* 结束时间
	*/
	private Date endTime;

	/**
	* 版本号
	*/
	private Integer recVer;

	public String getLocationName() {
		return locationName;
	}

	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}

	public String getTaskNo() {
		return taskNo;
	}

	public String getEpcIds() {
		return epcIds;
	}

	public void setEpcIds(String epcIds) {
		this.epcIds = epcIds;
	}

	public void setTaskNo(String taskNo) {
		this.taskNo = taskNo;
	}

	public Long getWarehouseZoneId() {
		return warehouseZoneId;
	}

	public void setWarehouseZoneId(Long warehouseZoneId) {
		this.warehouseZoneId = warehouseZoneId;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Date getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Integer getRecVer() {
		return recVer;
	}

	public void setRecVer(Integer recVer) {
		this.recVer = recVer;
	}

}
