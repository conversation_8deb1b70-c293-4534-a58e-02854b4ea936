package com.chinaservices.wms.module.performance.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.jpa.support.rule.OrderRule;
import com.chinaservices.wms.module.performance.domain.*;
import com.chinaservices.wms.module.performance.model.KpiCycle;
import com.chinaservices.wms.module.performance.model.KpiHeader;
import com.chinaservices.wms.module.performance.model.KpiRecord;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class KpiDataAnalysisService {

    @Autowired
    private KpiHeaderService kpiHeaderService;
    @Autowired
    private KpiCycleService kpiCycleService;

    /**
     * 考核统计
     * @return
     */
    public KpiStatisticsQuery statistics(KpiRecordCondition condition) {
        List<KpiHeader> headers = kpiHeaderService.find(new ConditionRule());
        int count = headers.size();//参与考核总人数
        KpiCycle first;
        if (StrUtil.isEmpty(condition.getSign())){
            first = kpiCycleService.findFirst(new ConditionRule(), new OrderRule().addDescOrder("create_time"));
        }else{
            first = kpiCycleService.findFirst(new ConditionRule().andEqual(KpiCycle::getSign, condition.getSign()));
        }
        List<KpiHeader> headerList = kpiHeaderService.find(new ConditionRule().andEqual(KpiHeader::getSign, first.getSign()));
        int sj = CollUtil.isEmpty(headerList) ? 0 : headerList.size();//实际考核人数
        return getKpiStatisticsQuery(count, sj, headerList);
    }

    private KpiStatisticsQuery getKpiStatisticsQuery(int count, int sj, List<KpiHeader> headerList) {
        KpiStatisticsQuery query = new KpiStatisticsQuery();
        query.setExamineUserCount(count);
        query.setExamineUserActualCount(sj);
        //计算headerList分数区分 8 - 10分 6 - 8分 0 - 6分,字段使用assessorFraction字段
        if (CollUtil.isNotEmpty(headerList)){
            Integer max = 0;//8 - 10分
            Integer centre = 0;//6 - 8分
            Integer min = 0;//0 - 6分
            for (KpiHeader header : headerList) {
                if (header.getAssessorFraction().compareTo(new BigDecimal("8")) >= 0 && header.getAssessorFraction().compareTo(new BigDecimal("10")) <= 0){
                    max++;
                }else if (header.getAssessorFraction().compareTo(new BigDecimal("6")) >= 0 && header.getAssessorFraction().compareTo(new BigDecimal("8")) <= 0){
                    centre++;
                }else {
                    min++;
                }
            }
            query.setScoreEightToTen(new KpiAccountForItem()
                    .setActualCount(max)
                    .setProportion(NumberUtil.decimalFormat("#.##%",NumberUtil.div(new BigDecimal(max), new BigDecimal(sj)))));

            query.setScoreSixToEight(new KpiAccountForItem()
                    .setActualCount(centre)
                    .setProportion(NumberUtil.decimalFormat("#.##%",NumberUtil.div(new BigDecimal(centre), new BigDecimal(sj)))));
            query.setScoreZeroToSix(new KpiAccountForItem()
                    .setActualCount(min)
                    .setProportion(NumberUtil.decimalFormat("#.##%",NumberUtil.div(new BigDecimal(min), new BigDecimal(sj)))));
        }
        return query;
    }

    public List<KpiPostScoreQuery> postScore(KpiRecordCondition condition) {
        //设置全员
        List<KpiHeader> headers = kpiHeaderService.find(new ConditionRule());
        int count = headers.size();//参与考核总人数
        KpiCycle first;
        if (StrUtil.isEmpty(condition.getSign())){
            first = kpiCycleService.findFirst(new ConditionRule(), new OrderRule().addDescOrder("create_time"));
        }else{
            first = kpiCycleService.findFirst(new ConditionRule().andEqual(KpiCycle::getSign, condition.getSign()));
        }
        List<KpiHeader> headerList = kpiHeaderService.find(new ConditionRule().andEqual(KpiHeader::getSign, first.getSign()));
        int sj = CollUtil.isEmpty(headerList) ? 0 : headerList.size();//实际考核人数
        KpiStatisticsQuery statisticsQuery = getKpiStatisticsQuery(count, sj, headerList);
        List<KpiPostScoreQuery> list = new ArrayList<>();
        list.add(new KpiPostScoreQuery().setPostName("全员")
                .setDistribution(statisticsQuery));
        //区分部门
        Map<String, List<KpiHeader>> listMap = headers.stream().collect(Collectors.groupingBy(KpiHeader::getPositionName));
        for (String positionName : listMap.keySet()) {
            //取出当前范围的记录信息
            List<KpiHeader> kpiHeaders = listMap.get(positionName).stream().filter(f -> f.getSign().equals(first.getSign())).toList();
            if (CollUtil.isEmpty(kpiHeaders)){
                list.add(new KpiPostScoreQuery().setPostName(positionName)
                        .setDistribution(new KpiStatisticsQuery()
                                .setScoreEightToTen(new KpiAccountForItem(0,"0"))
                                .setScoreSixToEight(new KpiAccountForItem(0,"0"))
                                .setScoreZeroToSix(new KpiAccountForItem(0,"0"))
                        ));
                continue;
            }
            list.add(new KpiPostScoreQuery().setPostName(positionName)
                    .setDistribution(getKpiStatisticsQuery(count, sj, kpiHeaders)));
        }
        return list;
    }

    public KpiScoreProportionQuery examineScore(KpiRecordCondition condition) {
        KpiCycle first;
        if (StrUtil.isEmpty(condition.getSign())){
            first = kpiCycleService.findFirst(new ConditionRule(), new OrderRule().addDescOrder("create_time"));
        }else{
            first = kpiCycleService.findFirst(new ConditionRule().andEqual(KpiCycle::getSign, condition.getSign()));
        }
        List<KpiHeader> headerList = kpiHeaderService.find(new ConditionRule().andEqual(KpiHeader::getSign, first.getSign()));
        if (CollUtil.isEmpty(headerList)){
            return new KpiScoreProportionQuery().setProportionList(Lists.newArrayList()).setTotalCount(0);
        }
        KpiScoreProportionQuery query = new KpiScoreProportionQuery();
        query.setTotalCount(headerList.size());
        Map<BigDecimal, List<KpiHeader>> map = headerList.stream().collect(Collectors.groupingBy(KpiHeader::getAssessorFraction));
        List<KpiScoreProportionQuery.ProportionItem> items = new ArrayList<>();
        for (BigDecimal score : map.keySet()) {
            List<KpiHeader> kpiHeaders = map.get(score);
            items.add(new KpiScoreProportionQuery.ProportionItem()
                    .setScore(score)
                    .setProportion(NumberUtil.decimalFormat("#.##%",NumberUtil.div(new BigDecimal(kpiHeaders.size()),new BigDecimal(headerList.size())))));
        }
        query.setProportionList(items);
        return query;
    }

    public List<KpiRankingQuery> examineRanking(KpiRecordCondition condition) {
        KpiCycle first;
        if (StrUtil.isEmpty(condition.getSign())){
            first = kpiCycleService.findFirst(new ConditionRule(), new OrderRule().addDescOrder("create_time"));
        }else{
            first = kpiCycleService.findFirst(new ConditionRule().andEqual(KpiCycle::getSign, condition.getSign()));
        }
        List<KpiHeader> headerList = kpiHeaderService.find(new ConditionRule().andEqual(KpiHeader::getSign, first.getSign())
                ,new OrderRule().addDescOrder("assessor_fraction"));
        if (CollUtil.isNotEmpty(headerList)){
            return headerList.stream().map(item -> new KpiRankingQuery()
                    .setUserName(item.getUserName())
                    .setPostName(item.getPositionName())
                    .setScore(item.getAssessorFraction())).toList();
        }
        return Lists.newArrayList();
    }

}
