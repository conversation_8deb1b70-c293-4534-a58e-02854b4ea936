package com.chinaservices.wms.module.asn.pa.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.module.base.ModuleBaseController;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.service.CommonService;
import com.chinaservices.wms.module.asn.pa.domain.*;
import com.chinaservices.wms.module.asn.pa.model.CsPaTask;
import com.chinaservices.wms.module.asn.pa.service.CsPaTaskDetailService;
import com.chinaservices.wms.module.asn.pa.service.CsPaTaskPassiveService;
import com.chinaservices.wms.module.asn.pa.service.CsPaTaskService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 上架任务
 */
@RestController
@RequestMapping("/api/inbound/paTask")
public class CsPaTaskController extends ModuleBaseController {

    @Autowired
    private CsPaTaskService csPaTaskService;
    @Autowired
    private CsPaTaskDetailService csPaTaskDetailService;

    @Autowired
    private CommonService commonService;
    @Autowired
    private CsPaTaskPassiveService  csPaTaskPassiveService;


    /**
     * 获取上架任务信息(pc和app共用)
     * @param paTaskCondition
     * @return
     */
    @PostMapping("/page")
    @SaCheckPermission("pa:paList:headBtn:page")
    public ResponseData<PageResult<PaTaskQuery>> page(@RequestBody @Valid PaTaskPageCondition paTaskCondition) {
        //执行分页查询
        PageResult<PaTaskQuery> pageResult = csPaTaskService.page(paTaskCondition);
        return ResponseData.success(pageResult);
    }

    /**
     * 获取上架任务信息(app)
     * @param paTaskCondition
     * @return
     */
    @PostMapping("/pda/page")
    public ResponseData<PageResult<PaTaskQuery>> appPage(@RequestBody @Valid PaTaskPageCondition paTaskCondition) {
        //执行分页查询
        SessionUserInfo userInfo = SessionContext.getSessionUserInfo();
        if (!userInfo.isSuperAdmin()){
            paTaskCondition.setPaPersonId(userInfo.getUserId());
        }
        PageResult<PaTaskQuery> pageResult = csPaTaskService.page(paTaskCondition);
        return ResponseData.success(pageResult);
    }

    /**
     * 获取上架任务详情(pc和app公用)
     * @param id
     * @return
     */
    @PostMapping("/getById/{id}")
    public ResponseData<PaTaskQuery> getById(@PathVariable(value = "id") Long id) {
        if (EmptyUtil.isNotEmpty(id)) {
            PaTaskQuery paTaskQuery = csPaTaskService.getById(id);
            return ResponseData.success(paTaskQuery);
        }

        return ResponseData.error(GlobalExceptionEnum.ID_NOT_EMPTY.getMsg());
    }

    /**
     * 获取上架任务的sn(app用)
     * @param paTaskCondition
     * @return
     */
    @PostMapping("findSn")
    public ResponseData<List<PaTaskSnQuery>> findSn(@RequestBody PaTaskCondition paTaskCondition) {
        List<PaTaskSnQuery> paTaskSnQueryList = csPaTaskService.findSn(paTaskCondition);
        return ResponseData.success(paTaskSnQueryList);
    }



    /**
     * 一键上架（批量上架确认）
     * @param paTaskBatchConfirmItem
     * @return
     */
    @PostMapping("/batchConfirm")
    @SaCheckPermission("pa:paList:table:confirm")
    public ResponseData batchConfirm(@RequestBody @Valid PaTaskBatchConfirmItem paTaskBatchConfirmItem) {
        csPaTaskService.batchConfirm(paTaskBatchConfirmItem);
        return ResponseData.success();
    }

    /**
     * 上架明细分页查询(pc和app公用)
     * @param condition
     * @return
     */
    @PostMapping("/paTaskDetailPage")
    @SaCheckPermission("pa:paList:table:view")
    public ResponseData<PaTaskDetailQuery> paTaskDetailPage(@RequestBody @Valid PaTaskDetailPageCondition condition) {
        PageResult<PaTaskDetailQuery> paTaskDetailQueryPageResult = csPaTaskDetailService.paTaskDetailPage(condition);
        return ResponseData.success(paTaskDetailQueryPageResult);
    }


    /**
     * 批量生成上架任务
     * @param paTaskBatchCreateItem
     * @return
     */
    @PostMapping("/batchCreate")
    public ResponseData<String> batchCreate(@RequestBody @Valid PaTaskBatchCreateItem paTaskBatchCreateItem) {
        csPaTaskService.batchCreate(paTaskBatchCreateItem);
        return ResponseData.success();
    }

    /**
     * 批量取消上架
     * @param paTaskDeleteItem
     * @return
     */
    @PostMapping("/batchDelete")
    @SaCheckPermission(value = {"pa:paList:table:cancel", "pa:paTaskList:table:cancel"}, mode = SaMode.OR)
    public ResponseData<CsPaTask> batchDelete(@RequestBody @Valid PaTaskBatchDeleteItem paTaskDeleteItem) {
        csPaTaskService.batchDelete(paTaskDeleteItem);
        return ResponseData.success();
    }

    /**
     * 出库上架分配/调整上架员
     */
    @PostMapping("/configuration/pa/person")
    public ResponseData<Boolean> allocPaPerson(@RequestBody @Valid AllocPaPersonItem item){
        return ResponseData.success(csPaTaskService.allocPaPerson(item));
    }

    /**
     * 查看关联单据
     */
    @PostMapping("/getTaskAssociation/{paNo}")
    public ResponseData<List<TaskAssociationListQuery>> getTaskAssociationByNo(@PathVariable(value = "paNo") String paNo){
        return ResponseData.success(csPaTaskService.getTaskAssociationByNo(paNo));
    }



    /**
     * 查看关联拣货容器号列表
     */
    @PostMapping("/getSoPalletNum/page")
    public ResponseData<PageResult<PalletListByPaNoQuery>> getSoPalletNum(@RequestBody @Valid PaTaskPageCondition paTaskCondition){
        return ResponseData.success(csPaTaskService.getSoPalletNum(paTaskCondition));
    }


    /**
     * 无源扫码验证
     */
    @PostMapping(value = "/scan/tag/verify")
    public ResponseData<Boolean> scanTagVerify(@RequestBody @Valid ScanTagVerifyItem paTaskDeleteItem) {
        return ResponseData.success(csPaTaskPassiveService.createPassivePaTask(paTaskDeleteItem));
    }
}
