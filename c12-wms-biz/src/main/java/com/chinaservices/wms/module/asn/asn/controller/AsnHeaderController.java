package com.chinaservices.wms.module.asn.asn.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.chinaservices.excel.util.ExcelUtil;
import com.chinaservices.module.base.ModuleBaseController;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.wms.module.asn.asn.domain.*;
import com.chinaservices.wms.module.asn.asn.lock.AsnHeaderLock;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.asn.asn.model.AsnHeader;
import com.chinaservices.wms.module.asn.asn.service.AsnHeaderService;
import com.chinaservices.wms.module.asn.asn.service.ReceptionWmsPushAsnService;
import com.chinaservices.wms.module.asn.receive.model.AsnReceive;
import com.chinaservices.wms.module.asn.receive.service.AsnReceiveService;
import com.chinaservices.wms.module.attachment.domain.CsAttachmentCondition;
import com.chinaservices.wms.module.attachment.domain.CsAttachmentQuery;
import com.chinaservices.wms.module.basic.lot.model.WarehouseLotDetail;
import com.chinaservices.wms.module.common.InboundCommonService;
import com.chinaservices.wms.module.excel.handler.basic.ImportCommonService;
import com.chinaservices.wms.module.common.InboundCommonService;
import com.chinaservices.wms.module.excel.item.AsnHeaderExcelItem;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 入库管理 -- 入库订单
 * <AUTHOR>
 * @Description: 预收到货管理 Controller
 */
@RestController
@RequestMapping("/api/inbound/asnHeader")
public class AsnHeaderController extends ModuleBaseController {


    @Autowired
    private AsnHeaderService asnHeaderService;

    @Autowired
    private ReceptionWmsPushAsnService receptionWmsPushAsnService;

    @Autowired
    private AsnReceiveService asnReceiveService;

    @Autowired
    private AsnHeaderLock asnHeaderLock;

    @Autowired
    private InboundCommonService inboundCommonService;

    /**
     * 获取入库计划列表
     *
     * @param condition 分页条件
     * @return {@link ResponseData }<{@link PageResult }<{@link AsnHeaderQuery }>>
     */
    @PostMapping("/page")
    @SaCheckPermission("asn:asnList:headBtn:page")
    public ResponseData<PageResult<AsnHeaderQuery>> page(@RequestBody AsnHeaderPageCondition condition) {
        //执行分页查询
        PageResult<AsnHeaderQuery> page = asnHeaderService.page(condition);
        return ResponseData.success(page);
    }

    /**
     * 根据ID获取入库计划信息
     * @param id
     * @return
     */
    @PostMapping("/getById/{id}")
    @SaCheckPermission(value = {"asn:asnList:table:through", "asn:asnList:table:edit"}, mode = SaMode.OR)
    public ResponseData<AsnHeaderInfo> getById(@PathVariable(value = "id") Long id) {
        AsnHeaderInfo asnInfoById = asnHeaderService.findAsnInfoById(id);
        return ResponseData.success(asnInfoById);
    }

    /**
     * 校验人卡控
     */
    @PostMapping("/checkReceiverOp")
    public ResponseData<Boolean> checkReceiverOp(@RequestBody Long[] ids) {
        return ResponseData.success(asnHeaderService.checkReceiverOp(ids));
    }

    /**
     * 新增或更新入库计划信息
     * @param asnHeaderItem
     * @return
     */
    @PostMapping("/save")
    @SaCheckPermission(value = {"asn:asnList:headBtn:add", "asn:asnList:table:copy", "asn:asnList:table:edit"}, mode = SaMode.OR)
    public ResponseData<AsnHeaderItem> save(@Valid  @RequestBody AsnHeaderItem asnHeaderItem) {
        asnHeaderService.check(asnHeaderItem);
        return ResponseData.success(asnHeaderItem);
    }


    /**
     * 超然子wms推送入库单，新增或更新入库计划信息
     *
     * @param asnHeaderItem 入库数据
     */
    @PostMapping("/pushWmsSave")
    @SaCheckPermission(value = {"asn:asnList:headBtn:add"}, mode = SaMode.OR)
    public ResponseData<AsnHeaderItem> pushWmsSave(@RequestBody @Valid AsnHeaderItem asnHeaderItem) {
        receptionWmsPushAsnService.pushWmsSave(asnHeaderItem);
        return ResponseData.success(asnHeaderItem);
    }

    /**
     * 超然子wms推送入库单删除，根据超燃子入库单号批量删除
     *
     * @param logisticNos ids集合
     * @return {@link ResponseData }
     */
    @PostMapping("/wmsDeleteByIds")
    @SaCheckPermission("asn:asnList:table:delete")
    ResponseData wmsDeleteByIds(@RequestBody String[] logisticNos) {
        asnHeaderLock.wmsDeleteByIds(logisticNos);
        return ResponseData.success();
    }


    /**
     * 根据Ids删除批量删除
     * @param ids ids集合
     * @return {@link ResponseData }
     */
    @PostMapping("/deleteByIds")
    @SaCheckPermission("asn:asnList:table:delete")
    public ResponseData deleteByIds(@RequestBody Long[] ids) {
        asnHeaderLock.deleteByIds(ids);
        return ResponseData.success();
    }

    /**
     *根据Ids批量取消订单
     * @param ids ids集合
     * @return {@link ResponseData }
     */
    @PostMapping("/cancelByIds")
    @SaCheckPermission(value = {"asn:asnList:headBtn:cancelOrder", "asn:asnList:table:cancelOrder"}, mode = SaMode.OR)
    public ResponseData cancelByIds(@RequestBody Long[] ids)
    {
        asnHeaderLock.cancelByIds(ids);
        return ResponseData.success();
    }

    /**
     * 根据Ids批量关闭订单(订单完结)
     * @param ids ids集合
     * @return {@link ResponseData }
     */
    @PostMapping("/closeByIds")
    @SaCheckPermission(value = {"asn:asnList:headBtn:finish", "asn:asnList:table:finish"}, mode = SaMode.OR)
    public ResponseData closeByIds(@RequestBody Long[] ids) {
        asnHeaderLock.closeByIds(ids);
        return ResponseData.success();
    }

    /**
     * 根据Ids批量审核订单
     *
     * @param ids ID集合
     * @return {@link ResponseData }
     */
    @PostMapping("/audit")
    @SaCheckPermission(value = {"asn:asnList:headBtn:audit", "asn:asnList:table:audit"}, mode = SaMode.OR)
    public ResponseData audit(@RequestBody Long[] ids) {
        asnHeaderLock.audit(ids);
        return ResponseData.success();
    }

    /**
     * 根据Ids批量取消审核订单
     *
     * @param ids ID集合
     * @return {@link ResponseData }
     */
    @PostMapping("/cancelAudit")
    @SaCheckPermission(value = {"asn:asnList:headBtn:cancelAudit", "asn:asnList:table:cancelAudit"}, mode = SaMode.OR)
    public ResponseData cancelAudit(@RequestBody Long[] ids)
    {
        asnHeaderLock.cancelAudit(ids);
        return ResponseData.success();
    }

    /**
     * 根据Ids批量订单收货
     * @param ids
     * @return
     */
    @PostMapping("/totalReceive")
    public ResponseData<AsnReceive> totalReceive(@RequestBody Object[] ids) {
        ResponseData<AsnReceive> msg = asnReceiveService.totalReceiveByAsnIds(ids);
        if (!msg.getSuccess()){
            return ResponseData.error(msg.getMsg());
        }else {
            return ResponseData.success();
        }
    }

    /**
     * 根据param生成批次属性
     * @param id
     * @return
     */
    @PostMapping("/lotId/{id}")
    public ResponseData<List<WarehouseLotDetail>> lotIdId(@PathVariable(value = "id")  Long id) {
        List<WarehouseLotDetail> warehouseLotDetailList = new ArrayList<>();
        List<Map<String, Object>> lotIdList = asnHeaderService.exportHandleRrece(id);
        for (int i = 0; i < lotIdList.size(); i++) {
            Map<String, Object> warehouseLotDetailMap = lotIdList.get(i);
            warehouseLotDetailList.add(BeanUtil.mapToBean(warehouseLotDetailMap, WarehouseLotDetail.class));
        }
        return ResponseData.success(warehouseLotDetailList);
    }

    /**
     * 入库单导入
     * @param file
     * @return
     */
    @PostMapping(value = "uploadRuleDistributionExcel")
    @SaCheckPermission("asnHeader:asnHeaderEdit:btnbar:import")
    public ResponseData<AsnHeader> uploadAsnExcel(@RequestParam(value = "file") MultipartFile file) {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            // 解析每行结果在listener中处理
            List<AsnHeaderExcelItem> asnHeaderExcelItem = ExcelUtil.importExcel(inputStream, AsnHeaderExcelItem.class);
            ResponseData<AsnHeader> msg = asnHeaderService.excelDataHandler(asnHeaderExcelItem);
            if (msg.getSuccess()){
                return ResponseData.success(200, msg.getMsg(), null);
            }else {
                return ResponseData.error(msg.getMsg());
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            return null;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                logger.error(e.getMessage());
            }
        }
    }

    /**
     * 获取入库文件列表
     * @param csAttachmentCondition
     * @return
     */
    @PostMapping("/getAttachmentById")
    public ResponseData<PageResult<CsAttachmentQuery>> getAttachmentById(@RequestBody CsAttachmentCondition csAttachmentCondition) {
        //执行分页查询
        PageResult<CsAttachmentQuery> pageResult = asnHeaderService.pageAtt(csAttachmentCondition);
        return ResponseData.success(pageResult);
    }

    /**
     * 根据人库详情id获取序列号列表
     * @param id
     * @return
     */
    @PostMapping("/getSnNoByAsnDetailId/{id}")
    public ResponseData<Map<String, List<AsnDetailSn>>> getSnNoByAsnDetailId(@PathVariable(value = "id")  Long id){
        return ResponseData.success(asnHeaderService.getSnNoByAsnDetailId(id));
    }

    /**
     * 分配/调整收货员
     */
    @PostMapping("/assignReceiveUser")
    @SaCheckPermission(value = {"asn:asnList:table:alloc", "asn:asnList:table:adjust"}, mode = SaMode.OR)
    public ResponseData<Boolean> assignReceiveUser(@RequestBody AsnHeaderReceiveItem asnHeaderReceiveItem) {
        asnHeaderService.assignReceiveUser(asnHeaderReceiveItem);
        return ResponseData.success();
    }

    /**
     * 获取入库计划列表
     *
     * @param condition 分页条件
     * @return {@link ResponseData }<{@link PageResult }<{@link AsnHeaderQuery }>>
     */
    @PostMapping("/pageQc")
//    @SaCheckPermission("asn:asnList:headBtn:pageQc")
    public ResponseData<PageResult<AsnHeaderQuery>> pageQc(@RequestBody AsnHeaderPageCondition condition) {
        //执行分页查询
        PageResult<AsnHeaderQuery> page = inboundCommonService.pageQc(condition);
        return ResponseData.success(page);
    }

    /**
     * 打印入库订单
     * @param id 主键
     * @return ResponseData<AsnHeaderPrintQuery>
     */
    @PostMapping("/print/{id}")
    @SaCheckPermission("asn:asnList:headBtn:print")
    public ResponseData<AsnHeaderPrintQuery> print(@PathVariable Long id) {
        return ResponseData.success(asnHeaderService.print(id));
    }

}
