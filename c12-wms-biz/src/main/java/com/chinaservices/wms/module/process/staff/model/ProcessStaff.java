package com.chinaservices.wms.module.process.staff.model;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 加工人员
 */
@Data
@Entity
@Table(name = "cs_process_staff")
@EqualsAndHashCode(callSuper = true)
public class ProcessStaff extends ModuleBaseModel {
  /**
   * 人员编码
   */
  private String staffNo;
  /**
   * 人员姓名
   */
  private String staffName;
  /**
   * 状态 (0-停用 1-启用)
   */
  private String status;
  /**
   * 仓库主键
   */
  private Long warehouseId;
  /**
   * 岗位 (manager-加工主管 operator-加工操作员 inspector-质检员 materialClerk-物料准备员、reworkSpecialist-返工专员)
   */
  private String position;
  /**
   * 联系电话
   */
  private String phone;
  /**
   * 家庭住址
   */
  private String address;
  /**
   * 紧急联系人
   */
  private String emergencyContact;
  /**
   * 紧急联系人电话
   */
  private String emergencyPhone;
  /**
   * 加工技能类型 (material-基础物料处理 assembly-产品组装与改装 tagging-标签与标识管理 electronic-电子产品拆检 inspection-检测与质检 packaging-包装与防护 repair-维修与翻修)
   */
  private String skillType;
}
