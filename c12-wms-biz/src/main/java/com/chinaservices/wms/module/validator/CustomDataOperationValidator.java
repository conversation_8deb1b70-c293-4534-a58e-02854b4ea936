package com.chinaservices.wms.module.validator;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.EnumUtil;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.wms.common.enums.common.DataOperationValidatorEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class CustomDataOperationValidator {

    @Autowired
    private DateOperationStatusValidator dateOperationStatusValidator;

    @Autowired
    private DataOperationReferenceValidator dataOperationReferenceValidator;

    @EventListener
    public void validateOperation(DataOperationValidationEvent event) {
        if (CollectionUtil.isEmpty(event.getIds())) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        switch (event.getOperationType()) {
            case ENABLE:
                dateOperationStatusValidator.validateStatusChange(event);
                break;
            case DISABLE, DELETE:
                dateOperationStatusValidator.validateStatusChange(event);
                this.dateOperationReferenceValidator(event);
                break;
        }
        if (!event.isAllValid()) {
            String errorMessage = DataOperationErrorMessageBuilder.buildErrorMessage(event);
            throw new ServiceException(errorMessage);
        }
        event.executeCustomValidation();
    }

    private void dateOperationReferenceValidator(DataOperationValidationEvent event) {
        DataOperationValidatorEnum validatorEnum = EnumUtil.getBy(DataOperationValidatorEnum::getDeleteObj, event.getEntityType());
        switch (validatorEnum) {
            case OWNER:
                dataOperationReferenceValidator.validateOwner(event);
                break;
            case CUSTOMER:
                dataOperationReferenceValidator.validateCustomer(event);
                break;
            case CARRIER:
                dataOperationReferenceValidator.validateCarrier(event);
                break;
            case PACKAGE:
                dataOperationReferenceValidator.validatePackage(event);
                break;
            case SKU:
                dataOperationReferenceValidator.validateSku(event);
                break;
            case PA_TASK_RULE:
                dataOperationReferenceValidator.validatePaTaskRule(event);
                break;
            case ALLOC_RULE:
                dataOperationReferenceValidator.validateAllocRule(event);
                break;
            case ROTATION_RULE:
                dataOperationReferenceValidator.validateRotationRule(event);
                break;
            case WV_RULE:
                dataOperationReferenceValidator.validateWvRule(event);
                break;
        }
    }
}