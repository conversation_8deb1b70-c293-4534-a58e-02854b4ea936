package com.chinaservices.wms.module.stock.adjust.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.chinaservices.core.exception.BizError;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.common.constant.BasicConstant;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.domain.IdsReq;
import com.chinaservices.wms.common.enums.adjust.AdjustModeEnum;
import com.chinaservices.wms.common.enums.adjust.AdjustStatusEnum;
import com.chinaservices.wms.common.enums.common.AuditStatusEnum;
import com.chinaservices.wms.common.enums.so.SnStatusEnum;
import com.chinaservices.wms.common.enums.stock.TraceBackBusinessTypeEnum;
import com.chinaservices.wms.common.exception.StockExcepitonEnum;
import com.chinaservices.wms.common.properties.ExecutorThreadProperties;
import com.chinaservices.wms.common.util.MapObjectUtil;
import com.chinaservices.wms.module.asn.asn.model.AsnDetailSn;
import com.chinaservices.wms.module.asn.asn.service.AsnDetailSnService;
import com.chinaservices.wms.module.common.AdjustDetailCommonService;
import com.chinaservices.wms.module.common.SkuCommonService;
import com.chinaservices.wms.module.stock.adjust.dao.AdjustDetailDao;
import com.chinaservices.wms.module.stock.adjust.dao.AdjustHeaderDao;
import com.chinaservices.wms.module.stock.adjust.domain.*;
import com.chinaservices.wms.module.stock.adjust.model.AdjustDetail;
import com.chinaservices.wms.module.stock.adjust.model.AdjustHeader;
import com.chinaservices.wms.module.stock.back.model.TraceBackRecordItem;
import com.chinaservices.wms.module.stock.back.service.TraceBackRecordService;
import com.chinaservices.wms.module.stock.back.model.TraceBackRecordItem;
import com.chinaservices.wms.module.stock.adjust.model.SnTraceBack;
import com.chinaservices.wms.module.stock.back.model.TraceBackRecordItem;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageQuery;
import com.chinaservices.wms.module.stock.loc.domain.LocSnItemQuery;
import com.chinaservices.wms.module.warning.service.InvWarningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinaservices.wms.common.exception.StockExcepitonEnum.ADJUST_FAIL_UNABLE_ERROR;

/**
 * 库存管理--库存调整
 * <AUTHOR> Skyler He
 */
@Service
public class AdjustHeaderService extends ModuleBaseServiceSupport<AdjustHeaderDao, AdjustHeader, Long> {


    @Autowired
    private AdjustDetailCommonService adjustDetailCommonService;
    @Autowired
    private AdjustDetailDao adjustDetailDao;
    @Autowired
    private NumberGenerator numberGenerator;
    @Autowired
    private AdjustDetailService adjustDetailService;
    @Autowired
    private SkuCommonService skuCommonService;
    @Autowired
    private StockService stockService;
    @Autowired
    private AsnDetailSnService asnDetailSnService;
    @Autowired
    private InvWarningService invWarningService;
    @Autowired
    @Qualifier(ExecutorThreadProperties.VIRTUAL_THREAD_EXECUTOR)
    private ExecutorService virtualThreadExecutor;


    /**
     * 库存盘点分页查询
     */
    public PageResult<AdjustHeaderPageQuery> page(AdjustHeaderPageCondition adjustHeaderCondition) {
        PageResult<AdjustHeaderPageQuery> page = dao.page(adjustHeaderCondition);
        page.getRows().forEach(e -> {
            switch (Objects.requireNonNull(AdjustStatusEnum.fromCode(e.getStatus()))) {
                case AUDIT: // 待审核
                    e.setDeleteButton(Boolean.TRUE);// 删除
                    e.setEditButton(Boolean.TRUE);// 编辑
                    e.setAuditButton(Boolean.TRUE);// 审核
                    break;
                case AUDITED: // 已审核
                    e.setAdjustButton(Boolean.TRUE);// 执行调整
                    break;
                default:
                    // 其他状态无需操作
                    break;
            }
        });
        return page;
    }

    /**
     * 新增或更新库存调整单
     */
    public void saveOrUpdate(AdjustHeaderItem adjustHeaderItem) {
        if (EmptyUtil.isEmpty(adjustHeaderItem.getId())) {
            batchSaveAdjustHeader(Collections.singletonList(adjustHeaderItem));
        }else {
            edit(adjustHeaderItem);
        }
    }

    /**
     * 批量新增库存调整单
     * @param adjustHeaderItemList 调整单
     */
    public void batchSaveAdjustHeader(List<AdjustHeaderItem> adjustHeaderItemList) {
        List<AdjustDetail> adjustDetailList = new ArrayList<>();
        List<String> soNoList = new ArrayList<>();
        adjustHeaderItemList.forEach(adjustHeaderItem -> {
            // 1.调整明细不能为空
            List<AdjustDetailItem> adjustDetailItemList = adjustHeaderItem.getAdjustDetailList();
            if (CollectionUtil.isEmpty(adjustDetailItemList)){
                throw new ServiceException(StockExcepitonEnum.ADJUST_DETAIL_NOT_NULL.getMsg());
            }
            // 2.如果新增，则添加单号
            AdjustHeader adjustHeader = new AdjustHeader();
            BeanUtil.copyProperties(adjustHeaderItem, adjustHeader);
            adjustHeader.setAdjustNo(numberGenerator.nextValue(IdRuleConstant.ADJUST_NO));
            adjustHeader.setStatus(AdjustStatusEnum.AUDIT.getCode());
            adjustHeader.setAdjustOp(SessionContext.getSessionUserInfo().getRealName());
            adjustHeader.setAdjustTime(new Date());
            dao.saveOrUpdate(adjustHeader);

            //保存调整单明细
            adjustDetailItemList.forEach(adjustDetailItem -> {
                AdjustDetail detail = adjustDetailCommonService.convertAdjustDetail(adjustDetailItem,adjustHeader);
                detail.setId(null);
                adjustDetailService.preSave(detail);
                adjustDetailList.add(detail);
                soNoList.addAll(MapObjectUtil.getMapToObjectValue(adjustDetailItem.getSnStr(), Boolean.TRUE));
            });
        });
        boolean exist = adjustDetailCommonService.hasDuplicates(soNoList);
        // todo 阿浅 同一个序列号不允许在同一个调整单中多次添加
//        if (exist){
//            throw new ServiceException(StockExcepitonEnum.ADJUST_ORDER_SO_NO_REPEAT.getMsg());
//        }
        adjustDetailDao.batchInsert(adjustDetailList);
    }

    /**
     * 编辑库存调整单
     */
    public void edit(AdjustHeaderItem adjustHeaderItem) {
        // 1.调整明细不能为空
        List<AdjustDetailItem> adjustDetailItemList = adjustHeaderItem.getAdjustDetailList();
        if (CollectionUtil.isEmpty(adjustDetailItemList)){
            throw new ServiceException(StockExcepitonEnum.ADJUST_DETAIL_NOT_NULL.getMsg());
        }
        // 2.编辑，则判断是否存在
        AdjustHeader adjustHeader = dao.findById(adjustHeaderItem.getId());
        if (Objects.isNull(adjustHeader)){
            throw new ServiceException(StockExcepitonEnum.ADJUST_ORDER_ADJUST_HEADER_ADJUSTMENT_CANNOT_QUERY.getMsg());
        }
        // 3. 状态 != 已审核 的调整单 ==> 无法编辑
        if (!AdjustStatusEnum.AUDIT.getCode().equals(adjustHeader.getStatus())){
            throw new ServiceException(StockExcepitonEnum.ADJUST_ORDER_ADJUST_CANNOT_EDIT.getMsg());
        }
        adjustDetailCommonService.getAdjustHeardWhereItem(adjustHeaderItem,adjustHeader);
        dao.saveOrUpdate(adjustHeader);

        // 3.编辑库存调整明细
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(AdjustDetail::getAdjustNo,adjustHeaderItem.getAdjustNo());
        List<AdjustDetail> detailList = adjustDetailDao.find(conditionRule);
        if (CollectionUtil.isNotEmpty(detailList)){
            // 获取 addData 中的所有 id
            Set<Long> addDataIds = adjustDetailItemList.stream()
                    .map(AdjustDetailItem::getId)
                    .collect(Collectors.toSet());

            // 过滤出 list 中不在 addData 的 id 需要删除
            Long[]  filteredList = detailList.stream()
                    .map(AdjustDetail::getId)
                    .filter(id -> !addDataIds.contains(id))
                    .toArray(Long[] ::new);
            adjustDetailDao.delete(filteredList);
        }
        Map<Long, AdjustDetail> detailMap = detailList.stream().collect(Collectors.toMap(AdjustDetail::getId, Function.identity(), (v1, v2) -> v1));
        //保存调整单明细
        adjustDetailItemList.forEach(adjustDetailItem -> {
            AdjustDetail detail = new AdjustDetail();
            if (EmptyUtil.isEmpty(adjustDetailItem.getId())){
                detail = adjustDetailCommonService.convertAdjustDetail(adjustDetailItem,adjustHeader);
                if (EmptyUtil.isEmpty(detail.getLotNum())){
                    detail.setLotNum(adjustDetailCommonService.getLotNum(detail));
                }
            }else {
                detail = detailMap.get(adjustDetailItem.getId());
                adjustDetailCommonService.getAdjustDetailWhereItem(adjustDetailItem,detail);
            }
            adjustDetailDao.saveOrUpdate(detail);
        });
    }

    /**
     * 根据Id查询调整单
     */
    public AdjustHeaderItem findAdjustById(Long id) {
        if (EmptyUtil.isEmpty(id)){
            throw new ServiceException(GlobalExceptionEnum.ID_NOT_EMPTY.getMsg());
        }
        AdjustHeader adjustHeader = dao.findById(id);
        if (Objects.isNull(adjustHeader)){
            throw new ServiceException(StockExcepitonEnum.ADJUST_ORDER_ADJUST_HEADER_ADJUSTMENT_CANNOT_QUERY.getMsg());
        }
        AdjustHeaderItem adjustHeaderItem = new AdjustHeaderItem();
        BeanUtil.copyProperties(adjustHeader, adjustHeaderItem);
        List<AdjustDetailItem> adjustDetailItems = adjustDetailService.find(AdjustDetailItem.class,
                new ConditionRule().andEqual(AdjustHeader::getAdjustNo, adjustHeader.getAdjustNo()));
        adjustDetailItems.forEach(e ->{
            Map<String, List<String>> stringListMap = Optional.ofNullable(e.getSnStr())
                    .map(MapObjectUtil::objectToMap)
                    .orElse(Collections.emptyMap());

            List<LocSnItemQuery> snMap = stringListMap.entrySet().stream()
                    .map(entry -> new LocSnItemQuery(entry.getKey(), entry.getValue()))
                    .collect(Collectors.toList());
            e.setSnMap(snMap);
        });
        adjustHeaderItem.setAdjustDetailList(adjustDetailItems);
        return adjustHeaderItem;
    }

    /**
     * Description : 根据Ids批量删除调整单
     */
    public void deleteByIds(Long[] ids) {
        // 状态 = 待审核 的调整单 ==> 删除
        List<String> statusList = Collections.singletonList(AdjustStatusEnum.AUDIT.getCode());
        adjustDetailCommonService.findAndValidateByIds(ids, statusList, StockExcepitonEnum.ADJUST_ORDER_ADJUST_HEADER_CANNOT_DELETE);
        dao.delete(ids);
        adjustDetailDao.delete(new ConditionRule().andIn(AdjustDetail::getCsAdjustHeaderId,ids));
    }

    /**
     * 调整单审核、取消审核
     *
     * @param auditType 审核：audit，取消审核：audited
     */
    public void auditAdjustHandler(Long[] ids,String auditType) {
        // 状态 = 待审核/审核 的调整单 ==> 待审核/审核
        List<String> statusList = Arrays.asList(AdjustStatusEnum.AUDITED.getCode(),AdjustStatusEnum.AUDIT.getCode());
        List<AdjustHeader> adjustHeaderList = adjustDetailCommonService.findAndValidateByIds(ids, statusList, StockExcepitonEnum.ADJUST_ORDER_EXIST_CLOSE_CANCEL_EXECUTE_ERROR);

        // 三.检查是否存在相同的操作类型
        adjustHeaderList.stream()
                .filter(e -> auditType.equals(e.getStatus()))
                .findFirst()
                .ifPresent(e -> {
                    throw new ServiceException(
                            AuditStatusEnum.AUDITED.getCode().equals(auditType)
                                    ? StockExcepitonEnum.ADJUST_ORDER_EXIST_AUDIT.getMsg()
                                    : StockExcepitonEnum.ADJUST_ORDER_EXIST_NOT_AUDIT.getMsg()
                    );
                });
        // 四.修改审核状态
        updateStatus(adjustHeaderList,auditType);
        dao.batchUpdate(adjustHeaderList);

    }

    /**
     * 关闭调整单
     * @param ids
     */
    public void close(Long[] ids) {
        // 状态 = 已执行 的调整单 ==> 关闭
        List<String> statusList = Collections.singletonList(AdjustStatusEnum.EXECUTE.getCode());
        List<AdjustHeader> adjustHeaderList = adjustDetailCommonService.findAndValidateByIds(ids, statusList, StockExcepitonEnum.ADJUST_ORDER_CANCEL_CLOSE_ERROR);

        // 修改状态为关闭
        updateStatus(adjustHeaderList,AdjustStatusEnum.CLOSE.getCode());
        dao.batchUpdate(adjustHeaderList);
    }

    public void cancelOrder(Long[] ids) {
        // 状态 = 审核/待审核 的调整单 ==> 取消
        List<String> statusList = Arrays.asList(AdjustStatusEnum.AUDIT.getCode(), AdjustStatusEnum.AUDITED.getCode());
        List<AdjustHeader> adjustHeaderList = adjustDetailCommonService.findAndValidateByIds(ids, statusList, StockExcepitonEnum.ADJUST_ORDER_CANCEL_ERROR);

        // 修改状态为取消
        updateStatus(adjustHeaderList,AdjustStatusEnum.CANCEL.getCode());
        dao.batchUpdate(adjustHeaderList);
    }

    /**
     *  根据货主和仓库查询商品信息
     * @param condition 货主，仓库，盘点单
     */
    public PageResult<AdjustSkuLocQuery> getAsnSkuPageByOwner(AdjustSkuPageCondition condition) {
        // 查询货主下的库存信息
        PageResult<LocListPageQuery> result = skuCommonService.getSkuByOwnerId(condition);
        PageResult<AdjustSkuLocQuery> convertRest = new PageResult<>();
        if (result == null){
            convertRest.setCount(0);
            convertRest.setLastPage(Boolean.TRUE);
            convertRest.setRows(new ArrayList<>());
            return convertRest;
        }
        convertRest.setCount(result.getCount());
        convertRest.setLastPage(result.isLastPage());
        convertRest = dataChanged(convertRest,result.getRows());
        return convertRest;
    }

    //遍历获取库存

    public PageResult<AdjustSkuLocQuery> dataChanged(PageResult<AdjustSkuLocQuery> convertRest,List<LocListPageQuery> pages) {
        List<AdjustSkuLocQuery> convert = new ArrayList<>();
        if (CollUtil.isNotEmpty(pages)) {
            pages.forEach(item -> {
                AdjustSkuLocQuery query = new AdjustSkuLocQuery();
                BeanUtil.copyProperties(item, query);
                query.setLocCode(item.getLocNum());
                if (item.getInvAvailableNum() != null && item.getInvAvailableNum() > 0) {
                    query.setQtyAvailableNumber(Convert.toBigDecimal(item.getInvAvailableNum()));
                } else {
                    query.setQtyAvailableNumber(BigDecimal.ZERO);
                }
                query.setPalletNo(item.getPalletNum());
                query.setLocCode(item.getLocNum());
                convert.add(query);
            });
        }
        convertRest.setRows(convert);
        return convertRest;
    }


    /**
     * 取消执行或者执行
     * @param execute  操作动作：取消执行 -> 已审核，执行 -> 已执行
     * @param idsReq 需要操作的id
     */
    public void executionAdjust(IdsReq idsReq,String execute) {
        // 一.查询并校验数据
        List<String> statusList = new ArrayList<>();
        BizError msg = null;
        String transactionType = TransactionType.TRAN_AD;
        if (AdjustStatusEnum.EXECUTE.getCode().equals(execute)){
            statusList = Collections.singletonList(AdjustStatusEnum.AUDITED.getCode());
            msg = StockExcepitonEnum.ADJUST_NO_UNABLE_ERROR;
        } else if(AdjustStatusEnum.AUDITED.getCode().equals(execute)){
            statusList = Collections.singletonList(AdjustStatusEnum.EXECUTE.getCode());
            msg = StockExcepitonEnum.ADJUST_NOT_EXECUTE_UNABLE_ERROR;
            transactionType = TransactionType.TRAN_CAD;
        }
        List<AdjustHeader> adjustHeaders = adjustDetailCommonService.findAndValidateByIds(idsReq.getIds().toArray(Long[]::new), statusList, msg);
        // 二.数据处理
        StringBuilder stringBuilder = new StringBuilder();
        Map<String, Map<Boolean, List<String>>> asnSnNoMap = new HashMap<>();
        List<AdjustHeader> successAdjustHeaders = new ArrayList<>();
        String executeName = AdjustStatusEnum.EXECUTE.getCode().equals(execute) ? BasicConstant.EXECUTE : BasicConstant.CANCEL_EXECUTE;
        for (AdjustHeader s : adjustHeaders){
            // 1.查询调整单明细数据并校验是否存在
            List<AdjustDetail> adjustDetailList = adjustDetailDao.find(AdjustDetail.class,new ConditionRule().andEqual(AdjustDetail::getCsAdjustHeaderId, s.getId()));
            if(CollectionUtil.isEmpty(adjustDetailList)){
                stringBuilder.append(String.format(ADJUST_FAIL_UNABLE_ERROR.getMsg(), s.getAdjustNo(), executeName, StockExcepitonEnum.ADJUST_ORDER_ADJUST_HEADER_ADJUSTMENT_CANNOT_QUERY.getMsg()));
                continue;
            }
            // 2.校验序列号是否符合条件
            try {
                adjustDetailCommonService.checkSn(adjustDetailList, execute);
            }catch (Exception e){
                stringBuilder.append(String.format(ADJUST_FAIL_UNABLE_ERROR.getMsg(), s.getAdjustNo(), executeName, StockExcepitonEnum.STOCK_SN_NO_REPEAT_ADJUST.getMsg()));
                continue;
            }

            // 3.执行操作
            List<InvLotLocQtyBO> invLotLocQtyBOS = new ArrayList<>();
            Map<Boolean, List<String>> snNoMap = new HashMap<>();
            for (AdjustDetail adjustDetail : adjustDetailList) {
                List<String> snNoList = MapObjectUtil.getMapToObjectValue(adjustDetail.getSnStr(), Boolean.TRUE);
                invLotLocQtyBOS.add(new InvLotLocQtyBO()
                        .setLocId(adjustDetail.getLocId())
                        .setLotNum(adjustDetail.getLotNum())
                        // 如果是操作执行，则新增时新增，否则新增时减少
                        .setUpdateNum(AdjustStatusEnum.EXECUTE.getCode().equals(execute)
                                ? AdjustModeEnum.ADD.getCode().equals(adjustDetail.getAdjustMode()) ? adjustDetail.getQtyAdjustEa() : adjustDetail.getQtyAdjustEa().negate()
                                : AdjustModeEnum.ADD.getCode().equals(adjustDetail.getAdjustMode()) ? adjustDetail.getQtyAdjustEa().negate() : adjustDetail.getQtyAdjustEa())
                        .setPalletNum(adjustDetail.getPalletNum())
                        .setSkuId(adjustDetail.getSkuId())
                        .setWarehouseId(adjustDetail.getWarehouseId())
                        .setSkuSn(snNoList)
                        .setOwnerId(adjustDetail.getOwnerId()));
                //零库存调整 = 新增 = 已入库   库存调整 = 减少 = 库存调整
                boolean b = AdjustStatusEnum.EXECUTE.getCode().equals(execute) == AdjustModeEnum.ADD.getCode().equals(adjustDetail.getAdjustMode());
                // 查询是否存在map的值，存在添加，不存在新增
                snNoMap.computeIfAbsent(b, k -> new ArrayList<>()).addAll(snNoList);
            }
            try {
                stockService.exec(new InvLotLocQtyBO()
                        .setWarehouseId(s.getWarehouseId())
                        .setWarehouseName(s.getWarehouseName())
                        .setOwnerId(s.getOwnerId())
                        .setOwnerName(s.getOwnerName())
                        .setList(invLotLocQtyBOS)
                        .setOrderNo(s.getAdjustNo())
                        .setTransactionType(transactionType));
                s.setStatus(execute);
                s.setAdjustOp(SessionContext.getSessionUserInfo().getRealName());
                s.setAdjustTime(new Date());
                preSave(s);
                successAdjustHeaders.add(s);
            } catch (ServiceException e) {
                stringBuilder.append(String.format(ADJUST_FAIL_UNABLE_ERROR.getMsg(), executeName, s.getAdjustNo(), e.getMessage()));
                continue;
            }
            // 3.若是执行调整，存在空批次号的时候，需要查询并保存批次号
            if (AdjustStatusEnum.EXECUTE.getCode().equals(execute)){
                adjustDetailList.stream()
                        .filter(e -> EmptyUtil.isEmpty(e.getLotNum()))
                        .distinct()
                        .forEach(e -> e.setLotNum(adjustDetailCommonService.getLotNum(e)));
                adjustDetailDao.batchUpdate(adjustDetailList);
            }
            // 4. 库存序列号状态更新
            if (!snNoMap.isEmpty()){
                asnSnNoMap.put(s.getAdjustNo(),snNoMap);
            }
        }
        dao.batchUpdate(successAdjustHeaders);
        // 4. 库存序列号状态更新
        if(!asnSnNoMap.isEmpty()){
            logger.info("库存序列号状态更新asnSnNoMap:{}",asnSnNoMap);
            SessionUserInfo sessionUserInfo = SessionContext.get();
            CompletableFuture.runAsync(() -> {
                asnDetailSnsUpdate(asnSnNoMap, sessionUserInfo);
            }, virtualThreadExecutor).exceptionally(ex -> {
                logger.error("异步任务失败", ex);
                return null;
            });
        }
        if(!EmptyUtil.isEmpty(stringBuilder.toString())){
            throw new ServiceException(stringBuilder.toString());
        }
    }

    //库存序列号状态更新
    public void asnDetailSnsUpdate(Map<String, Map<Boolean, List<String>>> asnSnNoMap, SessionUserInfo sessionUserInfo){
        logger.info("库存序列号状态更新:snTraceBackList");
        SessionContext.put(sessionUserInfo);
        asnSnNoMap.forEach((adjustNo, booleanMap) -> {
            if (booleanMap.isEmpty()) return;

            // 1. 合并所有SN号并批量查询
            List<String> allSnNos = booleanMap.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            List<AsnDetailSn> allAsnDetailSns = asnDetailSnService.listBySnNoList(allSnNos);

            // 2. 创建SN号到状态的映射
            Map<String, String> snStatusMap = new HashMap<>();
            booleanMap.forEach((isSo, snList) ->
                    snList.forEach(sn -> snStatusMap.put(sn,
                            isSo ? SnStatusEnum.SO.getCode() : SnStatusEnum.ADJUST.getCode()))
            );

            // 3. 批量更新状态
            allAsnDetailSns.forEach(sn ->
                    sn.setStatus(snStatusMap.get(sn.getSnNo()))
            );
            asnDetailSnService.batchUpdate(allAsnDetailSns);
        });
    }

    public void updateStatus(List<AdjustHeader> adjustHeaderList,String execute){
        // 修改状态为取消
        adjustHeaderList.forEach(e ->{
            e.setStatus(execute);
            e.setAdjustOp(SessionContext.getSessionUserInfo().getRealName());
            e.setAdjustTime(new Date());
            preSave(e);
        });
    }

}
