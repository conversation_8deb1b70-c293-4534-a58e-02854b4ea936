package com.chinaservices.wms.module.performance.service;

import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.module.performance.dao.KpiHeaderDao;
import com.chinaservices.wms.module.performance.domain.KpiHeaderPageCondition;
import com.chinaservices.wms.module.performance.domain.KpiHeaderQuery;
import com.chinaservices.wms.module.performance.domain.KpiHeaderRankingQuery;
import com.chinaservices.wms.module.performance.model.KpiHeader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
*/
@Service
@Slf4j
public class KpiHeaderService extends ModuleBaseServiceSupport<KpiHeaderDao, KpiHeader, Long> {

    public PageResult<KpiHeaderQuery> page(KpiHeaderPageCondition condition){
        return dao.page(condition);
    }

    /**
     * 绩效分页排名
     *
     * @param condition 条件
     * @return {@link PageResult }<{@link KpiHeaderRankingQuery }>
     */
    public PageResult<KpiHeaderRankingQuery> pageRanking(KpiHeaderPageCondition condition){
        return dao.pageRanking(condition);
    }
}
