package com.chinaservices.wms.module.performance.dao;

import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.sqlid.performance.PerformanceSqlId;
import com.chinaservices.wms.module.performance.domain.KpiIndexPageCondition;
import com.chinaservices.wms.module.performance.domain.KpiIndexQuery;
import com.chinaservices.wms.module.performance.model.KpiIndex;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class KpiIndexDao extends ModuleBaseDaoSupport<KpiIndex, Long> {
    public PageResult<KpiIndexQuery> page(KpiIndexPageCondition condition) {
        return sqlExecutor.page(KpiIndexQuery.class, PerformanceSqlId.KPI_INDEX_PAGE,condition);
    }

    public List<KpiIndexQuery> search(KpiIndexPageCondition condition) {
        return sqlExecutor.find(KpiIndexQuery.class, PerformanceSqlId.KPI_INDEX_PAGE,condition);
    }
}
