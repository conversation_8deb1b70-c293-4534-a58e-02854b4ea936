package com.chinaservices.wms.module.excel.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DownLoadTemplateEnum {

    WAREHOUSE_TEMPLATE("1", "warehouse", "仓库导入模板.xlsx"),
    WAREHOUSE_AREA_TEMPLATE("2", "warehouseArea", "区域导入模板.xlsx"),
    WAREHOUSE_ZONE_TEMPLATE("3", "warehouseZone", "库区导入模板.xlsx"),
    WAREHOUSE_SHELF_TEMPLATE("4", "warehouseShelf", "货架导入模板.xlsx"),
    WAREHOUSE_LOC_TEMPLATE("5", "warehouseLoc", "库位导入模板.xlsx"),
    OWNER_TEMPLATE("6", "owner", "货主导入模板.xlsx"),
    CUSTOMER_TEMPLATE("7", "customer", "客户导入模板.xlsx"),
    SUPPLIER_TEMPLATE("8", "supplier", "供应商导入模板.xlsx"),
    CARRIER_TEMPLATE("9", "carrier", "承运商导入模板.xlsx"),
    PACKAGE_TEMPLATE("10", "package", "包装导入模板.xlsx"),
    SKU_TEMPLATE("11", "sku", "商品导入模板.xlsx"),
    PALLET_TEMPLATE("12", "pallet", "容器号导入模板.xlsx"),
    SN_NO_TEMPLATE("13", "snNo", "序列号导入模板.xlsx"),
    RULE_CARRIER_DISTRIBUTION_TEMPLATE("14", "ruleCarrierDistribution", "承运商分配规则导入模板.xlsx"),
    PASSIVE_TAG("15", "passiveTag", "标签导入模板.xlsx"),
    PURCHASE_ORDER_TEMPLATE("16", "purchaseOrder", "采购订单导入模板.xlsx"),
    SALE_ORDER_TEMPLATE("17", "saleOrder", "销售订单导入模板.xlsx"),
    QC_SN_NO("18","qcSnNo","质检序列号导入模板.xlsx"),
    QC_SN("19", "qcSn", "序列号导入模板.xlsx"),
    PASSIVE_TAG_BIND("20", "passiveTagBind", "标签导入&绑定模板.xlsx"),
    ;

    private final String type;
    private final String code;
    private final String value;
}