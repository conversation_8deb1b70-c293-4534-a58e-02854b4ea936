package com.chinaservices.wms.module.performance.queue;

import com.chinaservices.core.redis.delay.RedisDelayQueueProcessor;
import com.chinaservices.core.redis.util.RedisKeyGenerator;
import com.chinaservices.wms.common.constant.RedisKeys;
import com.chinaservices.wms.module.performance.service.KpiAllocationService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Redis 延迟队列具体执行类
 */
@Data
@Slf4j
@Component
public class AutomaticallyEndTheAssessmentCycleListener implements RedisDelayQueueProcessor {

    @Autowired
    private KpiAllocationService kpiAllocationService;

    @Override
    public void execute(String sign) {
        log.info("开始执行Redis延迟队列：{}", sign);
        //具体执行方法
        kpiAllocationService.autoUpdateCycle(sign);
    }

    @Override
    public String delayKey() {
        return RedisKeyGenerator.gen(RedisKeys.KPI_CYCLE_QUEUE);
    }
}
