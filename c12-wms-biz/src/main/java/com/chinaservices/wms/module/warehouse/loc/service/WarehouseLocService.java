package com.chinaservices.wms.module.warehouse.loc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.enums.StatusEnum;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.GlobalExceptionEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.core.file.util.CustomFile;
import com.chinaservices.file.FileObject;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.number.NumberGenerator;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.BeanUtil;
import com.chinaservices.wms.common.constant.IdRuleConstant;
import com.chinaservices.wms.common.enums.basic.FieldTypeEnum;
import com.chinaservices.wms.common.enums.common.FileTypeEnum;
import com.chinaservices.wms.common.enums.common.WarehouseEnum;
import com.chinaservices.wms.common.enums.common.WmsDataSourceTypeEnum;
import com.chinaservices.wms.common.enums.rule.SortTypeEnum;
import com.chinaservices.wms.common.enums.warehouse.LocStatusEnum;
import com.chinaservices.wms.common.enums.warehouse.WarehouseLocUseTypeEnum;
import com.chinaservices.wms.common.exception.StockExcepitonEnum;
import com.chinaservices.wms.common.exception.WarehouseExceptionEnum;
import com.chinaservices.wms.module.stock.loc.domain.LocListCondition;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageQuery;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocService;
import com.chinaservices.wms.module.warehouse.area.domain.WarehouseAreaCondition;
import com.chinaservices.wms.module.warehouse.area.model.WarehouseArea;
import com.chinaservices.wms.module.warehouse.area.service.WarehouseAreaService;
import com.chinaservices.wms.module.warehouse.loc.dao.WarehouseLocDao;
import com.chinaservices.wms.module.warehouse.loc.dao.WarehouseLocFileDao;
import com.chinaservices.wms.module.warehouse.loc.domain.*;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLoc;
import com.chinaservices.wms.module.warehouse.loc.model.WarehouseLocFile;
import com.chinaservices.wms.module.warehouse.shelf.service.WarehouseShelfService;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import com.chinaservices.wms.module.warehouse.zone.model.WarehouseZone;
import com.chinaservices.wms.module.warehouse.zone.service.WarehouseZoneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仓库管理
 *
 * <AUTHOR>
 * @description 库位Service
 */
@Service
public class WarehouseLocService extends ModuleBaseServiceSupport<WarehouseLocDao, WarehouseLoc, Long> {
    @Autowired
    private NumberGenerator numberGenerator;
    public static final String AND = "and ";
    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private WarehouseZoneService warehouseZoneService;

    @Autowired
    private WarehouseAreaService warehouseAreaService;

    @Autowired
    private WarehouseLocFileDao warehouseLocFileDao;

    @Autowired
    private WarehouseShelfService warehouseShelfService;


    @Autowired
    private InvLotLocService invLotLocService;

    @Autowired
    private CustomFile customFile;

    /**
     * 分页查询库位
     *
     * @param warehouseLocPageCondition
     * @return
     */
    public PageResult<WarehouseLocQuery> page(WarehouseLocPageCondition warehouseLocPageCondition) {
        PageResult<WarehouseLocQuery> page = dao.page(warehouseLocPageCondition);
        page.getRows().forEach(e -> {
            if (YesNoEnum.YES.getCode().equals(e.getIsDefault())){
                e.setDeleteBtn(Boolean.FALSE);
                e.setEditBtn(Boolean.FALSE);
            }
        });
        return page;
    }
    /**
     * 库位分页查询
     * @param warehouseLocPageCondition
     * @return
     */
    public PageResult<WarehouseLocQuery> subPage(WarehouseLocPageCondition warehouseLocPageCondition) {
        return dao.subPage(warehouseLocPageCondition);
    }

    /**
     * 分页查询库位，包含默认库位
     * @param warehouseLocPageCondition
     * @return
     */
    public PageResult<WarehouseLocQuery> allPage(WarehouseLocPageCondition warehouseLocPageCondition) {
        return dao.allPage(warehouseLocPageCondition);
    }

    /**
     * 根据ID查询库位
     *
     * @param id
     * @return
     */
    public WarehouseLoc getById(Long id) {
        if (ObjUtil.isNull(id)) {
            throw new ServiceException(GlobalExceptionEnum.ID_NOT_EMPTY);
        }
        return dao.getByLOCId(id);
    }

    public List<WarehouseLoc> findByLocCodes(Set<String> locCodeSet){
        if (CollUtil.isEmpty(locCodeSet)){
            return new ArrayList<>();
        }
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(WarehouseLoc::getLocCode, locCodeSet);
        return this.find(conditionRule);
    }
    public void checkLocCode(WarehouseLocItem warehouseLocItem) {
        if (ObjUtil.isEmpty(warehouseLocItem.getLocCode())){
            throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_LOC_CODE_NOT_NULL);
        }

        if (ObjUtil.isEmpty(warehouseLocItem.getZoneCode())){
            throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_ZONE_NOT_NULL);
        }
        if (ObjUtil.isEmpty(warehouseLocItem.getWarehouseCode())){
            throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_CODE_NOT_NULL);
        }
        // 校验是否混商品为是，最大混商品数量必填
        if (BooleanUtil.and(StrUtil.isNotEmpty(warehouseLocItem.getIsMixSku()),
                ObjUtil.equals(YesNoEnum.YES.getCode(), warehouseLocItem.getIsMixSku()),
                ObjUtil.isNull(warehouseLocItem.getMaxMixSku()))) {
            throw new ServiceException(WarehouseExceptionEnum.MAX_MIX_SKU_EMPTY.getMsg());
        }
        // 校验是否混批次为是，最大混批次数量必填
        if (BooleanUtil.and(StrUtil.isNotEmpty(warehouseLocItem.getIsMixLot()),
                ObjUtil.equals(YesNoEnum.YES.getCode(), warehouseLocItem.getIsMixLot()),
                ObjUtil.isNull(warehouseLocItem.getMaxMixLot()))) {
            throw new ServiceException(WarehouseExceptionEnum.MAX_MIX_LOT_EMPTY.getMsg());
        }
    }
    /**
     * 新增或编辑库位（wms对接）
     *
     * @param warehouseLocItem
     * @return
     */
    public boolean crzSave(WarehouseLocItem warehouseLocItem) {
        //校验
        checkLocCode(warehouseLocItem);

        // 校验所属仓库是否存在
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(Warehouse::getUpWarehouseCode,  warehouseLocItem.getWarehouseCode());
        Warehouse warehouse = warehouseService.findFirst(conditionRule);
        if (ObjUtil.isNull(warehouse)) {
            throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_NOT_EXIST);
        }
        warehouseLocItem.setWarehouseName(warehouse.getWarehouseName());
        warehouseLocItem.setWarehouseId(warehouse.getId());

        // 校验所属区域是否存在
        if (ObjUtil.isNotEmpty(warehouseLocItem.getAreaCode())){
            ConditionRule conditionRuleArea = new ConditionRule();
            conditionRuleArea.andEqual(WarehouseArea::getWarehouseId,warehouse.getId());
            conditionRuleArea.andEqual(WarehouseArea::getUpAreaCode,warehouseLocItem.getAreaCode());
            WarehouseArea areaServiceFirst = warehouseAreaService.findFirst(conditionRuleArea);
            if (ObjUtil.isNull(areaServiceFirst)) {
                throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_AREA_NOT_EXIST,  warehouse.getWarehouseName());
            }
            warehouseLocItem.setAreaId(areaServiceFirst.getId());
            warehouseLocItem.setAreaCode(areaServiceFirst.getAreaCode());
        }

        // 校验所属库区是否存在
        ConditionRule conditionRuleZone = new ConditionRule();
        conditionRuleZone.andEqual(WarehouseZone::getWarehouseId,warehouse.getId());
        conditionRuleZone.andEqual(WarehouseZone::getUpZoneCode,warehouseLocItem.getZoneCode());
        WarehouseZone zoneServiceFirst = warehouseZoneService.findFirst(conditionRuleZone);
        if (ObjUtil.isNull(zoneServiceFirst)) {
                throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_ZONE_NOT_EXIST);
            }
            warehouseLocItem.setZoneId(zoneServiceFirst.getId());
            warehouseLocItem.setZoneName(zoneServiceFirst.getZoneName());


        ConditionRule conditionRuleLocCode = new ConditionRule();
        conditionRuleLocCode.andEqual(WarehouseLoc::getUpLocCode,warehouseLocItem.getLocCode());
        conditionRuleLocCode.andEqual(WarehouseLoc::getWarehouseId,warehouse.getId());
        conditionRuleLocCode.andEqual(WarehouseLoc::getZoneId,zoneServiceFirst.getId());
        conditionRuleLocCode.andEqual(WarehouseLoc::getSource,WmsDataSourceTypeEnum.WMS_PUSH.getCode());
        WarehouseLoc warehouseLocWms = dao.findFirst(conditionRuleLocCode);

        if (ObjUtil.isNotNull(warehouseLocWms)){
            warehouseLocItem.setUpLocCode(warehouseLocWms.getUpLocCode());
            warehouseLocItem.setLocName(warehouseLocWms.getLocName());
            warehouseLocItem.setId(warehouseLocWms.getId());
            WarehouseLoc byId = dao.findById(warehouseLocWms.getId());
            if(ObjUtil.equals(warehouseLocItem.getIsEnable(),YesNoEnum.NO.getCode())||!ObjUtil.equals(byId.getWarehouseId(),warehouseLocItem.getWarehouseId())){
                List<Long> ids = new ArrayList<>();
                ids.add(warehouseLocWms.getId());
                List<InvLotLoc> qtyCountByLocId = invLotLocService.findQtyCountByLocId(ids);
                if (CollectionUtil.isNotEmpty(qtyCountByLocId)&&ObjUtil.equals(warehouseLocItem.getIsEnable(),YesNoEnum.NO.getCode())){
                    throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_LOC_DISABLE_ERROR);
                }
                if (CollectionUtil.isNotEmpty(qtyCountByLocId)&&!ObjUtil.equals(byId.getWarehouseId(),warehouseLocItem.getWarehouseId())){
                    throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_LOC_DISABLE_ERROR);
                }
            }
        }else {
            warehouseLocItem.setId(null);
            warehouseLocItem.setUpLocCode(warehouseLocItem.getUpLocCode());
            warehouseLocItem.setLocName(numberGenerator.nextValue(IdRuleConstant.WAREHOUSE_LOC_NAME));

        }
        boolean duplicateCode ;


        // 校验库位代码是否重复
        WarehouseLoc warehouseLoc = new WarehouseLoc();
        BeanUtil.copyProperties(warehouseLocItem, warehouseLoc);
        if (ObjUtil.isNotEmpty(warehouseLocItem.getAreaCode())) {
            duplicateCode=dao.isDuplicate(warehouseLoc, "id",  "locCode",  "warehouseId","areaId","zoneId");

        }else {
            duplicateCode=dao.isDuplicate(warehouseLoc, "id",  "locCode",  "zoneId",  "warehouseId");

        }
        if (!ObjUtil.isNotNull(warehouseLocWms) && BooleanUtil.isTrue(duplicateCode)) {
            throw new ServiceException(GlobalExceptionEnum.CODE_EXIST, WarehouseEnum.LOC.getName(), warehouseLocItem.getLocCode());
        }

        // 校验库位使用类型只能是一种
        if (BooleanUtil.or(ObjUtil.equals(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), warehouseLocItem.getLocUseType()),
                ObjUtil.equals(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), warehouseLocItem.getLocUseType()))) {
            boolean duplicateUseType = dao.isDuplicate(warehouseLoc, "id", "locUseType", "warehouseCode", "zoneCode");
            if (BooleanUtil.isTrue(duplicateUseType)) {
                throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_LOC_USE_TYPE_DUPLICATE);
            }
        }

        warehouseLocItem.setIsDefault(YesNoEnum.NO.getCode());
        warehouseLocItem.setUpLocCode(warehouseLocItem.getLocCode());
        warehouseLocItem.setSource(WmsDataSourceTypeEnum.WMS_PUSH.getCode());
        SessionUserInfo sessionUserInfo = SessionContext.get();
        sessionUserInfo.setTenancy(10086L);
        sessionUserInfo.setCompanyId(10086L);
        sessionUserInfo.addAttribute("orgIdentifier","0001");
        SessionContext.put(sessionUserInfo);
        return dao.saveOrUpdate(warehouseLocItem);
    }
    /**
     * 新增或编辑库位
     *
     * @param warehouseLocItem
     * @return
     */
    public boolean save(WarehouseLocItem warehouseLocItem) {
        //如果是wms推送类型不允许编辑
        if (ObjUtil.equal(warehouseLocItem.getSource(), WmsDataSourceTypeEnum.WMS_PUSH.getCode())) {
            throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_ZONE_WMS_DATA_NOT_EDIT);
        }
        // 校验是否混商品为是，最大混商品数量必填
        if (BooleanUtil.and(StrUtil.isNotEmpty(warehouseLocItem.getIsMixSku()),
                ObjUtil.equals(YesNoEnum.YES.getCode(), warehouseLocItem.getIsMixSku()),
                ObjUtil.isNull(warehouseLocItem.getMaxMixSku()))) {
            throw new ServiceException(WarehouseExceptionEnum.MAX_MIX_SKU_EMPTY.getMsg());
        }
        // 校验是否混批次为是，最大混批次数量必填
        if (BooleanUtil.and(StrUtil.isNotEmpty(warehouseLocItem.getIsMixLot()),
                ObjUtil.equals(YesNoEnum.YES.getCode(), warehouseLocItem.getIsMixLot()),
                ObjUtil.isNull(warehouseLocItem.getMaxMixLot()))) {
            throw new ServiceException(WarehouseExceptionEnum.MAX_MIX_LOT_EMPTY.getMsg());
        }
        // 校验库位代码是否重复
        WarehouseLoc warehouseLoc = new WarehouseLoc();
        BeanUtil.copyProperties(warehouseLocItem, warehouseLoc);
        boolean duplicateCode = dao.isDuplicate(warehouseLoc, "id", "warehouseId","locCode");
        if (BooleanUtil.isTrue(duplicateCode)) {
            throw new ServiceException(GlobalExceptionEnum.CODE_EXIST, WarehouseEnum.LOC.getName(), warehouseLocItem.getLocCode());
        }
        // 校验库位名称是否重复
        boolean duplicateName = dao.isDuplicate(warehouseLoc, "id","warehouseId", "locName");
        if (BooleanUtil.isTrue(duplicateName)) {
            throw new ServiceException(GlobalExceptionEnum.NAME_EXIST, WarehouseEnum.LOC.getName(), warehouseLocItem.getLocName());
        }
        // 校验库位使用类型只能是一种
        if (BooleanUtil.or(ObjUtil.equals(WarehouseLocUseTypeEnum.TALLYING_STATION.getCode(), warehouseLocItem.getLocUseType()),
                ObjUtil.equals(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), warehouseLocItem.getLocUseType()))) {
            boolean duplicateUseType = dao.isDuplicate(warehouseLoc, "id", "locUseType");
            if (BooleanUtil.isTrue(duplicateUseType)) {
                throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_LOC_USE_TYPE_DUPLICATE);
            }
        }
        if (ObjUtil.isNotNull(warehouseLocItem.getId())){
            WarehouseLoc byId = dao.findById(warehouseLocItem.getId());
            if(ObjUtil.equals(warehouseLocItem.getIsEnable(),YesNoEnum.NO.getCode())||!ObjUtil.equals(byId.getWarehouseId(),warehouseLocItem.getWarehouseId())){
                List<Long> ids = new ArrayList<>();
                ids.add(warehouseLocItem.getId());
                List<InvLotLoc> qtyCountByLocId = invLotLocService.findQtyCountByLocId(ids);
                if (CollectionUtil.isNotEmpty(qtyCountByLocId)&&ObjUtil.equals(warehouseLocItem.getIsEnable(),YesNoEnum.NO.getCode())){
                    throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_LOC_DISABLE_ERROR);
                }
                if (CollectionUtil.isNotEmpty(qtyCountByLocId)&&!ObjUtil.equals(byId.getWarehouseId(),warehouseLocItem.getWarehouseId())){
                    throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_LOC_DISABLE_ERROR);
                }
            }
        }
        warehouseLocItem.setIsDefault(YesNoEnum.NO.getCode());
        Long id = warehouseLocItem.getId();
        warehouseLocItem.setSource(WmsDataSourceTypeEnum.CREATE.getCode());
        boolean saveResult = dao.saveOrUpdate(warehouseLocItem);
        // 新增时设置库位条形码
        if (BooleanUtil.and(ObjUtil.isNull(id), saveResult)) {
            FileObject fileObject = null;
            try {
                fileObject = customFile.barcodeGenerate(warehouseLocItem.getLocCode());
            } catch (Exception e) {
                throw new ServiceException(GlobalExceptionEnum.BARCODE_GENERATION_FAILED);
            }
            WarehouseLocFile warehouseLocFile = new WarehouseLocFile();
            warehouseLocFile.setBusinessId(Convert.toStr(warehouseLocItem.getId()));
            warehouseLocFile.setFileId(fileObject.getUuid());
            warehouseLocFile.setFileName(fileObject.getOrigFileName());
            warehouseLocFile.setOriginalName(fileObject.getOrigFileName());
            warehouseLocFile.setFileType(FileTypeEnum.png.getCode());
            warehouseLocFile.setFileSize(Convert.toStr(fileObject.getFileSize()));
            warehouseLocFile.setFilePath(fileObject.getUrl());
            warehouseLocFileDao.saveOrUpdate(warehouseLocFile);
        }
        return saveResult;
    }

    /**
     * 根据IDS批量删除库位
     *
     * @param ids
     * @return
     */
    public boolean deleteByIds(Object[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        // 校验是否存在启用的库位
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(WarehouseLoc::getId, ids);
        conditionRule.andEqual(WarehouseLoc::getIsEnable, StatusEnum.YES.getCode());
        List<WarehouseLoc> warehouseLocs = dao.find(conditionRule);
        if (CollectionUtil.isNotEmpty(warehouseLocs)) {
            throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_LOC_DELETE_NOT_DISABLE, warehouseLocs.stream().map(WarehouseLoc::getLocName).collect(Collectors.joining(StrUtil.COMMA)));
        }
        // 校验是否存在正常的库位
        conditionRule = new ConditionRule();
        conditionRule.andIn(WarehouseLoc::getId, ids);
        conditionRule.andEqual(WarehouseLoc::getStatus, LocStatusEnum.normal.getCode());
        warehouseLocs = dao.find(conditionRule);
        if (CollectionUtil.isNotEmpty(warehouseLocs)) {
            throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_LOC_DELETE_NOT_FREEZE, warehouseLocs.stream().map(WarehouseLoc::getLocName).collect(Collectors.joining(StrUtil.COMMA)));
        }
        // 删除库位条形码
        conditionRule = new ConditionRule();
        conditionRule.andIn(WarehouseLocFile::getBusinessId, ids);
        warehouseLocFileDao.delete(conditionRule);
        Long[] idArray = Arrays.stream(ids).map(Convert::toLong).toArray(Long[]::new);
        return dao.delete(idArray) > 0;
    }
    /**
     * 根据IDS批量删除库位(wms对接)
     *
     * @param codes
     * @return
     */
    public boolean crzDeleteByCodes(Object[] codes) {
        if (ArrayUtil.isEmpty(codes)) {
            throw new ServiceException(GlobalExceptionEnum.IDS_NOT_EMPTY);
        }
        ConditionRule conditionRuleX = new ConditionRule();
        conditionRuleX.andIn(WarehouseLoc::getUpLocCode, codes);
        conditionRuleX.andEqual(WarehouseLoc::getIsEnable, StatusEnum.YES.getCode());
        Long[] ids = dao.find(conditionRuleX).stream().map(WarehouseLoc::getId).toArray(Long[]::new);
        // 校验是否存在启用的库位
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(WarehouseLoc::getUpLocCode, codes);
        conditionRule.andEqual(WarehouseLoc::getIsEnable, StatusEnum.YES.getCode());
        List<WarehouseLoc> warehouseLocs = dao.find(conditionRule);
        if (CollectionUtil.isNotEmpty(warehouseLocs)) {
            throw new ServiceException(WarehouseExceptionEnum.WAREHOUSE_LOC_DELETE_NOT_DISABLE, warehouseLocs.stream().map(WarehouseLoc::getLocName).collect(Collectors.joining(StrUtil.COMMA)));
        }

        Long[] idArray = Arrays.stream(ids).map(Convert::toLong).toArray(Long[]::new);
        return dao.delete(idArray) > 0;
    }

    /**
     * 根据库位代码集合查询库位
     *
     * @param locCodeList
     * @return
     */
    public List<WarehouseLoc> listByLocCodeList(List<String> locCodeList) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(WarehouseLoc::getLocCode, locCodeList);
        return dao.find(WarehouseLoc.class, conditionRule);
    }

    /**
     * 根据库位名称集合查询库位
     *
     * @param locNameList
     * @return
     */
    public List<WarehouseLoc> listByLocNameList(List<String> locNameList) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(WarehouseLoc::getLocName, locNameList);
        return dao.find(WarehouseLoc.class, conditionRule);
    }

    /**
     * 根据库位名称集合查询库位
     *
     * @param locNameList
     * @return
     */
    public List<WarehouseLoc> listByLocNameList(List<String> locNameList,String warehouseName) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(WarehouseLoc::getLocName, locNameList);
        conditionRule.andIn(WarehouseLoc::getWarehouseName,warehouseName);
        return dao.find(WarehouseLoc.class, conditionRule);
    }

    /**
     * 库位分页查询
     *
     * @param warehouseLocPageCondition
     * @return
     */
    public PageResult<LocListPageQuery> pageLocList(WarehouseLocPageCondition warehouseLocPageCondition) {
        return dao.pageLocList(warehouseLocPageCondition);
    }

    /**
     * 库位分页查询
     *
     * @param warehouseLocPageCondition
     * @return
     */
    public PageResult<LocListPageQuery> pageLocListFrozen(WarehouseLocPageCondition warehouseLocPageCondition) {
        return dao.pageLocListFrozen(warehouseLocPageCondition);
    }

    public PageResult<LocListPageQuery> pageLocListAllFrozen(WarehouseLocPageCondition warehouseLocPageCondition){
        return dao.pageLocListAllFrozen(warehouseLocPageCondition);
    }

    /**
     * 根据库位编码、仓库编码获取csWhLocRecord库位信息
     *
     * @param locCode
     * @param warehouseId
     * @return
     */
    public WarehouseLoc getCdWhLoc(String locCode, Long warehouseId) {
        WarehouseLocCondition warehouseLocCondition = new WarehouseLocCondition();
        warehouseLocCondition.setLocCode(locCode);
        warehouseLocCondition.setWarehouseId(warehouseId);
        WarehouseLoc csWhLocRecord = dao.getCdWhLoc(warehouseLocCondition);
        //防止前台出现被删除后查询不到的错误
        if (csWhLocRecord != null) {
            return csWhLocRecord;
        }
        return null;
    }

    /**
     * @Description 库存冻结库位分页查询
     * @Param pageCondition
     * @Return * {@link PageResult< WarehouseLocQuery> }
     * @Date 2025/1/9 14:38
     * <AUTHOR>
     **/
    public PageResult<WarehouseLocQuery> getWarehouseLocPageForInventoryFreeze(WarehouseLocPageForInventoryFreezeCondition pageCondition) {
        pageCondition.setBusinessCodeList(Arrays.asList(WarehouseLocUseTypeEnum.TRANSITIONAL_STORAGE_LOCATION.getCode(), WarehouseLocUseTypeEnum.TALLYING_STATION.getCode()));
        return dao.getWarehouseLocPageForInventoryFreeze(pageCondition);
    }

    /**
     * @Description 根据区域id集合查询库位信息
     * @Param areaIdList
     * @Return * {@link List< WarehouseLoc> }
     * @Date 2025/1/22 13:55
     * <AUTHOR>
     **/
    public List<WarehouseLoc> getListByAreaIdList(List<Long> areaIdList) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andIn(WarehouseLoc::getAreaId, areaIdList);
        return dao.find(conditionRule);
    }

    /**
     * @Description 根据库区id集合查询库位信息
     * @Param warehouseZoneIdList
     * @Return * {@link List< WarehouseLoc> }
     * @Date 2025/1/22 13:56
     * <AUTHOR>
     **/
    public List<WarehouseLoc> getListByWarehouseZoneIdList(List<Long> warehouseZoneIdList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(WarehouseLoc::getZoneId, warehouseZoneIdList);
        return dao.find(conditionRule);
    }

    public Map<Long, WarehouseLoc> getWarehouseLocMap(List<Long> warehouseIdList) {
        List<WarehouseLoc> list = dao.findByIds(warehouseIdList.toArray(new Long[]{}));
        return list.stream().collect(Collectors.toMap(WarehouseLoc::getId, Function.identity()));
    }

    /**
     * 根据规则条件动态组装SQL查询数据
     * @param condition
     * @return
     */
    public List<LocListPageQuery> findLocList(LocListCondition condition) {
        StringBuffer orderBy = new StringBuffer(" order by ");
        StringBuffer equalsField = new StringBuffer("");
        Map<String, LocListCondition.LotAttQuery> attQueryMap = condition.getMap();
        for (String keyField : attQueryMap.keySet()) {
            LocListCondition.LotAttQuery attQuery = attQueryMap.get(keyField);
            FieldTypeEnum anEnum = EnumUtil.getBy(FieldTypeEnum::getCode, attQuery.getFieldType());
            if (ObjUtil.isNull(anEnum)){
                throw new ServiceException(StockExcepitonEnum.LOT_LOC_NO_DATA);
            }
            switch (anEnum){
                case STRING:
                    equalsField.append(AND).append(keyField).append(" = '").append(attQuery.getSortType()).append("' ");
                    break;
                case DATE:
                case DATETIME:
                    equalsField.append(AND).append(keyField).append(" is not null ");
                    equalsField.append(AND).append(keyField).append(" != '' ");
                    if (SortTypeEnum.SORT_TYPE_1.getCode().equals(attQuery.getSortType())){
                        //asc
                        orderBy.append(keyField).append(" asc,");
                    }else{
                        //desc
                        orderBy.append(keyField).append(" desc,");
                    }
                    break;
                case SELECT:
                    if (StrUtil.isEmpty(attQuery.getSortType())){
                        throw new ServiceException(StockExcepitonEnum.LOT_LOC_NO_DATA);
                    }
                    List<KeyValueObj> objList = JSONUtil.toList(attQuery.getSortType(), KeyValueObj.class);
                    if (CollUtil.isNotEmpty(objList)){
                        equalsField.append(AND).append(keyField).append(" in (");
                        for (KeyValueObj keyValueObj : objList) {
                            equalsField.append("'").append(keyValueObj.getKey()).append("',");
                        }
                        equalsField.deleteCharAt(equalsField.length()-1).append(") ");
                    }
                    break;
            }
        }
        if (!orderBy.toString().equals(" order by ")) {
            condition.setOrderField(orderBy.deleteCharAt(orderBy.length()-1).toString());
        }
        condition.setEqualsField(equalsField.toString());

        return dao.findLocList(condition);
    }

    public List<WarehouseLoc> getLocIdListByShelfId(Long shelfId) {
        return dao.find(new ConditionRule().andEqual(WarehouseLoc::getShelfId,shelfId));
    }

    /**
     *@Description 根据库位编码和仓库ID查询库位信息
     *@Param code
     *@param warehouseId
     *@Return * {@link List< WarehouseLoc> }
     *@Date 2025/7/1 16:16
     *<AUTHOR>
     **/
    public List<WarehouseLoc> listByCodeAndWarehouseId(String code, Long warehouseId) {
        ConditionRule conditionRule = new ConditionRule();
        conditionRule.andEqual(WarehouseLoc::getLocCode,code);
        conditionRule.andEqual(WarehouseLoc::getWarehouseId,warehouseId);
        return dao.find(conditionRule);
    }
}

