package com.chinaservices.wms.module.stock.mutli.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.chinaservices.module.base.ModuleBaseController;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.stock.multi.domain.*;
import com.chinaservices.wms.module.stock.mutli.service.InvLotAttService;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocService;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocSnService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;



/**
 * 多级库存
 */
@RestController
@RequestMapping("/api/inventory/invLotLoc")
public class InvLotLocController extends ModuleBaseController {



    @Autowired
    private InvLotLocService invLotLocService;

    @Autowired
    private InvLotLocSnService invLotLocSnService;
    @Autowired
    private InvLotAttService invLotAttService;


    /**
     * 根据货主分页查询库存
     */
    @PostMapping("/pageByOwner")
    @SaCheckPermission("multiStock:ownerList:headBtn:page")
    public ResponseData<PageResult<InvLotLocQuery>> pageByOwner(@RequestBody InvLotLocPageCondition invLotLocCondition) {
        // 执行分页查询
        PageResult<InvLotLocQuery> pageResult = invLotLocService.pageByOwner(invLotLocCondition);
        return ResponseData.success(pageResult);
    }

    /**
     * 根据货主分页查询库存导出
     */
    @PostMapping("/pageByOwner/page/export")
    public void pageByOwnerExport(@RequestBody InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {
        // 执行分页查询
        invLotLocService.pageByOwnerExport(invLotLocCondition, httpServletResponse);
    }

    /**
     * 根据商品分页查询库存
     *
     * @param invLotLocCondition
     * @return
     */
    @PostMapping("/pageBySku")
    @SaCheckPermission("multiStock:skuList:headBtn:page")
    public ResponseData<PageResult<InvLotLocQuery>> pageBySku(@RequestBody InvLotLocPageCondition invLotLocCondition) {

        // 执行分页查询
        PageResult<InvLotLocQuery> pageResult = invLotLocService.pageBySku(invLotLocCondition);
        return ResponseData.success(pageResult);
    }

    /**
     * 根据商品分页查询库存导出
     *
     * @param invLotLocCondition
     * @return
     */
    @PostMapping("/pageBySku/page/export")
    public void pageBySkuExport(@RequestBody InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {

        // 执行分页查询
       invLotLocService.pageBySkuExport(invLotLocCondition, httpServletResponse);
    }

    /**
     * 根据批次分页查询库存
     */
    @PostMapping("/pageByLotNum")
    @SaCheckPermission("multiStock:lotList:headBtn:page")
    public ResponseData<PageResult<InvLotLocQuery>> pageByLotNum(@RequestBody InvLotLocPageCondition invLotLocCondition) {

        // 执行分页查询
        PageResult<InvLotLocQuery> pageResult = invLotLocService.pageByLotNum(invLotLocCondition);

        return ResponseData.success(pageResult);
    }

    /**
     * 根据批次分页查询库存导出
     */
    @PostMapping("/pageByLotNum/page/export")
    public void pageByLotNumExport(@RequestBody InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {

        // 执行分页查询
        invLotLocService.pageByLotNumExport(invLotLocCondition, httpServletResponse);

    }


    /**
     * 根据库位分页查询库存
     *
     * @param invLotLocCondition
     * @return
     */
    @PostMapping("/pageByLoc")
    @SaCheckPermission("multiStock:locList:headBtn:page")
    public ResponseData<PageResult<InvLotLocQuery>> pageByLoc(@RequestBody InvLotLocPageCondition invLotLocCondition) {

        //执行分页查询
        PageResult<InvLotLocQuery> pageResult = invLotLocService.pageByLoc(invLotLocCondition);

        return ResponseData.success(pageResult);
    }

    /**
     * 根据库位分页查询库存导出
     *
     * @param invLotLocCondition
     * @return
     */
    @PostMapping("/pageByLoc/page/export")
    public void pageByLocExport(@RequestBody InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {

        //执行分页查询
        invLotLocService.pageByLocExport(invLotLocCondition, httpServletResponse);

    }

    /**
     * 根据商品库位分页查询库存
     *
     * @param invLotLocCondition
     * @return
     */
    @PostMapping("/pageBySkuLoc")
    @SaCheckPermission("multiStock:skuLocList:headBtn:page")
    public ResponseData<PageResult<InvLotLocQuery>> pageBySkuLoc(@RequestBody InvLotLocPageCondition invLotLocCondition) {

        // 执行分页查询
        PageResult<InvLotLocQuery> pageResult = invLotLocService.pageBySkuLoc(invLotLocCondition);
        return ResponseData.success(pageResult);
    }

    /**
     * 根据商品库位分页查询库存导出
     *
     * @param invLotLocCondition
     * @return
     */
    @PostMapping("/pageBySkuLoc/page/export")
    public void pageBySkuLocExport(@RequestBody InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {

        // 执行分页查询
        invLotLocService.pageBySkuLocExport(invLotLocCondition, httpServletResponse);
    }




    /**
     * 商品批次库位库存查询
     */
    @PostMapping("/pageBySkuLocLot")
    @SaCheckPermission("multiStock:skuLocLotList:headBtn:page")
    public ResponseData<PageResult<InvLotLocQuery>> pageBySkuLocLot(@RequestBody InvLotLocPageCondition invLotLocCondition) {

        // 执行分页查询
        PageResult<InvLotLocQuery> pageResult = invLotLocService.pageBySkuLocLot(invLotLocCondition);

        return ResponseData.success(pageResult);
    }


    /**
     * 商品批次库位库存查询导出
     */
    @PostMapping("/pageBySkuLocLot/page/export")
    public ResponseData pageBySkuLocLotExport(@RequestBody InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws IOException {

        // 执行分页查询
        PageResult<InvLotLocQuery> pageResult = invLotLocService.pageBySkuLocLotAndPalletNum(invLotLocCondition);

        return ResponseData.success(pageResult);
    }


    /**
     *  按商品/库位/批次号/容器号查询
     *@Param invLotLocCondition
     *@Return * {@link ResponseData< PageResult< InvLotLocQuery>> }
     *@Date 2025/3/7 9:36
     *<AUTHOR>
     **/
    @PostMapping("/pageBySkuLocLotAndPalletNum")
    @SaCheckPermission("multiStock:skuLocLotPalletList:headBtn:page")
    public ResponseData<PageResult<InvLotLocQuery>> pageBySkuLocLotAndPalletNum(@RequestBody InvLotLocPageCondition invLotLocCondition) {

        // 执行分页查询
        PageResult<InvLotLocQuery> pageResult = invLotLocService.pageBySkuLocLotAndPalletNum(invLotLocCondition);

        return ResponseData.success(pageResult);
    }


    /**
     *  按商品/库位/批次号/容器号查询导出
     *@Param invLotLocCondition
     *@param httpServletResponse
     *@Return Void
     *@Date 2025/3/7 9:46
     *<AUTHOR>
     **/
    @PostMapping("/pageBySkuLocLotAndPalletNum/page/export")
    public void pageBySkuLocLotAndPalletNumExport(@RequestBody InvLotLocPageCondition invLotLocCondition, HttpServletResponse httpServletResponse) throws Exception {

        // 执行导出
        invLotLocService.pageBySkuLocLotAndPalletNumExport(invLotLocCondition, httpServletResponse);

    }



   /**
    *   序列号库存分页查询
    *@Param condition
    *@Return * {@link ResponseData< PageResult< InvLotLocSnQuery>> }
    *@Date 2025/3/10 15:12
    *<AUTHOR>
    **/
    @PostMapping("/snStockPage")
    @SaCheckPermission("report:snStockList:headBtn:page")
    public ResponseData<PageResult<InvLotLocSnQuery>> snStockPage(@RequestBody InvLotLocSnPageCondition condition){
        return ResponseData.success(invLotLocSnService.snStockPage(condition));
    }

    /**
     *  序列号库存导出
     *@Param condition
     *@Return * {@link ResponseData< PageResult< InvLotLocSnQuery>> }
     *@Date 2025/3/10 15:12
     *<AUTHOR>
     **/
    @PostMapping("/exportSnStock")
    public void exportSnStock(@RequestBody InvLotLocSnPageCondition condition,HttpServletResponse response) throws IOException {
        invLotLocSnService.exportSnStock(condition,response);
    }

    /**
     * 箱码-序列码
     *
     * @param condition 条件
     * @return {@link ResponseData }<{@link List }<{@link InvLotLocSnBoxQuery }>>
     */
    @PostMapping("/snBoxCode")
    public ResponseData<List<InvLotLocSnBoxQuery>> snBoxCode(@RequestBody InvLotLocSnPageCondition condition)
    {
        return ResponseData.success(invLotLocSnService.snBoxCode(condition));
    }



    /**
     *  定时生成库龄报表数据接口
     *@Param
     *@Return * {@link ResponseData }
     *@Date 2025/3/12 18:58
     *<AUTHOR>
     **/
    @PostMapping("/ageReportData")
    public ResponseData ageReportData() {
        invLotLocService.ageReportData();
        return ResponseData.success();
    }


    /**
     * 商品大类库位库存查询
     */
    @PostMapping("/pageBySkuGroupLoc")
    @SaCheckPermission("multiStock:skuGroupLocList:headBtn:page")
    public ResponseData<PageResult<InvLotLocBySkuGroupQuery>> pageBySkuGroupLoc(@RequestBody InvLotLocBySkuGroupPageCondition invLotLocCondition) {
        // 执行分页查询
        PageResult<InvLotLocBySkuGroupQuery> pageResult = invLotLocService.pageBySkuGroupLoc(invLotLocCondition);
        return ResponseData.success(pageResult);
    }

    /**
     * 根据批次号查询批次属性数据
     * @param invLotLocCondition
     * @return
     */
    @PostMapping("/getLotByLotNum")
    public ResponseData<InvLotLocQuery> getLotByLotNum(@RequestBody InvLotLocSnQuery invLotLocCondition) {

        InvLotLocQuery invLotLocQuery = invLotAttService.getLotByLotNum(invLotLocCondition);
        return ResponseData.success(invLotLocQuery);
    }

    @PostMapping("/pageBySkuInv")
    public ResponseData<PageResult<InvLotLocQuery>> pageBySkuInv(@RequestBody InvLotLocPageCondition invLotLocCondition) {
        // 执行分页查询
        PageResult<InvLotLocQuery> pageResult = invLotLocService.pageBySkuInv(invLotLocCondition);
        return ResponseData.success(pageResult);
    }
}
