package com.chinaservices.wms.module.process.combinations.service;


import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.domain.IdCondition;
import com.chinaservices.wms.module.process.combinations.dao.CombinationsDetailsDao;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsDetailsPageCondition;
import com.chinaservices.wms.module.process.combinations.domain.CombinationsDetailsQuery;
import com.chinaservices.wms.module.process.combinations.model.CombinationsDetails;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName CombinationsDetailsService
 * <AUTHOR>
 * @Date 2025/4/15 9:25
 * @Description
 * @Version 1.0
 */
@Service
public class CombinationsDetailsService extends ModuleBaseServiceSupport<CombinationsDetailsDao, CombinationsDetails, Long> {

    /**
     *@Description 根据编号进行删除
     *@Param combinationsNoList
     *@Return Void
     *@Date 2025/4/15 11:34
     *<AUTHOR>
     **/
    public void deleteByCombinationsNoList(List<String> combinationsNoList) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(CombinationsDetails::getCombinationsNo,combinationsNoList);
        dao.delete(conditionRule);
    }

    /**
     *@Description 子件分页查询
     *@Param condition
     *@Return * {@link PageResult< CombinationsDetailsQuery> }
     *@Date 2025/4/15 14:46
     *<AUTHOR>
     **/
    public PageResult<CombinationsDetailsQuery> page(CombinationsDetailsPageCondition condition) {
        return dao.findPage(condition);
    }

    /**
     *@Description 根据主键id批量删除
     *@Param condition
     *@Return Void
     *@Date 2025/4/16 9:20
     *<AUTHOR>
     **/
    public void batchDel(IdCondition condition) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andIn(CombinationsDetails::getId,condition.getIdList());
        dao.delete(conditionRule);
    }

    /**
     *@Description 根据编号查询子件信息
     *@Param combinationsNo
     *@Return * {@link List< CombinationsDetailsQuery> }
     *@Date 2025/4/16 9:47
     *<AUTHOR>
     **/
    public List<CombinationsDetailsQuery> getByCombinationNo(String combinationsNo) {
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual(CombinationsDetails::getCombinationsNo,combinationsNo);
        return dao.find(CombinationsDetailsQuery.class,conditionRule);
    }
}
