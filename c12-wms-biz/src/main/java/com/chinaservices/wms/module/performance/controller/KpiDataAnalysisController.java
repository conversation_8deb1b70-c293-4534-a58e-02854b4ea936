package com.chinaservices.wms.module.performance.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.wms.module.performance.domain.*;
import com.chinaservices.wms.module.performance.service.KpiDataAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 绩效数据分析控制类
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/performance/ata/analysis")
public class KpiDataAnalysisController {


    @Autowired
    private KpiDataAnalysisService service;

    /**
     * 考核统计
     * @return
     */
    @PostMapping(value = "/statistics")
    @SaCheckPermission("performanceDataAnalysis:evaluationSummaryList:headBtn:page")
    public ResponseData<KpiStatisticsQuery> statistics(@RequestBody KpiRecordCondition condition){
        return ResponseData.success(service.statistics(condition));
    }

    /**
     * 岗位考核得分情况
     * @return
     */
    @PostMapping(value = "/post/score")
    @SaCheckPermission("performanceDataAnalysis:positionAssessmentScoreList:headBtn:page")
    public ResponseData<List<KpiPostScoreQuery>> postScore(@RequestBody KpiRecordCondition condition){
        return ResponseData.success(service.postScore(condition));
    }

    /**
     * 考核得分分布情况
     * @return
     */
    @PostMapping(value = "/examine/score")
    @SaCheckPermission("performanceDataAnalysis:scoreDistributionList:headBtn:page")
    public ResponseData<KpiScoreProportionQuery> examineScore(@RequestBody KpiRecordCondition condition){
        return ResponseData.success(service.examineScore(condition));
    }

    /**
     * 考核排名
     * @return
     */
    @PostMapping(value = "/examine/ranking")
    @SaCheckPermission("performanceDataAnalysis:performanceRankingList:headBtn:page")
    public ResponseData<List<KpiRankingQuery>> examineRanking(@RequestBody KpiRecordCondition condition){
        return ResponseData.success(service.examineRanking(condition));
    }
}
