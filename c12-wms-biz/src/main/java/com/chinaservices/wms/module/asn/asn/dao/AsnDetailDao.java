package com.chinaservices.wms.module.asn.asn.dao;

import com.chinaservices.module.base.ModuleBaseDaoSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.sqlid.asn.asn.AsnDetailSlqId;
import com.chinaservices.wms.common.sqlid.asn.asn.AsnDetailSqlId;
import com.chinaservices.wms.module.asn.asn.domain.*;
import com.chinaservices.wms.module.asn.asn.model.AsnDetail;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 预到货通知单明细 Dao
 */
@Repository
public class AsnDetailDao extends ModuleBaseDaoSupport<AsnDetail, Long> {

    /**
     * 入库计划明细分页查询
     * @param condition
     * @return
     */
    public PageResult<AsnDetailInfoQuery> page(AsnDetailPageCondition condition){
        return sqlExecutor.page(AsnDetailInfoQuery.class, AsnDetailSlqId.ASNDETAIL_QUERY_LIST, condition);
    }

    /**
     * 根据Id查询入库计划明细
     * @param id
     * @return
     */
    public AsnDetailInfoQuery getEntityById(Long id){
        return sqlExecutor.findFirst(AsnDetailInfoQuery.class, AsnDetailSlqId.ASNDETAIL_QUERY_LIST,"id", id);
    }

    /**
     * 根据预到货单头AsnId获取最大行号
     * @param condition
     * @return
     */
    public AsnDetailItem getMaxLineNo(AsnDetailCondition condition){
        return sqlExecutor.findFirst(AsnDetailItem.class, AsnDetailSlqId.ASNDETAIL_QUERY_MAX_LINENO_BY_ASN_ID, condition);
    }

    /**
     * 根据预到货单头AsnId获取预到货通知单明细
     * @param asnNo
     * @return
     */
    public List<AsnDetail> findByAsnNo(String asnNo) {
        return sqlExecutor.find(AsnDetail.class, AsnDetailSlqId.ASNDETAIL_QUERY_BY_ASN_ID, "asnNo", asnNo);
    }

    /**
     * 根据预到货单头AsnId获取预到货通知单明细
     * @param condition
     * @return
     */
    public List<Map<String, Object>> findByAsnId(AsnDetailCondition condition) {
        return sqlExecutor.find(AsnDetailSlqId.ASNDETAIL_QUERY_BY_ASN_ID, condition);
    }

    /**
     * 查询由条件
     *
     * @param condition 条件
     * @return {@link List }<{@link AsnDetail }>
     */
    public List<AsnDetail> findByCondition(AsnDetailCondition condition)
    {
        return sqlExecutor.find(AsnDetail.class, AsnDetailSlqId.ASNDETAIL_QUERY_BY_ASN_ID, condition);
    }


    /**
     * 根据预到货单头AsnId获取预到货通知单明细
     * @param condition
     * @return
     */
    public AsnDetail getByAsnIdAndLineNo(AsnDetailCondition condition) {
       return sqlExecutor.findFirst(AsnDetail.class, AsnDetailSlqId.ASNDETAIL_QUERY_BY_ASN_ID, condition);
    }

    /**
     * 根据批量ansId删除入库计划明细
     * @param asnIdArray
     */
    public void delByAsnId(Long[] asnIdArray) {
        sqlExecutor.update(AsnDetailSlqId.ASNDETAIL_DELETE_BY_ASN_ID,"asnIdArray", asnIdArray);
    }

    public List<AsnDetailInfo> getAsnDetailInfoByAsnNo(String asnNo) {
        return sqlExecutor.find(AsnDetailInfo.class,AsnDetailSqlId.GET_ASN_DETAIL_INFO_BY_ASN_ID,"asnNo",asnNo);
    }
    public List<AsnDetailInfo> getAsnDetailInfoByAsnNos(List<String> asnNos) {
        return sqlExecutor.find(AsnDetailInfo.class,AsnDetailSqlId.GET_ASN_DETAIL_INFO_BY_ASN_ID,"asnNos",asnNos);
    }

    /**
     * 入库订单详情查询
     *
     * @param condition 条件
     * @return {@link List }<{@link AsnDetailByReceiveItem }>
     */
    public List<AsnDetailByReceiveItem> selectListByAsnNo(AsnDetailByReceiveICondition condition) {
        return sqlExecutor.find(AsnDetailByReceiveItem.class,
                AsnDetailSqlId.QUERY_DETAIL_BY_ASN_NO, condition);
    }

    /**
     * 入库订单详情分页查询
     * @param condition 条件
     * @return {@link List }<{@link AsnDetailByReceiveItem }>
     */
    public PageResult<AsnDetailByAsnNoPageQuery> selectPageByAsnNo(AsnDetailByAsnNoPageCondition condition) {
        return sqlExecutor.page(AsnDetailByAsnNoPageQuery.class, AsnDetailSqlId.ASN_DETAIL_BY_ASN_NO_QUERY_PAGE_LIST, condition);
    }
}
