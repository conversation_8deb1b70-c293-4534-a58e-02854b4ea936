package com.chinaservices.wms.module.process.staff.model;


import com.chinaservices.module.base.ModuleBaseModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 资质证书
 */
@Data
@Entity
@Table(name = "cs_process_staff_certificate")
@EqualsAndHashCode(callSuper = true)
public class ProcessStaffCertificate extends ModuleBaseModel {
  /**
   * 加工人员id
   */
  private long staffId;
  /**
   * 证书类型
   */
  private String certificateType;
  /**
   * 证书有效期起始时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date validityPeriodFm;
  /**
   * 证书有效期截止时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date validityPeriodTo;
}
