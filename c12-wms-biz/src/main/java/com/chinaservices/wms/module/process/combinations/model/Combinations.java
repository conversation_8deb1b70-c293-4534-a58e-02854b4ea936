package com.chinaservices.wms.module.process.combinations.model;

import java.io.Serializable;
import java.util.Date;

import com.chinaservices.module.base.ModuleBaseModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 组合件信息表
 * cs_combinations
 */
@Data
@Entity
@Table(name = "cs_combinations")
public class Combinations extends ModuleBaseModel {

    /**
     * 组合编号
     */
    private String combinationsNo;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 商品编号
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 包装规格id
     */
    private Long packageId;

    /**
     * 包装规格名称
     */
    private String packageName;

    /**
     * 包装单位id
     */
    private Long packageUnitId;

    /**
     * 包装单位名称
     */
    private String packageUnitName;

    /**
     * 数量
     */
    private Long count;

    /**
     * 数量ea
     */
    private Long countEa;

    /**
     * 组合件状态:0-启用；1-停用
     * @see com.chinaservices.core.enums.YesNoEnum
     */
    private String combinationsStatus;

    /**
     * 组合件类型:1-组合件(A=B+C);
     * @see com.chinaservices.wms.common.enums.process.ProcessCombinationsTypeEnum
     */
    private String combinationsType;

    /**
     * 备注
     */
    private String remark;

}