package com.chinaservices.wms.module.stock.expirywarning.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.auth.module.message.feign.RemoteMessageSendService;
import com.chinaservices.auth.module.user.feign.RemoteUserService;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.module.base.ModuleBaseServiceSupport;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.wms.common.enums.stock.ExpiryWarningSaveStageEnum;
import com.chinaservices.wms.common.enums.stock.ExpiryWarningStatusEnum;
import com.chinaservices.wms.common.exception.StockExcepitonEnum;
import com.chinaservices.wms.module.basic.lot.model.WarehouseLotDetail;
import com.chinaservices.wms.module.basic.lot.service.WarehouseLotDetailService;
import com.chinaservices.wms.module.basic.sku.model.Sku;
import com.chinaservices.wms.module.basic.sku.service.SkuService;
import com.chinaservices.wms.module.message.service.SendMessageService;
import com.chinaservices.wms.module.stock.expirywarning.dao.ExpiryWarningDao;
import com.chinaservices.wms.module.stock.expirywarning.domain.ExpiryWarningPageCondition;
import com.chinaservices.wms.module.stock.expirywarning.domain.ExpiryWarningPageQuery;
import com.chinaservices.wms.module.stock.expirywarning.model.ExpiryWarning;
import com.chinaservices.wms.module.stock.expirywarning.model.ExpiryWarningSetting;
import com.chinaservices.wms.module.stock.mutli.model.InvLotAtt;
import com.chinaservices.wms.module.stock.mutli.model.InvLotLoc;
import com.chinaservices.wms.module.stock.mutli.service.InvLotAttService;
import com.chinaservices.wms.module.stock.mutli.service.InvLotLocService;
import com.chinaservices.wms.module.warehouse.warehouse.model.Warehouse;
import com.chinaservices.wms.module.warehouse.warehouse.service.WarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Chen
 * @date 2025/6/17
 * @description 效期预警Service层
 */
@Service
public class ExpiryWarningService extends ModuleBaseServiceSupport<ExpiryWarningDao, ExpiryWarning, Long> {

    @Autowired
    private ExpiryWarningSettingService expiryWarningSettingService;
    @Autowired
    private SkuService skuService;
    @Autowired
    private WarehouseLotDetailService warehouseLotDetailService;
    @Autowired
    private InvLotAttService invLotAttService;
    @Autowired
    private InvLotLocService invLotLocService;
    @Autowired
    private RemoteMessageSendService remoteMessageSendService;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private SendMessageService sendMessageService;

    /**
     * 分页查询效期预警
     * @param condition 查询条件
     * @return PageResult<ExpiryWarningPageQuery>
     */
    public PageResult<ExpiryWarningPageQuery> page(ExpiryWarningPageCondition condition){
        return dao.page(condition);
    }

    /**
     * 新增效期预警记录
     * @param ownerId     货主主键
     * @param warehouseId 仓库主键
     * @param skuId       商品主键
     */
    public void addExpiryWarning(String stage, String lotNum, Long ownerId, Long warehouseId, Long skuId, String... asnNo){
        ConditionRule conditionRule = ConditionRule.getInstance();
        conditionRule.andEqual("ownerId", ownerId);
        conditionRule.andEqual("warehouseId", warehouseId);
        conditionRule.andEqual("skuId", skuId);
        Sku sku = skuService.findById(skuId);
        List<InvLotLoc> invLotLocList = invLotLocService.findAll();
        ExpiryWarningSetting warningSetting = expiryWarningSettingService.findFirst(conditionRule);
        if(ObjUtil.isNotNull(warningSetting)){
            // 获取效期预警
            conditionRule.andEqual("lotNum", lotNum);
            ExpiryWarning expiryWarning = dao.findFirst(conditionRule);
            // 当前日期
            Date now = DateUtil.date();
            // 失效日期
            Date expiredDate = DateUtil.endOfDay(getExpiredDateByLotNum(lotNum, sku));
            // 入库效期
            if(ObjUtil.isNotNull(warningSetting.getInLiftDays())){
                Date inboundDate = DateUtil.beginOfDay(DateUtil.offsetDay(expiredDate, Math.negateExact(warningSetting.getInLiftDays() - 1)));
                // 已过期 提示用户已过期 中断入库操作
                if(now.compareTo(inboundDate) > 0 && StrUtil.equals(ExpiryWarningSaveStageEnum.INBOUND.getCode(), stage)) {
                    // 批量操作直接抛出入库单号即可
                    if(asnNo.length > 0){
                        throw new ServiceException(StockExcepitonEnum.EXPIRY_WARNING_BATCH_INBOUND_EXPIRED, asnNo[0], warningSetting.getInLiftDays());
                    }
                    throw new ServiceException(StockExcepitonEnum.EXPIRY_WARNING_INBOUND_EXPIRED, warningSetting.getInLiftDays());
                }
            }
            // 入库且不存在效期预警时 新增效期预警
            if(ObjUtil.isNull(expiryWarning) && StrUtil.equals(ExpiryWarningSaveStageEnum.INBOUND.getCode(), stage)){
                expiryWarning = new ExpiryWarning();
                expiryWarning.setSkuId(skuId);
                expiryWarning.setWarehouseId(warehouseId);
                expiryWarning.setOwnerId(ownerId);
                expiryWarning.setLotNum(lotNum);
                expiryWarning.setExpiryDate(expiredDate);
                expiryWarning.setInLiftDays(warningSetting.getInLiftDays());
                expiryWarning.setOutLiftDays(warningSetting.getOutLiftDays());
                expiryWarning.setWarningDays(warningSetting.getWarningDays());
                setExpiryStatus(expiryWarning);
                setAvailableQty(expiryWarning, invLotLocList);
                preSave(expiryWarning);
                dao.insert(expiryWarning);
            }
            // 出库效期
            if(ObjUtil.isNotNull(warningSetting.getOutLiftDays())){
                Date outboundDate = DateUtil.beginOfDay(DateUtil.offsetDay(expiredDate, Math.negateExact(warningSetting.getOutLiftDays() - 1)));
                // 已过期 提示用户已过期 中断入库操作
                if(now.compareTo(outboundDate) > 0 && StrUtil.equals(ExpiryWarningSaveStageEnum.OUTBOUND.getCode(), stage)){
                    throw new ServiceException(StockExcepitonEnum.EXPIRY_WARNING_OUTBOUND_EXPIRED, warningSetting.getOutLiftDays());
                }
            }
        }
    }

    /**
     * 定时器更新预警状态
     * @return Boolean
     */
    public Boolean updateStatusByTimer(){
        List<ExpiryWarning> expiryWarningList = dao.findAll();
        List<InvLotLoc> invLotLocList = invLotLocService.findAll();
        for (ExpiryWarning expiryWarning : expiryWarningList) {
            // 设置商品库存数
            setAvailableQty(expiryWarning, invLotLocList);
            // 为零则不继续更新
            if(ObjUtil.equal(expiryWarning.getAvailableQty(), BigDecimal.ZERO)){
                preSave(expiryWarning);
                continue;
            }
            // 设置效期预警状态
            setExpiryStatus(expiryWarning);
            SessionUserInfo sessionUserInfo = SessionContext.get();
            sessionUserInfo.setTenancy(expiryWarning.getTenancy());
            sessionUserInfo.setCompanyId(expiryWarning.getCompanyId());
            SessionContext.put(sessionUserInfo);
            preSave(expiryWarning);
        }
        return dao.batchUpdate(expiryWarningList) > 0;
    }

    /**
     * 根据失效时间变更效期状态
     * @param expiryWarning 效期预警实体类
     */
    private void setExpiryStatus(ExpiryWarning expiryWarning){
        // 默认不可见
        expiryWarning.setVisibility(YesNoEnum.NO.getCode());
        // 失效日期
        Date expiredDate = DateUtil.endOfDay(expiryWarning.getExpiryDate());
        // 临期日期
        Date nearExpireDate = DateUtil.beginOfDay(DateUtil.offsetDay(expiredDate, Math.negateExact(expiryWarning.getWarningDays() - 1)));
        // 当前日期
        Date now = DateUtil.date();
        // 当前日期 > 失效日期 = 已过期
        if(now.compareTo(expiredDate) > 0) {
            expiryWarning.setStatus(ExpiryWarningStatusEnum.EXPIRED.getCode());
            expiryWarning.setVisibility(YesNoEnum.YES.getCode());
            expiryWarning.setNearExpiryDays(null);
            expiryWarning.setExpiryDays(Math.toIntExact(DateUtil.between(DateUtil.beginOfDay(expiryWarning.getExpiryDate()), now, DateUnit.DAY)));
        }
        // 当前日期在失效日期之前 且 当前时间 > 临期时间 = 临期
        else if(now.compareTo(nearExpireDate) > 0) {
            expiryWarning.setVisibility(YesNoEnum.YES.getCode());
            // 设置失效日期偏移1天使得当天临期时间为1天而非0天
            expiryWarning.setNearExpiryDays(Math.toIntExact(DateUtil.between(now, DateUtil.offsetDay(expiredDate, 1), DateUnit.DAY)));
            // 第一次进入临期 发送站内信
            if(!StrUtil.equals(ExpiryWarningStatusEnum.NEAR_EXPIRY.getCode(), expiryWarning.getStatus())){
                Sku sku = skuService.findById(expiryWarning.getSkuId());
                Warehouse warehouse = warehouseService.findById(expiryWarning.getWarehouseId());
                sendMessageService.sendMessageByExpiryWarning(expiryWarning, warehouse, sku.getSkuName());
            }
            expiryWarning.setStatus(ExpiryWarningStatusEnum.NEAR_EXPIRY.getCode());
        }
    }

    /**
     * 通过批次号获取失效时间
     * @param lotNum 批次号
     * @return Date
     */
    private Date getExpiredDateByLotNum(String lotNum, Sku sku){
        try {// 获取批次属性明细信息
            ConditionRule conditionRule = ConditionRule.getInstance();
            conditionRule.andEqual("lotId", sku.getLotId());
            List<WarehouseLotDetail> detailList = warehouseLotDetailService.find(conditionRule);
            // 抽取失效日期对应的warehouseLotDetail信息 以获取lot_att
            WarehouseLotDetail warehouseLotDetail = detailList.stream().filter(
                    e -> StrUtil.equals("失效日期", e.getTitle())
            ).toList().getFirst();
            // 获取批次属性信息
            conditionRule = ConditionRule.getInstance();
            conditionRule.andEqual("lotNum", lotNum);
            InvLotAtt invLotAtt = invLotAttService.find(conditionRule).getFirst();
            // 抽取对应的Getter方法 转换为驼峰格式 get_lot_att03 -> getLotAtt03
            String getter = StrUtil.toCamelCase("get_" + warehouseLotDetail.getLotAtt());
            // 反射调用方法
            Method method = InvLotAtt.class.getMethod(getter);
            method.setAccessible(true);
            return Convert.convert(Date.class, method.invoke(invLotAtt));
        } catch (Exception e) {
            throw new ServiceException(StockExcepitonEnum.EXPIRY_WARNING_GET_EXPIRED_DATE_ERROR);
        }

    }

    /**
     * 效期预警设置商品库存数
     * @param expiryWarning 效期预警
     */
    private void setAvailableQty(ExpiryWarning expiryWarning, List<InvLotLoc> invLotLocList){
        List<InvLotLoc> subInvLotLocList = invLotLocList.stream().filter(invLotLoc ->
            ObjUtil.equals(invLotLoc.getOwnerId(), expiryWarning.getOwnerId()) &&
            ObjUtil.equals(invLotLoc.getWarehouseId(), expiryWarning.getWarehouseId()) &&
            ObjUtil.equals(invLotLoc.getSkuId(), expiryWarning.getSkuId()) &&
            StrUtil.equals(invLotLoc.getLotNum(), expiryWarning.getLotNum())
        ).toList();
        if(CollectionUtil.isNotEmpty(subInvLotLocList)){
            BigDecimal total = subInvLotLocList.stream()
                .map(InvLotLoc::getQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            expiryWarning.setAvailableQty(total);
        }
    }

}
