package com.chinaservices.wms.common.enums.stock;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 追溯业务类型枚举
 */
@Getter
@AllArgsConstructor
public enum TraceBackBusinessTypeEnum {
    INBOUND_CREATE("inbound_create", "入库建单"),
    RECEIVING("receiving", "收货"),
    PUT_AWAY("put_away", "上架"),
    PICKING("picking", "拣货"),
    CHECKING("checking", "复核"),
    PACKING("packing", "打包"),
    SHIPPING_CONFIRM("shipping_confirm", "发运确认"),
    QUALITY_CHECK("quality_check", "质检"),
    CROSS_DOCKING("cross_docking", "越库"),
    INVENTORY_ADJUSTMENT("inventory_adjustment", "库存调整"),
    INVENTORY_MOVEMENT("inventory_movement", "库存移动"),
    INVENTORY_FREEZE("inventory_freeze", "库存冻结"),
    INVENTORY_UNFREEZE("inventory_unfreeze", "库存解冻"),
    REPORTING_LOSSES_EXAMINE("reporting_losses_examine", "报损审核"),
    CONFIRM_REPORTING_LOSSES("confirm_reporting_losses", "确认报损"),
    //报损审核 确认报损
    ;

    private final String code;
    private final String name;
}
