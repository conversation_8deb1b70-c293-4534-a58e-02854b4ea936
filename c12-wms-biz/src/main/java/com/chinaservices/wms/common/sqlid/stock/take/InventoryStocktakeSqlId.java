package com.chinaservices.wms.common.sqlid.stock.take;


/**
 * @ClassName InventoryStocktakeSqlId
 * <AUTHOR>
 * @Date 2025/1/13 10:06
 * @Description
 * @Version 1.0
 */
public interface InventoryStocktakeSqlId {

    /**
     * 库存盘点分页列表查询
     */
    String INVENTORY_STOCKTAKE_ORDER_QUERY_PAGE_LIST = "inventory_stocktake_order_query_getPageList";

    /**
     * 库存盘点任务分页列表查询
     */
    String INVENTORY_STOCKTAKE_TASK_PAGE_LIST = "inventory_stocktake_task_query_getPageList";

    /**
     * 库存盘点任务分页列表查询
     */
    String INVENTORY_STOCKTAKE_TASK_PDA_PAGE_LIST = "inventory_stocktake_task_pda_getPageList";

    /**
     * 修改盘点单及对应盘点任务状态
     */
    String INVENTORY_STOCKTAKE_ORDER_UPDATE_STATUS_BY_ID_LIST = "inventory_stocktake_order_updateStatusByIdList";

    /**
     *  根据盘点单号查询最大任务编号
     **/
    String INVENTORY_STOCKTAKE_TASK_GET_MAX_TASK_NO = "inventory_stocktake_task_getMaxTaskNo";

    /**
     * 根据盘点单号查询可生成调整单数据
     */
    String INVENTORY_STOCKTAKE_TASK_DETAILS_LIST = "inventory_stocktake_task_details_query_findGeneraAdjustItemList";

    /**
     *  查询盘点数量不一致的任务明细
     **/
    String INVENTORY_STOCKTAKE_TASK_DETAILS_DIFFERENT = "inventory_stocktake_task_details_query_findDifferentsList";

    /**
     *  盘点作业分析分页查询
     **/
    String INVENTORY_STOCKTAKE_TASK_PERFORMANCE_ANALYSIS_PAGE_LIST = "inventory_stocktake_task_performanceAnalysisPageList";
}
