package com.chinaservices.wms.common.sqlid.so.pack;

public interface PackHeaderSqlId {

    /**
     * 分页查询
     */
    public static final String PACK_HEADER_QUERY_GET_PAGE_LIST = "packHeader_query_getPageList";
    /**
     * 标签号查询
     */
    public static final String PACK_HEADER_QUERY_GET_TAG_NO_LIST = "packHeader_query_getTagNoList";
    /**
     * 未打包分页
     */
    public static final String PACK_BASE_QUERY_GET_PAGE_LIST = "packBase_query_getPageList";

    /**
     * 未打包总数
     */
    public static final String PACK_BASE_QUERY_GET_PAGE_COUNT = "packBase_query_getPageCount";


    /**
     * 未打包分页查询
     */
    public static final String PACK_BASE_QUERY_GET_PACK_PAGE_LIST = "packBase_query_getPackPageList";
    /**
     * 已打包分页查询
     */
    public static final String PACK_BASE_QUERY_GET_PACK_ED_PAGE_LIST = "packBase_query_getPackEdPageList";
    /**
     * 打印清单数据
     */
    public static final String PACK_DATA_PRINT_GET_PACK_LIST = "packDetail_query_getPackList";
    /**
     * 打印DN数据
     */
    public static final String PACK_DATA_PRINT_GET_DN_PACK_LIST = "packDetail_query_dn_getPackList";


    /**
     * 根据打包哦任务号查询库存变化信息
     */
    public static final String PACK_BASE_QUERY_FIND_INV_LOT_INFO_BY_PACK_NO = "packBase_query_findInvLotInfoByPackNo";


    /**
     * 查询商品信息
     */
    public static final String PACK_BASE_QUERY_GET_PACK_BOX_DETAIL_BY_BOX_NOS = "packBase_query_getPackBoxDetailByBoxNos";


    public static final String PACK_BASE_QUERY_FIND_SO_NO_BY_PACK_NO = "packBase_query_findSoNoByPackNo";

    /**
     * 根据出库单和发运状态查询
     */
    public static final String QUERY_PACK_BOX_DETAIL_BY_SONOS = "packBox_query_getPackBoxDetailBySoNos";

    /**
     * 打包商品编码
     */
    public static final String PACK_BOX_DETAIL_QUERY_FIND_PACK_NO_SKU_CODE = "packBoxDetail_query_findPackNoSkuCode";

    /**
     * 根据打包任务号查询打包信息-单据图片预览
     */
    public static final String PACK_PREVIEW_QUERY_FIND_BY_PACK_NO = "pack_preview_query_findSoNoByPackNo";

    /**
     * 根据打包任务号查询发运单信息-单据图片预览
     */
    public static final String SHIPPING_PREVIEW_QUERY_FIND_BY_PACK_NO = "shipping_preview_query_findByPackNo";

    /**
     * 发运单是否存在对应商品
     */
    public static final String PACK_BOX_DETAIL_QUERY_IS_EXIST_BY_DISPATCH_NO_AND_SKU_ID = "packBoxDetail_query_isExistByDispatchNoAndSkuId";

    /**
     * 发运单是否存在对应商品
     */
    public static final String PACK_BOX_DETAIL_QUERY_FIND_SKU_EA = "packBoxDetail_query_findSkuEa";

}
