package com.chinaservices.wms.common.enums.message;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description 消息发送模板枚举
 */
@Getter
@AllArgsConstructor
public enum MessageSendTemplateEnum {

    /**
     * 即将滞库
     */
    STOCK_DAYS_ONE("stockDaysOne","当前仓库${warehouseName}的商品${skuName}（批次号${lotNum}）即将滞库，距离滞库时间还有${nearWarningDays}天，请检查商品库存信息"),

    /**
     * 滞库预警
     */
    STOCK_DAYS_TWO("stockDaysTwo","当前仓库${warehouseName}的商品${skuName}（批次号${lotNum}）已经滞库，请检查商品滞库状态"),

    /**
     * 文件分享通知
     */
    FILE_SHARE_CODE("fileShareCode", "用户${userName}分享文件给您，请前往物流文件管理进行查看"),

    /**
     * 文件临期通知
     */
    FILE_WARNING_CODE("fileWarningCode", "文件${fileCodeList}即将过期，请前往文件管理页面及时更新文件"),

    /**
     * 文件过期通知
     */
    FILE_EXPIRED_CODE("fileExpiredCode", "文件${fileCodeList}已过期，请前往文件管理页面及时更新文件"),

    /**
     * 新增档案
     */
    SAVE_ARCHIVE("saveArchive", "档案柜${archiveCabinetName}新增档案${archiveName}，请前往档案管理进行查看"),

    /**
     * 档案归还提醒
     */
    ARCHIVE_REMINDER_CODE("archiveReminderCode", "您借阅的档案${archiveName}已经超时，请尽快归还至档案柜${archiveCabinetName}的${tier}层${column}列${grid}格"),

    /**
     * 借阅异常
     */
    BORROW_ERROR_CODE("borrowErrorCode", "记录异常原因：${errorCreatorName}应借阅档案${shouldBorrow}，实际借阅档案${realBorrow}"),

    /**
     * 归还异常
     */
    RETURN_ERROR_CODE("returnErrorCode", "记录异常原因：${archiveName}档案位置错误，当前位置档案柜${realCabinetName}的${realPos}，档案原位置为档案柜${originCabinetName}的${originPos}"),

    /**
     * 库存预警
     */
    INV_WARNING("invWarning","仓库${warehouseName}:货主${ownerName}的商品${skuName}库存不足,可用库存为${qty},请及时补货"),

    /**
     * 商品临期预警
     */
    NEAR_EXPIRY_WARNING("nearExpiryWarning","当前仓库${warehouseName}的商品${skuName}（批次号${lotNum}）即将过期，距离商品失效时间${expiryDate}还剩${nearExpiryDays}天，请尽快处理"),

    /**
     * 标签已失效/已拆包/已损坏
     */
    TAG_STATUS_ILLEGAL("tagStatusIllegal", "标签号${tagNo}在系统中已失效/已拆包/已损坏，请核查"),

    /**
     * 无法判断标签作业类型
     */
    TAG_WORK_TYPE_JUDGE_FAIL("tagWorkTypeJudgeFail", "无法判断标签号${tagNo}是自动收货还是自动发运，请核查该标签是否绑定非货物信息，或已发运"),

    /**
     * 标签绑定入库订单未审核
     */
    TAG_BIND_ASN_UNINSPECTED("tagBindAsnUninspected", "标签号${tagNo}绑定入库订单${asnNo}未审核，请核查"),

    /**
     * 标签与入库订单商品不一致
     */
    TAG_ASN_SKU_MISMATCHED("tagAsnSkuMismatched", "标签号${tagNo}与入库订单${asnNo}商品编号不一致，请核查"),

    /**
     * 批次属性未填写无法自动收货
     */
    TAG_LOT_ATT_UNFILLED("tagLotAttUnfilled", "标签号${tagNo}绑定入库订单${asnNo}批次属性未填写，请在入库单编辑处进行编辑或手动收货"),

    /**
     * 出库识别标签反查入库信息查不到
     */
    TAG_DISPATCH_NO_ERROR("tagDispatchNoError", "标签号${tagNo}关联发运单${dispatchNo}查不到收货信息，请核查"),

    /**
     * 识别商品与发运单商品不一致
     */
    TAG_DISPATCH_SKU_MISMATCHED("tagDispatchSkuMismatched", "标签号${tagNo}绑定商品${skuName}与关联发运单${dispatchNo}商品信息不一致，请核查"),

    /**
     * 识别数量与发运单数量不一致
     */
    TAG_DISPATCH_EA_MISMATCHED("tagDispatchEaMismatched", "标签号${tagNoList}的商品数量与发运单${dispatchNo}不一致，请核查"),

    /**
     * 标签已收货
     */
    TAG_ALREADY_RECEIVED("tagAlreadyReceived", "标签号${tagNo}已收货，不允许再次收货"),
    ;

    private final String code;
    private final String desc;

}
