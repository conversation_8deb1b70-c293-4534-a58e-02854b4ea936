package com.chinaservices.wms.common.enums.process;

import com.chinaservices.core.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName ProcessOrderTypeEnum
 * <AUTHOR>
 * @Date 2025/1/13 14:36
 * @Description
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum ProcessOrderTypeEnum implements IEnum {

    /**
     * 加工单类型：1-加工单；2-拆分单
     */
    PROCESS_ORDER("1", "加工单"),
    SPLIT_ORDER("2", "拆分单"),
    ;

    private final String code;
    private final String name;
}
