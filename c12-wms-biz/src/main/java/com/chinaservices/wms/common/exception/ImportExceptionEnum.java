package com.chinaservices.wms.common.exception;

import com.chinaservices.core.exception.BizError;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导入错误枚举
 */
@Getter
@AllArgsConstructor
public enum ImportExceptionEnum implements BizError {

    IMPORT_DATA_EMPTY(20001, "导入数据为空"),
    IMPORT_ANALYSIS_ERROR(20002, "文件解析失败：【%s】"),
    IMPORT_DATA_BIND_ERROR(20004, "导入失败，归属校验失败：【%s】"),
    IMPORT_DATA_SUCCESS(20005, "导入成功，共【%s】条"),
    IMPORT_DATA_PCC_EMPTY(20006, "导入失败，省市区代码：【%s】不存在"),
    IMPORT_DATA_PROVINCE_EMPTY(20007, "导入失败，省代码：【%s】不存在"),
    IMPORT_DATA_PCC_NO_COMPLETE(20008, "导入失败，省市区名称填写不完整"),
    IMPORT_DATA_FAIL_NO_COMPLETE(20007, "导入失败，失败原因：【%s】"),
    IMPORT_DATA_FAIL_CODE_VALID(20008, "导入失败，格式错误，请输入不包含中文以及空格的编码"),
    IMPORT_DATA_EXIST_ERROR(20009, "导入失败,%s"),
    IMPORT_DATA_EXIST_ERROR_NOT_PARAMS(20010, "导入失败,缺少必要参数"),

    WAREHOUSE_NAME_DUPLICATE(20017, "导入失败，仓库名称：【%s】重复"),
    WAREHOUSE_NAME_EXIST(20018, "导入失败，仓库名称：【%s】已存在"),
    WAREHOUSE_PROVINCE_CITY_COUNTY_CODE_ERROR(20019, "导入失败，仓库省市区代码查询异常：【%s】"),

    WAREHOUSE_AREA_NAME_DUPLICATE(20022, "导入失败，区域名称：【%s】重复"),
    WAREHOUSE_AREA_NAME_EXIST(20023, "导入失败，区域名称：【%s】已存在"),
    WAREHOUSE_AREA_WAREHOUSE_EMPTY(20024, "导入失败，区域所属仓库：【%s】不存在"),

    WAREHOUSE_ZONE_NAME_DUPLICATE(20027, "导入失败，库区名称：【%s】重复"),
    WAREHOUSE_ZONE_NAME_EXIST(20028, "导入失败，库区名称：【%s】已存在"),
    WAREHOUSE_ZONE_AREA_EMPTY(20029, "导入失败，库区所属区域：【%s】不存在"),
    WAREHOUSE_ZONE_WAREHOUSE_EMPTY(20030, "导入失败，库区所属仓库：【%s】不存在"),
    WAREHOUSE_ZONE_AREA_BIND_WAREHOUSE_EMPTY(20031, "库区所属区域【%s】不归属于仓库【%s】"),

    WAREHOUSE_SHELF_NAME_DUPLICATE(20034, "导入失败，货架名称：【%s】重复"),
    WAREHOUSE_SHELF_NAME_EXIST(20035, "导入失败，货架名称：【%s】已存在"),
    WAREHOUSE_SHELF_ZONE_EMPTY(20036, "导入失败，货架所属库区名称：【%s】不存在"),
    WAREHOUSE_SHELF_CODE_ZONE_EMPTY(20037, "货架【%s】所属库区名称【%s】和库区编码不匹配"),

//    WAREHOUSE_LOC_CODE_DUPLICATE(20209, "导入失败，库位代码：【%s】重复"),
//    WAREHOUSE_LOC_NAME_DUPLICATE(20210, "导入失败，库位名称：【%s】重复"),
    WAREHOUSE_LOC_CODE_EXIST(20211, "仓库:【%s】的库位代码:【%s】已存在"),
    WAREHOUSE_LOC_NAME_EXIST(20212, "仓库:【%s】的库位名称:【%s】已存在"),
    WAREHOUSE_LOC_WAREHOUSE_EMPTY(20213,"导入失败，库位所属仓库：【%s】不存在"),
    WAREHOUSE_LOC_ZONE_EMPTY(20214, "导入失败，库位所属库区：【%s】不存在"),
    WAREHOUSE_LOC_SHELF_EMPTY(20215, "导入失败，库位所属货架：【%s】不存在"),
    WAREHOUSE_LOC_AREA_EMPTY(20216, "导入失败，库位所属区域：【%s】不存在"),
    WAREHOUSE_LOC_AREA_BIND_WAREHOUSE_EMPTY(20217, "库位所属库区【%s】不归属于仓库【%s】"),
    WAREHOUSE_LOC_SHELF_BIND_ZONE_EMPTY(20218, "库位所属货架【%s】不归属于库区【%s】"),
    WAREHOUSE_LOC_ZONE_BIND_WAREHOUSE_EMPTY(20219, "库位所属库区【%s】不归属于仓库【%s】"),

    ////////////////////////// 仓库唯一校验报错
    WAREHOUSE_AREA_EXIST(20240,"仓库:【%s】的区域 %s 已存在"),
    WAREHOUSE_ZONE_EXIST(20240,"仓库:【%s】的库区 %s 已存在"),
    WAREHOUSE_SHELF_EXIST(20240,"仓库:【%s】的货架 %s 已存在"),

    WAREHOUSE_AREA_DUPLICATE(20022, "仓库:【%s】的区域名称：%s重复"),
    WAREHOUSE_ZONE_DUPLICATE(20027, "仓库:【%s】的库区名称：%s重复"),
    WAREHOUSE_SHELF_DUPLICATE(20034, "仓库:【%s】的货架名称：%s重复"),
    WAREHOUSE_LOC_CODE_DUPLICATE(20209, "仓库:【%s】的库位代码：%s重复"),
    WAREHOUSE_LOC_NAME_DUPLICATE(20210, "仓库:【%s】的库位名称：%s重复"),


    //////////////////////////////基础数据

    PACKAGE_NAME_DUPLICATE(20076,"导入失败，包装名称：【%s】重复"),
    PACKAGE_NAME_EXIST(20076,"导入失败，包装名称：【%s】已存在"),
    SKU_CODE_DUPLICATE(20076,"导入失败，商品编码：【%s】重复"),
    SKU_CODE_EXIST(20076, "导入失败，商品编码：【%s】已存在"),
    SKU_OWNER_EMPTY(20081, "导入失败，商品所属货主：【%s】不存在"),
    SKU_OWNER_CODE_BIND_NAME_EMPTY(20081, "导入失败，货主代码：【%s】不为【%s】，应为【%s】"),
    SKU_PACKAGE_EMPTY(20082, "导入失败，商品包装规格：【%s】不存在"),
    SKU_RULE_LOT_HEADER_EMPTY(20077, "导入失败，商品批次属性：【%s】不存在"),
    SKU_RULE_ROTATION_HEADER_EMPTY(20078, "导入失败，商品周转规则：【%s】不存在"),
    SKU_RULE_ALLOC_HEADER_EMPTY(20079, "导入失败，商品分配规则：【%s】不存在"),
    SKU_RULE_PK_TASK_EMPTY(20080, "导入失败，商品上架规则：【%s】不存在"),
    SKU_DEFAULT_PACKAGE_UNIT_EMPTY(20084, "导入失败，商品包装规格【%s】的包装单位不存在"),
    SKU_DEFAULT_PACKAGE_UNIT_ERROR(20085, "第%s行，商品包装规格不存在包装单位"),
    SKU_PACKAGE_PRINT_UNIT_ERROR(20086, "第%s行，商品包装单位应为：%s"),

    PALLET_STATUS_ERROR(20089, "第【%s】行，容器号状态不符合规范"),
    PALLET_IS_USED_ERROR(20090, "第【%s】行，容器号是否被占用不符合规范"),
    PALLET_TYPE_ERROR(20091, "第【%s】行，容器号类型不符合规范"),
    PALLET_WAREHOUSE_EMPTY(20092, "导入失败，容器号所属仓库：【%s】不存在"),

    ASN_DETAIL_SN_NO_DUPLICATE(20093, "导入失败，入库单SN码：【%s】重复"),
    ASN_DETAIL_SN_NO_EXIST(20094, "导入失败，入库单SN码：%s已存在"),
    ASN_SN_SO_NOT_EXIST_CONTROLLER(20094,"入库单商品：【%s】序列号控制为否，不允许导入序列号"),
    ASN_DETAIL_SKU_NOT_EXIST(20095, "导入失败，入库单商品：【%s】不存在"),
    ASN_DETAIL_SKU_NAME_NOT_MATCH(20096, "第【%s】行，入库单商品名称：【%s】与系统商品名称：【%s】不匹配"),
    IMPORT_DATA_SKU_ERROR(20097, "导入失败，商品校验失败：%s"),
    ASN_DETAIL_NOT_SHIPPING(20097, "导入失败，退货入库只能添加已发运的SN码！"),

    QC_SN_NO_NOT_EXIST(20100,"导入失败,质检SN码:%s不存在"),
    QC_SN_NO_BOX_CODE_NOT_EXIST(20100,"质检SN码:%s的箱号不为%s"),
    QC_SN_NO_SKU_NAME_NOT_EXIST(20100,"导入失败,存在商品名称与质检商品名称:【%s】不匹配"),
    QC_SN_NO_SKU_CODE_NOT_EXIST(20100,"导入失败,存在商品代码与质检商品代码:【%s】不匹配"),
    QC_SN_NO_DUPLICATE(20101, "导入失败，导入的SN码：【%s】重复"),




    RULE_CARRIER_DISTRIBUTION_PROVINCE_NAME_DUPLICATE(20098, "导入失败，省份名称：【%s】重复"),
    RULE_CARRIER_DISTRIBUTION_PROVINCE_NAME_EXIST(20099, "导入失败，省份名称：【%s】已存在"),
    RULE_CARRIER_DISTRIBUTION_PROVINCE_CODE_ERROR(20100, "导入失败，承运商分配规则省代码查询异常：【%s】"),
    RULE_CARRIER_DISTRIBUTION_CARRIER_NAME_EMPTY(20101, "导入失败，承运商名称：【%s】不存在"),

    ////////////////////////// 无源标签
    PASSIVE_TAG_NO_DUPLICATE(20301, "导入失败，标签号：【%s】重复"),
    PASSIVE_TAG_NO_EXIST(20099, "导入失败，标签号：【%s】已存在"),
    PASSIVE_TAG_NO_ALREADY_EXIST(20303, "第%s行，标签号在系统已存在"),
    PASSIVE_TAG_WAREHOUSE_NOT_EXIST(20304, "第%s行，所属仓库不存在"),
    PASSIVE_TAG_BIND_PALLET_NOT_EMPTY(20305, "第%s行，绑定容器时容器类型和容器编号不能为空"), // todo *不存在
    PASSIVE_TAG_PALLET_NOT_EXIST(20306, "第%s行，%s、%s不在仓库内，请检查"),
    PASSIVE_TAG_PALLET_ALREADY_BEAN_BOUND(20307, "第%s行，%s、%s已被%s绑定"),
    PASSIVE_TAG_BIND_GOODS_NOT_EMPTY(20308, "第%s行，绑定货物时商品编码、序列商品和入库订单不能为空"), // todo *不存在
    PASSIVE_TAG_GOODS_ASN_NOT_EXIST(20309, "第%s行，归属仓库不存在入库订单%s"), // todo *入库订单
    PASSIVE_TAG_GOODS_ASN_HEADER_STATUS_ILLEGAL(20310, "第%s行，订单状态非未收货或未审核无法绑定标签，请检查"),
    PASSIVE_TAG_GOODS_ASN_HEADER_NOT_EXISTS_SKU(20311, "第%s行，入库订单%s中没有商品%s"), // todo *入库订单中 *商品
    PASSIVE_TAG_GOODS_SERIAL_BOX_SN_EMPTY(20312, "第%s行，绑定序列号商品时箱码和序列号二选一必填"), // todo *不存在
    PASSIVE_TAG_GOODS_SERIAL_BOX_SN_NOT_EXIST(20313, "第%s行，箱码/序列号在订单中无效，请检查"), // todo *不存在
    PASSIVE_TAG_GOODS_SERIAL_BOX_ALREADY_BEAN_BOUND(20314, "第%s行，箱码已绑定其它标签"), // todo *不存在
    PASSIVE_TAG_GOODS_SERIAL_SN_ALREADY_BEAN_BOUND(20315, "第%s行，序列号%s已绑定其它标签"), // todo *不存在
    PASSIVE_TAG_GOODS_SERIAL_SN_IN_BOX_ALREADY_BEAN_BOUND(20316, "第%s行，序列号关联箱码%s已绑定其他标签"), // todo *不存在
    PASSIVE_TAG_GOODS_GENERAL_PACKAGE_UNIT_NOT_EMPTY(20317, "第%s行，绑定普通商品时包装名称和包装单位不能为空"), // todo *不存在
    PASSIVE_TAG_GOODS_GENERAL_PACKAGE_NOT_EXIST(20318, "第%s行，包装名称不存在"), // todo *不存在
    PASSIVE_TAG_GOODS_GENERAL_SKU_PACKAGE_NOT_THE_SAME(20319, "第%s行，商品%s包装单位与%s不符，请检查"), //todo *商品
    PASSIVE_TAG_GOODS_GENERAL_PACKAGE_UNIT_NOT_EXIST(20320, "第%s行，包装单位不在该包装名称中，请检查"),
    PASSIVE_TAG_UPDATE_STATUS_ILLEGAL(20321, "第%s行，标签领用情况为已失效、已拆包、已损坏状态不允许更新"),
    PASSIVE_TAG_UPDATE_RECEIVE_STATUS_ILLEGAL(20322, "第%s行，标签收货状态为已收货不允许更新"),
    PASSIVE_TAG_UPDATE_WAREHOUSE_NOT_THE_SAME(20323, "第%s行，标签所属仓库和系统所属仓库不一致，不允许更新"),
    PASSIVE_TAG_IMPORT_ERROR(20399, "%s"),

    /*****************************************订单导入************************************************************/
    ORDER_IMPORT_SUPLIER_ERROR(20100, "导入失败，根据编号[%s]查询不到对应供应商/客户,请确认！"),
    ORDER_IMPORT_SKU_ERROR(20101, "导入失败，根据编号[%s]查询不到对应商品,请确认！"),
    ORDER_IMPORT_PACKAGE_UNIT_ERROR(20102, "导入失败，包装单位[%s]不在对应[%s]包装代码范围中,请确认！"),
    ORDER_IMPORT_WAREHOUSE_ERROR(20103, "导入失败，根据仓库[%s]查询不到对应仓库信息,请确认"),
    ORDER_IMPORT_OWNER_ERROR(20104,"导入失败，根据编号[%s]查询不到对应货主,请确认" ),
    ORDER_IMPORT_PACKAGE_ERROR(20105, "导入失败，根据编号[%s]查询不到对应包装,请确认！" ),
    ORDER_IMPORT_SUPLIER_NAME_ERROR(20106, "导入失败，根据编号[%s]查询到的供应商/客户名称与当前供应商/客户[%s]不一致,请确认！"),
    ORDER_IMPORT_OWNER_NAME_ERROR(20107, "导入失败，根据编号[%s]查询到的货主名称与当前货主[%s]不一致,请确认"),
    ORDER_IMPORT_SKU_OWNER_ERROR(20108, "导入失败，当前商品[%s]不属于当前货主[%s],请确认"),;

    private final Integer code;
    private final String msg;
}
