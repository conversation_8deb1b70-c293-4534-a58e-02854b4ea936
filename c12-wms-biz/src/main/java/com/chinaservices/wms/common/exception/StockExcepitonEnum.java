package com.chinaservices.wms.common.exception;

import com.chinaservices.core.exception.BizError;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存管理错误枚举
 */
@Getter
@AllArgsConstructor
public enum StockExcepitonEnum implements BizError {

    STOCKTAKE_ORDER_CANCEL_ERROR(15001,"%s盘点已取消/已关闭，无法取消盘点"),
    STOCKTAKE_ORDER_CANCEL_TASK_ERROR(15002,"%s盘点已开始，无法取消盘点任务"),
    STOCKTAKE_ORDER_CANCEL_DEL_ERROR(15003,"%s盘点已开始，无法删除盘点单"),
    STOCKTAKE_ORDER_CANCEL_CLOSE_ERROR(15004,"%s盘点单状态为【创建/已取消/已关闭】，无法关闭盘点单"),
    STOCKTAKE_ORDER_CANCEL_AGAIN_ERROR(15005,"%s盘点单状态未关闭，无法生成复盘单"),
    STOCKTAKE_TASK_CLOSE_ERROR(15006,"当前盘点任务状态已关闭，无法关闭盘点任务"),
    STOCKTAKE_TASK_STOCKTAKE_ERROR(15007,"当前盘点任务状态为已关闭/已取消，无法盘点任务"),
    STOCKTAKE_RANGE_ERROR(15008,"请选择区域/库区/库位"),
    STOCKTAKE_OPERATOR_TIME_ERROR(15009,"请选择动碰起止时间"),
    STOCKTAKE_TARGET_ERROR(15010,"请选择部分盘点的盘点内容"),
    STOCKTAKE_PALLET_ERROR(15011,"请选择容器号"),
    LOT_LOC_NO_DATA(15012,"暂无符合条件的库存数据"),
    STOCKTAKE_TYPE_ERROR(15013,"动碰盘点不可选择盲盘"),
    STOCKTAKE_ORDER_EXPORT_ERROR(15014,"盘点单导出失败,请联系管理人员！"),
    STOCKTAKE_TASK_EXPORT_ERROR(15015,"盘点任务导出失败,请联系管理人员！"),
    STOCKTAKE_ORDER_ADJUST_ERROR(15016,"%s盘点单状态未关闭，无法生成调整单"),
    STOCKTAKETASK_NOT_EXIST(15017,"%s盘点任务不存在"),
    STOCKTAKE_ORDER_ADJUST_DATA_ERROR(15018,"所选择盘点单查询不到盘盈/盘亏数据,无法生成调整单"),
    STOCKTAKE_TASK_STOCKTAKE_EXECUTE_ERROR(150019,"当前盘点任务盘点人已更新，您无权限进行盘点"),
    WAREHOUSE_QUERY_NO_LOC_DATA(15020,"所选择仓库查询不到库存数据,无法生成无源盘点任务"),

    /*********库存冻结**********/
    UNFREEZE_COUNT_NOT_NULL(15050,"解冻数量不能为空"),
    UNFREEZE_SN_NO_NOT_NULL(15051,"序列号不能为空"),
    BOX_NO_OR_SN_NO_NOT_EXIST(15052,"该序列号非当前商品对应的序列号范围，无法盘点"),
    NOT_UNFREEZE_SN_NO(15053,"该序列号非当前冻结的序列号，无法解冻"),




    /********** 调整单****************/
    ADJUST_ORDER_EXIST_AUDIT(15101,"存在已审核的调整单"),
    ADJUST_ORDER_EXIST_NOT_AUDIT(15102,"存在未审核的调整单"),
    ADJUST_ORDER_ADJUST_HEADER_ADJUSTMENT_CANNOT_QUERY(15103,"查询不到调整单信息"),
    ADJUST_ORDER_ADJUST_HEADER_CANNOT_DELETE(15104,"待审核的调整单才能删除"),
    ADJUST_ORDER_CANCEL_CLOSE_ERROR(15105,"%s调整单状态不为【已执行】，无法关闭调整单"),
    ADJUST_ORDER_CANCEL_ERROR(15105,"%s调整单状态为【已执行/已关闭/已取消】，无法取消调整单"),
    ADJUST_ORDER_EXIST_CLOSE_CANCEL_EXECUTE_ERROR(15006,"%s调整单状态存在已关闭/已取消/已执行，无法审核/取消审核"),
    ADJUST_DETAIL_NOT_NULL(15107,"商品明细不能为空"),
    ADJUST_ORDER_ADJUST_CANNOT_EDIT(15108,"只有待审核的调整单才能编辑"),
    ADJUST_DETAIL_NULL_ERROR(15109,"调整单明细删除失败,未查询到调整单明细请确认!"),
    ADJUST_NO_UNABLE_ERROR(15110,"调整单号%s状态存在已关闭/已取消/已执行/待审核,无法进行调整"),
    ADJUST_FAIL_UNABLE_ERROR(15111,"调整单号%s%s失败，失败原因【%s】"),
    ADJUST_NOT_EXECUTE_UNABLE_ERROR(15112,"调整单号%s状态存在已关闭/已取消/已审核/待审核,无法取消调整"),
    ADJUST_CANCEL_FAIL_UNABLE_ERROR(15113,"调整单号%取消调整失败"),
    STOCK_NOT_ENOUGH_ERROR(15114, "库存数不足，请修改库存数量，当前库存%s"),
    STOCK_FROZEN_NOT_ENOUGH_ERROR(15115, "冻结库存数不足，请修改库存数量，当前冻结库存%s"),

    STOCK_SN_NO_NOT_EXIST_ASN_SN(15116, "该序列号未录入过系统，无法调整"),
    STOCK_SN_NO_EXIST_INV_SN_NO_ADD(15117, "序列号:%s在库存中%s，无需添加"),
    STOCK_SN_NO_NOT_EXIST_INV_SN_NO_ADD(15118, "序列号:%s在库存中%s，无法调整"),
    STOCK_ZERO_ADJUST_ONLY_ADD(15119, "零库存调整只能增加"),
    STOCK_ZERO_ADJUST_ONLY_REDUCE(15120, "按库存调整只能减少"),
    STOCK_SN_NO_EXCEPTION(15121, "库存调整序列号%s异常，无法调整"),
    STOCK_SN_NO_BATCH_NOT_MATCH(15122, "单次调整不可添加不同批次的序列号"),
    //调整失败，存在重复调整的序列号
    STOCK_SN_NO_REPEAT_ADJUST(15123, "存在重复调整的序列号"),
    //该序列号不属于当前商品
    STOCK_SN_NO_BELONG_TO_OTHER_SKU(15124, "序列号:%s不属于当前商品:%s"),
    ADJUST_ORDER_SO_NO_REPEAT(15125, "同一个序列号不允许在同一个调整单中多次添加"),


    /********** 库存移动 ****************/
    MOVE_ORDER_NOT_EXIST(15200, "查询不到库存移动单"),
    MOVE_ORDER_NOT_AUDIT(15201, "存在未审核的库存移动单"),
    MOVE_ORDER_EXIST_AUDIT(15202, "存在已审核的库存移动单"),
    MOVE_ORDER_STATUS_ERROR(15203, "库存移动单状态为【已执行】，不允许做任何操作"),
    MOVE_ORDER_NOT_ENOUGH_STOCK(15204, "当前可用库存数不足，无法进行库存移动"),
    MOVE_ORDER_TARGET_STOCK_ERROR(15205, "当前目标库位不满足移动条件，无法进行库存移动"),
    MOVE_ORDER_CANNOT_DELETE(15206, "待审核的库存移动单才能删除"),
    MOVE_ORDER_EXIST_AUDIT_CAN_MOVE(15207, "已审核的库存移动单才能移动"),
    MOVE_ORDER_ADJUST_CANNOT_EDIT(15208, "只有待审核的库存移动单才能编辑"),
    GOODS_ALLOC_LOT_LOC_NO_DATA(15209,"商品【%s】暂无符合分配条件的库存数据"),
    GOODS_ZZ_LOT_LOC_NO_DATA(15210,"商品【%s】暂无符合周转条件的库存数据"),
    STOCK_SN_NO_EXIST_INV_SN_NO_MOVE(15211, "该序列号非当前库位的序列码，无法移动"),
    /// ////// pda移动
    PDA_MOVE_AUDIT_CAN_MOVE(15212,"已审核的库存移动单才能移出"),
    PDA_MOVE_AUDIT_SN_NOT_CAN_MOVE(15213,"存在不符合的sn码,请重新检查提交"),
    PDA_MOVE_AUDIT_SN_MOVING(15213,"请先将库存移动单移出"),
    PDA_MOVE_AUDIT_SN_AWAIT_MOVE(15213,"库存移动单已移出,请勿重复操作"),



    /********** 效期预警 ****************/
    EXPIRY_WARNING_SETTING_DUPLICATE(15301, "当前选择的仓库商品已设置预警，请勿重复设置"),
    EXPIRY_WARNING_GET_EXPIRED_DATE_ERROR(15302, "获取失效日期失败"),
    EXPIRY_WARNING_INBOUND_EXPIRED(15303, "当前商品已设置入库效期%s天，不允许入库！"),
    EXPIRY_WARNING_BATCH_INBOUND_EXPIRED(15304, "入库单号%s的商品已设置入库效期%s天，不允许入库！"),
    EXPIRY_WARNING_OUTBOUND_EXPIRED(15304, "当前商品已设置出库效期%s天，不允许出库！"),
    EXPIRY_WARNING_SETTING_NOT_EXIST(15305, "预警设置信息不存在"),
    EXPIRY_WARNING_SETTING_OUTBOUND_LONGER_THAN_INBOUND(15306, "出库效期不允许超过入库效期"),
    EXPIRY_WARNING_BATCH_INBOUND_ASN_THROW(15399, "%s"),

    INVENTORY_LOSS_EXIST (15214, "当前仓库+货主+商品数据已存在"),
    MOVE_ORDER_ADD_MOVE_OP(15212,"已审核才可以操作分配移动员"),


    /********** 序列号追溯 START ****************/
    SN_LIST_NOT_EMPTY(15301, "序列号列表不能为空"),
    THE_SN_IS_INCORRECT(15302, "序列号不正确,无数据信息"),
    QUERY_SN_LIST_FAILED(15303, "查询SN列表失败"),
    THREADD_TOKEN_EXIT(15304, "令牌：【%s】已存在，执行失败"),
    THREADD_TOKEN_NOT_EXIT(15305, "令牌：【%s】不存在，执行失败"),
    /********** 序列号追溯 END ****************/

    /********** 报损报溢 ****************/
    DELETE_ONLY_CREATE(15306, "编号%s非创建状态不能进行操作！"),

    CANCEL_ONLY_AUDIT(15307, "编号%s非审核状态不能进行操作！");


    private final Integer code;
    private final String msg;
}
