package com.chinaservices.wms.common.constant;

/**
 * @Author: <PERSON><PERSON>.<PERSON>
 * @Date: 2023-03-08  流水号生成规则名称常量
 */
public class IdRuleConstant {

    /**
     * 容器号默认标志 *
     */
    public static final String TRACE_ID = "*";
    /**
     * 收货
     */
    public static final String ORDER_NO = "SO";
    /**
     * 库存调整单号
     */
    public static final String ADJUST_NO = "AD";
    /**
     * 库存调整单号
     */
    public static final String MOVE_NO = "MD";
    /**
     * 库存盘点单号
     */
    public static final String COUNT_NO = "CNT";
    /**
     * 库存调整单号
     */
    public static final String TF_NO = "TF";
    /**
     * 库存冻结单号
     */
    public static final String HOLD_NO = "HN";
    /**
     * 批次号
     */
    public static final String LOT_NUM = "PP";
    /**
     * 盘点任务号
     */
    public static final String COUNT_TASK_NO = "CONT";
    /**
     *
     */
    public static final String STOCK_TRANSACTION = "TRA";
    /**
     * 出库单号
     */
    public static final String SO_NO = "SN_NO";
    /**
     * 容器号
     */
    public static final String PALLET_NUM = "PN";

    /**
     * 上架规则编号
     */
    public static final String RULE_CODE = "PR";
    /**
     * 分配ID
     */
    public static final String ACT_ID = "AI";
    /**
     * 波次号
     */
    public static final String WV_NO = "WV_NO";

    /**
     * 入库单号
     */
    public static final String ASN_NO = "ASN_NO";
    /**
     * 质检单号
     */
    public static final String  QC_NO = "QC_NO";
    /**
     * 上架任务号
     */
    public static final String  PA_NO = "PA";

    /**
     * 联系人代码
     */
    public static final String CONTACT_CODE = "CT";

    /**
     * 仓库代码
     */
    public static final String WAREHOUSE_CODE = "WAREHOUSE_CODE";
    /**
     * 库位名称代码
     */
    public static final String WAREHOUSE_LOC_NAME = "WAREHOUSE_LOC_NAME";
    /**
     * 区域代码
     */
    public static final String WAREHOUSE_AREA_CODE = "WAREHOUSE_AREA_CODE";
    public static final String WAREHOUSE_SHELF_CODE = "WAREHOUSE_SHELF_CODE";

    /**
     * 货主代码
     */
    public static final String OWNER_CODE = "HZ";

    /**
     * 货主代码
     */
    public static final String CUSTOMER_CODE = "KH";
    /**
     * 批次属性
     */
    public static final String WAREHOUSE_LOT = "WAREHOUSE_LOT";
    /**
     * 容器号
     */
    public static final String PALLET = "PALLET_NO";

    /**
     *  仓库库区代码
     */
    public static final String WAREHOUSE_ZONE_CODE = "WAREHOUSE_ZONE_CODE";


    /**
     * 包装代码
     */
    public static final String PACKAGE_CODE = "PACKAGE_CODE";

    /**
     * 货主联系人代码
     */
    public static final String OWNER_CONTACT_CODE = "HZLXR_CODE";

    /**
     * 货主联系人代码
     */
    public static final String CUSTOMER_CONTACT_CODE = "KHLXR_CODE";

    /**
     * 承运商代码
     */
    public static final String CYS_CODE = "CYS_CODE";

    /**
     * 承运商代码
     */
    public static final String SKU_GROUP = "SKU_GROUP";

    /**
     * 盘点单代码
     */
    public static final String STOCKTAKE_ORDER_CODE = "STOCKTAKE_ORDER_CODE";

    /**
     * 商品代码
     */
    public static final String SKU_CODE = "SKU_CODE";
    /**
     * 供应商代码
     */
    public static final String SUPPLIER_CODE = "GYS_CODE";

    /**
     * 库存冻结代码
     */
    public static final String INVENTORY_FREEZE_CODE = "INVENTORY_FREEZE_CODE";

    /**
     * 收货明细编号
     */
    public static final String RECEIVE_NO = "RECEIVE_NO";

    /**
     * 组编吗
     */
    public static final String GROUP_CODE = "GROUP_CODE";

    /**
     * 上架任务号
     */
    public static final String PA_CODE = "PA_CODE";
    /**
     * 拣货单号
     */
    public static final String PICKING_NO = "PICKING_NO";


    /**
     * 出库详情单号
     */
    public static final String SO_DETAIL_NO = "SO_DETAIL_NO";


    /**
     * 拣货任务号
     */
    public static final String AI_CODE = "AI_CODE";

    /**
     * 拣货波次号
     */
    public static final String PKWV_CODE = "PKWV_CODE";

    /**
     * 打包任务号
     */
    public static final String PACK_NO = "PACK_NO";
    /**
     * 复核单号
     */
    public static final String RECHECK_NO = "RECHECK_NO";

    /**
     * 箱码
     */
    public static final String BOX_NO = "BOX_NO";

    /**
     * 规则
     */
    public static final String RULE_NO = "WR";

    /**
     * 批次明细代码
     */
    public static final String PCMX_NO = "PCMX_NO";
    /**
     * 周转规则
     */
    public static final String RULE_ROTATION = "ZZGZ";

    public static final String RULE_ALLOC_NO = "FPGZ_NO";



    /**
     * 文件编号
     */
    public static final String LOGISTICS_FOLDER_DETAIL = "LOGISTICS_FOLDER_DETAIL";

    /**
     * 费用名称编号
     */
    public static final String FYMC_CODE = "FYMC_CODE";
    /**
     * 费用明细编号
     */
    public static final String FYMX_CODE = "FYMX_CODE";
    /**
     * 加工人员流水号
     */
    public static final String PROCESS_STAFF_NO = "PROCESS_STAFF_NO";

    public static final String INV_WARNING = "PW";

    /**
     * 费用明细编号
     */
    public static final String COMBINATIONS_NO = "COMBINATIONS_NO";


    /**
     * 采购订单编号
     */
    public static final String P_ORDER_CODE = "P_ORDER_CODE";

    /**
     * 库存报损编号
     */
    public static final String REPORTING_LOSSES_NO = "REPORTING_LOSSES_NO";
    /**
     * 销售订单编号
     */
    public static final String S_ORDER_CODE = "S_ORDER_CODE";

    /**
     * 耗材领用单号
     */
    public static final String RULE_HCO = "HCO";
    /**
     * 耗材入库单号
     */
    public static final String HCI_NO = "HCI";
    /**
     * 耗材产品单号
     */
    public static final String CON_NO = "CON";

    /**
     * 合并收货单单号
     */
    public static final String MERGE_NO = "MERGE_NO";

    /**
     * 档案  *
     */
    public static final String ARCHIVE_CODE = "ARCHIVE_CODE";

    /**
     * 档案柜
     */
    public static final String ARCHIVE_CABINET_CODE = "ARCHIVE_CABINET_CODE";

    /**
     * 加工单编号
     */
    public static final String PROCESS_ORDER_NO_PROCESS = "PROCESS_ORDER_NO_PROCESS";

    /**
     * 拆分单编号
     */
    public static final String PROCESS_ORDER_NO_SPLIT = "PROCESS_ORDER_NO_SPLIT";

    /**
     * 加工任务编号
     */
    public static final String PROCESS_TASK_NO = "PROCESS_TASK_NO";

    /**
     * 考核指标
     */
    public static final String KPI_INDEX_NO = "KPI_INDEX_NO";

    /**
     * 托盘编号
     */
    public static final String TRAY_CODE = "TP";
    /**
     * 拣货员规则编码
     */
    public static final String AR_RULE_CODE = "AR";
}
