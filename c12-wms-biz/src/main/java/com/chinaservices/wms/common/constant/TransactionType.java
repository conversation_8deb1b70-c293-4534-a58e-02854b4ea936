package com.chinaservices.wms.common.constant;

/**
 * By: 交易类型常量类 www.chinaservices.com.cn
 * <AUTHOR>
 * @Date 2019/10/28
 */
public class TransactionType {
    /**
     * 收货确认 --收货确认
     */
    public static final String TRAN_RCV = "RCV";
    /**
     * 质检收货确认 -- 无
     */
    public static final String TRAN_QC_RCV = "QCRCV";
    /**
     * 上架 --上架
     */
    public static final String TRAN_PA = "PA";
    /**
     * 取消上架 --取消上架
     */
    public static final String TRAN_CR_PA = "CRPA";
    /**
     * 出库上架 --上架
     */
    public static final String TRAN_OUT_PA = "OUTPA";
    /**
     * 出库复核 --复核
     */
    public static final String TRAN_OUT_QA = "OUTQA";
    /**
     * 取消出库上架
     */
    public static final String TRAN_CR_OUT_PA = "CROUTPA";
    /**
     * 分配 --分配
     */
    public static final String TRAN_INA = "INA";
    /**
     * 取消分配 --取消分配
     */
    public static final String TRAN_CR_INA = "CRINA";
    /**
     * 拣货 --拣货
     */
    public static final String TRAN_PK = "PK";
    /**
     * 二次分拣 --二次分拣
     */
    public static final String TRAN_S_PK = "SPK";
    /**
     * 取消拣货 --无
     */
    public static final String TRAN_CPK = "CPK";
    /**
     * 调整 --调整
     */
    public static final String TRAN_AD = "AD";
    /**
     * 取消调整 -- 取消调整
     */
    public static final String TRAN_CAD = "CAD";
    /**
     * 待移动 --无
     */
    public static final String TRAN_W_MV = "W_MV";
    /**
     * 取消待移动 -- 无
     */
    public static final String TRAN_CW_MV = "CMV";
    /**
     * 移动 --移动
     */
    public static final String TRAN_MV = "MV";
    /**
     * 转移 --无
     */
    public static final String TRAN_TF = "TF";
    /**
     * 发货 --发运
     */
    public static final String TRAN_SP = "SP";
    /**
     * 取消发货 --无
     */
    public static final String TRAN_CSP = "CSP";
    /**
     * 冻结 --冻结
     */
    public static final String TRAN_FREE = "FREE";
    /**
     * 解冻 --解冻
     */
    public static final String TRAN_CR_FREE = "CRFREE";
    /** 库内质检单创建（非冻结商品） --无*/
    public static final String TRAN_QC_CRT_NF = "QC_CRT_NF";
    /** 库内质检单编辑-新增商品（非冻结商品） --无*/
    public static final String TRAN_QC_EDT_ADD_NF = "QC_EDT_ADD_NF";
    /** 库内质检单取消质检（非冻结商品） --无*/
    public static final String TRAN_QC_CAN_NF = "QC_CAN_NF";
    /** 库内质检单确认质检（非冻结商品） --无*/
    public static final String TRAN_QC_CONF_NF = "QC_CONF_NF";
    /** 库内质检单创建（冻结商品） --无*/
    public static final String TRAN_QC_CRT_F = "QC_CRT_F";
    /** 库内质检单编辑保存-新增商品（冻结商品）--无 */
    public static final String TRAN_QC_EDT_SAVE_F = "QC_EDT_SAVE_F";
    /** 库内质检单取消质检（冻结商品） --无*/
    public static final String TRAN_QC_CAN_F = "QC_CAN_F";
    /** 库内质检单确认质检（冻结商品）--无*/
    public static final String TRAN_QC_CONF_F = "QC_CONF_F";
    /** 出库质检单创建（质检商品源库位）-- 质检出库 */
    public static final String TRAN_QC_OUT_CRT = "QC_OUT_CRT";
    /** 出库质检单编辑保存-新增商品（质检商品源库位）--无 */
    public static final String TRAN_QC_OUT_EDT_SAVE = "QC_OUT_EDT_SAVE";
    /** 出库质检单取消质检（质检商品源库位） -- 取消质检出库*/
    public static final String TRAN_QC_OUT_CAN = "QC_OUT_CAN";
    /** 出库质检单确认质检（质检商品源库位）--确认质检 */
    public static final String TRAN_QC_OUT_CONF = "QC_OUT_CONF";
    /** 出库质检单质检返库（质检商品源库位） --返库上架*/
    public static final String TRAN_QC_OUT_RET = "QC_OUT_RET";
    /**
     * 取消越库 --取消越库
     */
    public static final String TRAN_CROSS="CROSS";
    /**
     * 越库 -- 越库
     */
    public static final String TRAN_ROSS="ROSS";

    // ==================== 报损相关交易类型 ====================
    /**
     * 报损单审核
     */
    public static final String TRAN_DAMAGE_AUDIT = "DAMAGE_AUDIT";
    /**
     * 报损单取消审核
     */
    public static final String TRAN_CR_DAMAGE_AUDIT = "CR_DAMAGE_AUDIT";

    /**
     * 报损单确认报损 --报损
     */
    public static final String TRAN_DAMAGE_CONFIRM = "DAMAGE_CONFIRM";

    // ==================== 加工相关交易类型 ====================

    /**
     * 加工分配 --加工分配
     */
    public static final String TRAN_PROCESS_ALLOC = "PROCESS_ALLOC";

    /**
     * 取消加工分配 --取消加工分配
     */
    public static final String TRAN_CR_PROCESS_ALLOC = "CR_PROCESS_ALLOC";

    /**
     * 加工拣货 --加工拣货
     */
    public static final String TRAN_PROCESS_PICK = "PROCESS_PICK";

    /**
     * 加工入库 --加工入库
     */
    public static final String TRAN_PROCESS_IN = "PROCESS_IN";

    /**
     * 加工出库 --加工出库
     */
    public static final String TRAN_PROCESS_OUT = "PROCESS_OUT";

    /**
     * 加工上架 --加工上架
     */
    public static final String TRAN_PROCESS_PA = "PROCESS_PA";

    /**
     * 加工退料 --加工退料
     */
    public static final String TRAN_PROCESS_RETURN = "PROCESS_RETURN";

    // ==================== 拆分相关交易类型 ====================

    /**
     * 拆分分配 --拆分分配
     */
    public static final String TRAN_SPLIT_ALLOC = "SPLIT_ALLOC";

    /**
     * 取消拆分分配 --取消拆分分配
     */
    public static final String TRAN_CR_SPLIT_ALLOC = "CR_SPLIT_ALLOC";

    /**
     * 拆分拣货 --拆分拣货
     */
    public static final String TRAN_SPLIT_PICK = "SPLIT_PICK";

    /**
     * 拆分出库 --拆分出库
     */
    public static final String TRAN_SPLIT_OUT = "SPLIT_OUT";

    /**
     * 拆分入库 --拆分入库
     */
    public static final String TRAN_SPLIT_IN = "SPLIT_IN";

    /**
     * 拆分上架 --拆分上架
     */
    public static final String TRAN_SPLIT_PA = "SPLIT_PA";

    /**
     * 拆分退料 --拆分退料
     */
    public static final String TRAN_SPLIT_RETURN = "SPLIT_RETURN";
}
