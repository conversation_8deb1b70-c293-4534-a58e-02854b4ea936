package com.chinaservices.wms.common.sqlid.asn.pa;

/**
 * <AUTHOR>
 * @date 2025/01/17
 **/
public interface PaTaskSqlId {

    /**
     * 上架任务（ASN）分页查询
     */
    String PATASK_QUERY_ASN_LIST = "paTask_query_getPageList";

    /**
     * 上架任务明细（ASN）分页查询
     */
    String PATASKDETAIL_QUERY_ASN_LIST = "paTaskDetail_query_getPageList";

    /**
     * 上架任务（SO）分页查询
     */
    String PATASK_QUERY_SO_LIST = "paTask_query_getOutboundPageList";

    /**
     * 根据asnId查询创建状态的上架任务
     */
    String PATASK_QUERY_BY_ASN_ID = "paTask_query_getByAsnId";

    /**
     * 通过上架任务号paNo，获得最新任务序号
     */
    String PATASK_QUERY_MAX_LINENO_BY_PA_NO = "paTask_query_getMaxLineNoByPaNo";

    /**
     * 汇总上架ID的上架数
     */
    String PATASK_QUERY_PA_QTY_BY_PA_NO = "paTask_query_getPaQtyByPaNo";

    /**
     * 根据id查询数据findQueryById
     */
    String PA_TASK_QUERY_PA_QTY_BY_ID = "paTask_query_getPaQtyById";

    /**
     * 计算库位上架平均时效
     */
    String PATASK_QUERY_AVG_TIME_BY_WAREHOUSE_ID = "patask_query_avg_time_by_warehouse_id";

    /**
     * 统计查询上架任务
     */
    String PUT_ON_SHELVES_PAGE = "put_on_shelves_page";

    /**
     * 根据paNo查询上架任务（ASN）信息
     */
    String PATASK_QUERY_ASN_BY_PA_NO = "paTask_query_getByPaNo";

    /**
     * 根据打包单号查询所有上架任务
     */
    String PATASK_QUERY_LIST_BY_PACK_NO = "paTask_query_getByPackNo";

    /**
     * 根据打包单号查询所有上架任务
     */
    String PATASK_QUERY_BY_SONOSET = "paTask_query_by_soNoSet";

    /**
     * 根据上架任务号查询该上架任务对应的数据容器号和每个容器中存在的ea值
     */
    String PATASK_QUERY_PALLET_LIST_BYPANOLIST = "paTask_query_pallet_list_byPaNoList";

    /**
     * 根据上架号查询所有拣货容器号列表
     */
    String PATASK_QUERY_PALLET_PAGE_BYPANOLIST = "paTask_query_pallet_page_byPaNoList";
}
