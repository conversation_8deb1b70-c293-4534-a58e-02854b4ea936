package com.chinaservices.wms.common.enums.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum DataOperationValidatorEnum {

//    WAREHOUSE("仓库", ""),
//    WAREHOUSE_AREA("区域", ""),
//    WAREHOUSE_ZONE("库区", ""),
//    WAREHOUSE_SHELF("货架", ""),
//    WAREHOUSE_LOC("库位", ""),
    OWNER("货主", OwnerReferenceType.values()),
    CUSTOMER("客户", CustomerReferenceType.values()),
//    SUPPLIER("供应商", ""),
    CARRIER("承运商", CarrierReferenceType.values()),
    PACKAGE("包装", PackageReferenceType.values()),
    SKU("商品", SkuReferenceType.values()),
//    PALLET("容器号", ""),
//    LOT("批次属性", ""),
//    LOT_RULE("批次规则", ""),
    PA_TASK_RULE("上架规则", PaTaskRuleReferenceType.values()),
    ALLOC_RULE("分配规则", AllocRuleReferenceType.values()),
    ROTATION_RULE("周转规则", RotationRuleReferenceType.values()),
    WV_RULE("波次规则", WvRuleReferenceType.values()),
//    CARRIER_DISTRIBUTION_RULE_SHIPPER("承运商分配规则", ""),
//    PICKING_STAFF_RULE_PICKING_TASK("拣货员分配规则", ""),
//    FEE_NAME_FEE("费用名称", "计费")
    ;

    private final String deleteObj;
    private final ReferenceType[] referenceTypes;

    public interface ReferenceType {
        String getName();
    }

    @Getter
    @AllArgsConstructor
    public enum OwnerReferenceType implements ReferenceType {

        SKU("商品"),
        ;

        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum CustomerReferenceType implements ReferenceType {

        SO("出库单"),
        ;

        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum CarrierReferenceType implements ReferenceType {

        SO("出库单"),
        PACK("发运单"),
        ;

        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum PackageReferenceType implements ReferenceType {

        SKU("商品"),
        ;

        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum SkuReferenceType implements ReferenceType {

        ASN_DETAIL("入库明细"),
        ;

        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum AllocReferenceType implements ReferenceType {

        SKU("商品"),
        PICKING_STAFF("拣货员规则"),
        ;

        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum PaTaskRuleReferenceType implements ReferenceType {

        SKU("商品"),
        ;

        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum AllocRuleReferenceType implements ReferenceType {

        SKU("商品"),
        PICKING_STAFF("拣货员规则"),
        ;

        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum RotationRuleReferenceType implements ReferenceType {

        SKU("商品"),
        ;

        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum WvRuleReferenceType implements ReferenceType {

        WV("波次单"),
        ;

        private final String name;
    }
}
