package com.chinaservices.wms.common.exception;

import com.chinaservices.core.exception.BizError;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 出库管理异常枚举
 */
@Getter
@AllArgsConstructor
public enum SoExcepitonEnum implements BizError {

    SO_AUDIT_ERROR_HAVE_AUDIT(14001, "存在已审核的订单，无法审核"),
    SO_AUDIT_ERROR_HAVE_ALLOCATE(14002, "存在已分配的订单，无法审核"),
    SO_CANCEL_AUDIT_ERROR_HAVE_UNREVIEWED(14003, "存在未审核出库单，无法取消审核"),
    SO_CANCEL_AUDIT_ERROR_HAVE_SAVE_WV(14004, "出库单【%s】已生成波次单无法取消审核，请先取消波次单"),
    SO_FINISH_ERROR_HAVE_UN_ALLOCATE(14005, "存在未分配的出库单,无法操作"),
    SO_FINISH_ERROR_HAVE_CANCEL_FINISH(14006, "存在已取消或已关闭订单，无法操作"),

    ALLOCATION_TYPE_ERROR(14007, "分配类型异常"),
    ALLOCATION_WV_NO_NOT_EMPTY(14008, "波次单号不能为空"),
    ALLOCATION_PROCES_NO_NOT_EMPTY(14083, "加工单号不能为空"),
    ALLOCATION_PROCES_DETAIL_ERROR(14084, "加工任务明细ID不正确"),
    ALLOCATION_PROCES_NO_ERROR(14084, "加工/拆分单号不正确"),
    ALLOCATION_WV_NO_IS_ERROR(14009, "波次单号不正确"),
    ALLOCATION_SO_ALLOCATION_NOT_EXIST(14010, "拣货单号不正确"),
    ALLOCATION_THE_OUTBOUND_ORDER_DETAILS_ARE_BLANK(14011, "出库订单明细为空"),
    ALLOCATION_THE_OUTBOUND_ORDER_IS_EMPTY(14012, "出库订单为空"),
    ALLOCATION_ERROR_SKU_EA_SUM(14013, "商品【%s】分配失败，总分配数EA以超出订货数EA"),
    ALLOCATION_THE_PRODUCT_CANNOT_BE_EMPTY(14014, "商品不能为空"),
    ALLOCATION_BATCH_ATTRIBUTES_CANNOT_BE_EMPTY(14015, "批次属性不能为空"),
    ALLOCATION_CREATE_PICKING_TASK_ERROR(14016, "创建拣货任务失败"),
    ALLOCATION_INBOUND_NO_CANNOT_BE_EMPTY(14017, "入库单号不能为空"),
    ALLOCATION_PICKING_LIST_CANNOT_BE_EMPTY(14018, "拣货单不能为空"),
    ALLOCATION_THE_ASSIGNMENT_DETAIL_CANNOT_BE_EMPTY(14019, "分配明细不能为空"),
    ALLOCATION_THE_OUTBOUND_DETAIL_ORDER_NUMBER_CANNOT_BE_EMPTY(14020, "出库明细单号不能为空"),

    PICKING_SELECT_SO_WV(14021, "生成拣货单必须选择出库单或波次单"),
    PICKING_QUERY_OP_USER_ERROR(14022, "查询拣货员失败"),
    PICKING_QUERY_OP_USER_NOT_EXIST(14023, "拣货员不存在"),
    PICKING_QUERY_OP_USER_NAME_NOT_MATCH(14024, "拣货员名称不一致"),
    PICKING_CANCEL(14025, "拣货单已取消，不允许操作"),
    PICKING_CANCEL_ERROR_ON_PICKING(14026, "拣货单已经开始拣货，不允许取消"),
    PICKING_COMPLETE_PICKING(14027, "拣货单已完全拣货，不允许重复操作"),
    PICKING_TASK_PICKING_LT_ALLOCATION(14028, "拣货任务单：【%s】分配数为：【%s】，拣货数不能大于分配数"),
    PICKING_NOT_ALLOCATION(14029, "拣货单：【%s】未分配拣货任务"),
    PICKING_ALLOCATION_ERROR(14030, "拣货单：【%s】分配数错误"),
    PICKING_NO_DIFFERENT_NOT_CANCELLED(14031, "拣货单不同的任务不能同时取消"),
    PICKING_ON_PICKING_CANCEL_ERROR(14032, "拣货任务单：【%s】已经开始拣货，不允许取消"),

    WV_ORDER_TIME_EMPTY(14033, "波次订单时间不能为空"),
    WV_NO_CREATE_ERROR(14034, "没有可生成波次的订单"),
    WV_CANCEL_ERROR_ALLOCATION(14035, "存在已分配订单无法取消"),
    WV_NOT_EXIST(14036, "波次单不存在"),

    SECOND_EMPTY(14037, "分拣信息不能为空"),
    SECOND_SORTING_LT_PICKING(14038, "总分拣数量不能大于拣货数【%s】"),
    SECOND_SORTING_NOT_EMPTY_ZERO(14039, "分拣数量不能为空或为0"),
    SECOND_WV_SO_NOT_RELATION(14040, "波次单未关联出库单"),
    SECOND_SKU_NOT_EXIST(14041, "分拣商品不存在"),

    CHECK_LT_EXCESSIVE(14042, "总复核数量不能大于待复核数【%s】"),

    PACK_CUSTOMER_ADDRESS_DIFF(14043, "客户或收货地址不同无法合并打包"),
    PACK_NO_CARRIER_TRACKING_ASSIGNED(14044, "未分配了承运商或物流单号"),
    EXIST_NOT_CHECK(14045,"存在未复核订单，不可打包"),
    EXIST_IS_PACK(14046,"存在已打包订单,不可打包"),
    SHIPPED_NOT_DELETE(14047, "已发运无法取消打包任务"),

    ALLOCATION_THE_DETAIL_ID_CANNOT_BE_EMPTY(14048, "分配明细ID集合不能为空"),
    CHECK_PICKING_NOT_EXIST(14049, "生成复核单拣货信息不能为空！"),
    LOGISTIC_NO_GENERAL_WITH_CHINESE(14050, "上游单号不允许中文字符"),

    THE_ITEM_IS_NOT_FULLY_PACKED(14051, "出库单中尚有未装箱的商品，请先完成装箱"),
    PICKING_SKU_SOURCE_NOT_UNSPECIFIED_LOC(14052, "商品来源非指定库位"),
    PICKING_SKU_SOURCE_NOT_UNSPECIFIED_LOT(14053, "商品来源非指定批次"),
    PICKING_STATUS_ALL_COMPLETE_PICKING(14054, "该批次的任务已全部完成，无需重复拣货"),
    SNNO_LOTNUM_IRRELEVANT(14055, "序列号【%s】不属于【%s】批次"),
    PICKING_LOC_IRRELEVANT(14056, "库位不在拣货范围内"),
    SO_ALLOC_GOODS_EMPTY(14057, "没有可分配的商品"),
    SO_ALLOC_GOODS_NOT_RULE_ZH(14058, "商品【%s】无周转规则，停止自动分配"),
    SO_ALLOC_GOODS_NOT_ALLOC_RULE_ZH(14060, "商品【%s】无分配规则，停止自动分配"),
    ALLOCATION_PRIORITY_ERROR(14059, "分配优先级错误"),
    SO_ALLOC_GOODS_NOT_RULE_ZZ_AND_FP(14060, "商品【%s】无周转规则和分配规则，停止自动分配"),
    SO_ALLOC_GOODS_NOT_LOC_VALUE(14061, "商品【%s】无库存数据，停止自动分配"),
    SO_PICKING_PALLET_NO_NOT_EXIST(14062, "目标容器号【%s】不存在"),
    SO_PICKING_PALLET_NO_NOT_ENABLED(14063, "目标容器号【%s】未启用"),

    PACK_SHIPPING_TIME_TYPE_ERROR(14064, "发运时间类型不合法"),
    PACK_SHIPPED_NOT_EXIST(14066, "发运信息【%s】不存在"),
    PACK_SHIPPED_ALREADY_PUSHED(14067, "发运信息【%s】已推送，不允许再次推送"),
    PACK_SHIPPED_PUSH_ILLEGAL_STATUS(14068, "运单已派单，不允许推送到TMS系统"),
    PACK_SHIPPED_TRANSIT_CANNOT_CANCEL(14069, "运单已发运，不允许取消"),
    PACK_SHIPPED_PUSHED_OR_NOT_IN_TRANSIT(14070, "运单已推送或运单状态非运输中，不允许完成"),

    SO_OWNER_INFO_INCORRECT(14070,"货主信息在系统中查不到，请检查"),
    SO_WAREHOUSE_INFO_INCORRECT(14071,"仓库信息在系统中查不到，请检查"),
    SO_SKU_INFO_INCORRECT(14072,"商品信息在系统中查不到，请检查"),
    SO_PACKIGE_INFO_INCORRECT(14073,"包装信息在系统中查不到，请检查"),
    RECHECK_NOT_EXIST(14074,"出库复核信息不存在!" ),
    RECHECK_STATUS_IS_NOT_A_COMPLETE_RECHECK(14075, "【%s】复核状态非完全复核，请检查确认"),
    PACKAGING_TASK_HAS_BEEN_GENERATED(14076, "【%s】已生成打包任务，请检查确认"),
    RECHECK_ID_EMPTY(14077, "出库ID不能为空"),
    THE_SELECTED_OUTBOUND_PACKING_TASKS_CONTAIN_INVALID_OR_DELETED_ITEMS(14078, "选中的出库打包任务中包含无效或已删除的任务"),
    PACKAGING_HEADER_STATUS_NOT_ALLOW_MODIFY(14079, "已打包和已取消不允修改状态"),
    PACKAGING_HEADER_DATA_NULL(14080, "打包任务数据为空"),
    PACK_CUSTOMER_ADDRESS_NOT_FIND(14081, "未找到打包任务单号对应的复核单"),

    SO_RECHECK_NOT_EXIST(14082, "复核单信息【%s】不存在"),
    PICKING_TASK_COMPLETED_NO_NEED_TO_ASSIGN(14083, "选择拣货任务存在已完结，无需分配/调整拣货员"),
    SKU_INFO_INCONFORMITY(14084, "商品信息不一致，请核查"),
    SKU_QTY_GREATER_THAN_PICKING_SKU(14085, "商品数量大于未拣货商品数量，请核查"),
    ;
    private final Integer code;
    private final String msg;
}
