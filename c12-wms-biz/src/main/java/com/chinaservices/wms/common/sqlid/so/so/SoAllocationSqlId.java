package com.chinaservices.wms.common.sqlid.so.so;

/**
 * 拣货分配_SQL_ID
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
public interface SoAllocationSqlId
{
    /**
     * 根据pickingNo查询分配明细
     */
    String SOALLOCATION_QUERY_BY_PICKING_NO = "soAllocation_query_getByPickingNo";
    /**
     * 商品明细拣货任务查询分页列表
     */
    String SOALLOCATION_QUERY_TASK_PAGE_LIST = "soAllocation_query_getTaskPageList";
    /**
     * 商品明细未分配查询分页列表
     */
    String SOALLOCATION_QUERY_PAGE_LIST = "soAllocation_query_getPageList";
    /**
     * 已分配拣货员分组分页列表
     */
    String SOALLOCATION_QUERY_ALLOCATED_PAGE_LIST = "soAllocation_query_allocated_getPageList";
    /**
     * 根据条件获取查询分页列表
     */
    String SOALLOCATION_QUERY_GETPICKINGLIST = "soAllocation_query_getPickingList";
    /**
     * 根据条件获取查询分页列表
     */
    String SOALLOCATION_QUERY_BYID = "soAllocation_query_getById";
    /**
     * 查询二次分拣符合时间范围的拣货单
     */
    String SOALLOCATION_QUERY_PICKING_NO_BY_SORTING_TIME = "soAllocation_query_pickingNoBySortingTime";
    /**
     * 拣货任务商品信息
     */
    String SORTING_SKU_QUERY = "soAllocation_query_sorting_sku";
    /**
     * 拣货任务 查询信息列表
     */
    String SOALLOCATION_QUERY_INFO_LIST = "soAllocation_query_info_list";
    /**
     * 查询拣货任务绩效分析
     */
    String SOALLOCATION_QUERY_PERFORMANCE_ANALYSIS = "soAllocation_query_performanceAnalysis";

    /**
     * 商品明细拣货任务查询分页列表
     */
    String SOALLOCATION_QUERY_TASK_LIST = "soAllocation_query_getTaskList";
}
