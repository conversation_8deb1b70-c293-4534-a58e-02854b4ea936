package com.chinaservices.wms.common.sqlid.passive.take;

public interface PassiveAutoInventorySqlId {
    /**
     * 自动盘点分页查询
     */
    public static final String PASSIVE_AUTO_INVENTORY_GETPAGE_QUERY = "passive_auto_inventory_getPage_query";
    /**
     * 自动盘点统计查询
     */
    public static final String PASSIVE_AUTO_INVENTORY_GETCOUNT_QUERY = "passive_auto_inventory_getCount_query";
    /**
     * 获取需要更新的uuid
     */
    public static final String PASSIVE_AUTO_INVENTORY_UUID_QUERY = "passive_auto_inventory_uuid_query";
    /**
     * 获取需要更新的epcIDs
     */
    public static final String PASSIVE_AUTO_INVENTORY_EPC_QUERY = "passive_auto_inventory_epc_query";
    /**
     * 库位盘点情况
     */
    public static final String PASSIVE_AUTO_INVENTORY_LOC_QUERY = "passive_auto_inventory_loc_query";
    /**
     * 自动盘点日志分页查询
     */
    public static final String PASSIVE_AUTO_INVENTORY_GETLOGPAGE_QUERY = "passive_auto_inventory_getLogPage_query";
    /**
     * 自动盘点列表查询
     */
    public static final String PASSIVE_AUTO_INVENTORY_GETLIST_QUERY = "passive_auto_inventory_getList_query";
    /**
     * 自动盘点仓库ID分组查询
     */
    public static final String PASSIVE_AUTO_INVENTORY_WAREHOUSE_ID_QUERY = "passive_auto_inventory_warehouse_id_query";
    /**
     * 自动盘点日志最新日志
     */
    public static final String PASSIVE_AUTO_INVENTORY_NEW_LOG_QUERY = "passive_auto_inventory_new_log_query";
    /**
     * 自动盘点日志最新已结束日志
     */
    public static final String PASSIVE_AUTO_INVENTORY_NEW_END_LOG_QUERY = "passive_auto_inventory_new_end_log_query";
    /**
     * 自动盘点无源任务号查询
     */
    public static final String PASSIVE_AUTO_INVENTORY_GET_QUERY = "passive_auto_inventory_get_query";
    /**
     * 自动盘点货架不为空数据查询
     */
    public static final String PASSIVE_AUTO_INVENTORY_LIST_BY_SHELF_NOT_NULL_QUERY = "passive_auto_inventory_list_by_shelf_not_null_query";
    /**
     * 自动盘点商品信息查询
     */
    public static final String PASSIVE_AUTO_INVENTORY_SKU_INFO_QUERY = "passive_auto_inventory_sku_info_query";
    /**
     * 修改状态为以扫描
     */
    public static final String PASSIVE_AUTO_INVENTORY_UPDATE_SCAN = "passive_auto_inventory_update_scan";
}
