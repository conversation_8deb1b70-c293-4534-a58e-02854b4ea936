package com.chinaservices.wms.common.sqlid.so.wv;

/**
* <AUTHOR>
* @description Wxheader
* @date  2024-12-26
**/
public interface WvHeaderSqlId {

    /**
    * 分页查询
    */
    public static final String CS_WV_HEADER_QUERY_GET_PAGE_LIST = "csWvHeader_query_getPageList";
    /**
     * 根据条件获取查询列表
     */
    String WVDETAIL_QUERY_GETLIST = "wvDetail_query_getList";
    String WVDETAIL_UPDATE_INCREASE_QTY_ALLOCATION_EA_BY_SO_DETAIL_ID = "wvDetail_update_increaseQtyAllocationEaBySoDetailId";
    String WVHEADER_QUERY_UPDATESTATUSBYWVIDSORSOIDS = "wvHeader_query_updateStatusByWvIdsOrSoIds";
    String WVHEADER_QUERY_GETHEADERPRINT = "wvHeader_query_getHeaderPrint";
    String WVHEADER_QUERY_DETAILCOUNT = "wvHeader_query_getDetailCount";
    String WVDETAIL_DELETE_BYWVIDS = "wvDetail_delete_deleteByWvIds";

    String WVHEADER_QUERY_GETWVNOBYDETAIL = "wvHeard_query_getWvNoByDetail";
    //根据出库单号获取波次的出库单
    String WVHEADER_QUERY_GETWVBYWVNO = "wvHeader_query_getWvByWvNo";
}
