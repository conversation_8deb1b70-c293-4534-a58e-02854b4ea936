package com.chinaservices.wms.common.sqlid.passive.tag;

public interface PassiveTagSqlId {

    /**
     * 无源标签分页查询
     */
    String PASSIVE_TAG_GETPAGE_QUERY = "passive_tag_getPage_query";

    /**
     * 无源标签日志分页查询
     */
    String PASSIVE_TAG_GETLOGPAGE_QUERY = "passive_tag_getLogPage_query";

    /**
     * 无源标签识别分页查询
     */
    String PASSIVE_TAG_RECOGNITION_QUERY_GET_PAGE_LIST = "passiveTagRecognition_query_getPageList";

    /**
     * 通过标签号获取重复无源标签
     */
    String PASSIVE_TAG_IMPORTDUPLICATE_QUERY = "passive_tag_importDuplicate_query";

    /**
     * 查询所有标签号和标签种类
     */
    String PASSIVE_TAG_FINDALLTYPEANDNO = "passive_tag_findAllTypeAndNo";

    /**
     * 查询移动端上架任务查询扫描标签数据
     */
    String PASSIVE_TAG_MOBILE_PA_TASK_QUERY_GET_LIST = "passiveTagMobilePaTask_query_getList";

}
