package com.chinaservices.wms.common.exception;

import com.chinaservices.core.exception.BizError;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 无源管理异常枚举
 */
@Getter
@AllArgsConstructor
public enum PassiveExceptionEnum implements BizError {

    PASSIVE_TAG_NO_EXIST(20001,"该标签号已存在"),
    PASSIVE_TAG_NOT_EXIST(20002,"该标签不存在"),
    PASSIVE_TAG_NOT_REQUISITION(20002,"存在状态为领用标签，不可以删除操作"),
    PASSIVE_TAG_NOT_REQUISITION_CAN_REQUISITION(20003,"状态为未领用标签，可以进行领用"),
    PASSIVE_TAG_REQUISITION_CAN_RESTORE(20004,"状态为已领用标签，可以进行归还"),
    PASSIVE_TAG_REQUISITION_CAN_BIND(20005,"状态为已领用标签，可以进行绑定"),
    PASSIVE_TAG_NOT_EXIST_IN_SYSTEM(20006,"在系统重查无此编号"),
    PASSIVE_DEVICE_EXIST(20007,"该设备类型已存在"),
    PASSIVE_DEVICE_SN_EXIST(20007,"设备SN已存在"),
    PASSIVE_DEVICE_NAME_EXIST(20007,"设备名称已存在"),
    PASSIVE_DEVICE_EXIST_DEVICE(20008,"设备类型被使用，不允许删除"),
    PASSIVE_TEMPLATE_DATA_NOT_NULL(20009,"模板数据不能为空"),
    PASSIVE_TEMPLATE_TYPE_NOT_NULL(20010,"编号类型不能为空"),
    PASSIVE_DEVICE_NOT_EXIST(20011,"该设备不存在" ),
    PASSIVE_DEVICE_GROUP_NOT_EXIST(20011,"该设备类型不存在"),
    PASSIVE_DEVICE_PARENT_EXIST(20013,"必须先选择上级设备才可以进行设备选择"),
    PASSIVE_TAG_TYPE_IMPORT(20016,"第%s行标签种类为空，无法导入"),
    PASSIVE_TAG_NO_IMPORT(20017,"第%s行标签号为空，无法导入"),
    PASSIVE_TAG_DUPLICATE_IMPORT(20018,"存在重复标签无法导入"),
    PASSIVE_TAG_TYPE_ERROR_IMPORT(20019,"第%s行标签种类不存在，无法导入"),
    PASSIVE_TAG_LOGOUT_FAIL_NOT_DELETE(20020,"%s标签注销失败，无法删除"),
    PASSIVE_TAG_PALLET_NO_NOT_ENABLED(20021, "容器号【%s】未启用"),
    PASSIVE_TAG_NO_BIND(20022, "此容器号已被标签%s绑定"),
    PASSIVE_AUTO_INVENTORY_BIND_SUBJECT_NOT_NULL(20023, "绑定对象不能为空！"),
    PASSIVE_AUTO_INVENTORY_NOW_NULL(20024, "没有执行中的盘点日志！"),
    USER_EMPTY(20025, "用户不存在"),
    USER_PERMISSION_DENIED(20026, "当前用户无法进行操作"),
    PASSIVE_TAG_WAREHOUSE_NOT_SAME(20027, "标签号归属仓库与损坏标签归属仓库不一致，请核查"),
    PASSIVE_TAG_UNPACK_STATUS_ILLEGAL(20028, "标签当前状态非已绑定，无法拆包"),
    PASSIVE_TAG_UNPACK_BIND_SUBJECT_ILLEGAL(20029, "标签绑定对象非货物，无法拆包"),
    PASSIVE_TAG_UNPACK_BIND_BOX_EA_NOE_ENOUGH(20030, "标签绑定箱码商品EA数不足，无法拆包"),
    PASSIVE_TAG_UNPACK_BIND_SN_EA_NOE_ENOUGH(20031, "标签绑定序列号商品EA数不足，无法拆包"),
    PASSIVE_TAG_UNPACK_BIND_SKU_EA_NOE_ENOUGH(20032, "标签绑定普通商品EA数不足，无法拆包"),
    PASSIVE_TAG_UNPACK_EA_NOT_SAME(20033, "拆包后的商品EA数不一致，请核查"),
    PASSIVE_TAG_UNPACK_ASN_NO_OR_SKU_NOT_SAME(20034, "拆包后标签的入库单号或商品信息与原标签不一致，请核查"),
    PASSIVE_TAG_UNREGISTER_FAILED(20035, "无源标签注销失败，请重试"),
    PASSIVE_TAG_DAMAGED_STATUS_ILLEGAL(20036, "替换标签当前领用情况为【%s】，无法替换"),
    PASSIVE_TAG_DAMAGED_ACTIVATE_STATUS_ILLEGAL(20037, "替换标签未激活，无法替换"),
    PASSIVE_TAG_BIND_BOX_DUPLICATE(20038, "箱码已绑定其它标签"),
    PASSIVE_TAG_BIND_BOX_SN_DUPLICATE(20039, "箱码关联序列号%s已绑定其它标签"),
    PASSIVE_TAG_BIND_SN_DUPLICATE(20040, "序列号%s已绑定其它标签"),
    PASSIVE_TAG_BIND_SN_IN_BOX_DUPLICATE(20041, "序列号关联箱码%s已绑定其他标签"),

    PASSIVE_DEVICE_ID_EXIST(2027, "设备ID已存在"),
    PASSIVE_DEVICE_PORTS_EXIST(2028, "设备入库失败，设备端口号重复"),
    PASSIVE_DEVICE_TYPE_NOT_EXIST(2029, "设备类型不存在" ),
    PASSIVE_DEVICE_PORT_NUM_ERROR(2030, "设备入库失败：端口数量不符合要求"),
    THE_PORT_NUMBER_IS_ALREADY_OCCUPIED(2031,"设备端口号重复" ),
    PASSIVE_DEVICE_TYPE_NOT_ALLOW_EDIT(2032, "该设备类型不允许修改" ),
    PASSIVE_PARENT_DEVICE_NOT_EXIST(2033,"父级设备不存在" ),
    PASSIVE_PARENT_DEVICE_TYPE_ERROR(2034,"无效的父级设备类型" ),
    PASSIVE_DEVICE_PORT_IN_USE(2035, "端口号4-15已被占用，无法修改端口数量！"),
    THERE_IS_NO_AUTOMATIC_DOOR_DEVICE_AT_THIS_LOCATION_PLEASE_VERIFY(2036,"该位置没有自动门设备，请核查"),
    PASSIVE_DEVICE_PORT_IN_INOUT(2036, "【%s】"),
    PASSIVE_DEVICE_PORT_IN_WZ_INPUT(2037, "{%s}任务在执行中，请核查"),
    PASSIVE_DEVICE_TASK_ID_NOT(2038, "任务id不存在"),
    PASSIVE_TAG_DISPATCH_NOT_RECEIVE(2039, "{%s}关联{%s}，没有收货信息，请核查"),
    PASSIVE_TAG_SKU_DISPATCH_SKU_INCONFORMITY(2040, "{%s}绑定{%s}与关联{%s}商品信息不一致，请核查"),
    PASSIVE_SKU_QTY_INCONFORMITY(2041, "数量信息不一致，请核查"),
    PASSIVE_NOT_TAG_DISPATCH(2042, "无法判断{%s}是自动收货还是自动发运，请核查该标签是否绑定非货物信息，或已发运"),
    ;

    private final Integer code;
    private final String msg;
}
