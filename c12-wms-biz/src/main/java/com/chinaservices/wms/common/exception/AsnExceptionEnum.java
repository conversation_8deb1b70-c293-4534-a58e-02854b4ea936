package com.chinaservices.wms.common.exception;

import com.chinaservices.core.exception.BizError;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 入库管理异常枚举
 */
@Getter
@AllArgsConstructor
public enum AsnExceptionEnum implements BizError {

//**************质检管理**********
    QC_HEADER_NOT_EXIST(13200, "质检单不存在"),
    ASN_QC_INSPECTED(13201, "请先选择质检阶段"),
    ACTUALQUALITYINSPECTION_RETURNWAREHOUSE(13202, "商品都已质检返库，无法操作"),
    QC_HEADER_NOT_OUTBOUNDINSPECTED(13203, "出库质检才可以操作返库上架"),

    DETAIL_NOT_EMPTY(13001, "商品明细不能为空"),
    SN_DUPLICATED(13002, "SN码不能重复"),
    SN_NOT_EQUALS_EA(13003, "sn码与预收数EA不匹配"),
    SO_NO_NOT_EMPTY(13004, "出库单号不能为空"),
    RECEIVE_STATUS_NOT_RECEIVED(13005, "收货状态不为未收货"),
    STATUS_NOT_CREATE_REVIEWED(13001, "【%s】状态不为创建或订单已审核，不能操作！"),
    DETAIL_EMPTY(13004, "【%s】不存在商品明细，不能操作！"),
    STATUS_NOT_CREATE_UNREVIEWED(13002, "【%s】状态不为创建或订单未审核，不能操作！"),
    STATUS_NOT_CREATE_PART_ALL_REVIEWED(13003, "【%s】状态不为创建/部分收货/完全收货或订单已审核，不能操作！"),
    CANCEL_AUDIT_ERROR(13005, "入库单取消审核失败！"),
    ASN_NO_NOT_EMPTY(13006, "入库单号不能为空"),
    ASN_NO_NOT_SUCCESS(13007, "入库单号不正确"),
    RECEIVE_STATUS_ALL_RECEIVED(13008, "入库单【%s】已完全收货，无需再收货确认"),
    ORDER_STATUS_CANCEL(13009, "订单【%s】已取消，不可进行收货操作"),
    PA_TASK_NOT_EXIST(13010, "上架任务不存在"),
    PA_TASK_ALREADY_PA(13011, "已上架的任务不能重复上架"),
    PA_NUM_LT_READY_PA_NUM(13012, "上架数不能大于待上架数量，请重新操作"),
    PA_TASK_RECEIVE_EMPTY(13013, "生成上架任务时收货明细为空"),
    RECEIVE_ALREADY_PA(13014, "勾选的收货明细已经上架"),
    THE_SN_CODE_MUST_HAVE_BEEN_RECEIVED(13016, "上架的sn码必须已收货"),
    SN_NO_NOT_REPEAT_RECEIVE(13016, "商品SN码不允许重复收货"),
    LOGISTIC_NO_GENERAL_WITH_CHINESE(13016, "上游单号不允许中文字符"),
    SN_CODE_INCORRECT(13018, "SN码不正确，无法操作"),
    ASSIGN_RECEIVER_FIRST(13018, "请先分配收货员再进行收货操作"),
    ASSIGN_A_LOADER_BEFORE_LISTING(13019, "请先分配上架员再进行上架操作"),
    THE_TARGET_LOCATION_IS_EMPTY_WHEN_IT_IS_PUT_ON_THE_SHELF(13019, "上架时目标库位为空"),
    THE_TARGET_CONTAINER_NUMBER_IS_EMPTY_WHEN_IT_IS_PUT_ON_THE_SHELF(13020, "上架时目标容器号为空"),
    RULE_PA_LOC_NOT_MATCH(13018, "商品：%s，上架规则未匹配到合适的库位，请上架时进行手工选择库位"),
    THE_TARGET_LOCATION_IS_NOT_EXIST_WHEN_IT_IS_PUT_ON_THE_SHELF(13019, "上架时目标库位不存在，请重新扫描"),
    THE_TARGET_LOCATION_IS_NOT_ENABLE_WHEN_IT_IS_PUT_ON_THE_SHELF(13020, "上架时目标库位未启用，请重新扫描"),
    THE_TARGET_CONTAINER_NUMBER_IS_NOT_EXIST_WHEN_IT_IS_PUT_ON_THE_SHELF(13021, "上架时目标容器号不存在，请重新扫描"),
    THE_TARGET_CONTAINER_NUMBER_IS_NOT_ENABLE_WHEN_IT_IS_PUT_ON_THE_SHELF(13022, "上架时目标容器号未启用，请重新扫描"),
    THE_TARGET_CONTAINER_NUMBER_IS_USED_WHEN_IT_IS_PUT_ON_THE_SHELF(13023, "上架时目标容器号已被占用，请重新扫描"),
    ASN_RTN_SHIPPING(13024, "入库单类型为退货入库才可以添加已发运的SN码"),
    ASN_RTN_SHIPPING_ONLY(13024, "入库单类型为退货入库只能添加已发运的SN码"),
    ASN_NO_CZ_MERGE_DATE(13025, "入库单【%s】已被合并收货，合并失败"),
    MERGE_ERROR_ASN_WAREHOUSE_INCONSISTENT(13026, "入库单【%s】仓库不同，合并失败"),
    MERGE_ERROR_ASN_RCV_OP_INCONSISTENT(13027, "入库单【%s】收货人不同，合并失败"),
    MERGE_ERROR_ASN_STATUS_NOT_NEW(13028, "入库单【%s】状态不能是非创建状态，合并失败"),
    MERGE_ERROR_ASN_AUDIT_NOT_NEW(13029, "入库单【%s】审核状态不能是未审核，合并失败"),
    MERGE_ERROR_ASN_RECEIVE(13030, "入库单【%s】收货状态不正确，合并失败"),
    MERGE_ERROR(13032, "合并失败,请联系管理员"),
    MERGE_DELETE_NO_ERROR(13033, "收货单单号不正确，删除失败"),
    MERGE_DELETE_NO_RCV_STATUS_ERROR(13034, "【%s】收货状态非未收货，不允许删除"),
    LOGISTIC_NO_NOT_EMPTY(13026, "上游单号不能为空"),
    OWEN_INFO_NOT_EMPTY(13027, "货主信息在系统中查不到，请检查"),
    WAREHOUSE_INFO_NOT_EMPTY(13028, "仓库信息在系统中查不到，请检查"),
    SKU_INFO_NOT_EMPTY(13029, "商品信息在系统中查不到，请检查"),
    PACKAGE_INFO_NOT_EMPTY(13030, "包装信息在系统中查不到，请检查"),
    PACKAGE_UNIT_INFO_NOT_EMPTY(13031, "包装单位信息在系统中查不到，请检查"),
    OWEN_ID_NOT_EMPTY(13032, "货主代码不能为空"),
    WAREHOUSE_ID_NOT_EMPTY(13033, "仓库代码不能为空"),
    SKU_ID_NOT_EMPTY(13034, "商品代码不能为空"),
    PACKAGE_ID_NOT_EMPTY(13035, "包装代码不能为空"),
    PACKAGE_UNIT_ID_NOT_EMPTY(13036, "包装单位代码不能为空"),
    LOGISTIC_NO_PUSHED_NOT_PUSH(13037, "上游单号已推送，不能重复推送"),
    MERGE_ORDER_TASK_JXZ_ERROR(13035, "订单收货任务【%s】正在进行中，无法取消审核"),
    RECEIVE_BATCH_ERROR(13036, "批量收货失败，系统异常"),
    ASN_RCV_ERROR_BY_NOT_REQUIRED_ATT(13038, "入库单【%s】存在未填写的必填项批次属性，请在入库单编辑处进行编辑或手动收货"),
    SO_PA_DETAIL_NOT_CANANL(13037, "上架明细【%s】取消失败,出库上架任务不允许取消！"),
    SO_PA_EA_GT_PICKING_EA(13038, "上架数超过可上架库存"),
    PA_FM_PALLET_LIST_NOT_NULL(13039, "出库上架源容器号不能为空"),
    PA_FM_PALLET_SUM_NOT_EQ_PAEA(13040, "上架数不等于所选容器可上架库存数"),
    PA_FM_PALLET_SUM_GT_EQ_PAEA(13041, "上架数超过可上架库存"),
    TAG_NO_NOT_DATABASE(13039, "【%s】在系统中不存在，请核查"),
    RECEIVE_TAG_NO_STATUS_ERROR(13040, "【%s】【%s】，请核查"),
    RECEIVE_TAG_NO_BUILDER_ORDER_AUDIT_ERROR(13041, "【%s】绑定的【%s】未审核，请核查"),
    RECEIVE_TAG_NO_NOT_EQ_SKU_CODE(13042, "【%s】商品编号与入库订单商品编号不一致"),
    RECEIVE_TAG_BUILDER_ORDER_NOT_ATT_ERROR(13043, "【%s】绑定【%s】批次属性未填写，请在入库单编辑处进行编辑或手动收货"),
    THE_GOODS_NOT_THIS_PA_TASK(13044, "【%s】不属于此上架任务"),
    PA_TASK_EA_LE_TAG_EA(13045, "上架EA数大于未上架EA数，请核查"),
    ORDER_BY_PASSIVES_NOT_RECEIVE(13046, "订单属于无源入库订单，不允许手动收货"),
    ORDER_BY_PASSIVES_NOT_MERGE_RECEIVE(13047, "入库订单【%s】是无源入库单，不允许合并收货"),
    PASSIVES_ALL_RECEIVE(13048, "标签号【%s】已收货，不允许再次收货"),
    ;

    private final Integer code;
    private final String msg;
}
