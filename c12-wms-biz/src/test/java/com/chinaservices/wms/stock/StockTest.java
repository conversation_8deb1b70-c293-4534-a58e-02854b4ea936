package com.chinaservices.wms.stock;


import cn.hutool.json.JSONUtil;
import com.chinaservices.core.constant.AppConstant;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.core.test.CustomBootTest;
import com.chinaservices.wms.common.constant.StatusConstant;
import com.chinaservices.wms.common.constant.TransactionType;
import com.chinaservices.wms.common.enums.stock.InventoryFreezeTypeEnum;
import com.chinaservices.wms.module.stock.common.exec.StockService;
import com.chinaservices.wms.module.stock.common.model.qty.bo.InvLotLocQtyBO;
import com.chinaservices.wms.module.stock.common.model.qty.query.StockFreezeQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

@CustomBootTest(profile = AppConstant.UAT_CODE)
@Slf4j
public class StockTest {


    @Autowired
    public StockService stockService;
    /**
     * 拣货
     */
    @Test
    public void pk() {
        try{
            stockService.exec(new InvLotLocQtyBO().setLocId(1893903617133969409L)
                            .setSkuId(1893833180630810625L)
                            .setUpdateNum(new BigDecimal(1))
                            .setOrderNo("test")
                            .setLotNum("PP20250226000055")
                            .setOwnerId(1890208746363748353L)
                            .setSkuSn(Lists.newArrayList("************"))
                            .setWarehouseId(1893903616664207362L).setToPallet("test")
                    .setSkuSn(Lists.newArrayList("KKKL03190000029"))
                    .setTransactionType(TransactionType.TRAN_PK)
            );
        }catch (ServiceException e){
//            log.error(e.getErrorMessage(), e);
        }
    }

    /**
     * 收货
     */
    @Test
    public void rcv(){
        stockService.exec(new InvLotLocQtyBO().setLocId(1888786703030222850L).setLotNum("PP20250210000018").setOrderNo("test").setPalletNum("111")
                .setUpdateNum(new BigDecimal(100)).setOwnerId(1888773507858890753L).setSkuId(1888775705045041153L).setWarehouseId(1888786702497546242L)
                .setTransactionType(TransactionType.TRAN_RCV)
        );
    }

    /**
     * 冻结
     */
    @Test
    public void freeze(){
        StockFreezeQuery stockFreezeQuery = new StockFreezeQuery().setFreezeType(InventoryFreezeTypeEnum.LOT).setWarehouseId(1888773898839326721L).setFreezeTargetCode("PP20250210000017");
        stockService.exec(new InvLotLocQtyBO().setStockFreezeQuery(stockFreezeQuery).setOrderNo("111")
                .setTransactionType(TransactionType.TRAN_FREE)
        );
    }

    /**
     * 解冻
     */
    @Test
    public void crFreeze(){
        StockFreezeQuery stockFreezeQuery = new StockFreezeQuery().setFreezeType(InventoryFreezeTypeEnum.LOC_LOT_ID).setOwnerCode("HZ20250210004").setWarehouseId(1888786702497546242L).setFreezeTargetId(1888775512522293249L);
        stockService.exec(new InvLotLocQtyBO()
                .setStockFreezeQuery(stockFreezeQuery)
                        .setLocId(1888794164873199617L).setOwnerId(1888773507858890753L).setWarehouseId(1888786702497546242L).setSkuId(1888775512522293249L)
                .setUpdateNum(new BigDecimal(1))
                .setTransactionType(TransactionType.TRAN_CR_FREE)
        );
    }

    @Test
    public void sPk(){
        String a  = "{\"availableQty\":null,\"invLotLoc\":null,\"invLotLocList\":[],\"isFrozen\":\"\",\"list\":[],\"locCode\":\"\",\"locId\":1944648949939834881,\"lotAttMaps\":null,\"lotNum\":\"PP000120250718000002\",\"orderNo\":\"AI000120250718008\",\"ownerCode\":\"\",\"ownerId\":1942784368992849922,\"ownerName\":\"\",\"palletNum\":\"RQH000120250709001\",\"qtyEaBefore\":null,\"qtyPackOp\":null,\"remark\":\"\",\"skuCode\":\"\",\"skuId\":1944602811123634178,\"skuName\":\"\",\"skuSn\":[],\"stockFreezeQuery\":null,\"toLocCode\":\"\",\"toLocId\":1944648949939834881,\"toLocNum\":\"\",\"toOwnerId\":null,\"toOwnerName\":\"\",\"toPallet\":\"RQH000120250709001\",\"toWarehouseId\":null,\"toWarehouseName\":\"\",\"tranIsFrozen\":\"\",\"transactionType\":\"CRINA\",\"updateNum\":1.00000000,\"warehouseCode\":\"\",\"warehouseId\":1942783819589357569,\"warehouseName\":\"\",\"whetherSerialController\":\"\"}";
        InvLotLocQtyBO invLotLocQtyBO = JSONUtil.toBean(a,InvLotLocQtyBO.class);
        stockService.exec(invLotLocQtyBO);
    }

    /**
     * 移动
     */
    @Test
    public void mv(){
        stockService.exec(new InvLotLocQtyBO().setLocId(1888777036094832642L)
                .setLotNum("PP20250210000017")
                .setOrderNo("test")
                .setPalletNum(null)
                .setToLocId(1888777219742433282L)
                .setUpdateNum(new BigDecimal(600))
                .setOwnerId(1888773507858890753L)
                .setSkuId(1888775278056505345L)
                .setWarehouseId(1888773898839326721L)
                .setIsFrozen(StatusConstant.YES)
                .setTransactionType(TransactionType.TRAN_MV)
        );
    }

    /**
     * 调整
     */
    @Test
    public void ad(){
        stockService.exec(new InvLotLocQtyBO().setList(Lists.newArrayList(new InvLotLocQtyBO().setLocId(1893903617133969409L)
                                .setSkuId(1893833180630810625L)
                .setUpdateNum(new BigDecimal(-1))
                                        .setLotNum("PP20250226000055")
                .setOwnerId(1890208746363748353L)
                .setSkuSn(Lists.newArrayList("************"))
                                .setWarehouseId(1893903616664207362L)
                        ))
                .setOrderNo("111").setOwnerId(1890208746363748353L)
                .setTransactionType(TransactionType.TRAN_AD)
        );
    }

    @Test
    public void spk(){
        InvLotLocQtyBO invLotLocQtyBO = JSONUtil.toBean("{\"locId\":1893903617133969409,\"skuId\":1893833180630810625,\"updateNum\":1,\"orderNo\":\"test\",\"lotNum\":\"PP20250226000055\",\"ownerId\":1890208746363748353,\"skuSn\":[\"************\"],\"warehouseId\":1893903616664207362,\"toPallet\":\"test\",\"skuSn\":[\"KKKL03190000029\"],\"transactionType\":\"TRAN_SPK\"}", InvLotLocQtyBO.class);
    }

    /**
     * 发运
     */
    @Test
    public void sp(){
        stockService.exec(new InvLotLocQtyBO().setLocId(1888786703030222850L).setLotNum("PP20250218000001").setOrderNo("test")
                .setUpdateNum(new BigDecimal(5)).setOwnerId(1888767706851246082L).setSkuId(1891662435297329154L).setWarehouseId(1888770625357021186L)
                .setTransactionType(TransactionType.TRAN_SP)
        );
    }

    @Test
    public void qcConfNf(){
        stockService.exec(new InvLotLocQtyBO().setLocId(1889240609573109761L).setLotNum("PP20250211000017").setOrderNo("test")
                .setUpdateNum(new BigDecimal(5)).setOwnerId(1889240946409275393L).setSkuId(1889241823694094338L).setWarehouseId(1889239679184207873L)
                .setTransactionType(TransactionType.TRAN_QC_CONF_NF)
        );
    }
}
