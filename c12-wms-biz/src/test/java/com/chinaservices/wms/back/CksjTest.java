package com.chinaservices.wms.back;

import cn.hutool.json.JSONUtil;
import com.chinaservices.core.test.CustomBootTest;
import com.chinaservices.jpa.support.rule.ConditionRule;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.wms.common.enums.asn.PaSourceEnum;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskBatchConfirmItem;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskPageCondition;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskQuery;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskSoCreateItem;
import com.chinaservices.wms.module.asn.pa.service.CsPaTaskService;
import com.chinaservices.wms.module.so.picking.dao.SoPickingDao;
import com.chinaservices.wms.module.so.picking.model.SoPicking;
import com.chinaservices.wms.module.so.so.domain.OrderForCreatePaTaskItem;
import com.chinaservices.wms.module.so.so.domain.ValidateOrderForDateItem;
import com.chinaservices.wms.module.so.so.service.SoCreatePaTaskService;
import com.chinaservices.wms.module.so.so.service.SoHeaderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@CustomBootTest(profile = "uat")
public class CksjTest {

    @Autowired
    private SoHeaderService soHeaderService;
    @Autowired
    private CsPaTaskService csPaTaskService;
    @Autowired
    private SoPickingDao soPickingDao;
    @Autowired
    private SoCreatePaTaskService soCreatePaTaskService;

    private void initTastUserInfo(){
        SessionUserInfo info = SessionContext.get();
        info.setCompanyId(1942781918210207745L);
        info.setTenancy(1942781918210207745L);
        info.setUserId(1942781989080854530L);
        SessionContext.put(info);
    }

    /**
     * 4.1.4 订单完结按钮逻辑验证
     */
    @Test
    public void test1() {
        ValidateOrderForDateItem validateOrderForDateItem = soHeaderService.validateOrderForCompletion(1943546511027605506L);
        log.info(JSONUtil.toJsonStr(validateOrderForDateItem));
    }

    /**
     * 4.1.5 生成上架任务验证
     */
    @Test
    public void test2() {
        initTastUserInfo();
        OrderForCreatePaTaskItem paTaskItem = soHeaderService.orderForCreatePaTask(1943546511027605506L);
        log.info(JSONUtil.toJsonStr(paTaskItem));
    }


    @Test
    public void test3() {
        initTastUserInfo();
        PaTaskPageCondition condition = new PaTaskPageCondition();
        condition.setPageSize(20);
        condition.setPageNumber(1);
        PageResult<PaTaskQuery> paTaskItem = csPaTaskService.page(condition);
        log.info(JSONUtil.toJsonStr(paTaskItem));
    }

    @Test
    public void test4() {
        initTastUserInfo();
        String param = "{\"paTaskConfirmItemList\":[{\"toLocName\":\"水果库位003\",\"toPalletNo\":\"\",\"fmPalletNoList\":[\"RQH000120250710003\"],\"toLocCode\":\"KW0709003\",\"treatQty\":\"5\",\"qtyPa\":\"5\",\"packageUnitName\":\"件\",\"minQuantity\":1,\"toLocId\":\"1942785314154090498\",\"qtyPaEa\":5,\"id\":\"1945306315135913985\",\"snNoList\":[]}]}";
        csPaTaskService.batchConfirm(JSONUtil.toBean(param, PaTaskBatchConfirmItem.class));
        //log.info(JSONUtil.toJsonStr(paTaskItem));
    }


    @Test
    public void test6() {
        String param = "JHD000120250724000001";
        SoPicking first = soPickingDao.findFirst(new ConditionRule().andEqual(SoPicking::getPickingNo, param));
        log.info("更新前拣货状态：{}",first.getStatus());
        soCreatePaTaskService.updatePickingStatus(first);
        log.info("更新后拣货状态：{}",first.getStatus());
    }

}
