package com.chinaservices.wms.passive;

import cn.hutool.json.JSONUtil;
import com.chinaservices.core.constant.AppConstant;
import com.chinaservices.core.test.CustomBootTest;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.wms.module.asn.receive.service.AsnReceivePassiveService;
import com.chinaservices.wms.module.passive.take.dto.PassiveTaskMockCondition;
import com.chinaservices.wms.module.passive.task.service.PassiveTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@CustomBootTest(profile = AppConstant.UAT_CODE)
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PassiveAsnPaTask {

    private final AsnReceivePassiveService asnReceivePassiveService;
    private final PassiveTaskService passiveTaskService;
    public static void initUserInfo(){
        SessionUserInfo userInfo = SessionContext.get();
        userInfo.setUserId(1948343261621915650L);
        userInfo.setCompanyId(1942781918210207745L);
        userInfo.setTenancy(1942781918210207745L);
        SessionContext.put(userInfo);
    }
    @Test
    public void test1(){
        initUserInfo();
        asnReceivePassiveService.createAsnOrderOrSoOrderByTag("20250724");
    }
    @Test
    public void test2(){
        initUserInfo();
        String param = "[\n" +
                "  {\n" +
                "    \"id\": 1949757122619117569,\n" +
                "    \"taskId\": \"1487\",\n" +
                "    \"type\": \"-1\",\n" +
                "    \"epcList\": [\n" +
                "      {\n" +
                "        \"epc\": \"20250725002\",\n" +
                "        \"taskid\": \"1487\",\n" +
                "        \"id\": \"1\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]";
        passiveTaskService.getTaskInfo(JSONUtil.toList(param, PassiveTaskMockCondition.class));
    }
}
