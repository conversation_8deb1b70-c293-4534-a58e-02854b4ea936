package com.chinaservices.wms.module.rule.common;

import com.chinaservices.wms.module.asn.receive.model.AsnReceive;
import com.chinaservices.wms.module.rule.common.domain.BatchRulePaResult;
import com.chinaservices.wms.module.rule.common.service.BatchRulePaService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量上架规则处理测试
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class RulePaCommonServiceBatchTest {
    
    @Autowired
    private RulePaCommonService rulePaCommonService;
    
    @Autowired
    private BatchRulePaService batchRulePaService;
    
    /**
     * 测试批量处理功能
     */
    @Test
    public void testBatchCheckRulePaTaskAndGetLoc() {
        // 准备测试数据
        List<AsnReceive> testData = createTestData();
        
        log.info("开始测试批量处理，数据量: {}", testData.size());
        
        // 执行批量处理
        BatchRulePaResult result = rulePaCommonService.batchCheckRulePaTaskAndGetLoc(testData);
        
        // 验证结果
        assertBatchResult(result, testData.size());
        
        // 打印处理报告
        String summary = batchRulePaService.getProcessingSummary(result);
        log.info("处理报告:\n{}", summary);
    }
    
    /**
     * 测试大批量数据处理性能
     */
    @Test
    public void testLargeBatchPerformance() {
        // 创建大量测试数据
        List<AsnReceive> largeTestData = createLargeTestData(1000);
        
        log.info("开始性能测试，数据量: {}", largeTestData.size());
        
        long startTime = System.currentTimeMillis();
        
        // 执行批量处理
        BatchRulePaResult result = rulePaCommonService.batchCheckRulePaTaskAndGetLoc(largeTestData);
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        log.info("性能测试完成:");
        log.info("数据量: {}", largeTestData.size());
        log.info("总耗时: {} ms", totalTime);
        log.info("平均每条: {} ms", (double) totalTime / largeTestData.size());
        log.info("处理速度: {} 条/秒", (double) largeTestData.size() / totalTime * 1000);
        
        // 验证结果
        assertBatchResult(result, largeTestData.size());
    }
    
    /**
     * 测试异常处理
     */
    @Test
    public void testExceptionHandling() {
        // 创建包含异常数据的测试集
        List<AsnReceive> testDataWithErrors = createTestDataWithErrors();
        
        log.info("开始异常处理测试，数据量: {}", testDataWithErrors.size());
        
        // 执行批量处理
        BatchRulePaResult result = rulePaCommonService.batchCheckRulePaTaskAndGetLoc(testDataWithErrors);
        
        // 验证异常处理
        log.info("异常处理测试结果:");
        log.info("总数: {}, 成功: {}, 失败: {}", 
                result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
        
        if (!result.isAllSuccess()) {
            log.info("失败详情:\n{}", result.getFailureSummary());
        }
    }
    
    /**
     * 创建测试数据
     */
    private List<AsnReceive> createTestData() {
        List<AsnReceive> testData = new ArrayList<>();
        
        for (int i = 1; i <= 50; i++) {
            AsnReceive asnReceive = new AsnReceive();
            asnReceive.setId((long) i);
            asnReceive.setSkuCode("SKU" + String.format("%03d", i % 10 + 1)); // 循环使用10个SKU
            asnReceive.setQtyRcvEa(BigDecimal.valueOf(i));
            asnReceive.setRcvPallet("PALLET" + i);
            asnReceive.setRcvLocId((long) (i % 5 + 1)); // 循环使用5个收货库位
            asnReceive.setLotNum("LOT" + i);
            testData.add(asnReceive);
        }
        
        return testData;
    }
    
    /**
     * 创建大量测试数据
     */
    private List<AsnReceive> createLargeTestData(int count) {
        List<AsnReceive> testData = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            AsnReceive asnReceive = new AsnReceive();
            asnReceive.setId((long) i);
            asnReceive.setSkuCode("SKU" + String.format("%03d", i % 100 + 1)); // 循环使用100个SKU
            asnReceive.setQtyRcvEa(BigDecimal.valueOf(i % 1000 + 1));
            asnReceive.setRcvPallet("PALLET" + i);
            asnReceive.setRcvLocId((long) (i % 20 + 1)); // 循环使用20个收货库位
            asnReceive.setLotNum("LOT" + i);
            testData.add(asnReceive);
        }
        
        return testData;
    }
    
    /**
     * 创建包含错误的测试数据
     */
    private List<AsnReceive> createTestDataWithErrors() {
        List<AsnReceive> testData = new ArrayList<>();
        
        // 正常数据
        for (int i = 1; i <= 20; i++) {
            AsnReceive asnReceive = new AsnReceive();
            asnReceive.setId((long) i);
            asnReceive.setSkuCode("SKU" + String.format("%03d", i));
            asnReceive.setQtyRcvEa(BigDecimal.valueOf(i));
            asnReceive.setRcvPallet("PALLET" + i);
            asnReceive.setRcvLocId((long) i);
            asnReceive.setLotNum("LOT" + i);
            testData.add(asnReceive);
        }
        
        // 异常数据 - 空商品编码
        for (int i = 21; i <= 25; i++) {
            AsnReceive asnReceive = new AsnReceive();
            asnReceive.setId((long) i);
            asnReceive.setSkuCode(null); // 空商品编码
            asnReceive.setQtyRcvEa(BigDecimal.valueOf(i));
            testData.add(asnReceive);
        }
        
        // 异常数据 - 不存在的商品编码
        for (int i = 26; i <= 30; i++) {
            AsnReceive asnReceive = new AsnReceive();
            asnReceive.setId((long) i);
            asnReceive.setSkuCode("INVALID_SKU" + i); // 不存在的商品编码
            asnReceive.setQtyRcvEa(BigDecimal.valueOf(i));
            testData.add(asnReceive);
        }
        
        return testData;
    }
    
    /**
     * 验证批量处理结果
     */
    private void assertBatchResult(BatchRulePaResult result, int expectedTotal) {
        assert result != null : "处理结果不能为空";
        assert result.getTotalCount() == expectedTotal : "总数不匹配";
        assert result.getSuccessCount() + result.getFailureCount() == expectedTotal : "成功数+失败数应等于总数";
        assert result.getProcessingTime() > 0 : "处理时间应大于0";
        
        log.info("批量处理结果验证通过");
    }
}
