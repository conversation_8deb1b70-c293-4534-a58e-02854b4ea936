package com.chinaservices.wms.module.rule.common;

import com.chinaservices.wms.module.asn.receive.model.AsnReceive;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 批量上架规则处理测试
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class RulePaCommonServiceTest {
    
    @Autowired
    private RulePaCommonService rulePaCommonService;
    
    /**
     * 测试批量处理功能
     */
    @Test
    public void testBatchCheckRulePaTaskAndGetLoc() {
        // 准备测试数据
        List<AsnReceive> testData = createTestData();
        
        log.info("开始测试批量处理，数据量: {}", testData.size());
        
        long startTime = System.currentTimeMillis();
        
        // 执行批量处理
        Map<String, Long> result = rulePaCommonService.batchCheckRulePaTaskAndGetLoc(testData);
        
        long endTime = System.currentTimeMillis();
        
        // 验证结果
        assert result != null : "处理结果不能为空";
        assert result.size() <= testData.size() : "结果数量不应超过输入数量";
        
        log.info("批量处理完成:");
        log.info("输入数据量: {}", testData.size());
        log.info("输出结果数量: {}", result.size());
        log.info("处理耗时: {} ms", endTime - startTime);
        log.info("平均处理时间: {} ms/条", (double)(endTime - startTime) / testData.size());
        
        // 打印部分结果
        result.entrySet().stream()
                .limit(10)
                .forEach(entry -> log.info("商品编码: {}, 库位ID: {}", entry.getKey(), entry.getValue()));
        
        log.info("批量处理测试通过");
    }
    
    /**
     * 测试单个处理与批量处理的性能对比
     */
    @Test
    public void testPerformanceComparison() {
        List<AsnReceive> testData = createTestData();
        
        log.info("开始性能对比测试，数据量: {}", testData.size());
        
        // 单个处理
        long singleStartTime = System.currentTimeMillis();
        Map<String, Long> singleResults = new java.util.HashMap<>();
        for (AsnReceive asnReceive : testData) {
            Long locId = rulePaCommonService.checkRulePaTaskAndGetLoc(asnReceive);
            singleResults.put(asnReceive.getSkuCode(), locId);
        }
        long singleEndTime = System.currentTimeMillis();
        long singleTime = singleEndTime - singleStartTime;
        
        // 批量处理
        long batchStartTime = System.currentTimeMillis();
        Map<String, Long> batchResults = rulePaCommonService.batchCheckRulePaTaskAndGetLoc(testData);
        long batchEndTime = System.currentTimeMillis();
        long batchTime = batchEndTime - batchStartTime;
        
        // 性能对比
        log.info("性能对比结果:");
        log.info("单个处理耗时: {} ms", singleTime);
        log.info("批量处理耗时: {} ms", batchTime);
        log.info("性能提升: {}%", (double)(singleTime - batchTime) / singleTime * 100);
        log.info("批量处理速度: {} 条/秒", (double)testData.size() / batchTime * 1000);
        
        // 验证结果一致性
        assert singleResults.size() == batchResults.size() : "单个处理和批量处理结果数量应一致";
        
        log.info("性能对比测试完成");
    }
    
    /**
     * 创建测试数据
     */
    private List<AsnReceive> createTestData() {
        List<AsnReceive> testData = new ArrayList<>();
        
        for (int i = 1; i <= 50; i++) {
            AsnReceive asnReceive = new AsnReceive();
            asnReceive.setId((long) i);
            asnReceive.setSkuCode("SKU" + String.format("%03d", i % 10 + 1)); // 循环使用10个SKU
            asnReceive.setQtyRcvEa(BigDecimal.valueOf(i));
            asnReceive.setRcvPallet("PALLET" + i);
            asnReceive.setRcvLocId((long) (i % 5 + 1)); // 循环使用5个收货库位
            asnReceive.setLotNum("LOT" + i);
            testData.add(asnReceive);
        }
        
        return testData;
    }
    
    /**
     * 测试空数据处理
     */
    @Test
    public void testEmptyDataHandling() {
        // 测试空列表
        Map<String, Long> emptyResult = rulePaCommonService.batchCheckRulePaTaskAndGetLoc(new ArrayList<>());
        assert emptyResult.isEmpty() : "空列表应返回空结果";
        
        // 测试null
        Map<String, Long> nullResult = rulePaCommonService.batchCheckRulePaTaskAndGetLoc(null);
        assert nullResult.isEmpty() : "null应返回空结果";
        
        log.info("空数据处理测试通过");
    }
}
