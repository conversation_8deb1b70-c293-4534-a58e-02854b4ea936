package com.chinaservices.wms.lucky;


import com.chinaservices.core.constant.AppConstant;
import com.chinaservices.core.enums.YesNoEnum;
import com.chinaservices.core.test.CustomBootTest;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.common.domain.IdsReq;
import com.chinaservices.wms.common.enums.adjust.AdjustStatusEnum;
import com.chinaservices.wms.common.util.MapObjectUtil;
import com.chinaservices.wms.module.common.SkuCommonService;
import com.chinaservices.wms.module.stock.adjust.domain.AdjustSkuPageCondition;
import com.chinaservices.wms.module.stock.adjust.service.AdjustHeaderService;
import com.chinaservices.wms.module.stock.loc.domain.LocListPageQuery;

import com.chinaservices.wms.module.warning.InvWarningItem;
import com.chinaservices.wms.module.warning.service.InvWarningService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import java.util.*;

@CustomBootTest(profile = AppConstant.TEST_CODE)
public class MoveServiceTest {

    @Autowired
    private SkuCommonService skuCommonService;
    /**
     * 承运商和承运商运费规则查询测试类
     */
    @Test
    public void testSearch() {
        AdjustSkuPageCondition condition = new AdjustSkuPageCondition();
        condition.setIsFrozen(YesNoEnum.NO.getCode());
        condition.setOwnerId(1888908823859625986L);
        condition.setWarehouseId(1888909129020407809L);
        PageResult<LocListPageQuery> skuByOwnerId = skuCommonService.getSkuByOwnerId(condition);
        System.out.println("aaaaaaaaaaaaa"+Arrays.toString(skuByOwnerId.getRows().toArray()));
    }

    @Test
    public void objectToMap(){
        HashMap<String, List<String>> map = new HashMap<>();
        HashMap<String, List<String>> mapNull = new HashMap<>();
        map.put("0",Arrays.asList("1","2","3"));
        map.put("1",Arrays.asList("11","12","13"));
        map.put("2",Arrays.asList("21","22","23"));
        map.put("3",Arrays.asList("31","32","33"));
        String s = MapObjectUtil.mapToObject(map);
        System.out.println("ssss:"+s);
        System.out.println("mapNull"+mapNull);
        List<String> mapToObjectValue = MapObjectUtil.getMapToObjectValue(s,Boolean.TRUE);
        System.out.println("list:"+mapToObjectValue);
        Map<String, List<String>> stringListMap = MapObjectUtil.objectToMap(s);
        System.out.println("map:"+stringListMap);
    }

    @Autowired
    private AdjustHeaderService adjustHeaderService;
    @Test
    public void s(){
        IdsReq idsReq = new IdsReq();
        idsReq.setIds(List.of(1905438086197776386L));
        adjustHeaderService.executionAdjust(idsReq, AdjustStatusEnum.EXECUTE.getCode());
    }

    @Autowired
    private InvWarningService invWarningService;
    @Test
    public void save(){
        InvWarningItem item = new InvWarningItem();
        item.setWarehouseId(1888909129020407809L);
        item.setWarehouseName("阿浅的仓库");
        item.setOwnerId(1888908823859625986L);
        item.setOwnerName("阿浅的货主");
        item.setSkuId(1888910788481581058L);
        item.setSkuCode("SP20250110001");
        item.setSkuName("阿浅的商品001");
        item.setPackageId(1888908946782093314L);
        item.setPackageUnitId(1888908951337107457L);
        item.setPackageUnitName("大托盘");
        item.setMinInv(BigDecimal.valueOf(1200));
        invWarningService.save(item);
    }
}
