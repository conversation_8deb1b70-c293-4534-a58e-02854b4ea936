package com.chinaservices.wms.xujian;

import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.test.CustomBootTest;
import com.chinaservices.sdk.pojo.PageResult;
import com.chinaservices.wms.module.asn.pa.domain.PaTaskBatchConfirmItem;
import com.chinaservices.wms.module.asn.pa.service.CsPaTaskService;
import com.chinaservices.wms.module.so.so.domain.SoHeaderQuery;
import com.chinaservices.wms.module.so.so.domain.SoHeardPageQuery;
import com.chinaservices.wms.module.so.so.service.SoHeaderService;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/1/11 14:28
 * @Description TODO
 */
@CustomBootTest(profile = "uat")
public class SoReverseTest {

    private static final Logger log = LoggerFactory.getLogger(SoReverseTest.class);

    @Autowired
    private SoHeaderService  soHeaderService;

    /**
     * 出库单分页列表
     */
    @Test
    public void testAddAsnMerge(){
        String param = "{\"pageNumber\":1,\"pageSize\":20,\"soNo\":null,\"skuName\":null,\"soType\":null,\"ownerName\":null,\"warehouseName\":null,\"logisticNo\":null,\"orderSource\":null,\"datePicker\":null,\"allocStatus\":null,\"clientName\":null,\"carrierName\":null,\"priority\":null,\"status\":null,\"paNo\":null,\"isCreatePaTask\":null,\"paStatus\":null,\"createStartTime\":\"\",\"createEndTime\":\"\"}";
        PageResult<SoHeaderQuery> page = soHeaderService.page(JSONUtil.toBean(param, SoHeardPageQuery.class));
        System.out.println(JSONUtil.toJsonStr(page));
    }

}
