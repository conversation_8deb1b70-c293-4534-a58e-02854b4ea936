package com.chinaservices.wms.xujian;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.test.CustomBootTest;
import com.chinaservices.wms.common.enums.AllocationTypeEnum;
import com.chinaservices.wms.module.so.so.domain.AutoSaveItem;
import com.chinaservices.wms.module.so.so.domain.GetRightPickingListCondition;
import com.chinaservices.wms.module.so.so.domain.GetRightPickingListQuery;
import com.chinaservices.wms.module.so.so.domain.PickingBasicsCondition;
import com.chinaservices.wms.module.so.so.service.SoAllocationService;
import com.chinaservices.wms.module.so.wv.domain.WvHeaderItem;
import com.chinaservices.wms.module.so.wv.service.WvHeaderService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@CustomBootTest(profile = "uat")
public class WvTest {

    @Autowired
    private WvHeaderService wvHeaderService;
    @Autowired
    private SoAllocationService soAllocationService;
    @Test
    public void wvPageTest(){
        WvHeaderItem item = new WvHeaderItem();
        item.setOrderStartTime(DateUtil.parseDateTime("2025-02-25 00:00:00"));
        item.setOrderEndTime(DateUtil.parseDateTime("2025-02-25 00:00:00"));
        item.setWvRuleId(1894199409549815810L);
        item.setWvRuleName("重合度3+销/调");
        item.setWarehouseId(1892748447043858434L);
        wvHeaderService.save(item);
    }

    @Test
    public void sss(){
        List<GetRightPickingListQuery> queryList = soAllocationService.getRightPickingList(new GetRightPickingListCondition()
                .setAllocationType(AllocationTypeEnum.OUTBOUND.getCode())
                .setSoNo("SO000120250709000005")
                .setQueryScope("1"));
        log.info("queryList={}", JSONUtil.toJsonStr(queryList));
    }

    @Test
    public void saveAuto(){
        soAllocationService.autoSave(new AutoSaveItem()
                        .setAllocationType("1")
                .setWvNo("WV20250306015")
                .setSoDetailIds(Lists.newArrayList(1897539353664073730L,1897539353844428802L)));
    }

    @Test
    public void sdfp(){
        soAllocationService.pickingBasics(new PickingBasicsCondition()
                .setAllocationType("1")
                .setSkuId(1897159957748953089L)
                .setWaveTimesDetail(
                        new PickingBasicsCondition.WaveTimesDetail()
                                .setWvNo("WV20250305018")
                                .setSoNo("SN20250305000011")
                ));
    }

    @Test
    public void getRightPickingList(){
        soAllocationService.getRightPickingList(new GetRightPickingListCondition()
                .setAllocationType("0")
                .setSoNo("SN20250305000005")
                .setQueryScope("1"));
    }
}
