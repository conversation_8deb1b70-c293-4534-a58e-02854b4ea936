<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chinaservices.wms</groupId>
        <artifactId>c12-core</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>c12-wms</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>c12-wms-api</module>
        <module>c12-wms-biz</module>
    </modules>

    <properties>
        <spring-boot.version>3.3.6</spring-boot.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-edi-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-wms-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-auth-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-edi-api</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://192.168.0.37:9999/repository/maven-releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://192.168.0.37:9999/repository/maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>

</project>