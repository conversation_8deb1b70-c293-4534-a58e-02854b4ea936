package com.chinaservices.wms.module.stock.multi.domain;


import com.chinaservices.wms.common.domain.WmsPageCondition;
import lombok.Data;

import java.math.BigDecimal;


/**
 * By:厦门同创空间信息技术有限公司 www.chinaservices.com.cn
 * <AUTHOR>
 */
@Data
public class InvLotLocUpdateQtyCondition extends WmsPageCondition {

    /**
     * 库存主键
     */
    private Long id;
    /**
     * 库位主键
     */
    private Long locId;
    /**
     * 批次号
     */
    private String lotNum;
    /**
     * 容器号
     */
    private String palletNum;
    /**
     * 仓库主键
     */
    private Long warehouseId;
    /**
     * 货主id
     */
    private String ownerId;

    /**
     * 商品id
     */
    private String skuId;

    /**
     * 库存操作数
     */
    private BigDecimal updateQty;
    /**
     * 冻结操作数
     */
    private BigDecimal updateQtyHold;
    /**
     * 分配操作数
     */
    private BigDecimal updateQtyAlloc;
    /**
     * 拣货操作数
     */
    private BigDecimal updateQtyPk;
    /**
     * 待质检操作数
     */
    private BigDecimal updateQtyQc;
    /**
     * 上架待入操作数
     */
    private BigDecimal updateQtyPaIn;
    /**
     * 上架待出操作数
     */
    private BigDecimal updateQtyPaOut;
    /**
     * 调整待增操作数
     */
    private BigDecimal updateQtyAdIn;
    /**
     * 调整待减操作数
     */
    private BigDecimal updateQtyAdOut;
    /**
     * 移动待入操作数
     */
    private BigDecimal updateQtyMvIn;
    /**
     * 移动待出操作数
     */
    private BigDecimal updateQtyMvOut;
    /**
     * 转移待入操作数
     */
    private BigDecimal updateQtyTfIn;
    /**
     * 转移待出操作数
     */
    private BigDecimal updateQtyTfOut;
    /**
     * 补货待入操作数
     */
    private BigDecimal updateQtyRpIn;
    /**
     * 补货待出操作数
     */
    private BigDecimal updateQtyRpOut;
    /**
     * 待移动数
     */
    private BigDecimal updateQtyWaitMove;
    /**
     * 待移动数
     */
    private BigDecimal updateQtyWaitDamage;

    /**
     * 版本号
     */
    private Long recVer;
}
