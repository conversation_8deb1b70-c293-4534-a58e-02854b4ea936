package com.chinaservices.wms.module.so.so.domain;


import com.chinaservices.sdk.pojo.Condition;
import com.chinaservices.wms.module.so.pack.domain.TagNoSkuQuery;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 拣货任务单扫码拣货条件
 *
 * <AUTHOR>
 * @date 2025/01/19
 */
@Data
public class SoAllocationPickingScanCondition implements Condition
{
    /**
     * 拣货单号
     */
    @NotBlank(message = "拣货单号不能为空")
    private String pickingNo;
    /**
     * 拣货任务单号
     */
    private String pickingTaskNo;
    /**
     * 拣货数
     */
    @Min(value = 1, message = "拣货数不能小于{value}")
    @Digits(integer = 8, fraction = 0, message = "不能输入小数")
    @NotNull(message = "拣货数量不能为空")
    private BigDecimal pickingEa;
    /**
     * 库位Id
     */
    @NotNull(message = "库位Id不能为空")
    private Long locId;
    /**
     * 批次号
     */
    @NotBlank(message = "批次号不能为空")
    private String lotNum;
    /**
     * 商品序列号集合
     */
    private List<String> snNoList;

    /**
     * 容器号
     */
    private String toPalletNum;
    /**
     * 标签号集合
     */
    private List<TagNoSkuQuery> tagNoList;
}
