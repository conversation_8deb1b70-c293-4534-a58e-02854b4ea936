package com.chinaservices.wms.module.asn.pa.domain;

import com.chinaservices.module.domain.ModuleBaseUpdateItem;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 上架确认
 */
@Data
public class PaTaskConfirmItem extends ModuleBaseUpdateItem {
    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long id;

    /**
     * 上架任务号
     */
    @NotNull(message = "上架任务号不能为空")
    private String paNo;

    /**
     * 源容器号
     */
    //@NotEmpty(message = "源容器号不能为空")
    private String fmPalletNo;

    private List<String> fmPalletNoList;

    /**
     * 上架数
     */
    private BigDecimal qtyPa;

    /**
     * 上架数EA
     */
    @NotNull(message = "上架数不能为空")
    private BigDecimal qtyPaEa;

    /**
     * 目标库位主键
     */
    private Long toLocId;

    /**
     * 目标库位code
     */
    private String toLocCode;

    /**
     * 目标库位名称
     */
    @NotBlank(message = "目标库位不能为空")
    private String toLocName;

    /**
     * 目标容器号id
     */
    private Long toPalletId;

    /**
     * 目标容器号名称
     */
    private String toPalletNo;

    /**
     * 目标容器号名称
     */
    private String status;


    /**
     * 序列码集合
     */
    private List<String> snNoList;
}
