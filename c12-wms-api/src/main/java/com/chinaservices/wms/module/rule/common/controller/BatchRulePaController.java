package com.chinaservices.wms.module.rule.common.controller;

import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.util.EmptyUtil;
import com.chinaservices.wms.module.asn.receive.model.AsnReceive;
import com.chinaservices.wms.module.asn.receive.service.AsnReceiveService;
import com.chinaservices.wms.module.rule.common.domain.BatchRulePaResult;
import com.chinaservices.wms.module.rule.common.service.BatchRulePaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 批量上架规则处理控制器
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@RestController
@RequestMapping("/api/rule/batch-pa")
@Tag(name = "批量上架规则", description = "批量上架规则处理相关接口")
public class BatchRulePaController {
    
    @Autowired
    private BatchRulePaService batchRulePaService;
    
    @Autowired
    private AsnReceiveService asnReceiveService;
    
    /**
     * 批量处理收货明细的上架规则
     */
    @PostMapping("/process")
    @Operation(summary = "批量处理上架规则", description = "根据收货明细ID列表批量处理上架规则")
    public ResponseData<BatchRulePaResult> batchProcessRulePa(
            @Parameter(description = "收货明细ID列表") @RequestBody List<Long> asnReceiveIds) {
        
        try {
            log.info("开始批量处理上架规则，收货明细ID数量: {}", 
                    EmptyUtil.isEmpty(asnReceiveIds) ? 0 : asnReceiveIds.size());
            
            // 参数验证
            if (EmptyUtil.isEmpty(asnReceiveIds)) {
                return ResponseData.error("收货明细ID列表不能为空");
            }
            
            // 查询收货明细
            List<AsnReceive> asnReceiveList = asnReceiveService.findByIds(
                    asnReceiveIds.toArray(new Long[0]));
            
            if (EmptyUtil.isEmpty(asnReceiveList)) {
                return ResponseData.error("未找到对应的收货明细记录");
            }
            
            if (asnReceiveList.size() != asnReceiveIds.size()) {
                log.warn("部分收货明细ID不存在，请求数量: {}, 实际查询到: {}", 
                        asnReceiveIds.size(), asnReceiveList.size());
            }
            
            // 执行批量处理
            BatchRulePaResult result = batchRulePaService.processBatchRulePa(asnReceiveList);
            
            log.info("批量处理完成，总数: {}, 成功: {}, 失败: {}", 
                    result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
            
            return ResponseData.success(result);
            
        } catch (Exception e) {
            log.error("批量处理上架规则失败", e);
            return ResponseData.error("批量处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量处理并更新收货明细的库位信息
     */
    @PostMapping("/process-and-update")
    @Operation(summary = "批量处理并更新", description = "批量处理上架规则并更新收货明细的库位信息")
    public ResponseData<BatchRulePaResult> batchProcessAndUpdate(
            @Parameter(description = "收货明细ID列表") @RequestBody List<Long> asnReceiveIds) {
        
        try {
            log.info("开始批量处理并更新，收货明细ID数量: {}", 
                    EmptyUtil.isEmpty(asnReceiveIds) ? 0 : asnReceiveIds.size());
            
            // 参数验证
            if (EmptyUtil.isEmpty(asnReceiveIds)) {
                return ResponseData.error("收货明细ID列表不能为空");
            }
            
            // 查询收货明细
            List<AsnReceive> asnReceiveList = asnReceiveService.findByIds(
                    asnReceiveIds.toArray(new Long[0]));
            
            if (EmptyUtil.isEmpty(asnReceiveList)) {
                return ResponseData.error("未找到对应的收货明细记录");
            }
            
            // 执行批量处理并更新
            BatchRulePaResult result = batchRulePaService.processBatchRulePaAndUpdate(asnReceiveList);
            
            log.info("批量处理并更新完成，总数: {}, 成功: {}, 失败: {}", 
                    result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
            
            return ResponseData.success(result);
            
        } catch (Exception e) {
            log.error("批量处理并更新失败", e);
            return ResponseData.error("批量处理并更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取处理结果摘要
     */
    @PostMapping("/summary")
    @Operation(summary = "获取处理摘要", description = "获取批量处理结果的详细摘要")
    public ResponseData<String> getProcessingSummary(
            @Parameter(description = "批量处理结果") @RequestBody BatchRulePaResult result) {
        
        try {
            if (result == null) {
                return ResponseData.error("处理结果不能为空");
            }
            
            String summary = batchRulePaService.getProcessingSummary(result);
            return ResponseData.success(summary);
            
        } catch (Exception e) {
            log.error("获取处理摘要失败", e);
            return ResponseData.error("获取处理摘要失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ASN ID批量处理
     */
    @PostMapping("/process-by-asn")
    @Operation(summary = "根据ASN批量处理", description = "根据ASN ID批量处理其下所有收货明细的上架规则")
    public ResponseData<BatchRulePaResult> batchProcessByAsnIds(
            @Parameter(description = "ASN ID列表") @RequestBody List<Long> asnIds) {
        
        try {
            log.info("开始根据ASN ID批量处理，ASN数量: {}", 
                    EmptyUtil.isEmpty(asnIds) ? 0 : asnIds.size());
            
            // 参数验证
            if (EmptyUtil.isEmpty(asnIds)) {
                return ResponseData.error("ASN ID列表不能为空");
            }
            
            // 查询ASN下的所有收货明细
            List<AsnReceive> asnReceiveList = asnReceiveService.findByAsnIds(asnIds);
            
            if (EmptyUtil.isEmpty(asnReceiveList)) {
                return ResponseData.error("未找到对应ASN的收货明细记录");
            }
            
            log.info("查询到收货明细数量: {}", asnReceiveList.size());
            
            // 执行批量处理
            BatchRulePaResult result = batchRulePaService.processBatchRulePa(asnReceiveList);
            
            log.info("根据ASN批量处理完成，总数: {}, 成功: {}, 失败: {}", 
                    result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
            
            return ResponseData.success(result);
            
        } catch (Exception e) {
            log.error("根据ASN批量处理失败", e);
            return ResponseData.error("根据ASN批量处理失败: " + e.getMessage());
        }
    }
}
