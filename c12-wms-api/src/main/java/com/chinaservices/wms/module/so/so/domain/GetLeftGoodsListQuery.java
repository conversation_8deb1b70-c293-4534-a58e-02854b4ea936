package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.sdk.json.jackson.annotation.JsonNumberFormat;
import com.chinaservices.sdk.pojo.Query;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 分配功能-左侧商品列表返回内容
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetLeftGoodsListQuery implements Query {

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品ID
     */
    private Long skuId;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 分配状态
     */
    private String allocStatus;

    /**
     * 订货数量
     */
    private BigDecimal qtySoEa;

    /**
     * 订货数
     */
    private BigDecimal qtySo;

    /**
     * 包装单位ID
     */
    private Long packageUnitId;

    /**
     * 包装单位名称
     */
    private String packageUnitName;

    /**
     * 包装单位EA名称
     */
    private String packageUnitNameEa;

    /**
     * 包装ID
     */
    private Long packageId;

    /**
     * 包装名称
     */
    private String packageName;

    /**
     * 总重量（KG）
     */
    @JsonNumberFormat(scale = 4)
    private BigDecimal totalWeight;

    /**
     * 未分配数
     */
    private BigDecimal unAllocQty;

    /**
     * 出库单号
     */
    private String soNo;

    /**
     * 出库订单明细主键
     */
    private Long soDetailId;

    /**
     * 出库订单明细单号
     */
    private String soDetailNo;

    /**
     * 是否展示分配按钮
     */
    private boolean isAllocBtn = true;

    /**
     * 订单状态 order_status（create=创建、cancel=取消订单、close=关闭订单/订单完结）
     */
    private String soOrderStatus;

    /**
     * 加工拆分任务明细表ID
     */
    private Long processTaskDetailsId;

    /**
     * 加工拆分任务单号
     */
    private String processOrderNo;

    /**
     * 分配类型 0=出库分配，1=波次分配，2=加工单，3=拆分分配
     */
    private String allocationType;
}
