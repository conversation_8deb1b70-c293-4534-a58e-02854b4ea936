package com.chinaservices.wms.module.so.picking.domain;

import com.chinaservices.sdk.pojo.Query;
import com.chinaservices.wms.common.enums.SoAssignPickersStatusEnum;

import java.util.Date;
import java.util.List;

/**
 * 拣货查询
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
public class SoPickingQuery implements Query
{

    /**
     * 主键
     */
    private Long id;

    /**
     * 拣货单号
     */
    private String pickingNo;
    /**
     * 拣货任务数
     */
    private Long pickingTaskNum;

    /**
     * 拣货单状态<br/>
     *   picking_status
     *   （not_generate：未生成，unpicked：未拣货，partial_picking：部分拣货，complete_picking：完全拣货，cancelled：已作废）
     */
    private String status;
    /**
     * 拣货状态
     */
    private String pickingStatus;
    /**
     * 是否分配拣货员<br/>
     *   assign_pickers_status
     *   （unallocated:未分配，partial_allocation：部分分配，full_allocation：完全分配）
     * @see SoAssignPickersStatusEnum
     */
    private String assignPickersStatus;

    /**
     * 库存周转规则主键
     */
    private Long rotationRuleId;

    /**
     * 库存周转规则名称
     */
    private String rotationRuleName;

    /**
     * 分配规则主键
     */
    private Long allocRuleId;

    /**
     * 分配规则名称
     */
    private String allocRuleName;

    /**
     * 出库单Id
     */
    private Long soId;

    /**
     * 出库单号
     */
    private String soNo;

    /**
     * 波次单Id
     */
    private Long wvId;

    /**
     * 波次单号
     */
    private String wvNo;

    /**
     * 加工单编号
     */
    private String processOrderNo;

    /**
     * 加工单类型
     */
    private String processOrderType;
    /**
     * 上架任务单号
     */
    private String paNo;
    /**
     * 出库单数量
     */
    private Long soNoQty;
    /**
     * 出库单号集合
     */
    private List<SoPickingSoNoQuery> soNoList;

    /**
     * 标签号
     */
    private String tagNo;
    /**
     * 标签号数量
     */
    private Long tagNoQty;
    /**
     * 标签号集合
     */
    private List<TagNoQuery> tagNoList;
    /**
     * 备注
     */
    private String remark;
    /**
     * 拣货员
     */
    private String pickingOpName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 操作栏按钮权限 -- 分配拣货员
     */
    private boolean assignPicked = false;
    /**
     * 操作栏按钮权限 -- 取消拣货单
     */
    private boolean cancelled = false;


    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getPickingNo()
    {
        return pickingNo;
    }

    public void setPickingNo(String pickingNo)
    {
        this.pickingNo = pickingNo;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public Long getRotationRuleId()
    {
        return rotationRuleId;
    }

    public void setRotationRuleId(Long rotationRuleId)
    {
        this.rotationRuleId = rotationRuleId;
    }

    public String getRotationRuleName()
    {
        return rotationRuleName;
    }

    public void setRotationRuleName(String rotationRuleName)
    {
        this.rotationRuleName = rotationRuleName;
    }

    public Long getAllocRuleId()
    {
        return allocRuleId;
    }

    public void setAllocRuleId(Long allocRuleId)
    {
        this.allocRuleId = allocRuleId;
    }

    public String getAllocRuleName()
    {
        return allocRuleName;
    }

    public void setAllocRuleName(String allocRuleName)
    {
        this.allocRuleName = allocRuleName;
    }

    public Long getSoId()
    {
        return soId;
    }

    public void setSoId(Long soId)
    {
        this.soId = soId;
    }

    public String getSoNo()
    {
        return soNo;
    }

    public void setSoNo(String soNo)
    {
        this.soNo = soNo;
    }

    public Long getWvId()
    {
        return wvId;
    }

    public void setWvId(Long wvId)
    {
        this.wvId = wvId;
    }

    public String getWvNo()
    {
        return wvNo;
    }

    public void setWvNo(String wvNo)
    {
        this.wvNo = wvNo;
    }

    public String getRemark()
    {
        return remark;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public Date getCreateTime()
    {
        return createTime;
    }

    public void setCreateTime(Date createTime)
    {
        this.createTime = createTime;
    }

    public String getPickingStatus()
    {
        return pickingStatus;
    }

    public void setPickingStatus(String pickingStatus)
    {
        this.pickingStatus = pickingStatus;
    }

    public String getAssignPickersStatus()
    {
        return assignPickersStatus;
    }

    public void setAssignPickersStatus(String isAssignPickers)
    {
        this.assignPickersStatus = isAssignPickers;
    }

    public List<SoPickingSoNoQuery> getSoNoList()
    {
        return soNoList;
    }

    public void setSoNoList(List<SoPickingSoNoQuery> soNoList)
    {
        this.soNoList = soNoList;
    }

    public Long getSoNoQty()
    {
        return soNoQty;
    }

    public void setSoNoQty(Long soNoQty)
    {
        this.soNoQty = soNoQty;
    }

    public boolean isAssignPicked()
    {
        return assignPicked;
    }

    public void setAssignPicked(boolean assignPicked)
    {
        this.assignPicked = assignPicked;
    }

    public boolean isCancelled()
    {
        return cancelled;
    }

    public void setCancelled(boolean cancelled)
    {
        this.cancelled = cancelled;
    }

    public Long getPickingTaskNum()
    {
        return pickingTaskNum;
    }

    public void setPickingTaskNum(Long pickingTaskNum)
    {
        this.pickingTaskNum = pickingTaskNum;
    }

    public String getPickingOpName()
    {
        return pickingOpName;
    }

    public void setPickingOpName(String pickingOpName)
    {
        this.pickingOpName = pickingOpName;
    }

    public String getPaNo() {
        return paNo;
    }

    public void setPaNo(String paNo) {
        this.paNo = paNo;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getProcessOrderNo() {
        return processOrderNo;
    }

    public void setProcessOrderNo(String processOrderNo) {
        this.processOrderNo = processOrderNo;
    }

    public String getProcessOrderType() {
        return processOrderType;
    }

    public void setProcessOrderType(String processOrderType) {
        this.processOrderType = processOrderType;
    }

    public List<TagNoQuery> getTagNoList() {
        return tagNoList;
    }

    public void setTagNoList(List<TagNoQuery> tagNoList) {
        this.tagNoList = tagNoList;
    }

    public Long getTagNoQty() {
        return tagNoQty;
    }

    public void setTagNoQty(Long tagNoQty) {
        this.tagNoQty = tagNoQty;
    }

    public String getTagNo() {
        return tagNo;
    }

    public void setTagNo(String tagNo) {
        this.tagNo = tagNo;
    }
}