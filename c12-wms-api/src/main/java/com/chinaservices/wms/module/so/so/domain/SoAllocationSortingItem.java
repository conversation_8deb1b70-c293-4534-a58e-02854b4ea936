package com.chinaservices.wms.module.so.so.domain;



import com.chinaservices.module.domain.ModuleBaseUpdateItem;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 拣货任务分拣项目
 *
 * <AUTHOR>
 * @date 2025/01/22
 */
public class SoAllocationSortingItem extends ModuleBaseUpdateItem {


    /**
     * 分拣数
     */
    private BigDecimal sortingEa;
    /**
     * 二次分拣状态
     */
    private String secondarySortingStatus;
    /**
     * 分拣时间
     */
    private Date sortingTime;
    /**
     * 拣货人主键
     */
    private Long sortingOpId;
    /**
     * 拣货人名称
     */
    private String sortingOpName;
    /**
     * 二次分拣任务状态
     */
    private String sortingTaskStatus;


    public BigDecimal getSortingEa()
    {
        return sortingEa;
    }

    public void setSortingEa(BigDecimal sortingEa)
    {
        this.sortingEa = sortingEa;
    }

    public String getSecondarySortingStatus()
    {
        return secondarySortingStatus;
    }

    public void setSecondarySortingStatus(String secondarySortingStatus)
    {
        this.secondarySortingStatus = secondarySortingStatus;
    }

    public Date getSortingTime()
    {
        return sortingTime;
    }

    public void setSortingTime(Date sortingTime)
    {
        this.sortingTime = sortingTime;
    }

    public Long getSortingOpId()
    {
        return sortingOpId;
    }

    public void setSortingOpId(Long sortingOpId)
    {
        this.sortingOpId = sortingOpId;
    }

    public String getSortingOpName()
    {
        return sortingOpName;
    }

    public void setSortingOpName(String sortingOpName)
    {
        this.sortingOpName = sortingOpName;
    }

    public String getSortingTaskStatus() {
        return sortingTaskStatus;
    }

    public void setSortingTaskStatus(String sortingTaskStatus) {
        this.sortingTaskStatus = sortingTaskStatus;
    }
}
