package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.module.domain.ModuleBaseUpdateItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取分配页面左侧商品列表查询条件
 * <AUTHOR>
 * @date 2025/1/15 15:26
 * @Description 获取分配页面左侧商品列表查询条件
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreatePickingTaskItem extends ModuleBaseUpdateItem {


    /**
     * 商品主键
     */
    private Long skuId;

    /**
     * 库位主键
     */
    private Long locId;

    /**
     * 容器号主键
     */
    private String palletNo;

    /**
     * 批次号
     */
    private String lotNum;

    /**
     * 出库订单明细单号
     */
    private String soDetailNo;

    /**
     * 出库单单号
     */
    private String soNo;

    /**
     * 波次单单号
     */
    private String wvNo;

    /**
     * 分配数ea
     */
    private Long qtyAllocationEa;

    /**
     * 拣货任务号
     */
    private String pickingTaskNo;

    /**
     * 仓库ID
     */
    private Long wareHouseId;

    /**
     * 加工/拆分单编号
     */
    private String processOrderNo;

    /**
     * 加工/拆分单明细ID-拆分单使用
     */
    private Long processTaskDetailsId;

    /**
     * 加工单任务编号-加工单使用
     */
    private String processTaskNo;
}
