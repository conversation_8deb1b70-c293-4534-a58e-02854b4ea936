package com.chinaservices.wms.module.so.picking.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 拣货单明细
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PickingDetail {

    /**
     * 出库单号
     */
    private String soNo;

    /**
     * 拣货单明细ID
     */
    private Long id;

    /**
     * 拣货单号
     */
    private String pickingNo;

    /**
     * 拣货任务号
     */
    private String pickingTaskNo;

    /**
     * 拣货状态 picking_status （not_generate：未生成，unpicked：未拣货，partial_picking：部分拣货，complete_picking：完全拣货，cancelled：已作废）
     */
    private String status;

    /**
     * 分配数
     */
    private BigDecimal qtyPack;

    /**
     * 分配数EA
     */
    private BigDecimal qtyEa;

    /**
     * 包装单位
     */
    private String packageUnitName;

    /**
     * 包装单位Ea
     */
    private String packageUnitNameEa;


    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品ID
     */
    private Long skuId;

    /**
     * 商品ID合集
     */
    private List<Long> skuIds;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 目标库位
     */
    private String locName;

    /**
     * 目标库位ID
     */
    private Long locId;

    /**
     * 批次号
     */
    private String lotNum;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 上架任务号
     */
    private String paNo;

    private boolean cancelAllocBtn = true;

    /**
     * 已拣货数EA
     */
    private BigDecimal pickingEa;
}
