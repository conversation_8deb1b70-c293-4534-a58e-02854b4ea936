package com.chinaservices.wms.module.asn.pa.domain;

import com.chinaservices.sdk.pojo.Query;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 上架任务请求结果
 */
@Data
public class PaTaskQuery implements Query {

    /**
     * 主键
     */
    private String id;

    /**
     * 上架任务号
     */
    private String paNo;

    /**
     * 仓库代码
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 订单类型:ASN入库单,SO出库单
     */
    private String paSource;



    /**
     * 出/入库订单id
     */
    private Long orderId;

    /**
     * 出/入库订单单号
     */
    private String orderNo;

    /**
     * 收货明细主键
     */
    private String receiveNo;


    /**
     * 行状态(1:待上架,2:已上架)
     */
    private String status;

    /**
     * 货主代码
     */
    private Long ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 商品代码
     */
    private Long skuId;

    /**
     * 商品代码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 批次號
     */
    private String lotNum;

    /**
     * 包装编码
     */
    private Long packageId;

    /**
     * 包装名称
     */
    private String packageName;

    /**
     * 包装单位
     */
    private Long packageUnitId;

    /**
     * 包装单位名称
     */
    private String packageUnitName;

    /**
     * 源库位主键
     */
    private Long fmLocId;

    /**
     * 源库位名称
     */
    private String fmLocName;

    /**
     * 源容器号
     */
    private String fmPalletId;

    /**
     * 源容器号
     */
    private String fmPalletNo;

    /**
     * 源容器号列表
     */
    private List<PalletListByPaNoQuery> fmPalletNoList;

    /**
     * 目标库位主键
     */
    private Long toLocId;

    /**
     * 目标库位code
     */
    private String toLocCode;

    /**
     * 目标库位代码
     */
    private String toLocName;

    /**
     * 目标容器号id
     */
    private String toPalletId;

    /**
     * 目标容器号名称
     */
    private String toPalletNo;

    /**
     * 待上架数
     */
    private BigDecimal treatQty;

    /**
     * 待上架数EA
     */
    private BigDecimal treatQtyEa;

    /**
     * 待上架数EA
     */
    private BigDecimal qtyPlanEa;

    /**
     * 上架数EA
     */
    private BigDecimal qtyPaEa;

    /**
     * 上架时间
     */
    private Date paTime;

    /**
     * 上架人
     */
    private Long paPersonId;
    /**
     * 上架人
     */
    private String paPersonName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本
     */
    private Long recVer;

    private Integer minQuantity;


    /**
     * 上架确认按钮权限
     */
    private boolean confirmBtn = true;


    /**
     * 取消上架按钮权限
     */
    private boolean cancelBtn = true;

    /**
     * 上架明细按钮权限
     */
    private boolean detailBtn = true;

    /**
     * 分配上架员按钮权限
     */
    private boolean allocPaPersonBtn = false;

    /**
     * 调整上架员按钮权限
     */
    private boolean adjustPaPersonBtn = false;

    /**
     * 序列码
     */
    private boolean isSerialCode = true;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 上架订单类型 数据字典：pa_task_order_type
     */
    private String orderType;

    /**
     * 标签号集合
     */
    private List<String> tagNoList;
}
