package com.chinaservices.wms.module.asn.pa.domain;

import com.chinaservices.sdk.pojo.PageCondition;
import com.chinaservices.wms.common.domain.WmsBasePageCondition;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description: 上架任务 查询条件
 */
@Data
public class PaTaskPageCondition extends PageCondition {

    /**
     * 入库单号
     */
    private String orderNo;

    /**
     * 入库单号 批量
     */
    private List<String> orderNos;

    /**
     * 货主编码
     */
    private String ownerId;
    private Set<String> ownerIds;

    /**
     * 商品编码
     */
    private String skuId;

    /**
     * 状态(1:未上架,2:已上架)
     */
    private String status;


    /**
     * 源容器号主键
     */
    private String fmPalletId;

    /**
     * 源容器号
     */
    private String fmPalletNo;

    /**
     * 上架任务号
     */
    private String paNo;

    /**
     * 上架人
     */
    private Long paPersonId;


    /**
     * 单据类型-asn入库，so出库
     */
    //@NotBlank(message = "上架类型不能为空")
    private String paSource;

    /**
     * 创建时间从
     */
    private String orderTimeFrom;

    /**
     * 创建时间到
     */
    private String orderTimeTo;

    /**
     * 标签号
     */
    private String tagNo;

    /**
     * 客户ID
     */
    private String clientId;
}
