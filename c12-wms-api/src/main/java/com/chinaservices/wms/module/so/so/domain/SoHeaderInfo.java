package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.wms.module.so.so.domain.SoDetailInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @program: c12-wms
 * @ClassName SoHeaderInfo
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2025-01-13 14:55
 **/
@Data
public class SoHeaderInfo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 出库单类型(数据字典so_type)
     */
    private String soType;

    /**
     * 出库单类型
     */
    private String soTypeStr;

    /**
     * 是否关联订单：1-是；0-否
     */
    private String relationOrder;

    /**
     * 上游单号
     */
    private String logisticNo;


    /**
     * 货主主键
     */
    private Long ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 仓库主键
     */
    private Long warehouseId;
    /**
     * 仓库名称
     */
    private String warehouseName;


    /**
     * 订单时间
     */
    private Date orderTime;

    /**
     * 出库单号
     */
    private String soNo;

    /**
     * 预计发货时间从
     */
    private Date startTime;
    /**
     * 预计发货时间到
     */
    private Date endTime;

    /**
     * 订单优先级别(数据字典priority)
     */
    private String priority;

    /**
     * 订单优先级别
     */
    private String priorityStr;

    /**
     * 客户主键
     */
    private Long clientId;

    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 客户电话
     */
    private String clientMobile;

    /**
     * 客户收货地址
     */
    private String clientAddress;


    /**
     * 审核状态(数据字典wms_audit_status)
     */
    private String auditStatus;


    /**
     * 状态(order_status)
     */
    private String status;

    /**
     * 分配状态(so_alloc_status)
     */
    private String allocStatus;

    /**
     * 订单来源(order_source_type)
     */
    private String orderSource;

    /**
     * 销售单号
     */
    private String saleOrderNo;

    /**
     * 是否可编辑关联销售单：1-是；0-否
     */
    private String editRelation;

    /**
     * 出库订单明细列表
     */
    private List<SoDetailInfo> soDetailInfos;

    /**
     * 承运商id
     */
    private Long carrierId;

    /**
     * 承运商名称
     */
    private String carrierName;
}
