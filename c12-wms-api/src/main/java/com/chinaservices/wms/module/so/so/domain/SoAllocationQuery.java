package com.chinaservices.wms.module.so.so.domain;


import com.chinaservices.sdk.pojo.Query;
import com.chinaservices.wms.common.enums.AllocStatusEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 拣货任务商品详情
 *
 * <AUTHOR>
 * @date 2025/01/19
 */
@Data
public class SoAllocationQuery implements Query {
    /**
     * 主表id
     */
    private Long id;
    /**
     * 波次单号
     */
    private String wvNo;

    /**
     * 容器主键
     */
    private String palletId;
    /**
     * 拣货单号
     */
    private String pickingNo;
    /**
     * 拣货任务号
     */
    private String pickingTaskNo;
    /**
     * 上架任务号
     */
    private String paNo;
    /**
     * 任务状态
     */
    private String taskStatus;
    /**
     * 批次属性
     */
    private String lotNum;
    /**
     * 复核数
     */
    private String qtyCheckUom;
    /**
     * 复核数Ea
     */
    private BigDecimal qtyCheckEa;

    /**
     * 打包代码
     */
    private Long packageId;
    /**
     * 打包名称
     */
    private String packageName;
    /**
     * 包装单位编码
     */
    private Long packageUnitId;
    /**
     * 包装单位名称
     */
    private String packageUnitName;
    /**
     * 拣货状态<br/>
     * alloc_status
     * （distributing：分配中，unpicked：未拣货，partial_picking：部分拣货，
     * complete_picking：完全拣货，cancelled：已作废）
     * @see AllocStatusEnum
     */
    private String pickingStatus;

    /**
     * 拣货数（带单位）
     */
    private String qtyPack;

    /**
     * 分配拣货数Ea/重量
     */
    private BigDecimal qtyEa;

    /**
     * 分配拣货数Ea/重量
     */
    private String qtyEaStr;
    /**
     * 已拣货数
     */
    private BigDecimal pickingEa;

    /**
     * 已拣货数
     */
    private String pickingEaStr;
    /**
     * 未拣货数
     */
    private BigDecimal unQtyEa;
    /**
     * 拣货人主键
     */
    private Long pickingOpId;
    /**
     * 拣货人名称
     */
    private String pickingOpName;
    /**
     * 商品主键
     */
    private Long skuId;
    /**
     * 商品代码
     */
    private String skuCode;
    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 货主主键
     */
    private Long ownerId;
    /**
     * 货主名称
     */
    private String ownerName;
    /**
     * 状态
     */
    private String status;

    /**
     * 发运订单号
     */
    private String soNo;
    /**
     * 库位主键
     */
    private Long warehouseId;
    /**
     * 库位主键
     */
    private Long locId;
    /**
     * 库位名称
     */
    private String locCode;
    /**
     * 目标库位代码
     */
    private Long toLocId;
    /**
     * 到目标库位代码
     */
    private String toLocCode;
    /**
     * 到目标容器号
     */
    private String toPalletNum;
    /**
     * 二次分拣状态
     */
    private String secondarySortingStatus;
    /**
     * 分拣数
     */
    private BigDecimal sortingEa;

    /*包装数量*/
    private Long quantity;

    /**
     * 加工单编号
     */
    private String processOrderNo;
    /**
     * 加工单类型：1-加工单；2-拆分单
     */
    private String processOrderType;
    /**
     * 加工任务编码
     */
    private String processTaskNo;
    /**
     * 加工单任务明细id
     */
    private Long processTaskDetailsId;

    /**
     * 二次分拣任务状态
     */
    private String sortingTaskStatus;
    /**
     * 拣货任务操作栏按钮 -- 分配拣货员
     */
    private boolean assign = false;
    /**
     * 拣货任务操作栏按钮 -- 确认拣货
     */
    private boolean picking = false;
    /**
     * 拣货任务操作栏按钮 -- 取消任务
     */
    private boolean cancel = false;

    /**
     * 分拣操作栏按钮 -- 分拣
     */
    private boolean sorting = false;


}
