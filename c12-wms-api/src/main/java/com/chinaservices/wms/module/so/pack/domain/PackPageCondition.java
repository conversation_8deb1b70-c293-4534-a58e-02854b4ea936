package com.chinaservices.wms.module.so.pack.domain;

import com.chinaservices.sdk.pojo.Condition;
import com.chinaservices.wms.common.domain.WmsBasePageCondition;

/**
 * 出库打包分页查询条件
 **/
public class PackPageCondition extends WmsBasePageCondition {

    /**
     * 出库单号
     */
    private String soNo;
    /**
     * 打包状态
     */
    private String packStatus;
    /**
     * 客户主键
     */
    private Long customerId;

    /**
     * 是否打包
     */
    private Boolean isPack;

    /**
     * 分页索引
     */
    private Integer startPage;

    /**
     * 承运商id
     */
    private Long carrierId;

    /**
     * 承运商名称
     */
    private String carrierName;
    /**
     * 上架任务号
     */
    private String paNo;
    /**
     * 打包人id
     */
    private Long packagerId;
    /**
     * 打包任务号
     */
    private String packNo;
    /**
     * 打包人名称
     */
    private String packagerName;

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getPackNo() {
        return packNo;
    }

    public void setPackNo(String packNo) {
        this.packNo = packNo;
    }

    public String getCarrierName() {
        return carrierName;
    }

    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName;
    }

    public String getSoNo() {
        return soNo;
    }

    public String getPackStatus() {
        return packStatus;
    }

    public void setPackStatus(String packStatus) {
        this.packStatus = packStatus;
    }

    public void setSoNo(String soNo) {
        this.soNo = soNo;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Boolean getIsPack() {
        return isPack;
    }

    public void setIsPack(Boolean pack) {
        isPack = pack;
    }

    public Boolean getPack() {
        return isPack;
    }

    public void setPack(Boolean pack) {
        isPack = pack;
    }

    public Integer getStartPage() {
        return startPage;
    }

    public void setStartPage(Integer startPage) {
        this.startPage = startPage;
    }

    public String getPaNo() {
        return paNo;
    }

    public void setPaNo(String paNo) {
        this.paNo = paNo;
    }

    public Long getPackagerId() {
        return packagerId;
    }

    public void setPackagerId(Long packagerId) {
        this.packagerId = packagerId;
    }

    public String getPackagerName() {
        return packagerName;
    }

    public void setPackagerName(String packagerName) {
        this.packagerName = packagerName;
    }
}
