package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.sdk.pojo.PageCondition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
public class PickingAssignLocPageCondition extends PageCondition {

    /**
     * 出库订单明细id
     */
    private String soDetailNo;

    /**
     * 分配类型 0=出库分配，1=波次分配，2=加工单，3=拆分分配
     */
    private String allocationType;

    /**
     * 类型(1:库位,2:容器号，3:批次号,4:库区)
     */
    private String type;

    /**
     * 关键词
     */
    private String keyWord;

    /**
     * 隐藏库位
     */
    private List<String> hideLocList;

    public List<String> getHideLocList() {
        return hideLocList;
    }

    public void setHideLocList(List<String> hideLocList) {
        this.hideLocList = hideLocList;
    }

    public String getSoDetailNo() {
        return soDetailNo;
    }

    public void setSoDetailNo(String soDetailNo) {
        this.soDetailNo = soDetailNo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getAllocationType() {
        return allocationType;
    }

    public void setAllocationType(String allocationType) {
        this.allocationType = allocationType;
    }


}
