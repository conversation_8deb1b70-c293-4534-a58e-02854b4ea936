package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.wms.common.domain.WmsBasePageCondition;
import lombok.Data;

import java.util.Date;

/**
 * 出库管理页面查询条件=
 * &#064;Date:  2025-1-8
 * 出库管理：条件查询
 * <AUTHOR>
 */
@Data
public class SoHeardPageQuery extends WmsBasePageCondition {

    /**
     * 出库单号
     */
    private String soNo;


    /**
     * 订单类型:数据字典（so_type）
     */
    private String soType;

    /**
     * 商品id
     */
    private String skuId;
    /**
     * 商品代码
     */
    private String skuCode;
    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 货主主键
     */
    private String ownerId;
    /**
     *  货主代码
     */
    private String ownerCode;
    /**
     *  货主名称
     */
    private String ownerName;
    /**
     * 仓库主键
     */
    private String warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 上游单号
     */
    private String logisticNo;
    /**
     * 订单来源：数据字典（order_source_type）
     */
    private String orderSource;
    /**
     * 创建时间从
     */
    private Date createStartTime;

    /**
     * 创建时间至
     */
    private Date createEndTime;
    /**
     * 分配状态
     */
    private String allocStatus;

    /**
     * 客户id
     */
    private String clientId;
    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 承运商id
     */
    private Long carrierId;

    /**
     * 承运商名称
     */
    private String carrierName;

    /**
     * 优先级
     */
    private String priority;


    /**
     * 货主数组
     */
    private String[] ownerIdArray;
    /**
     * 仓库数组
     */
    private String[] warehouseIdArray;
    /**
     * 商品数组
     */
    private String[] skuIdArray;

    /**
     * 订单状态,数据字典：order_status
     */
    private String status;

    /**
     * 上架任务号
     */
    private String paNo;

    /**
     * 是否生成上架任务，1=是，0=否,默认0
     */
    private String isCreatePaTask;

    /**
     * 上架状态,数据字典：pa_status
     */
    private String paStatus;
}
