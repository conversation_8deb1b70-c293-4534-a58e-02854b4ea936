package com.chinaservices.wms.module.so.picking.domain;

import com.chinaservices.module.domain.ModuleBaseUpdateItem;
import lombok.Data;


/**
 * 拣货项目
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
public class SoPickingItem extends ModuleBaseUpdateItem {


	/**
	* 主键
	*/
	private Long id;

	/**
	* 拣货单号
	*/
	private String pickingNo;

	/**
	* 状态
	*/
	private String status;

	/**
	* 库存周转规则主键
	*/
	private Long rotationRuleId;

	/**
	* 库存周转规则名称
	*/
	private String rotationRuleName;

	/**
	* 分配规则主键
	*/
	private Long allocRuleId;

	/**
	* 分配规则名称
	*/
	private String allocRuleName;

	/**
	 * 仓库id
	 */
	private Long warehouseId;

	/**
	* 出库单Id
	*/
	private Long soId;

	/**
	* 出库单号
	*/
	private String soNo;

	/**
	* 波次单Id
	*/
	private Long wvId;

	/**
	* 波次单号
	*/
	private String wvNo;

	/**
	* 备注
	*/
	private String remark;


	/**
	 * 加工/拆分单编号
	 */
	private String processOrderNo;

	/**
	 * 加工单类型：1-加工单；2-拆分单
	 */
	private String processOrderType;
}
