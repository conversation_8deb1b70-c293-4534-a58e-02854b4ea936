package com.chinaservices.wms.module.so.pack.domain;

import com.chinaservices.sdk.pojo.Query;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 出库打包分页结果
 **/
public class PackBaseQuery implements Query {

    /**
     * 出库打包主键
     */
    private Long id;

    /**
     * 出库单号
     */
    private String soNo;
    /**
     * 出库单号集合
     */
    private List<PackSoNoListQuery> soNos;
    /**
     * 出库单号集合
     */
    private String soNoList;

    /**
     * 上架任务号
     */
    private String paNo;
    /**
     * 上架任务号数量
     */
    private Long paNoNum;
    /**
     * 上架任务号集合
     */
    private List<String> paNoList;

    /**
     * 复核完成时间
     */
    private Date checkTime;

    /**
     * 客户主键
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 地址
     */
    private String address;


    /**
     * 打包任务号
     */
    private String packNo;

    /**
     * 出库复核单号
     */
    private String recheckNo;
    /**
     * 出库打包详情
     */
    private List<PackDetailQuery> packDetailQueries;


    /**
     * 出库单主键
     */
    private Long soId;

    /**
     * 发运状态
     */
    private String shippingStatus;
    /**
     * 打包状态
     */
    private String packStatus;


    /**
     * 承运商名称
     */
    private String carrierName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 出库数量
     */
    private Integer soNum;
    /**
     * 打包人id
     */
    private Long packagerId;
    /**
     * 打包人名称
     */
    private String packagerName;

    public String getCarrierName() {
        return carrierName;
    }

    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName;
    }

    public Long getId() {
        return id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getSoNum() {
        return soNum;
    }

    public List<PackSoNoListQuery> getSoNos() {
        return soNos;
    }

    public void setSoNos(List<PackSoNoListQuery> soNos) {
        this.soNos = soNos;
    }

    public String getSoNoList() {
        return soNoList;
    }

    public void setSoNoList(String soNoList) {
        this.soNoList = soNoList;
    }

    public void setSoNum(Integer soNum) {
        this.soNum = soNum;
    }



    public void setId(Long id) {
        this.id = id;
    }

    public String getSoNo() {
        return soNo;
    }

    public void setSoNo(String soNo) {
        this.soNo = soNo;
    }

    public Date getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    public String getPackStatus() {
        return packStatus;
    }

    public void setPackStatus(String packStatus) {
        this.packStatus = packStatus;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPackNo() {
        return packNo;
    }

    public void setPackNo(String packNo) {
        this.packNo = packNo;
    }

    public List<PackDetailQuery> getPackDetailQueries() {
        return packDetailQueries;
    }

    public void setPackDetailQueries(List<PackDetailQuery> packDetailQueries) {
        this.packDetailQueries = packDetailQueries;
    }

    public String getRecheckNo() {
        return recheckNo;
    }

    public void setRecheckNo(String recheckNo) {
        this.recheckNo = recheckNo;
    }

    public Long getSoId() {
        return soId;
    }

    public void setSoId(Long soId) {
        this.soId = soId;
    }

    public String getShippingStatus() {
        return shippingStatus;
    }

    public void setShippingStatus(String shippingStatus) {
        this.shippingStatus = shippingStatus;
    }

    public String getPaNo() {
        return paNo;
    }

    public void setPaNo(String paNo) {
        this.paNo = paNo;
    }

    public Long getPackagerId() {
        return packagerId;
    }

    public void setPackagerId(Long packagerId) {
        this.packagerId = packagerId;
    }

    public String getPackagerName() {
        return packagerName;
    }

    public void setPackagerName(String packagerName) {
        this.packagerName = packagerName;
    }

    public Long getPaNoNum() {
        return paNoNum;
    }

    public void setPaNoNum(Long paNoNum) {
        this.paNoNum = paNoNum;
    }

    public List<String> getPaNoList() {
        return paNoList;
    }

    public void setPaNoList(List<String> paNoList) {
        this.paNoList = paNoList;
    }

    /**
     * 将 soNoList (JSON) 转换成 soNos (List<PackSoNoListQuery>)
     */
    public void convertJsonToSoNos() {
        if (soNoList != null && !soNoList.isEmpty()) {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                // 1. 解析 JSON 字符串为 List<PackSoNoListQuery>
                List<PackSoNoListQuery> parsedList = objectMapper.readValue(
                        soNoList,
                        new TypeReference<List<PackSoNoListQuery>>() {}
                );

                // 2. 去重逻辑：按照 soId 去重（保留第一个出现的记录）
                this.soNos = parsedList.stream()
                        .filter(item -> item.getSoId() != null) // 可选：过滤掉 soId 为 null 的项
                        .collect(Collectors.toMap(
                                PackSoNoListQuery::getSoId, // 以 soId 作为去重 key
                                item -> item,               // 值保持不变
                                (existing, replacement) -> existing // 如果 soId 重复，保留已存在的记录
                        ))
                        .values() // 获取去重后的值
                        .stream()
                        .collect(Collectors.toList()); // 转换回 List

            } catch (IOException e) {
                throw new RuntimeException("解析 so_no_list JSON 失败", e);
            }
        }
    }
}
