package com.chinaservices.wms.module.archive.archivePassive.domain;

import com.chinaservices.sdk.pojo.Query;
import lombok.Data;

@Data
public class ArchivePassiveTagQuery implements Query {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 标签号
     */
    private String tagNo;

    /**
     * 标签种类（0-普通标签，1-托盘标签）
     */
    private Integer tagType;

    private String tagTypeName;

    /**
     * 状态（1-未领用，2-已领用，3-已绑定，4-已失效）
     */
    private Integer status;

    /**
     * 绑定对象（1-货物，2-容器）
     */
    private Integer bindSubject;

    /**
     * 编号类型（0-托盘号，1-批次号，2-sscc，3-商品序列号，4-箱号）
     */
    private String subjectType;

    /**
     * 对象Id
     */
    private Long subjectId;

    /**
     * 档案名称
     */
    private String archiveName;

    /**
     * 档案编号
     */
    private String archiveNo;

    /**
     * 档案柜名称
     */
    private String archiveCabinetName;

    /**
     * 档案柜id
     */
    private Long archiveCabinetId;


    /**
     * 编辑按钮
     */
    private Boolean editButton = Boolean.FALSE;
    /**
     * 删除按钮
     */
    private Boolean deleteButton = Boolean.FALSE;
    /**
     * 归还
     */
    private Boolean returnButton = Boolean.FALSE;
    /**
     * 绑定
     */
    private Boolean bindButton = Boolean.FALSE;
    /**
     * 解绑
     */
    private Boolean unbindButton = Boolean.FALSE;
    /**
     * 操作日志按钮
     */
    private Boolean logButton = Boolean.FALSE;
    /**
     * 注册按钮
     */
    private Boolean registerButton = Boolean.FALSE;
    /**
     * 激活按钮
     */
    private Boolean activateButton = Boolean.FALSE;
}
