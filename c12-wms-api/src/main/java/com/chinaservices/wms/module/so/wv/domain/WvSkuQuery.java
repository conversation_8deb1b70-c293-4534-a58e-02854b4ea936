package com.chinaservices.wms.module.so.wv.domain;


import com.chinaservices.sdk.pojo.Query;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 波次明细里的商品信息
 *
 * <AUTHOR>
 */
@Data
public class WvSkuQuery implements Query {

    /**
     *商品名称
     */
    private String skuName;
    /**
     * 商品编码
     */
    private String skuCode;
    /**
     * 预计发货数EA
     */
    private BigDecimal qtySoEa;
    /**
     * 已发货数ea
     */
    private BigDecimal qtyShippedEa;
    /**
     * 未发货数ea
     */
    private BigDecimal qtyUnShippedEa;

    /**
     * 出库单单号
     */
    private String soNo;

    /**
     * 出库单详情单号
     */
    private String soDetailNo;

    /**
     * 上架任务列表
     */
    private List<String> paTaskNoList;

    /**
     * 上架任务状态
     */
    private String paStatus;

    /**
     * 已分配数EA
     */
    private BigDecimal qtyAllocationEa;

    /**
     * 已拣货数EA
     */
    private BigDecimal qtyPickedEa;
}
