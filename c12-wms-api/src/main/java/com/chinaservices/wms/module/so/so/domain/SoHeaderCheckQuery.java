package com.chinaservices.wms.module.so.so.domain;


import com.chinaservices.sdk.pojo.Query;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * @Author:Frank
 * 构建查询条件
 */
@Data
public class SoHeaderCheckQuery implements Query {
    /**
     * 复核数Ea
     */
    private BigDecimal qtyCheckEa;
    /**
     * 差异数
     */
    private BigDecimal unQtyCheckEa;
    /**
     * 品类数量
     */
    private Long categoryNum;
    /**
     * 复核状态
     */
    private String checkStatus;
    /**
     * 复核状态
     */
    private String checkStatusStr;
    /**
     * 状态
     */
    private String status;
    /**
     * 复核人
     */
    private String checkOp;
    /**
     * 复核时间
     */
    private Date checkTime;
    /**
     * 拣货完成时间
     */
    private Date pickedTime;
    /**
     * 拣货数Ea
     */
    private BigDecimal qtyEa;
    /**
     * 拣货时间
     */
    private Date pickingTime;
    /**
     * 出库单号
     */
    private String soNo;
    /**
     * 出库单ID
     */
    private Long soId;
    /**
     * 复核单号
     */
    private String recheckNo;
    /**
     * 拣货单号
     */
    private String pickingNo;
    /**
     * 上架任务号
     */
    private String paNo;
    /**
     * 上架任务号数量
     */
    private Long paNoNum;
    /**
     * 上架任务号集合
     */
    private List<String> paNoList;
    /**
     * 是否复核
     */
    private Boolean isRecheck;

    /**
     * 打包任务号
     */
    private String packNo;
    /**
     * 承运商id
     */
    private Long carrierId;
    /**
     * 承运商名称
     */
    private String carrierName;
    /**
     * 是否生成打包任务
     */
    private String isGeneratePackTask;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 地址
     */
    private String address;

    /**
     * ID
     */
    private Long id;


}
