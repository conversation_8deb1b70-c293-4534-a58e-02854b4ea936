package com.chinaservices.wms.module.basic.cspackage.domain;

import com.chinaservices.sdk.pojo.PageCondition;

/**
 * <AUTHOR>
 * @Description: 包装管理 条件查询
 */
public class PackagePageCondition extends PageCondition {
    /**
     * id
     */
    private String id;
    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * id列表
     */
    private String[] idList;

    /**
     * 包装编码
     */
    private String code;

    /**
     * 包装名称
     */
    private String name;

    /**
     * 包装类型(package:包装，bulk:散装)
     */
    private String type;

    /**
     * 信息来源 manually_create 手动创建/ wms_push WMS推送
     */
    private String informationSources;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String[] getIdList() {
        return idList;
    }

    public void setIdList(String[] idList) {
        this.idList = idList;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getInformationSources() {
        return informationSources;
    }

    public void setInformationSources(String informationSources) {
        this.informationSources = informationSources;
    }
}
