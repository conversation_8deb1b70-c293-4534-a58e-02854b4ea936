package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.sdk.pojo.Condition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取分配页面左侧商品列表查询条件
 * <AUTHOR>
 * @date 2025/1/15 15:26
 * @Description 获取分配页面左侧商品列表查询条件
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetLeftGoodsListCondition implements Condition {

    /**
     * 分配类型 0=出库分配，1=波次分配，2=加工单，3=拆分分配
     */
    private String allocationType;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 出库单号，出库单分配-请求参数
     */
    private String soNo;

    /**
     * 波次单号，波次单分配-请求参数
     */
    private String wvNo;

    /**
     * 加工单单号，加工单分配-请求参数
     */
    private String processOrderNo;
}
