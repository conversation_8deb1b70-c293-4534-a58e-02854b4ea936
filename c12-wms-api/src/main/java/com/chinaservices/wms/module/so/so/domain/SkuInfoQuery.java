package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.sdk.pojo.Query;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SkuInfoQuery implements Query {

    private Long skuId;

    private String skuName;

    private String skuCode;

    /**
     * 订货数EA
     */
    private BigDecimal qtySoEa;

    /**
     * 当前可分配库存总数
     */
    private BigDecimal assignableTotal;

    /**
     * 加工拆分任务明细表ID
     */
    private Long processTaskDetailsId;

    /**
     * 加工拆分单号
     */
    private String processOrderNo;

    /**
     * 加工拆分任务单号
     */
    private String processTaskNo;

    /**
     * 分配类型 0=出库分配，1=波次分配，2=加工单，3=拆分分配
     */
    private String allocationType;

    /**
     * 出库订单明细号
     */
    private String soDetailNo;

    /**
     * 分配规则
     */
    private RuleInfo allocRole;

    /**
     * 周转规则
     */
    private RuleInfo rotationRole;



    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RuleInfo {

        /**
         * 规则ID
         */
        private Long id;

        /**
         * 规则代码
         */
        private String roleCode;

        /**
         * 规则名称
         */
        private String ruleName;
    }
}
