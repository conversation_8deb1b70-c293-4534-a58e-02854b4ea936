package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.module.domain.ModuleBaseUpdateItem;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CancalSaveItem extends ModuleBaseUpdateItem  {

    /**
     * 分配类型 0=出库分配，1=波次分配，2=加工单，3=拆分分配
     */
    private String allocationType;

    /**
     * 分配明细ID集合
     */
    @NotEmpty(message = "分配明细ID集合不能为空")
    private List<Long> idList;


    /**
     * 是否取消拣货
     */
    private boolean isCancelPicking = false;
}
