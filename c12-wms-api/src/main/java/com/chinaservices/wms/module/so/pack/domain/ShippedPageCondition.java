package com.chinaservices.wms.module.so.pack.domain;

import cn.hutool.core.util.StrUtil;
import com.chinaservices.wms.common.domain.WmsBasePageCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

import java.util.List;

/**
 * 发运管理分页条件
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ShippedPageCondition extends WmsBasePageCondition {
    /**
     * 打包任务号
     */
    private String packNo;
    /**
     * 客户主键
     */
    private Long customerId;
    /**
     * 承运商主键
     */
    private Long carrierId;
    /**
     * 运输单号
     */
    private String shippingOrderNo;
    /**
     * 发运单号
     */
    private String dispatchNo;
    /**
     * 标签号
     */
    private String tagNo;
    /**
     * 识别状态
     */
    private String recognitionStatus;
    /**
     * 识别异常原因
     */
    private String recognition;
    /**
     * 推送状态 wms_push_status (notPushed-未推送，alreadyPushed-已推送)
     */
    private String pushStatus;
    /**
     * 发运状态 pack_shipping_status (0-待派车，1-待发运，2-运输中，3-运输完成，4-已取消)
     */
    private String shippingStatus;
    /**
     * 上架任务号
     */
    private String paNo;
    /**
     * 运输单号列表（出库订单跳转使用）
     */
    private List<String> shippingOrderNoList;

    public void setShippingOrderNoList(String shippingOrderNoList) {
        if (StrUtil.isNotBlank(shippingOrderNoList)) {
            this.shippingOrderNoList = StrUtil.split(shippingOrderNoList, ",");
        }else{
            this.shippingOrderNoList = null;
        }
    }
}
