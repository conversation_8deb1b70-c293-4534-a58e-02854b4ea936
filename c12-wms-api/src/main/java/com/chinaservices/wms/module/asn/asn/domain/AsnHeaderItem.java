package com.chinaservices.wms.module.asn.asn.domain;

import com.chinaservices.module.domain.ModuleBaseUpdateItem;
import com.chinaservices.wms.module.asn.asn.domain.AsnDetailItem;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.Date;
import java.util.List;

/**
 * 新增入库订单
 * <AUTHOR>
 * @Description: 预到货通知单 Item
 */
public class AsnHeaderItem extends ModuleBaseUpdateItem {

    /**
     * 主键
     */
    private Long id;

    /**
     * 入库单号:自动生成
     */
    private String asnNo;

    /**
     * 订单状态:新增时默认创建 （asn_status）
     */
    private String status;

    /**
     * 审核状态：新增时默认未审核 （wms_audit_status）
     */
    private String auditStatus;

    /**
     * 货主主键
     */
    @NotNull(message = "货主不能为空")
    private Long ownerId;
    /**
     * 货主代码
     */
    private String ownerCode;

    /**
     * 入库单类型:asn_type
     */
    @NotNull(message = "入库单类型不能为空")
    private String asnType;

    /**
     * 出库单号
     */
    private String soNo;

    /**
     * 仓库主键
     */
    @NotNull(message = "仓库不能为空")
    private Long warehouseId;
    /**
     * 仓库代码
     */
    private String warehouseCode;

    /**
     * 订单时间
     */
    @NotNull(message = "订单时间不能为空")
    private Date orderTime;

    /**
     * 预计到货时间从
     */
    private Date startTime;

    /**
     * 预计到货时间到
     */
    private Date endTime;

    /**
     * 优先级别
     */
    private String priority;

    /**
     * 上游单号
     */
    private String logisticNo;

    /**
     * 是否关联订单：1-是；0-否
     */
    private String relationOrder;

    /**
     * 供应商主键
     */
    private Long supplierId;


    /**
     * 商品List
     */
    @Valid
    private List<AsnDetailItem> asnDetailItemList;

    /**
     * 收货状态 收货状态（not_received_goods=未收货、part_received_goods=部分收货、all_received_goods=完全收货）
     */
    private String receiveStatus;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 新增类型(add-新增/编辑，copy-复制)
     */
    @NotBlank(message = "新增类型不能为空")
    private String addType;

    /**
     * 采购单号
     */
    private String pOrderNo;

    /**
     * 合并收货单ID
     */
    private Long mergeId;

    /**
     * 合并收货单单号
     */
    private String mergeNo;

    /**
     * 合并收货单单号
     */
    private String isMerge;

    public Long getMergeId() {
        return mergeId;
    }

    public void setMergeId(Long mergeId) {
        this.mergeId = mergeId;
    }

    public String getMergeNo() {
        return mergeNo;
    }

    public void setMergeNo(String mergeNo) {
        this.mergeNo = mergeNo;
    }

    public String getIsMerge() {
        return isMerge;
    }

    public void setIsMerge(String isMerge) {
        this.isMerge = isMerge;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAsnNo() {
        return asnNo;
    }

    public void setAsnNo(String asnNo) {
        this.asnNo = asnNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getAsnType() {
        return asnType;
    }

    public void setAsnType(String asnType) {
        this.asnType = asnType;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getLogisticNo() {
        return logisticNo;
    }

    public void setLogisticNo(String logisticNo) {
        this.logisticNo = logisticNo;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public List<AsnDetailItem> getAsnDetailItemList() {
        return asnDetailItemList;
    }

    public void setAsnDetailItemList(List<AsnDetailItem> asnDetailItemList) {
        this.asnDetailItemList = asnDetailItemList;
    }

    public String getReceiveStatus() {
        return receiveStatus;
    }

    public void setReceiveStatus(String receiveStatus) {
        this.receiveStatus = receiveStatus;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public String getSoNo() {
        return soNo;
    }

    public void setSoNo(String soNo) {
        this.soNo = soNo;
    }

    public String getAddType() {
        return addType;
    }

    public void setAddType(String addType) {
        this.addType = addType;
    }

    public String getRelationOrder() {
        return relationOrder;
    }

    public void setRelationOrder(String relationOrder) {
        this.relationOrder = relationOrder;
    }

    public String getpOrderNo() {
        return pOrderNo;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public void setpOrderNo(String pOrderNo) {
        this.pOrderNo = pOrderNo;
    }


}
