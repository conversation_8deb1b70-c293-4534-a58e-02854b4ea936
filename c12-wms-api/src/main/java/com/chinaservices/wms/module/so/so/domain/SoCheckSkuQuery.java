package com.chinaservices.wms.module.so.so.domain;


import com.chinaservices.sdk.pojo.Query;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 复核商品查询
 *
 * <AUTHOR>
 * @date 2025/01/22
 */
@Data
public class SoCheckSkuQuery implements Query
{
    /**
     * 复核详情Id
     */
    private Long recheckDetailId;
    /**
     * 出库单
     */
    private String soNo;
    /**
     * 上架任务号
     */
    private String paNo;
    /**
     * 复核单号
     */
    private String recheckNo;
    /**
     * 出库商品详情编号
     */
    private String soDetailNo;
    /**
     * 应复核数
     */
    private BigDecimal qtyEa;
    /**
     * 已复核数
     */
    private BigDecimal qtyCheckEa;
    /**
     * 未复核数|差异数
     */
    private BigDecimal unQtyCheckEa;
    /**
     * 商品主键
     */
    private Long skuId;
    /**
     * 商品代码
     */
    private String skuCode;
    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 复核
     */
    private boolean recheck = false;


}
