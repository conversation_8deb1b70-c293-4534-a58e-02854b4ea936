package com.chinaservices.wms.module.so.pack.domain;

import com.chinaservices.sdk.pojo.Query;
import com.chinaservices.wms.module.so.picking.domain.TagNoQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 发运分页查询结果
 **/
@Data
public class ShippedQuery implements Query {
    /**
     * 主键
     */
    private Long id;
    /**
     * 打包任务号
     */
    private String packNo;
    /**
     * 运输单号
     */
    private String shippingOrderNo;
    /**
     * 上架任务号
     */
    private String paNo;
    /**
     * 上架任务号数量
     */
    private Long paNoNum;
    /**
     * 上架任务号集合
     */
    private List<String> paNoList;
    /**
     * 客户主键
     */
    private Long customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 收货地址
     */
    private String address;
    /**
     * 仓库主键
     */
    private String warehouseId;
    /**
     * 仓库地址
     */
    private String warehouseAddress;
    /**
     * 发运状态 pack_shipping_status (0-待派车，1-待发运，2-运输中，3-运输完成，4-已取消)
     */
    private String shippingStatus;
    /**
     * 发运状态
     */
    private String shippingStatusStr;
    /**
     * 快递面单状态
     */
    private String packOrderStatus;
    /**
     * 打印图片
     */
    private String packPrintUrl;
    /**
     * 快递公司订单号
     */
    private String kdComCrderNum;
    /**
     * 打印次数
     */
    private Integer packPrintCount;
    /**
     * 推送状态 wms_push_status (notPushed-未推送，alreadyPushed-已推送)
     */
    private String pushStatus;
    /**
     * 承运商主键
     */
    private Long carrierId;
    /**
     * 承运商名称
     */
    private String carrierName;
    /**
     * 司机
     */
    private String driver;
    /**
     * 车牌号
     */
    private String licensePlate;
    /**
     * 发车时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date shippingTime;
    /**
     * 卸货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date unloadingTime;
    /**
     * 发运单号
     */
    private String dispatchNo;
    /**
     * 标签号
     */
    private String tagNo;
    /**
     * 标签号数量
     */
    private Long tagNoQty;
    /**
     * 标签号集合
     */
    private List<TagNoQuery> tagNoList;
    /**
     * 标签号识别数量
     */
    private Long tagNoRecognitionQty;
    /**
     * 标签号识别集合
     */
    private List<TagNoRecognitionQuery> tagNoRecognitionList;
    /**
     * 标签号识别异常集合
     */
    private List<TagNoRecognitionQuery> tagNoRecognitionExceptionList;
    /**
     * 识别状态
     */
    private String recognitionStatus;

    /**
     * 是否展示分配承运商按钮
     */
    private Boolean allocCarrierBtn = false;
    /**
     * 是否展示编辑承运商按钮
     */
    private Boolean editCarrierBtn = false;
    /**
     * 是否展示发运确认按钮
     */
    private Boolean dispatchBtn = false;
    /**
     * 是否展示推送按钮
     */
    private Boolean pushBtn = false;
    /**
     * 是否展示取消按钮
     */
    private Boolean cancelBtn = false;
    /**
     * 是否展示运输完成按钮
     */
    private Boolean completeBtn = false;
}
