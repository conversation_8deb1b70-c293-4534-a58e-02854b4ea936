package com.chinaservices.wms.module.so.so.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @program: c12-wms
 * @ClassName SoDetailInfo
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2025-01-13 14:55
 **/
@Data
public class SoDetailInfo {
    /**
     * 商品id
     */
    private Long id;
    /**
     * 商品主键
     */
    private Long skuId;
    /**
     * 商品代码
     */
    private String skuCode;

    /**
     * 批次属性主键
     */
    private Long lotId;
    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 出库单号
     */
    private String soNo;

    /**
     * 出库明细单单号
     */
    private String soDetailNo;

    /**
     * 包装主键
     */
    private Long packageId;
    /**
     * 包装名称
     */
    private String packageName;

    /**
     * 包装单位主键
     */
    private Long packageUnitId;
    /**
     * 包装单位名称
     */
    private String packageUnitName;
    /**
     * 状态
     */
    private String status;

    /**
     * 总净重
     */
    private BigDecimal sumWeight;


    /**
     * 总毛重
     */
    private BigDecimal sumGrossWeight;

    /**
     * 订单数
     */
    private BigDecimal qtySo;

    /**
     * 订单数EA 预计发货数EA
     */
    private BigDecimal qtySoEa;
    /**
     * 分配数EA
     */
    private BigDecimal qtyAllocationEa;
    /**
     * 拣货数EA 已拣货数EA
     */
    private BigDecimal qtyPickedEa;
    /**
     * 发货EA 已发货数EA
     */
    private BigDecimal qtyShippedEa;
    /**
     * 批次属性1
     */
    private String lotAtt01;
    /**
     * 批次属性2
     */
    private String lotAtt02;
    /**
     * 批次属性3
     */
    private String lotAtt03;
    /**
     * 批次属性4
     */
    private String lotAtt04;
    /**
     * 批次属性5
     */
    private String lotAtt05;
    /**
     * 批次属性6
     */
    private String lotAtt06;
    /**
     * 批次属性7
     */
    private String lotAtt07;
    /**
     * 批次属性8
     */
    private String lotAtt08;
    /**
     * 批次属性9
     */
    private String lotAtt09;
    /**
     * 批次属性10
     */
    private String lotAtt10;
    /**
     * 批次属性11
     */
    private String lotAtt11;
    /**
     * 批次属性12
     */
    private String lotAtt12;

    /**
     * 批次属性集合
     */
    private String attribute;


    /**
     * 供应商名称(用于批次属性4)
     */
    private String supplierName;

    /**
     * 供应商主键(用于批次属性4)
     */
    private Long supplierId;

    /**
     * ea单位
     */
    private String packageUnitEaName;

    /**
     * 复核数ea
     */
    private BigDecimal qtyCheckEa;

    /**
     * 批次号
     */
    private String lotNum;

    /**
     * 出库复核单号
     */
    private String recheckNo;

    /**
     * 运输单
     */
    private List<String> waybillNoList;

    /**
     * 出库上架数EA
     */
    private BigDecimal qtyPaEa;

    /**
     * 销售单明细id
     */
    private Long orderSkuId;
}
