package com.chinaservices.wms.module.stock.multi.domain;



import com.chinaservices.sdk.pojo.Query;

import java.math.BigDecimal;

/**
 * 多级库存查询
 */
public class InvLotLocQuery implements Query{
    /**
     * 主键
     */
    private Long id;
    /**
     * 商品主键
     */
    private Long skuId;


    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 货主主键
     */
    private Long ownerId;

    /**
     * 货主编码
     */
    private String ownerCode;


    /**
     * 货主
     */
    private String ownerName;
    /**
     * 仓库主键
     */
    private Long warehouseId;
    /**
     * 所属仓库
     */
    private String warehouseName;


    /**
     * 库存数
     */
    private BigDecimal qty;
    

    /**
     * 可用数
     */
    private BigDecimal availableQty;
    /**
     * 冻结数
     */
    private BigDecimal qtyHold;
    /**
     * 分配数
     */
    private BigDecimal qtyAlloc;
    /**
     * 拣货数
     */
    private BigDecimal qtyPk;
    /**
     * 待质检数
     */
    private BigDecimal qtyQc;
    /**
     * 上架待入数
     */
    private BigDecimal qtyPaIn;
    /**
     * 上架待出数
     */
    private BigDecimal qtyPaOut;
    /**
     * 调整待增数
     */
    private BigDecimal qtyAdIn;
    /**
     * 调整待减数
     */
    private BigDecimal qtyAdOut;
    /**
     * 移动待入数
     */
    private BigDecimal qtyMvIn;
    /**
     * 移动待出数
     */
    private BigDecimal qtyMvOut;
    /**
     * 转移待入数
     */
    private BigDecimal qtyTfIn;
    /**
     * 转移待出数
     */
    private BigDecimal qtyTfOut;
    /**
     * 补货待入数
     */
    private BigDecimal qtyRpIn;
    /**
     * 补货待出数
     */
    private BigDecimal qtyRpOut;

    /**
     * 待移动数
     */
    private BigDecimal qtyWaitMove;

    /**
     * 待报损数
     */
    private BigDecimal qtyWaitDamage;
    /**
     * 库位主键
     */
    private Long locId;
    /**
     * 库位编码
     */
    private String locCode;

    /**
     * 库位名称
     */
    private String locName;

    /**
     * 批次属性id
     */
    private Long lotId;

    /**
     * 容器号
     */
    private String palletNum;
    /**
     * 批次号
     */
    private String lotNum;

    /**
     * 批次属性1
     */
    private String lotAtt01;
    /**
     * 批次属性2
     */
    private String lotAtt02;
    /**
     * 批次属性3
     */
    private String lotAtt03;
    /**
     * 批次属性4
     */
    private String lotAtt04;
    /**
     * 批次属性5
     */
    private String lotAtt05;
    /**
     * 批次属性6
     */
    private String lotAtt06;
    /**
     * 批次属性7
     */
    private String lotAtt07;
    /**
     * 批次属性8
     */
    private String lotAtt08;
    /**
     * 批次属性9
     */
    private String lotAtt09;
    /**
     * 批次属性10
     */
    private String lotAtt10;
    /**
     * 批次属性11
     */
    private String lotAtt11;
    /**
     * 批次属性12
     */
    private String lotAtt12;
    /**
     * 创建人
     */
    private Long creator;

    private String lotStr;



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getAvailableQty() {
        return availableQty;
    }

    public void setAvailableQty(BigDecimal availableQty) {
        this.availableQty = availableQty;
    }

    public BigDecimal getQtyHold() {
        return qtyHold;
    }

    public void setQtyHold(BigDecimal qtyHold) {
        this.qtyHold = qtyHold;
    }

    public BigDecimal getQtyAlloc() {
        return qtyAlloc;
    }

    public void setQtyAlloc(BigDecimal qtyAlloc) {
        this.qtyAlloc = qtyAlloc;
    }

    public BigDecimal getQtyPk() {
        return qtyPk;
    }

    public void setQtyPk(BigDecimal qtyPk) {
        this.qtyPk = qtyPk;
    }

    public BigDecimal getQtyQc() {
        return qtyQc;
    }

    public void setQtyQc(BigDecimal qtyQc) {
        this.qtyQc = qtyQc;
    }

    public BigDecimal getQtyPaIn() {
        return qtyPaIn;
    }

    public void setQtyPaIn(BigDecimal qtyPaIn) {
        this.qtyPaIn = qtyPaIn;
    }

    public BigDecimal getQtyPaOut() {
        return qtyPaOut;
    }

    public void setQtyPaOut(BigDecimal qtyPaOut) {
        this.qtyPaOut = qtyPaOut;
    }

    public BigDecimal getQtyAdIn() {
        return qtyAdIn;
    }

    public void setQtyAdIn(BigDecimal qtyAdIn) {
        this.qtyAdIn = qtyAdIn;
    }

    public BigDecimal getQtyAdOut() {
        return qtyAdOut;
    }

    public void setQtyAdOut(BigDecimal qtyAdOut) {
        this.qtyAdOut = qtyAdOut;
    }

    public BigDecimal getQtyMvIn() {
        return qtyMvIn;
    }

    public void setQtyMvIn(BigDecimal qtyMvIn) {
        this.qtyMvIn = qtyMvIn;
    }

    public BigDecimal getQtyMvOut() {
        return qtyMvOut;
    }

    public void setQtyMvOut(BigDecimal qtyMvOut) {
        this.qtyMvOut = qtyMvOut;
    }

    public BigDecimal getQtyTfIn() {
        return qtyTfIn;
    }

    public void setQtyTfIn(BigDecimal qtyTfIn) {
        this.qtyTfIn = qtyTfIn;
    }

    public BigDecimal getQtyTfOut() {
        return qtyTfOut;
    }

    public void setQtyTfOut(BigDecimal qtyTfOut) {
        this.qtyTfOut = qtyTfOut;
    }

    public BigDecimal getQtyRpIn() {
        return qtyRpIn;
    }

    public void setQtyRpIn(BigDecimal qtyRpIn) {
        this.qtyRpIn = qtyRpIn;
    }

    public BigDecimal getQtyRpOut() {
        return qtyRpOut;
    }

    public void setQtyRpOut(BigDecimal qtyRpOut) {
        this.qtyRpOut = qtyRpOut;
    }

    public Long getLocId() {
        return locId;
    }

    public void setLocId(Long locId) {
        this.locId = locId;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }

    public String getPalletNum() {
        return palletNum;
    }

    public void setPalletNum(String palletNum) {
        this.palletNum = palletNum;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getLotAtt06() {
        return lotAtt06;
    }

    public void setLotAtt06(String lotAtt06) {
        this.lotAtt06 = lotAtt06;
    }

    public String getLotAtt07() {
        return lotAtt07;
    }

    public void setLotAtt07(String lotAtt07) {
        this.lotAtt07 = lotAtt07;
    }

    public String getLotAtt08() {
        return lotAtt08;
    }

    public void setLotAtt08(String lotAtt08) {
        this.lotAtt08 = lotAtt08;
    }

    public String getLotAtt09() {
        return lotAtt09;
    }

    public void setLotAtt09(String lotAtt09) {
        this.lotAtt09 = lotAtt09;
    }

    public String getLotAtt10() {
        return lotAtt10;
    }

    public void setLotAtt10(String lotAtt10) {
        this.lotAtt10 = lotAtt10;
    }

    public String getLotAtt11() {
        return lotAtt11;
    }

    public void setLotAtt11(String lotAtt11) {
        this.lotAtt11 = lotAtt11;
    }

    public String getLotAtt12() {
        return lotAtt12;
    }

    public void setLotAtt12(String lotAtt12) {
        this.lotAtt12 = lotAtt12;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getLotId() {
        return lotId;
    }

    public void setLotId(Long lotId) {
        this.lotId = lotId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getLocName() {
        return locName;
    }

    public void setLocName(String locName) {
        this.locName = locName;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getLotStr() {
        return lotStr;
    }

    public void setLotStr(String lotStr) {
        this.lotStr = lotStr;
    }

    public BigDecimal getQtyWaitMove() {
        return qtyWaitMove;
    }

    public void setQtyWaitMove(BigDecimal qtyWaitMove) {
        this.qtyWaitMove = qtyWaitMove;
    }

    public BigDecimal getQtyWaitDamage() {
        return qtyWaitDamage;
    }

    public void setQtyWaitDamage(BigDecimal qtyWaitDamage) {
        this.qtyWaitDamage = qtyWaitDamage;
    }
}
