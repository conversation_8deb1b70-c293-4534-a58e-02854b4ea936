package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.sdk.pojo.Query;
import com.chinaservices.wms.module.so.picking.domain.PickingDetail;
import com.chinaservices.wms.module.so.wv.domain.WvPickingDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GetRightPickingListQuery implements Query {

    /**
     * 拣货单ID
     */
    private Long id;

    /**
     * 拣货单号
     */
    private String pickingNo;

    /**
     * 分配类型 0=出库分配，1=波次分配，2=加工单，3=拆分分配
     */
    private String allocationType;

    private boolean createPickingBtn = true;

    /**
     * 出库订单明细id
     */
    private String soDetailId;

    /**
     * 分配明细-出库
     */
    private List<PickingDetail> outboundDetailList;

    /**
     * 分配明细-波次
     */
    private List<WvPickingDetail> wvDetailList;

    /**
     * 加工/拆分明细-出库
     */
    private List<PickingDetail> processOrderList;
}
