package com.chinaservices.wms.module.stock.multi.domain;



import com.chinaservices.core.annotation.BigDecimalFormat;
import com.chinaservices.sdk.pojo.Query;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 多级库存查询
 */
@Data
public class InvLotLocBySkuGroupQuery implements Query{

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 货主代码
     */
    private String ownerCode;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 商品大类
     */
    private String skuGroupName;

    /**
     * 商品大类
     */
    private String skuGroupCode;

    /**
     * 商品代码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 总库存数
     */
    private BigDecimal qty = BigDecimal.ZERO;

    /**
     * 可用库存数
     */
    private BigDecimal qtyKy = BigDecimal.ZERO;

    /**
     * 冻结数
     */
    @BigDecimalFormat(pattern = "#,##0")
    private BigDecimal qtyHold;

    /**
     * 分配数
     */
    @BigDecimalFormat(pattern = "#,##0")
    private BigDecimal qtyAlloc;

    /**
     * 拣货数
     */
    @BigDecimalFormat(pattern = "#,##0")
    private BigDecimal qtyPk;

    /**
     * 质检数
     */
    @BigDecimalFormat(pattern = "#,##0")
    private BigDecimal qtyQc;

    /**
     * 待移动数
     */
    @BigDecimalFormat(pattern = "#,##0")
    private BigDecimal qtyWaitMove;

    /**
     * 待移动数
     */
    @BigDecimalFormat(pattern = "#,##0")
    private BigDecimal qtyWaitDamage;

}
