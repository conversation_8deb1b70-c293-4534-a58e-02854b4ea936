package com.chinaservices.wms.module.basic.sku.domain;

import com.chinaservices.wms.common.domain.WmsBasePageCondition;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 商品分页查询
 */
public class SkuPageCondition extends WmsBasePageCondition {
    /**
     * id
     */
    private String id;



    /**
     * 货主id
     */
    private String ownerId;
    /**
     * 来源类型
     */
    private String source;

    /**
     * 商品代码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;


    /**
     * 商品条码1
     */
    private String barCode;

    /**
     * 状态(1:启用,0:禁用)
     */
    private String status;

    /**
     * 商品简称
     */
    private String shortName;

    /**
     * 商品类型(1:普货,2:冷链商品,3:危化品)
     */
    private String type;

    private String asnNo;

    /**
     * 质检阶段
     */
    private String qcInspection;

    /**
     * 是否序列号控制
     */
    private String whetherSerialController;

    /**
     * 排除的商品id
     */
    private List<Long> filterSkuIdList;

    /**
     * 效期预警查询条件——仅存在必填失效时间的商品进行显示
     */
    private String title;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getWhetherSerialController() {
        return whetherSerialController;
    }

    public void setWhetherSerialController(String whetherSerialController) {
        this.whetherSerialController = whetherSerialController;
    }

    public String getQcInspection() {
        return qcInspection;
    }

    public void setQcInspection(String qcInspection) {
        this.qcInspection = qcInspection;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public String getAsnNo() {
        return asnNo;
    }

    public void setAsnNo(String asnNo) {
        this.asnNo = asnNo;
    }

    public List<Long> getFilterSkuIdList() {
        return filterSkuIdList;
    }

    public void setFilterSkuIdList(List<Long> filterSkuIdList) {
        this.filterSkuIdList = filterSkuIdList;
    }
}
