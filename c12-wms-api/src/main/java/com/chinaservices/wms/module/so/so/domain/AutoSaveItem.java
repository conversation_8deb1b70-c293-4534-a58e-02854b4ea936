package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.module.domain.ModuleBaseUpdateItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AutoSaveItem extends ModuleBaseUpdateItem {

    /**
     * 分配类型 0=出库分配，1=波次分配，2=加工单，3=拆分分配
     */
    private String allocationType;

    /**
     * 波次单号，波次分配时使用
     */
    private String wvNo;

    /**
     * 出库单号，出库分配时使用
     */
    private String soNo;

    /**
     * 出库单明细ID集合
     */
    private List<Long> soDetailIds;

    /**
     * 加工单任务编号集合
     */
    private List<String> processTaskNoList;

    /**
     * 拆分单明细ID集合
     */
    private List<Long> processTaskDetailIdList;
}
