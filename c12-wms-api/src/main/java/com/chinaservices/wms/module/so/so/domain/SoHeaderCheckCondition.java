package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.wms.common.domain.WmsBasePageCondition;
import lombok.Data;

import java.util.Date;

/**
 * 出库复核列表查询条件
 *
 * <AUTHOR>
 * @date 2025/01/23
 */
@Data
public class SoHeaderCheckCondition extends WmsBasePageCondition
{

    /**
     * 出库单号
     */
    private String soNo;

    /**
     * 复核单号
     */
    private String recheckNo;
    /**
     * 复核状态
     */
    private String checkStatus;
    /**
     * 复核员
     */
    private String checkOp;
    /**
     * 复核开始时间
     */
    private Date startTime;
    /**
     * 承运商id
     */
    private Long carrierId;
    /**
     * 复核结束时间
     */
    private Date endTime;
    /**
     * 打包任务号
     */
    private String packNo;

    /**
     * 是否生成打包任务
     */
    private String isGeneratePackTask;

    /**
     * 客户主键
     */
    private Long customerId;
    /**
     * 上架任务号
     */
    private String paNo;
}
