package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.module.domain.ModuleBaseUpdateItem;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 获取分配页面左侧商品列表查询条件
 * <AUTHOR>
 * @date 2025/1/15 15:26
 * @Description 获取分配页面左侧商品列表查询条件
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreatePickingTasksItem extends ModuleBaseUpdateItem {

    /**
     * 分配类型 0=出库分配，1=波次分配，2=加工单，3=拆分分配
     */
    private String allocationType;

    /**
     * 拣货任务
     */
    @NotEmpty(message = "拣货任务不能为空")
    private List<CreatePickingTaskItem> createPickingTaskItemList;
}
