package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.sdk.pojo.Query;
import com.chinaservices.wms.common.enums.SecondarySortingStatusEnum;
import com.chinaservices.wms.module.so.picking.domain.SoPickingSoNoQuery;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 二 次分拣查询
 *
 * <AUTHOR>
 * @date 2025/01/20
 */
@Data
public class SecondarySortingQuery implements Query
{
    /**
     * 波次Id
     */
    private String wvId;
    /**
     * 波次单号
     */
    private String wvNo;
    /**
     * 拣货单号
     */
    private String pickingNo;
    /**
     * 上架任务号
     */
    private String paNo;
    /**
     * 上架任务号数量
     */
    private Long paNoNum;
    /**
     * 上架任务号集合
     */
    private List<String> paNoList;
    /**
     * 上架任务号
     */
    private String taskStatus;
    /**
     * 出库单数量
     */
    private Long soNum;
    /**
     * 出库单号集合
     */
    private List<SoPickingSoNoQuery> soNos;
    /**
     * 二次分拣状态<br/>
     * secondary_sorting_status
     * （un_sorting：未分拣，partial_sorting：部分分拣，complete_sorting：完全分拣）
     *
     * @see SecondarySortingStatusEnum
     */
    private String secondarySortingStatus;
    /**
     * 拣货数EA
     */
    private BigDecimal pickingEa;
    /**
     * 分拣数EA
     */
    private BigDecimal sortingEa;
    /**
     * 未拣货数EA
     */
    private BigDecimal unSortingEa;
    /**
     * 分拣员
     */
    private String sortingOpName;
    /**
     * 分拣时间
     */
    private Date sortingTime;
    /**
     * 出库详情ID列表
     */
    private String soDetailIdList;

}
