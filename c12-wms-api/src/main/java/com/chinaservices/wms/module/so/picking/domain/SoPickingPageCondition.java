package com.chinaservices.wms.module.so.picking.domain;

import com.chinaservices.sdk.pojo.PageCondition;
import com.chinaservices.wms.common.enums.SoAssignPickersStatusEnum;

import java.util.List;


/**
 * 拣货分页条件
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
public class SoPickingPageCondition extends PageCondition {
    /**
    * 拣货单号
    */
    private String pickingNo;
    /**
     * 拣货任务单号
     */
    private String pickingTaskNo;

    /**
    * 状态
    */
    private String status;
    /**
    * 状态集合
    */
    private List<String> statusList;

    /**
    * 出库单Id
    */
    private Long soId;

    /**
    * 出库单号
    */
    private String soNo;

    /**
    * 波次单Id
    */
    private Long wvId;

    /**
    * 波次单号
    */
    private String wvNo;

    /**
     * 上架任务号
     */
    private String paNo;

    /**
     * 是否分配拣货员<br/>
     *   assign_pickers_status
     *   （unallocated:未分配，partial_allocation：部分分配，full_allocation：完全分配）
     * @see SoAssignPickersStatusEnum
     */
    private String assignPickersStatus;
    /**
     * 拣货员名称
     */
    private String pickingOpName;

    /**
     * 拣货员主键
     */
    private Long pickingOpId;

    /**
     * 拣货单号集合
     */
    private List<String> pickingNoList;
    /**
     * 标签号
     */
    private String tagNo;

    public String getPickingNo() {
        return pickingNo;
    }

    public void setPickingNo(String pickingNo) {
       this.pickingNo = pickingNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
       this.status = status;
    }

    public Long getSoId() {
        return soId;
    }

    public void setSoId(Long soId) {
       this.soId = soId;
    }

    public String getSoNo() {
        return soNo;
    }

    public void setSoNo(String soNo) {
       this.soNo = soNo;
    }

    public Long getWvId() {
        return wvId;
    }

    public void setWvId(Long wvId) {
       this.wvId = wvId;
    }

    public String getWvNo() {
        return wvNo;
    }

    public void setWvNo(String wvNo) {
       this.wvNo = wvNo;
    }

    public String getAssignPickersStatus()
    {
        return assignPickersStatus;
    }

    public void setAssignPickersStatus(String assignPickersStatus)
    {
        this.assignPickersStatus = assignPickersStatus;
    }

    public String getPickingOpName()
    {
        return pickingOpName;
    }

    public void setPickingOpName(String pickingOpName)
    {
        this.pickingOpName = pickingOpName;
    }

    public String getPickingTaskNo()
    {
        return pickingTaskNo;
    }

    public void setPickingTaskNo(String pickingTaskNo)
    {
        this.pickingTaskNo = pickingTaskNo;
    }

    public Long getPickingOpId() {
        return pickingOpId;
    }

    public void setPickingOpId(Long pickingOpId) {
        this.pickingOpId = pickingOpId;
    }

    public List<String> getStatusList()
    {
        return statusList;
    }

    public void setStatusList(List<String> statusList)
    {
        this.statusList = statusList;
    }

    public List<String> getPickingNoList()
    {
        return pickingNoList;
    }

    public void setPickingNoList(List<String> pickingNoList)
    {
        this.pickingNoList = pickingNoList;
    }

    public String getPaNo() {
        return paNo;
    }

    public void setPaNo(String paNo) {
        this.paNo = paNo;
    }

    public String getTagNo() {
        return tagNo;
    }

    public void setTagNo(String tagNo) {
        this.tagNo = tagNo;
    }
}
