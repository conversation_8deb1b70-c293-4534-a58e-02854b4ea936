package com.chinaservices.wms.module.asn.pa.domain;

import com.chinaservices.sdk.pojo.Query;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 上架任务请求结果
 */
@Data
public class PaTaskDetailQuery implements Query {

    /**
     * 主键
     */
    private String id;

    /**
     * 上架任务id
     */
    private Long paId;

    /**
     * 上架任务号
     */
    private String paNo;
    /**
     * 上架任务号
     */
    private String paDetailNo;

    /**
     * 仓库代码
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 订单类型:ASN入库单,SO出库单
     */
    private String paSource;



    /**
     * 出/入库订单id
     */
    private Long orderId;

    /**
     * 出/入库订单单号
     */
    private String orderNo;

    /**
     * 收货明细主键
     */
    private Long receiveNo;


    /**
     * 行状态(1:待上架,2:已上架)
     */
    private String status;

    /**
     * 货主代码
     */
    private Long ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 商品代码
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 批次號
     */
    private String lotNum;

    /**
     * 包装编码
     */
    private Long packageId;

    /**
     * 包装名称
     */
    private String packageName;

    /**
     * 包装单位
     */
    private Long packageUnitId;

    /**
     * 包装单位名称
     */
    private String packageUnitName;

    /**
     * 源库位主键
     */
    private Long fmLocId;

    /**
     * 源库位名称
     */
    private String fmLocName;

    /**
     * 源容器号
     */
    private String fmPalletId;

    /**
     * 源容器号
     */
    private String fmPalletNo;

    /**
     * 目标库位主键
     */
    private Long toLocId;

    /**
     * 目标库位代码
     */
    private String toLocCode;

    /**
     * 目标库位名称
     */
    private String toLocName;

    /**
     * 目标容器号id
     */
    private String toPalletId;

    /**
     * 目标容器号名称
     */
    private String toPalletNo;


    /**
     * 待上架数EA
     */
    private BigDecimal treatQtyEa;

    /**
     * 待上架数EA
     */
    private BigDecimal qtyPlanEa;

    /**
     * 上架数EA
     */
    private BigDecimal qtyPaEa;

    /**
     * 上架时间
     */
    private Date paTime;

    /**
     * 上架人
     */
    private Long paPersonId;
    /**
     * 上架人
     */
    private String paPersonName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本
     */
    private Long recVer;

    /**
     * 货架名称
     */
    private String shelfName;

    /**
     * 库区名称
     */
    private String zoneName;

    /**
     * 是否存在序列号 TRUE=存在，FASLE=不存在
     */
    private Boolean isExistenceSn = Boolean.FALSE;

    /**
     * 标签号
     */
    private String tagNo;
}
