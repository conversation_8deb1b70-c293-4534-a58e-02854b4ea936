package com.chinaservices.wms.module.rule.alloc.domain;

import com.chinaservices.module.domain.ModuleBaseUpdateItem;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 分配规则：Item
 * <AUTHOR>
 */
@Data
public class RuleAllocHeaderItem extends ModuleBaseUpdateItem {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 规则代码
     */
    private String ruleCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 包装id
     */
    private Long packageId;

    /**
     * 包装代码
     */
    private String packageCode;
    /**
     * 包装名称
     */
    private String packageName;

    /**
     * 清仓优先
     */
    private String isClearFirst;
    /**
     * 优先顺序
     */
    private String priority;

    /**
     * 是否默认
     */
    private String isDefault;

    private String status = "1";

    /**
     * 分配规则明细
     */
    @NotEmpty(message = "分配规则明细不能为空号")
    private List<RuleAllocDetailQuery> ruleAllocDetailQueryList;


}
