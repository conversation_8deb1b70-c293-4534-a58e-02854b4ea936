package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.sdk.pojo.Condition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetRightPickingListCondition implements Condition {

    /**
     * 分配类型 0=出库分配，1=波次分配，2=加工/拆分分配
     */
    private String allocationType;

    /**
     * 查询范围(1：出库单2：出库单+波次单)
     */
    private String queryScope;

    /**
     * 波次单号，波次分配时使用
     */
    private String wvNo;

    /**
     * 出库单号，出库分配时使用
     */
    private String soNo;

    /**
     * 加工拆分任务单号
     */
    private String processOrderNo;
}
