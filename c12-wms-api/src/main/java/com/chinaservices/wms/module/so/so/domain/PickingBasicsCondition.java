package com.chinaservices.wms.module.so.so.domain;

import com.chinaservices.module.domain.ModuleBaseCondition;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PickingBasicsCondition extends ModuleBaseCondition {

    /**
     * 分配类型 0=出库分配，1=波次分配，2=加工单，3=拆分分配
     */
    private String allocationType;

    /**
     * 商品ID
     */
    private Long skuId;

    /**
     * allocationType = 0 使用
     */
    @NotNull(message = "出库订单明细")
    private OutboundDetail outboundDetail;

    /**
     * allocationType = 1 使用
     */
    private WaveTimesDetail waveTimesDetail;

    /**
     * allocationType = 2 使用
     */
    private ProcessTaskDetail processTaskDetail;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OutboundDetail {

        /**
         * 出库订单明细id
         */
        private String soDetailNo;

        /**
         * 出库单号
         */
        private String soNo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WaveTimesDetail {

        /**
         * 波次单号
         */
        private String wvNo;

        /**
         * 出库单号
         */
        private String soNo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProcessTaskDetail {

        /**
         * 加工/拆分单编号
         */
        private String processOrderNo;

        /**
         * 加工/拆分单任务编号
         */
        private String processTaskNo;

        /**
         * 加工/拆分单明细ID
         */
        private Long processTaskDetailsId;
    }
}
