package com.chinaservices.wms.module.so.so.domain;


import com.chinaservices.sdk.pojo.Query;
import com.chinaservices.wms.module.so.wv.domain.WvSkuQuery;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Auther: Vickyy.Liu
 * @Date: 2023-3-7
 * 构建查询条件
 */
@Data
public class SoHeaderQuery implements Query {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 出库单号
     */
    private String soNo;
    /**
     * 上游单号
     */
    private String logisticNo;
    /**
     * 销售单号
     */
    private String saleOrderNo;

    /**
     * 订单类型:数据字典（so_type）
     */
    private String soType;
    /**
     * 订单来源：数据字典（order_source_type）
     */
    private String orderSource;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 承运商id
     */
    private Long carrierId;

    /**
     * 承运商名称
     */
    private String carrierName;

    /**
     * 审核状态：wms_audit_status
     */
    private String auditStatus;
    /**
     * 优先级
     */
    private String priority;
    /**
     * 分配状态
     */
    private String allocStatus;
    /**
     * 货主主键
     */
    private String ownerId;
    /**
     * 货主代码
     */
    private String ownerCode;
    /**
     * 货主名称
     */
    private String ownerName;
    /**
     * 仓库主键
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 客户id
     */
    private String clientId;
    /**
     * 客户名称
     */
    private String clientName;
    /**
     * 订单状态：数据字典（order_status）
     */
    private String status;
    /**
     * 运输单号
     */
    private String shippingOrderNo;
    /**
     * 商品明细
     */
    private List<WvSkuQuery> soDetail;



    // 拣货状态
    private String pickStatus;
    // 波次表的出库明细
    private String wvDetailId;
    /**
     * 波次明细表存在状态
     */
    private String wvDetailStatus;

    /**
     * 出库单有多少条生成波次
     */
    private Long soDetailCount;


    /**
     * 订单取消
     */
    private Boolean isCancel = Boolean.FALSE;
    /**
     * 订单完结
     */
    private Boolean isComplete = Boolean.FALSE;
    /**
     * 查看分配明细
     */
    private Boolean isViewDetail = Boolean.FALSE;
    /**
     * 分配
     */
    private Boolean isAllocated = Boolean.FALSE;

    /**
     * 编辑
     */
    private Boolean isEdit = Boolean.FALSE;

    /**
     * 未审核
     */
    private Boolean isAudit = Boolean.FALSE;

    /**
     * 取消审核
     */
    private Boolean isCancelAudit = Boolean.FALSE;

    /**
     * 已分配数EA
     */
    private BigDecimal qtyAllocationEa;

    /**
     * 已拣货数EA
     */
    private BigDecimal qtyPickedEa;

    /**
     * 上架任务号
     */
    private String paNo;

    /**
     * 上架状态
     */
    private String paStatus;
}
