package com.chinaservices.wms.module.so.so.domain;


import com.chinaservices.module.domain.ModuleBaseUpdateItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 拣货任务单拣货数量更新项
 *
 * <AUTHOR>
 * @date 2025/01/19
 */
@Data
public class SoAllocationPickingItem extends ModuleBaseUpdateItem
{
    private Long id;
    /**
     * 已拣货数
     */
    private BigDecimal pickingEa;
    /**
     * 拣货时间
     */
    private Date pickingTime;
    /**
     * 拣货状态
     */
    private String pickingStatus;
    /**
     * 拣货序列号字符串，多个,隔开
     */
    private String pickingSnNoStr;
    /**
     * 任务状态
     */
    private String taskStatus;
    /**
     * 二次分拣任务状态
     */
    private String sortingTaskStatus;
    /**
     * 二次分拣状态
     */
    private String secondarySortingStatus;
    /**
     * 标签号字符串，多个,隔开
     */
    private String tagNo;
}
