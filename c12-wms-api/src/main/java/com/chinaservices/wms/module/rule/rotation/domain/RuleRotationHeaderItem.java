package com.chinaservices.wms.module.rule.rotation.domain;

import com.chinaservices.module.domain.ModuleBaseUpdateItem;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 周转规则 Item
 */
@Data
public class RuleRotationHeaderItem extends ModuleBaseUpdateItem {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 规则代码
     */
    private String ruleCode;

    /**
     * 规则名称
     */
    @NotBlank(message = "请输入规则名称")
    private String ruleName;

    /**
     * 是否默认
     */
    private String isDefault;

    /**
     * 批次属性id
     */
    @NotNull
    private Long lotId;

    /**
     * 批次属性code
     */
    @NotBlank(message = "请选择批次属性")
    private String lotCode;

    /**
     * 批次属性名称
     */
    @NotBlank(message = "请选择批次属性")
    private String lotName;

    /**
     * 仓库ID
     */
    @NotNull(message = "请选择仓库")
    private Long warehouseId;

    /**
     * 仓库名称
     */
    @NotBlank(message = "请选择仓库")
    private String warehouseName;

    private String status = "1";

    @NotEmpty(message = "周转规则明细不能为空")
    private List<RuleRotationDetailItem> ruleRotationDetailItemList;

}
