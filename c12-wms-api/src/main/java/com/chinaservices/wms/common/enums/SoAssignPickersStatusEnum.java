package com.chinaservices.wms.common.enums;

import cn.hutool.core.util.StrUtil;

/**
 * 分配分配拣货员状态枚举<br/>
 * assign_pickers_status
 * （unallocated:未分配，partial_allocation：部分分配，full_allocation：完全分配）
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
public enum SoAssignPickersStatusEnum
{
    /**
     * 未分配
     */
    UNALLOCATED("unallocated", "未分配"),
    /**
     * 部分分配
     */
    PARTIAL_ALLOCATION("partial_allocation", "部分分配"),
    /**
     * 完全分配
     */
    FULL_ALLOCATION("full_allocation", "完全分配"),
    ;

    private final String code;
    private final String name;

    SoAssignPickersStatusEnum(String code, String name)
    {
        this.code = code;
        this.name = name;
    }

    public String getCode()
    {
        return code;
    }

    public String getName()
    {
        return name;
    }

    public static SoAssignPickersStatusEnum fromCode(String code) {
        if (StrUtil.isEmpty(code)) {
            return null;
        }
        for (SoAssignPickersStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

}
